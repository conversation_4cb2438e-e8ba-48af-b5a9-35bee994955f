# Design System & Style Guide

## 🎨 Color Palette

### Primary Colors
```css
:root {
  /* Primary Blue - Trust & Professionalism */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;  /* Main brand color */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Secondary Green - Growth & Success */
  --secondary-50: #ecfdf5;
  --secondary-100: #d1fae5;
  --secondary-200: #a7f3d0;
  --secondary-300: #6ee7b7;
  --secondary-400: #34d399;
  --secondary-500: #10b981;  /* Accent color */
  --secondary-600: #059669;
  --secondary-700: #047857;
  --secondary-800: #065f46;
  --secondary-900: #064e3b;
}
```

### Neutral Colors
```css
:root {
  /* Grays - Professional & Clean */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
}
```

## 📝 Typography System

### Font Stack
```css
:root {
  /* Primary Font - Inter */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Secondary Font - Poppins (for headings) */
  --font-secondary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Monospace Font */
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}
```

### Typography Scale
```css
/* Heading Styles */
.text-h1 {
  font-family: var(--font-secondary);
  font-size: 3rem;        /* 48px */
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.text-h2 {
  font-family: var(--font-secondary);
  font-size: 2.25rem;     /* 36px */
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.text-h3 {
  font-family: var(--font-secondary);
  font-size: 1.875rem;    /* 30px */
  font-weight: 600;
  line-height: 1.3;
}

.text-h4 {
  font-family: var(--font-primary);
  font-size: 1.5rem;      /* 24px */
  font-weight: 600;
  line-height: 1.4;
}

/* Body Text */
.text-body-lg {
  font-family: var(--font-primary);
  font-size: 1.125rem;    /* 18px */
  font-weight: 400;
  line-height: 1.6;
}

.text-body {
  font-family: var(--font-primary);
  font-size: 1rem;        /* 16px */
  font-weight: 400;
  line-height: 1.6;
}

.text-body-sm {
  font-family: var(--font-primary);
  font-size: 0.875rem;    /* 14px */
  font-weight: 400;
  line-height: 1.5;
}
```

## 🔲 Spacing System

### Spacing Scale (8px base unit)
```css
:root {
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
}
```

## 🎭 Component Styles

### Button Variants
```css
/* Primary Button */
.btn-primary {
  background: var(--primary-600);
  color: white;
  padding: var(--space-3) var(--space-6);
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid var(--primary-600);
}

.btn-primary:hover {
  background: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Secondary Button */
.btn-secondary {
  background: transparent;
  color: var(--primary-600);
  padding: var(--space-3) var(--space-6);
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid var(--primary-600);
}

.btn-secondary:hover {
  background: var(--primary-600);
  color: white;
  transform: translateY(-1px);
}
```

### Card Styles
```css
.card {
  background: white;
  border-radius: 1rem;
  padding: var(--space-6);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid var(--gray-200);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-premium {
  background: linear-gradient(135deg, white 0%, var(--gray-50) 100%);
  border: 1px solid var(--gray-200);
  backdrop-filter: blur(10px);
}
```

## ✨ Animation System

### Transition Utilities
```css
:root {
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;
  --transition-slower: 0.5s ease;
  
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
}
```

### Animation Classes
```css
.animate-fade-in {
  animation: fadeIn 0.6s var(--ease-out);
}

.animate-slide-up {
  animation: slideUp 0.6s var(--ease-out);
}

.animate-scale-in {
  animation: scaleIn 0.3s var(--ease-out);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}
```

## 📐 Layout System

### Container Sizes
```css
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container { max-width: 640px; }
}

@media (min-width: 768px) {
  .container { max-width: 768px; }
}

@media (min-width: 1024px) {
  .container { max-width: 1024px; }
}

@media (min-width: 1280px) {
  .container { max-width: 1280px; }
}
```

### Grid System
```css
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-6);
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-6);
}
```

This design system ensures consistency, scalability, and premium quality across all components and pages.
