/**
 * Schopio Design System Constants
 * Trust-building colors and design tokens
 */

// Brand Colors - Trust-building psychology
export const COLORS = {
  // Primary - Deep Blue (Trust & Professionalism)
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb', // Main brand color
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  
  // Success - Green (Growth & Success)
  success: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981', // Main success color
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
  },
  
  // Warning - Orange
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  
  // Error - Red
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
  
  // Neutral - Gray
  neutral: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  }
} as const

// Typography Scale
export const TYPOGRAPHY = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'monospace'],
  },
  fontSize: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
    '6xl': '3.75rem',  // 60px
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  }
} as const

// Spacing Scale
export const SPACING = {
  0: '0',
  1: '0.25rem',   // 4px
  2: '0.5rem',    // 8px
  3: '0.75rem',   // 12px
  4: '1rem',      // 16px
  5: '1.25rem',   // 20px
  6: '1.5rem',    // 24px
  8: '2rem',      // 32px
  10: '2.5rem',   // 40px
  12: '3rem',     // 48px
  16: '4rem',     // 64px
  20: '5rem',     // 80px
  24: '6rem',     // 96px
  32: '8rem',     // 128px
} as const

// Border Radius
export const RADIUS = {
  none: '0',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',
} as const

// Shadows
export const SHADOWS = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
} as const

// Animation Durations
export const ANIMATION = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  }
} as const

// Business Constants
export const BUSINESS = {
  pricing: {
    basePerStudent: 80, // ₹80 per student per month
    yearlyDiscount: 18.75, // 18.75% discount for yearly plans
    yearlyPerStudent: 65, // ₹65 per student per month (yearly)
  },
  billing: {
    gracePeriodDays: 7,
    suspensionDays: 15,
    cancellationDays: 30,
  },
  demo: {
    durationMinutes: 30,
    availableSlots: ['10:00', '11:00', '14:00', '15:00', '16:00'],
  },
  trial: {
    periodDays: 14,
  }
} as const

// API Endpoints
export const API_ENDPOINTS = {
  leads: '/api/leads',
  demoBooking: '/api/demo-booking',
  pricing: '/api/pricing',
  admin: {
    auth: '/api/admin/auth',
    leads: '/api/admin/leads',
    clients: '/api/admin/clients',
    billing: '/api/admin/billing',
    analytics: '/api/admin/analytics',
  },
  school: {
    auth: '/api/school/auth',
    subscription: '/api/school/subscription',
    payments: '/api/school/payments',
    support: '/api/school/support',
  }
} as const

// Feature Flags
export const FEATURES = {
  enableDemoBooking: true,
  enablePricingCalculator: true,
  enableLiveChat: false,
  enableAIInsights: true,
  enableWebApp: true,
} as const

// Social Links
export const SOCIAL_LINKS = {
  website: 'https://schopio.orionixtech.com',
  linkedin: 'https://linkedin.com/company/schopio',
  twitter: 'https://twitter.com/schopio',
  facebook: 'https://facebook.com/schopio',
  youtube: 'https://youtube.com/@schopio',
} as const

// Contact Information
export const CONTACT = {
  email: '<EMAIL>',
  phone: '+91 9304928363',
  address: 'Bangalore, Karnataka, India',
  supportEmail: '<EMAIL>',
  salesEmail: '<EMAIL>',
} as const

// SEO Constants
export const SEO = {
  siteName: 'Schopio - Complete School Management System & ERP Software',
  tagline: 'Best School Management System & ERP Software in India',
  description: 'Schopio is the leading school management system and ERP software in India. Complete educational management software with 8+ modules, AI-powered insights, student information system, and school administration tools. Trusted by 500+ schools.',
  keywords: [
    // Primary target keywords
    'school management system',
    'school ERP',
    'ERP system',
    'educational management software',
    'school administration software',
    // Secondary target keywords
    'school courses management',
    'best ERP system for schools',
    'student information system',
    'school billing software',
    'educational institution management',
    // Long-tail keywords
    'school management system India',
    'best school ERP software',
    'complete school management solution',
    'AI-powered school management',
    'school administration system',
    'education ERP software',
    'school management platform',
    'student management system',
    'school fee management software',
    'academic management system'
  ],
  // Page-specific SEO data
  pages: {
    home: {
      title: 'Best School Management System & ERP Software in India | Schopio',
      description: 'Schopio is India\'s leading school management system and ERP software. Complete educational management solution with 8+ modules, AI insights, and student information system. Trusted by 500+ schools.',
      keywords: 'school management system, school ERP, educational management software, best school management system India'
    },
    solutions: {
      title: 'Complete School Management Solutions & ERP System | Schopio',
      description: 'Comprehensive school management solutions and ERP system for educational institutions. Student information system, academic management, billing, and administration tools in one platform.',
      keywords: 'school management solutions, educational ERP system, school administration software, student information system'
    },
    packages: {
      title: 'School Management System Pricing & ERP Software Packages | Schopio',
      description: 'Affordable school management system pricing and ERP software packages. Complete educational management solution starting from ₹20 per student. Compare plans and features.',
      keywords: 'school management system pricing, school ERP software cost, educational management software price, school administration software packages'
    },
    aiFeatures: {
      title: 'AI-Powered School Management System & Educational Analytics | Schopio',
      description: 'Advanced AI-powered school management system with predictive analytics, automated insights, and intelligent educational management features. Transform your school with AI.',
      keywords: 'AI school management system, educational analytics, AI-powered ERP, smart school management, predictive analytics education'
    }
  }
}
