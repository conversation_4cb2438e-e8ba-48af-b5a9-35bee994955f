CREATE TABLE IF NOT EXISTS "subscription_change_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"subscription_id" uuid,
	"admin_id" uuid,
	"change_type" varchar(50) NOT NULL,
	"old_values" jsonb,
	"new_values" jsonb,
	"effective_date" date NOT NULL,
	"billing_adjustment" jsonb,
	"commission_adjustment" jsonb,
	"reason" text,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "billing_subscriptions" ADD COLUMN "operational_expenses" jsonb;--> statement-breakpoint
ALTER TABLE "billing_subscriptions" ADD COLUMN "database_costs" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "billing_subscriptions" ADD COLUMN "website_maintenance" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "billing_subscriptions" ADD COLUMN "support_costs" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "billing_subscriptions" ADD COLUMN "infrastructure_costs" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "billing_subscriptions" ADD COLUMN "total_operational_expenses" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "billing_subscriptions" ADD COLUMN "notes" text;--> statement-breakpoint
ALTER TABLE "billing_subscriptions" ADD COLUMN "setup_fee" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "operational_expenses" jsonb;--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "database_costs" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "website_maintenance" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "support_costs" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "infrastructure_costs" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
ALTER TABLE "subscriptions" ADD COLUMN "total_operational_expenses" numeric(10, 2) DEFAULT '0';--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "subscription_change_log" ADD CONSTRAINT "subscription_change_log_subscription_id_billing_subscriptions_id_fk" FOREIGN KEY ("subscription_id") REFERENCES "public"."billing_subscriptions"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "subscription_change_log" ADD CONSTRAINT "subscription_change_log_admin_id_admin_users_id_fk" FOREIGN KEY ("admin_id") REFERENCES "public"."admin_users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
