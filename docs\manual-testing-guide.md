# 🧪 **COMPREHENSIVE MANUAL TESTING GUIDE**

**Testing Date:** July 8, 2025  
**System Status:** Database cleaned, ready for testing  
**Admin Preserved:** ✅ Super Administrator (<EMAIL>)

---

## 🎯 **TESTING OVERVIEW**

The database has been completely cleaned except for the admin login. You can now manually test every function as both client and admin to verify the complete payment and discount system functionality.

### **🔑 ADMIN CREDENTIALS**
- **Email:** <EMAIL>
- **Password:** [Use your existing admin password]
- **Role:** super_admin

---

## 📋 **TESTING CHECKLIST**

### **1. 👨‍💼 ADMIN PORTAL TESTING**

#### **🔐 Admin Authentication**
- [ ] **Login Test**
  - Navigate to: `http://localhost:3000/admin/login`
  - Login with preserved admin credentials
  - Verify dashboard loads correctly

#### **🏫 Client Management**
- [ ] **Create New School Client**
  - Go to Admin → Clients → Add New Client
  - Fill in school details (name, email, contact info)
  - Set subscription details (monthly amount, student count)
  - Verify client creation success

- [ ] **Billing Subscription Setup**
  - Create billing subscription for the new client
  - Set monthly amount (e.g., ₹5000 for 100 students)
  - Configure billing cycle and due dates
  - Verify subscription appears in billing dashboard

#### **💰 Discount Management**
- [ ] **Create Time-Limited Discount**
  - Navigate to subscription details
  - Create 20% discount for 6 months
  - Set start and end dates
  - Verify discount appears in subscription

- [ ] **Apply Multiple Discounts**
  - Create different discount percentages (10%, 15%, 25%)
  - Test different durations (3, 6, 12 months)
  - Verify discount calculations are correct

#### **🤝 Partner Management**
- [ ] **Create Partner Account**
  - Go to Admin → Partners → Add New Partner
  - Set partner details and commission percentage (25%)
  - Assign referral code
  - Link partner to school client

- [ ] **Commission Configuration**
  - Configure commission percentage per partner
  - Set holding period (30 days)
  - Configure operational expenses
  - Verify commission calculation logic

#### **💸 Expense Management**
- [ ] **Operational Expenses**
  - Add monthly operational costs per subscription
  - Set expense categories (hosting, support, etc.)
  - Verify expenses affect commission calculations

#### **📊 Reporting & Analytics**
- [ ] **Commission Dashboard**
  - View partner commission summary
  - Check eligible vs held commissions
  - Verify commission calculation accuracy

---

### **2. 🏫 SCHOOL PORTAL TESTING**

#### **🔐 School Authentication**
- [ ] **School Registration**
  - Navigate to: `http://localhost:3000/register`
  - Register new school account
  - Verify email verification process

- [ ] **School Login**
  - Login with school credentials
  - Verify school dashboard loads

#### **💳 Billing & Payments**
- [ ] **Billing Dashboard**
  - View current subscription details
  - Check monthly amount and due dates
  - Verify discount display (if applicable)

- [ ] **Payment Processing**
  - Navigate to payment section
  - Test Razorpay integration (test mode)
  - Process a monthly payment
  - Verify payment confirmation

- [ ] **Advance Payments**
  - Test multi-month payment (3-6 months)
  - Verify advance payment balance tracking
  - Check remaining months calculation

- [ ] **Discount Verification**
  - If discount applied by admin, verify it shows correctly
  - Check discounted amount calculation
  - Verify discount expiration display

#### **📄 Invoice & Receipt Management**
- [ ] **Invoice Generation**
  - Verify invoices are generated automatically
  - Check invoice number uniqueness
  - Download PDF invoices

- [ ] **Payment History**
  - View all payment transactions
  - Check payment status and dates
  - Verify receipt downloads

---

### **3. 🤝 PARTNER PORTAL TESTING**

#### **🔐 Partner Authentication**
- [ ] **Partner Login**
  - Login with partner credentials (created by admin)
  - Verify partner dashboard loads

#### **💰 Commission Tracking**
- [ ] **Commission Dashboard**
  - View referred schools
  - Check commission earnings
  - Verify commission calculations exclude discounts

- [ ] **Earnings History**
  - View monthly earnings breakdown
  - Check holding period status
  - Verify commission release dates

#### **💸 Withdrawal Management**
- [ ] **Withdrawal Requests**
  - Create withdrawal request
  - Verify minimum amount requirements
  - Check withdrawal status tracking

---

### **4. 💰 DISCOUNT SYSTEM INTEGRATION TESTING**

#### **🔄 End-to-End Discount Flow**
- [ ] **Admin Creates Discount**
  - Admin creates 15% discount for 3 months
  - Applies to specific school subscription

- [ ] **School Sees Discount**
  - School logs in and sees discounted amount
  - Discount details displayed correctly
  - Original amount vs discounted amount shown

- [ ] **Payment with Discount**
  - School processes payment with discount applied
  - Payment amount reflects discount
  - Invoice shows discount breakdown

- [ ] **Commission Calculation**
  - Partner commission calculated on original amount (excluding discount)
  - Admin absorbs discount cost
  - Commission tracking shows correct amounts

#### **⏰ Discount Expiration Testing**
- [ ] **Automatic Expiration**
  - Create short-term discount (test with 1-day duration)
  - Verify automatic deactivation
  - Check subscription returns to original amount

---

### **5. 📧 EMAIL & NOTIFICATION TESTING**

#### **📨 Automated Emails**
- [ ] **Payment Confirmation**
  - Process payment and verify confirmation email
  - Check email content and PDF attachment

- [ ] **Invoice Generation**
  - Verify invoice generation emails
  - Check PDF invoice attachment

- [ ] **Payment Reminders**
  - Test overdue payment reminders
  - Verify reminder email content

#### **🔔 System Notifications**
- [ ] **Admin Notifications**
  - New payment notifications
  - Discount expiration alerts
  - Commission payout notifications

---

### **6. 🔧 SYSTEM INTEGRATION TESTING**

#### **💳 Razorpay Integration**
- [ ] **Payment Gateway**
  - Test Razorpay test mode payments
  - Verify payment success/failure handling
  - Check webhook processing

#### **📊 Database Consistency**
- [ ] **Data Integrity**
  - Verify all transactions are recorded
  - Check foreign key relationships
  - Confirm data consistency across tables

#### **⚡ Performance Testing**
- [ ] **Page Load Times**
  - Test dashboard loading speeds
  - Verify query performance with indexes
  - Check responsive design

---

## 🎯 **TESTING SCENARIOS**

### **Scenario 1: Complete School Onboarding**
1. Admin creates new school client
2. Sets up billing subscription (₹7500/month)
3. Creates 20% discount for first 6 months
4. School registers and logs in
5. School sees discounted amount (₹6000/month)
6. School processes first payment
7. Partner commission calculated on ₹7500 (original amount)

### **Scenario 2: Multi-Month Advance Payment**
1. School wants to pay for 6 months in advance
2. School processes advance payment
3. System tracks remaining months
4. Monthly deductions from advance balance
5. Discount applied to each month if active

### **Scenario 3: Partner Commission Flow**
1. Partner refers new school
2. School makes monthly payments
3. Commission held for 30 days
4. Commission becomes eligible for payout
5. Partner requests withdrawal
6. Admin processes payout

---

## 🚨 **CRITICAL TESTING POINTS**

### **🔍 Must Verify:**
- [ ] **Discount Exclusion:** Partners don't see discount amounts in their commission calculations
- [ ] **Data Consistency:** All payment transactions properly recorded
- [ ] **Email Delivery:** All automated emails working correctly
- [ ] **PDF Generation:** Invoices and receipts generate properly
- [ ] **Commission Accuracy:** Mathematical calculations are correct
- [ ] **Security:** Role-based access control working
- [ ] **Performance:** Page loads under 2 seconds

### **⚠️ Watch For:**
- Payment processing errors
- Email delivery failures
- Discount calculation mistakes
- Commission calculation errors
- Database constraint violations
- UI/UX issues

---

## 📝 **TESTING NOTES**

### **🔧 Development Server**
Make sure the development server is running:
```bash
npm run dev
# or
bun dev
```

### **🌐 Testing URLs**
- **Landing Page:** http://localhost:3000
- **Admin Login:** http://localhost:3000/admin/login
- **School Registration:** http://localhost:3000/register
- **School Login:** http://localhost:3000/login

### **💳 Test Payment Details**
Use Razorpay test mode credentials:
- **Test Card:** 4111 1111 1111 1111
- **CVV:** Any 3 digits
- **Expiry:** Any future date

---

## ✅ **COMPLETION CHECKLIST**

Mark each section as complete:
- [ ] Admin Portal Testing (Complete)
- [ ] School Portal Testing (Complete)
- [ ] Partner Portal Testing (Complete)
- [ ] Discount System Testing (Complete)
- [ ] Email System Testing (Complete)
- [ ] Integration Testing (Complete)

**🎯 Goal:** Verify 100% functionality before production deployment

---

**🚀 Ready to start testing! Begin with admin login and work through each section systematically.**
