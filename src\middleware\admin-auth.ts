import { Context } from 'hono'
import jwt from 'jsonwebtoken'
import { db } from '@/src/db'
import { adminUsers } from '@/src/db/schema'
import { eq } from 'drizzle-orm'
import { auditLogger } from '@/src/services/auditLogger'
import { securityMonitor } from '@/src/services/securityMonitor'
import { rateLimiter } from '@/src/services/rateLimiter'
import crypto from 'crypto'

// Admin user interface for context
export interface AdminUser {
  id: string
  email: string
  name: string
  role: string
  permissions: string[]
}

// Helper function to generate JWT token for admin with enhanced security
export function generateAdminToken(adminId: string, email: string, role: string, permissions: string[]): string {
  const jti = crypto.randomUUID() // JWT ID for token tracking
  const iat = Math.floor(Date.now() / 1000) // Issued at

  return jwt.sign(
    {
      userId: adminId,
      email,
      role,
      permissions,
      type: 'admin',
      jti, // JWT ID for revocation
      iat, // Issued at timestamp
      iss: 'schopio-admin', // Issuer
      aud: 'schopio-admin-panel' // Audience
    },
    process.env.JWT_SECRET || 'fallback-secret-key',
    {
      expiresIn: '8h', // Reduced from 24h for better security
      algorithm: 'HS256'
    }
  )
}

// Enhanced admin authentication middleware with security monitoring
export const adminAuthMiddleware = async (c: Context, next: () => Promise<void>) => {
  const startTime = Date.now()
  const requestId = crypto.randomUUID()
  const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
  const userAgent = c.req.header('user-agent') || 'unknown'

  // Add request ID to context for tracking
  c.set('requestId', requestId)

  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // Log failed authentication attempt
      await auditLogger.logAuth('failed_login', {
        email: 'unknown',
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Missing authorization header'
      })

      await securityMonitor.logSecurityEvent({
        type: 'unauthorized_access',
        ipAddress,
        userAgent,
        details: {
          endpoint: c.req.path,
          method: c.req.method,
          reason: 'Missing authorization header',
          requestId
        },
        severity: 'medium'
      })

      return c.json({
        error: 'Authorization token required',
        requestId
      }, 401)
    }

    const token = authHeader.substring(7)

    // Enhanced JWT verification
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key', {
      algorithms: ['HS256'],
      issuer: 'schopio-admin',
      audience: 'schopio-admin-panel'
    }) as any

    if (!decoded.userId || !decoded.email || decoded.type !== 'admin') {
      await auditLogger.logAuth('failed_login', {
        email: decoded.email || 'unknown',
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Invalid token format'
      })

      return c.json({
        error: 'Invalid admin token',
        requestId
      }, 401)
    }

    // Fetch admin user from database to ensure they still exist and are active
    const [admin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.id, decoded.userId))
      .limit(1)

    if (!admin) {
      await auditLogger.logAuth('failed_login', {
        email: decoded.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Admin user not found'
      })

      return c.json({
        error: 'Admin user not found',
        requestId
      }, 401)
    }

    if (!admin.isActive) {
      await auditLogger.logAuth('failed_login', {
        adminId: admin.id,
        email: admin.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Admin account deactivated'
      })

      await securityMonitor.logSecurityEvent({
        type: 'unauthorized_access',
        adminId: admin.id,
        ipAddress,
        userAgent,
        details: {
          reason: 'Deactivated account access attempt',
          adminEmail: admin.email,
          requestId
        },
        severity: 'high'
      })

      return c.json({
        error: 'Admin account is deactivated',
        requestId
      }, 403)
    }

    // Add admin to context
    const adminUser: AdminUser = {
      id: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role,
      permissions: Array.isArray(admin.permissions) ? admin.permissions : []
    }

    c.set('admin', adminUser)

    // Log successful authentication
    await auditLogger.logAuth('login', {
      adminId: admin.id,
      email: admin.email,
      ipAddress,
      userAgent,
      success: true
    })

    // Log data access for audit trail
    await auditLogger.logDataAccess('admin_api_access', {
      adminId: admin.id,
      resource: c.req.path,
      ipAddress,
      userAgent,
      success: true
    })

    await next()

    // Log request completion time for monitoring
    const duration = Date.now() - startTime
    if (duration > 5000) { // Log slow requests (>5 seconds)
      await securityMonitor.logSecurityEvent({
        type: 'suspicious_activity',
        adminId: admin.id,
        ipAddress,
        userAgent,
        details: {
          type: 'slow_request',
          duration,
          endpoint: c.req.path,
          method: c.req.method,
          requestId
        },
        severity: 'low'
      })
    }

  } catch (error) {
    let errorMessage = 'Authentication failed'
    let statusCode = 500

    if (error instanceof jwt.JsonWebTokenError) {
      errorMessage = 'Invalid token'
      statusCode = 401
    } else if (error instanceof jwt.TokenExpiredError) {
      errorMessage = 'Token expired'
      statusCode = 401
    } else if (error instanceof jwt.NotBeforeError) {
      errorMessage = 'Token not active'
      statusCode = 401
    }

    // Log authentication error
    await auditLogger.logAuth('failed_login', {
      email: 'unknown',
      ipAddress,
      userAgent,
      success: false,
      errorMessage
    })

    await securityMonitor.logSecurityEvent({
      type: 'unauthorized_access',
      ipAddress,
      userAgent,
      details: {
        error: errorMessage,
        endpoint: c.req.path,
        method: c.req.method,
        requestId
      },
      severity: 'medium'
    })

    console.error('Admin auth middleware error:', error)
    return c.json({
      error: errorMessage,
      requestId
    }, statusCode as any)
  }
}

// Role-based permission middleware
export const requireAdminRole = (allowedRoles: string[]) => {
  return async (c: Context, next: () => Promise<void>) => {
    const admin = c.get('admin') as AdminUser
    
    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    // Super admin has access to everything
    if (admin.role === 'super_admin') {
      await next()
      return
    }

    if (!allowedRoles.includes(admin.role)) {
      return c.json({ 
        error: 'Insufficient permissions',
        required: allowedRoles,
        current: admin.role
      }, 403)
    }

    await next()
  }
}

// Permission-based middleware
export const requirePermission = (permission: string) => {
  return async (c: Context, next: () => Promise<void>) => {
    const admin = c.get('admin') as AdminUser
    
    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    // Super admin has all permissions
    if (admin.permissions?.includes('*') || admin.role === 'super_admin') {
      await next()
      return
    }

    // Check specific permission
    if (!admin.permissions?.includes(permission)) {
      return c.json({ 
        error: 'Insufficient permissions',
        required: permission,
        current: admin.permissions
      }, 403)
    }

    await next()
  }
}

// Security headers middleware for admin routes
export const adminSecurityHeaders = async (c: Context, next: () => Promise<void>) => {
  // Enhanced security headers for admin panel
  c.header('X-Content-Type-Options', 'nosniff')
  c.header('X-Frame-Options', 'DENY')
  c.header('X-XSS-Protection', '1; mode=block')
  c.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
  c.header('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:")
  c.header('Referrer-Policy', 'strict-origin-when-cross-origin')
  c.header('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
  
  await next()
}

// Enhanced rate limiting middleware for admin routes
export const adminRateLimit = rateLimiter.createMiddleware('admin:api', {
  keyGenerator: (c: Context) => {
    const ip = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const admin = c.get('admin') as AdminUser | undefined

    // Use admin ID if authenticated, otherwise use IP
    return admin ? `admin:${admin.id}` : `ip:${ip}`
  },
  onLimitReached: async (c: Context, result) => {
    const ip = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'
    const admin = c.get('admin') as AdminUser | undefined

    // Log rate limit violation
    await securityMonitor.logSecurityEvent({
      type: 'rate_limit_exceeded',
      adminId: admin?.id,
      ipAddress: ip,
      userAgent,
      details: {
        endpoint: c.req.path,
        method: c.req.method,
        remaining: result.remaining,
        resetTime: result.resetTime,
        retryAfter: result.retryAfter
      },
      severity: 'medium'
    })

    return c.json({
      error: 'Rate limit exceeded',
      message: 'Too many requests from this admin account or IP address',
      retryAfter: result.retryAfter,
      resetTime: result.resetTime
    }, 429)
  }
})

// Specific rate limiting for admin login
export const adminLoginRateLimit = rateLimiter.createMiddleware('admin:login', {
  keyGenerator: (c: Context) => {
    return c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
  },
  onLimitReached: async (c: Context, result) => {
    const ip = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'

    // Log potential brute force attack
    await securityMonitor.logSecurityEvent({
      type: 'failed_login',
      ipAddress: ip,
      userAgent,
      details: {
        endpoint: c.req.path,
        method: c.req.method,
        type: 'brute_force_attempt',
        retryAfter: result.retryAfter
      },
      severity: 'high'
    })

    return c.json({
      error: 'Too many login attempts',
      message: 'Account temporarily locked due to multiple failed login attempts',
      retryAfter: result.retryAfter,
      resetTime: result.resetTime
    }, 429)
  }
})

// Helper function to get current admin from context
export const getCurrentAdmin = (c: Context): AdminUser | null => {
  return c.get('admin') as AdminUser || null
}

// Helper function to check if admin has specific role
export const hasAdminRole = (admin: AdminUser, role: string): boolean => {
  return admin.role === 'super_admin' || admin.role === role
}

// Helper function to check if admin has any of the specified roles
export const hasAnyAdminRole = (admin: AdminUser, roles: string[]): boolean => {
  return admin.role === 'super_admin' || roles.includes(admin.role)
}

// Helper function to check if admin has specific permission
export const hasPermission = (admin: AdminUser, permission: string): boolean => {
  return admin.role === 'super_admin' || 
         admin.permissions?.includes('*') || 
         admin.permissions?.includes(permission)
}

// Admin role definitions with their default permissions
export const ADMIN_ROLES = {
  super_admin: {
    name: 'Super Administrator',
    permissions: ['*'],
    description: 'Full system access'
  },
  sales: {
    name: 'Sales Manager',
    permissions: [
      'leads:read', 'leads:write', 'leads:convert',
      'clients:read', 'clients:write',
      'demos:read', 'demos:write'
    ],
    description: 'Sales team access'
  },
  support: {
    name: 'Support Agent',
    permissions: [
      'clients:read', 'tickets:read', 'tickets:write',
      'billing:read', 'subscriptions:read'
    ],
    description: 'Customer support access'
  },
  billing: {
    name: 'Billing Manager',
    permissions: [
      'billing:read', 'billing:write', 'invoices:read', 'invoices:write',
      'payments:read', 'subscriptions:read', 'subscriptions:write'
    ],
    description: 'Billing and finance access'
  }
} as const

export type AdminRoleType = keyof typeof ADMIN_ROLES
