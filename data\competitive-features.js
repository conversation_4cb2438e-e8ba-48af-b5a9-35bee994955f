// Competitive Feature Comparison Data - Exact from Screenshot
// This data matches the Feature Comparison Overview table shown

export const competitiveFeatures = [
  {
    id: 1,
    feature: "Finance + Payroll + Audit",
    category: "Financial Management",
    competitors: {
      status: "partial",
      description: "Partial",
      icon: "✅",
      color: "text-green-600"
    },
    ourSolution: {
      status: "complete",
      description: "Full Lifecycle",
      details: "Complete financial ecosystem including payroll processing, audit trails, automated reconciliation, and comprehensive reporting",
      icon: "✅",
      color: "text-green-600"
    },
    priority: "high"
  },
  {
    id: 2,
    feature: "Fee Management + Razorpay",
    category: "Payment Processing",
    competitors: {
      status: "basic",
      description: "Basic UPI",
      icon: "✅",
      color: "text-green-600"
    },
    ourSolution: {
      status: "advanced",
      description: "Full Digital Reconciliation",
      details: "Advanced payment gateway integration with automatic reconciliation, multiple payment methods, and real-time transaction tracking",
      icon: "✅",
      color: "text-green-600"
    },
    priority: "high"
  },
  {
    id: 3,
    feature: "AI-Powered Analytics & Predictive",
    category: "Artificial Intelligence",
    competitors: {
      status: "rare",
      description: "Rare",
      icon: "🚫",
      color: "text-red-600"
    },
    ourSolution: {
      status: "advanced",
      description: "Gemma-3.27B Insights",
      details: "Advanced AI model providing student dropout prediction, performance analytics, personalized intervention recommendations, and trend forecasting",
      icon: "✅",
      color: "text-green-600"
    },
    priority: "critical"
  },
  {
    id: 4,
    feature: "Transport Management System",
    category: "Safety & Transportation",
    competitors: {
      status: "minimal",
      description: "Minimal",
      icon: "🚫",
      color: "text-red-600"
    },
    ourSolution: {
      status: "advanced",
      description: "Comprehensive Management",
      details: "Route planning, driver management, vehicle maintenance tracking, parent notifications, and safety protocols",
      icon: "✅",
      color: "text-green-600"
    },
    priority: "high"
  },
  {
    id: 5,
    feature: "Hostel + Admission + Library",
    category: "Comprehensive Modules",
    competitors: {
      status: "offered",
      description: "Offered",
      icon: "✅",
      color: "text-green-600"
    },
    ourSolution: {
      status: "unified",
      description: "Unified, Role-based UX",
      details: "Integrated modules with seamless data flow, unified user experience, and role-based access control across all functions",
      icon: "✅",
      color: "text-green-600"
    },
    priority: "medium"
  },
  {
    id: 6,
    feature: "Multilingual Web Portals",
    category: "User Experience",
    competitors: {
      status: "limited",
      description: "Limited",
      icon: "⚠️",
      color: "text-yellow-600"
    },
    ourSolution: {
      status: "modern",
      description: "Modern, Responsive UI",
      details: "Web-responsive design, progressive web application, cross-browser compatibility, and comprehensive multilingual support",
      icon: "✅",
      color: "text-green-600"
    },
    priority: "high"
  },
  {
    id: 7,
    feature: "Real-time Chat Across Roles",
    category: "Communication",
    competitors: {
      status: "basic",
      description: "Basic",
      icon: "⚠️",
      color: "text-yellow-600"
    },
    ourSolution: {
      status: "comprehensive",
      description: "Cross-role Chat & Feedback",
      details: "Real-time messaging between all stakeholders, group chats, file sharing, feedback systems, and notification management",
      icon: "✅",
      color: "text-green-600"
    },
    priority: "medium"
  }
];

// Status color mapping for UI - Based on Screenshot
export const statusColors = {
  // Our Solution (Green checkmarks)
  complete: "text-green-600 bg-green-50",
  advanced: "text-green-600 bg-green-50",
  unified: "text-green-600 bg-green-50",
  modern: "text-green-600 bg-green-50",
  comprehensive: "text-green-600 bg-green-50",

  // Competitors - Partial/Available (Green checkmarks)
  partial: "text-green-600 bg-green-50",
  offered: "text-green-600 bg-green-50",

  // Competitors - Limited (Yellow warning)
  basic: "text-yellow-600 bg-yellow-50",
  limited: "text-yellow-600 bg-yellow-50",

  // Competitors - Poor/Missing (Red X)
  rare: "text-red-600 bg-red-50",
  minimal: "text-red-600 bg-red-50"
};

// Icon mapping based on screenshot
export const statusIcons = {
  // Green checkmarks for available features
  partial: "✅",
  offered: "✅",
  complete: "✅",
  advanced: "✅",
  unified: "✅",
  modern: "✅",
  comprehensive: "✅",

  // Yellow warning for limited features
  basic: "⚠️",
  limited: "⚠️",

  // Red X for missing/poor features
  rare: "🚫",
  minimal: "🚫"
};

// Priority-based sorting
export const getFeaturesByPriority = () => {
  return competitiveFeatures.sort((a, b) => {
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    return priorityOrder[a.priority] - priorityOrder[b.priority];
  });
};

// Category-based grouping
export const getFeaturesByCategory = () => {
  return competitiveFeatures.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = [];
    }
    acc[feature.category].push(feature);
    return acc;
  }, {});
};

// Key differentiators for hero section
export const keyDifferentiators = [
  {
    title: "Complete Basic Plan",
    description: "All 8+ modules included vs. competitors' 2-module limitation",
    icon: "🎯"
  },
  {
    title: "AI-Powered Insights",
    description: "Gemma-3.27B predictions vs. basic reporting",
    icon: "🤖"
  },
  {
    title: "Modern Technology",
    description: "Responsive, web-based design vs. outdated interfaces",
    icon: "💻"
  },
  {
    title: "Real-time Updates",
    description: "Live notifications, chat, and data sync vs. delayed systems",
    icon: "⚡"
  }
];

// Competitive advantage statistics
export const competitiveStats = {
  modulesInBasicPlan: 8,
  competitorModulesInBasic: 2,
  aiAccuracy: 85,
  competitorAiAvailability: 15,
  webResponsiveness: 100,
  competitorWebSupport: 60,
  realTimeFeatures: 12,
  competitorRealTimeFeatures: 3
};

export default competitiveFeatures;
