# 📊 Schopio Monitoring & Logging Guide

## 🎯 **MONITORING OVERVIEW**

### **Monitoring Strategy**
- **Application Performance**: Response times, error rates, throughput
- **Infrastructure Health**: Server resources, database performance
- **Business Metrics**: User activity, subscription metrics, revenue tracking
- **Security Monitoring**: Authentication failures, suspicious activity

### **Monitoring Stack**
- **Application**: Vercel Analytics + Custom metrics
- **Database**: Neon built-in monitoring
- **Logs**: Vercel Function logs + Custom logging
- **Alerts**: Email + Slack notifications
- **Dashboards**: Custom monitoring dashboard

---

## 📈 **APPLICATION MONITORING**

### **1. Performance Monitoring**

#### **Core Web Vitals**
```typescript
// Performance metrics tracking
const performanceMetrics = {
  LCP: 'Largest Contentful Paint < 2.5s',
  FID: 'First Input Delay < 100ms', 
  CLS: 'Cumulative Layout Shift < 0.1',
  TTFB: 'Time to First Byte < 600ms'
};

// Custom performance tracking
export function trackPerformance(metric: string, value: number) {
  // Send to monitoring service
  analytics.track('performance_metric', {
    metric,
    value,
    timestamp: Date.now(),
    environment: process.env.NODE_ENV
  });
}
```

#### **API Performance Monitoring**
```typescript
// API response time tracking
export async function monitorAPICall(
  endpoint: string,
  method: string,
  handler: () => Promise<Response>
): Promise<Response> {
  const startTime = Date.now();
  
  try {
    const response = await handler();
    const duration = Date.now() - startTime;
    
    // Track successful API call
    analytics.track('api_call', {
      endpoint,
      method,
      status: response.status,
      duration,
      success: true
    });
    
    return response;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // Track failed API call
    analytics.track('api_call', {
      endpoint,
      method,
      duration,
      success: false,
      error: error.message
    });
    
    throw error;
  }
}
```

### **2. Error Monitoring**

#### **Error Tracking Setup**
```typescript
// Global error handler
export function setupErrorMonitoring() {
  // Unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    
    analytics.track('error', {
      type: 'unhandled_rejection',
      reason: String(reason),
      timestamp: Date.now(),
      environment: process.env.NODE_ENV
    });
  });
  
  // Uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    
    analytics.track('error', {
      type: 'uncaught_exception',
      message: error.message,
      stack: error.stack,
      timestamp: Date.now(),
      environment: process.env.NODE_ENV
    });
  });
}
```

#### **Custom Error Tracking**
```typescript
// Application error tracking
export function trackError(error: Error, context?: Record<string, any>) {
  console.error('Application Error:', error);
  
  analytics.track('application_error', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: Date.now(),
    environment: process.env.NODE_ENV,
    url: typeof window !== 'undefined' ? window.location.href : undefined
  });
}

// Usage in API routes
export async function handleAPIError(error: Error, request: Request) {
  trackError(error, {
    endpoint: request.url,
    method: request.method,
    userAgent: request.headers.get('user-agent')
  });
}
```

### **3. Business Metrics Monitoring**

#### **User Activity Tracking**
```typescript
// User activity metrics
export const userMetrics = {
  // Authentication events
  trackLogin: (userId: string, method: string) => {
    analytics.track('user_login', {
      userId,
      method,
      timestamp: Date.now()
    });
  },
  
  // Feature usage
  trackFeatureUsage: (userId: string, feature: string) => {
    analytics.track('feature_usage', {
      userId,
      feature,
      timestamp: Date.now()
    });
  },
  
  // Page views
  trackPageView: (userId: string, page: string) => {
    analytics.track('page_view', {
      userId,
      page,
      timestamp: Date.now()
    });
  }
};
```

#### **Subscription Metrics**
```typescript
// Subscription tracking
export const subscriptionMetrics = {
  // New subscription
  trackSubscriptionCreated: (subscriptionId: string, amount: number) => {
    analytics.track('subscription_created', {
      subscriptionId,
      amount,
      timestamp: Date.now()
    });
  },
  
  // Payment events
  trackPaymentSuccess: (paymentId: string, amount: number) => {
    analytics.track('payment_success', {
      paymentId,
      amount,
      timestamp: Date.now()
    });
  },
  
  trackPaymentFailure: (subscriptionId: string, reason: string) => {
    analytics.track('payment_failure', {
      subscriptionId,
      reason,
      timestamp: Date.now()
    });
  },
  
  // Churn tracking
  trackSubscriptionCancelled: (subscriptionId: string, reason: string) => {
    analytics.track('subscription_cancelled', {
      subscriptionId,
      reason,
      timestamp: Date.now()
    });
  }
};
```

---

## 🗄️ **DATABASE MONITORING**

### **1. Neon Database Monitoring**

#### **Built-in Metrics**
```typescript
// Database performance metrics
const databaseMetrics = {
  connectionPool: 'Active/idle connections',
  queryPerformance: 'Query execution times',
  storageUsage: 'Database size and growth',
  backupStatus: 'Backup success/failure',
  replicationLag: 'Read replica lag (if applicable)'
};
```

#### **Custom Database Monitoring**
```typescript
// Database query monitoring
export async function monitorDatabaseQuery<T>(
  queryName: string,
  query: () => Promise<T>
): Promise<T> {
  const startTime = Date.now();
  
  try {
    const result = await query();
    const duration = Date.now() - startTime;
    
    // Track successful query
    analytics.track('database_query', {
      queryName,
      duration,
      success: true,
      timestamp: Date.now()
    });
    
    // Alert on slow queries (>5 seconds)
    if (duration > 5000) {
      analytics.track('slow_query_alert', {
        queryName,
        duration,
        timestamp: Date.now()
      });
    }
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // Track failed query
    analytics.track('database_query', {
      queryName,
      duration,
      success: false,
      error: error.message,
      timestamp: Date.now()
    });
    
    throw error;
  }
}
```

### **2. Database Health Checks**

#### **Health Check Endpoints**
```typescript
// Database health check
export async function checkDatabaseHealth(): Promise<{
  status: 'healthy' | 'unhealthy';
  responseTime: number;
  details: Record<string, any>;
}> {
  const startTime = Date.now();
  
  try {
    // Simple connectivity test
    await db.select().from(clients).limit(1);
    
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'healthy',
      responseTime,
      details: {
        connectivity: 'ok',
        responseTime: `${responseTime}ms`
      }
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'unhealthy',
      responseTime,
      details: {
        connectivity: 'failed',
        error: error.message,
        responseTime: `${responseTime}ms`
      }
    };
  }
}
```

---

## 📝 **LOGGING SYSTEM**

### **1. Structured Logging**

#### **Log Levels & Format**
```typescript
// Log levels
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

// Structured log format
interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  userId?: string;
  requestId?: string;
  environment: string;
}

// Logger implementation
export class Logger {
  private static formatLog(entry: LogEntry): string {
    return JSON.stringify({
      ...entry,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV
    });
  }
  
  static error(message: string, context?: Record<string, any>, userId?: string) {
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel.ERROR,
      message,
      context,
      userId,
      environment: process.env.NODE_ENV || 'development'
    };
    
    console.error(this.formatLog(logEntry));
    
    // Send to monitoring service for production
    if (process.env.NODE_ENV === 'production') {
      analytics.track('log_entry', logEntry);
    }
  }
  
  static info(message: string, context?: Record<string, any>, userId?: string) {
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel.INFO,
      message,
      context,
      userId,
      environment: process.env.NODE_ENV || 'development'
    };
    
    console.log(this.formatLog(logEntry));
  }
  
  static warn(message: string, context?: Record<string, any>, userId?: string) {
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel.WARN,
      message,
      context,
      userId,
      environment: process.env.NODE_ENV || 'development'
    };
    
    console.warn(this.formatLog(logEntry));
  }
}
```

### **2. Request Logging**

#### **API Request Logging**
```typescript
// Request logging middleware
export function requestLoggingMiddleware() {
  return async (c: Context, next: () => Promise<void>) => {
    const startTime = Date.now();
    const requestId = crypto.randomUUID();
    
    // Log request start
    Logger.info('Request started', {
      requestId,
      method: c.req.method,
      url: c.req.url,
      userAgent: c.req.header('user-agent'),
      ip: c.req.header('x-forwarded-for') || 'unknown'
    });
    
    try {
      await next();
      
      const duration = Date.now() - startTime;
      
      // Log successful request
      Logger.info('Request completed', {
        requestId,
        method: c.req.method,
        url: c.req.url,
        status: c.res.status,
        duration: `${duration}ms`
      });
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Log failed request
      Logger.error('Request failed', {
        requestId,
        method: c.req.method,
        url: c.req.url,
        duration: `${duration}ms`,
        error: error.message,
        stack: error.stack
      });
      
      throw error;
    }
  };
}
```

### **3. Security Logging**

#### **Security Event Logging**
```typescript
// Security event tracking
export const securityLogger = {
  // Authentication events
  loginAttempt: (email: string, success: boolean, ip: string) => {
    Logger.info('Login attempt', {
      event: 'login_attempt',
      email,
      success,
      ip,
      timestamp: Date.now()
    });
  },
  
  // Failed authentication
  authenticationFailure: (email: string, reason: string, ip: string) => {
    Logger.warn('Authentication failure', {
      event: 'auth_failure',
      email,
      reason,
      ip,
      timestamp: Date.now()
    });
  },
  
  // Suspicious activity
  suspiciousActivity: (userId: string, activity: string, details: Record<string, any>) => {
    Logger.warn('Suspicious activity detected', {
      event: 'suspicious_activity',
      userId,
      activity,
      details,
      timestamp: Date.now()
    });
  },
  
  // Rate limit violations
  rateLimitViolation: (ip: string, endpoint: string, attempts: number) => {
    Logger.warn('Rate limit violation', {
      event: 'rate_limit_violation',
      ip,
      endpoint,
      attempts,
      timestamp: Date.now()
    });
  }
};
```

---

## 🚨 **ALERTING SYSTEM**

### **1. Alert Configuration**

#### **Alert Rules**
```typescript
// Alert thresholds
const alertThresholds = {
  errorRate: 5, // 5% error rate
  responseTime: 5000, // 5 seconds
  databaseConnections: 80, // 80% of connection pool
  diskUsage: 85, // 85% disk usage
  memoryUsage: 90, // 90% memory usage
  failedLogins: 10 // 10 failed logins in 5 minutes
};

// Alert channels
const alertChannels = {
  email: ['<EMAIL>', '<EMAIL>'],
  slack: '#alerts',
  sms: ['+91XXXXXXXXXX'] // For critical alerts only
};
```

#### **Alert Implementation**
```typescript
// Alert manager
export class AlertManager {
  static async sendAlert(
    severity: 'low' | 'medium' | 'high' | 'critical',
    title: string,
    message: string,
    context?: Record<string, any>
  ) {
    const alert = {
      severity,
      title,
      message,
      context,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV
    };
    
    // Log the alert
    Logger.error(`ALERT [${severity.toUpperCase()}]: ${title}`, {
      message,
      context
    });
    
    // Send notifications based on severity
    switch (severity) {
      case 'critical':
        await this.sendEmailAlert(alert);
        await this.sendSlackAlert(alert);
        await this.sendSMSAlert(alert);
        break;
      case 'high':
        await this.sendEmailAlert(alert);
        await this.sendSlackAlert(alert);
        break;
      case 'medium':
        await this.sendSlackAlert(alert);
        break;
      case 'low':
        // Log only, no immediate notification
        break;
    }
  }
  
  private static async sendEmailAlert(alert: any) {
    // Email notification implementation
  }
  
  private static async sendSlackAlert(alert: any) {
    // Slack notification implementation
  }
  
  private static async sendSMSAlert(alert: any) {
    // SMS notification implementation (critical only)
  }
}
```

### **2. Automated Monitoring**

#### **Health Check Monitoring**
```typescript
// Automated health checks
export async function runHealthChecks() {
  const checks = [
    { name: 'database', check: checkDatabaseHealth },
    { name: 'api', check: checkAPIHealth },
    { name: 'external_services', check: checkExternalServices }
  ];
  
  for (const { name, check } of checks) {
    try {
      const result = await check();
      
      if (result.status === 'unhealthy') {
        await AlertManager.sendAlert(
          'high',
          `${name} health check failed`,
          `Health check for ${name} returned unhealthy status`,
          result.details
        );
      }
    } catch (error) {
      await AlertManager.sendAlert(
        'critical',
        `${name} health check error`,
        `Health check for ${name} threw an error: ${error.message}`,
        { error: error.stack }
      );
    }
  }
}

// Run health checks every 5 minutes
setInterval(runHealthChecks, 5 * 60 * 1000);
```
