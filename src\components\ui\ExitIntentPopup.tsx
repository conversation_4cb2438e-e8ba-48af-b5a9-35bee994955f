'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  X, 
  Download, 
  Calendar, 
  CheckCircle, 
  ArrowRight, 
  Gift,
  Clock,
  Star,
  AlertTriangle
} from 'lucide-react'

const ExitIntentPopup = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [hasShown, setHasShown] = useState(false)
  const [email, setEmail] = useState('')

  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    const handleMouseLeave = (e: MouseEvent) => {
      // Only trigger if mouse leaves from the top of the page and hasn&apos;t been shown before
      if (e.clientY <= 0 && !hasShown) {
        timeoutId = setTimeout(() => {
          setIsVisible(true)
          setHasShown(true)
        }, 100)
      }
    }

    const handleMouseEnter = () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }

    // Also show after 30 seconds if user hasn't left
    const autoShowTimeout = setTimeout(() => {
      if (!hasShown) {
        setIsVisible(true)
        setHasShown(true)
      }
    }, 30000)

    document.addEventListener('mouseleave', handleMouseLeave)
    document.addEventListener('mouseenter', handleMouseEnter)

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave)
      document.removeEventListener('mouseenter', handleMouseEnter)
      if (timeoutId) clearTimeout(timeoutId)
      clearTimeout(autoShowTimeout)
    }
  }, [hasShown])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle email submission
    console.log('Exit intent email:', email)
    setIsVisible(false)
  }

  const handleClose = () => {
    setIsVisible(false)
  }

  const offers = [
    {
      icon: Download,
      title: "Free Implementation Guide",
      description: "Step-by-step 14-day implementation checklist",
      value: "Comprehensive Guide"
    },
    {
      icon: Calendar,
      title: "Priority Demo Booking",
      description: "Skip the queue - book demo for tomorrow",
      value: "Limited slots"
    },
    {
      icon: CheckCircle,
      title: "Free School Audit",
      description: "Identify significant efficiency improvements",
      value: "Professional Analysis"
    }
  ]

  return (
    <AnimatePresence>
      {isVisible && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.15 }}
            className="fixed inset-0 bg-black/40 z-50"
            onClick={handleClose}
          />

          {/* Popup */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            <Card className="bg-white border-0 shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto will-change-transform">
              <CardHeader padding="lg" className="relative">
                <button
                  onClick={handleClose}
                  className="absolute top-4 right-4 text-slate-400 hover:text-slate-600 transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>

                <div className="text-center">
                  <div className="inline-flex items-center gap-2 bg-red-100 text-red-700 border border-red-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
                    <AlertTriangle className="w-4 h-4" />
                    Wait! Don&apos;t Leave Empty-Handed
                  </div>
                  
                  <h2 className="text-3xl font-bold text-slate-900 mb-4">
                    Get <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">Premium</span> Free Resources
                  </h2>
                  
                  <p className="text-lg text-slate-600 mb-6">
                    Before you go, grab these exclusive resources that have helped 500+ schools transform their operations.
                  </p>

                  <div className="flex items-center justify-center gap-1 mb-6">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                    <span className="ml-2 text-slate-600 text-sm">Rated 4.9/5 by school administrators</span>
                  </div>
                </div>
              </CardHeader>

              <CardContent padding="lg">
                {/* Offers Grid */}
                <div className="grid md:grid-cols-3 gap-4 mb-8">
                  {offers.map((offer, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-br from-blue-50 to-emerald-50 border border-blue-200 rounded-lg p-4 text-center"
                    >
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                        <offer.icon className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-bold text-slate-900 mb-2">{offer.title}</h4>
                      <p className="text-sm text-slate-600 mb-2">{offer.description}</p>
                      <div className="bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full text-xs font-bold">
                        {offer.value}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Email Form */}
                <div className="bg-gradient-to-r from-blue-600 to-emerald-600 rounded-xl p-6 text-white">
                  <div className="text-center mb-6">
                    <div className="inline-flex items-center gap-2 bg-white/20 px-3 py-1 rounded-full text-sm font-bold mb-3">
                      <Gift className="w-4 h-4" />
                      Limited Time Offer
                    </div>
                    <h3 className="text-xl font-bold mb-2">Get All 3 Resources Instantly</h3>
                    <p className="text-blue-100">Enter your email and we&apos;ll send everything immediately. No spam, unsubscribe anytime.</p>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <input
                        type="email"
                        required
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter your email address"
                        className="w-full px-4 py-3 rounded-lg text-slate-900 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-white/50"
                      />
                    </div>
                    
                    <Button
                      type="submit"
                      size="lg"
                      icon={ArrowRight}
                      iconPosition="right"
                      className="w-full bg-white text-blue-600 hover:bg-blue-50 font-bold py-4 text-lg"
                    >
                      Send Me The Free Resources
                    </Button>
                  </form>

                  <div className="flex items-center justify-center gap-4 mt-6 text-sm text-blue-200">
                    <div className="flex items-center gap-1">
                      <CheckCircle className="w-4 h-4" />
                      <span>Instant delivery</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <CheckCircle className="w-4 h-4" />
                      <span>No spam ever</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <CheckCircle className="w-4 h-4" />
                      <span>Unsubscribe anytime</span>
                    </div>
                  </div>
                </div>

                {/* Urgency */}
                <div className="text-center mt-6">
                  <div className="inline-flex items-center gap-2 bg-red-50 text-red-700 border border-red-200 px-4 py-2 rounded-full text-sm">
                    <Clock className="w-4 h-4" />
                    <span>This offer expires when you close this window</span>
                  </div>
                </div>

                {/* Alternative Action */}
                <div className="text-center mt-6">
                  <button
                    onClick={handleClose}
                    className="text-slate-500 hover:text-slate-700 text-sm underline transition-colors"
                  >
                    No thanks, I&apos;ll figure it out myself
                  </button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

export default ExitIntentPopup
