import { emailService } from './emailService'
import { db } from '@/src/db'
import { 
  supportTickets, 
  ticketMessages, 
  clients, 
  clientUsers, 
  adminUsers,
  partners,
  schoolReferrals
} from '@/src/db/schema'
import { eq, and } from 'drizzle-orm'

export interface TicketNotificationData {
  ticketId: string
  title: string
  description?: string
  priority: string
  status: string
  category?: string
  schoolName: string
  schoolEmail: string
  createdBy: {
    name: string
    email: string
  }
  assignedAdmin?: {
    name: string
    email: string
  }
  partner?: {
    name: string
    email: string
  }
  message?: string
  messageFrom?: 'client' | 'admin'
}

export class SupportNotificationService {
  private emailService = emailService

  constructor() {
    // Email service is already initialized
  }

  /**
   * Send notification when a new ticket is created by school
   */
  async notifyTicketCreated(ticketId: string): Promise<void> {
    try {
      const ticketData = await this.getTicketNotificationData(ticketId)
      if (!ticketData) return

      // Notify admin team
      await this.notifyAdminTeam('ticket_created', ticketData)

      // Notify partner if school was referred
      if (ticketData.partner) {
        await this.notifyPartner('ticket_created', ticketData)
      }

      // Send confirmation to school
      await this.notifySchool('ticket_created_confirmation', ticketData)

    } catch (error) {
      console.error('Failed to send ticket creation notifications:', error)
    }
  }

  /**
   * Send notification when ticket status is updated
   */
  async notifyTicketStatusUpdate(ticketId: string, oldStatus: string, newStatus: string): Promise<void> {
    try {
      const ticketData = await this.getTicketNotificationData(ticketId)
      if (!ticketData) return

      const notificationData = {
        ...ticketData,
        oldStatus,
        newStatus
      }

      // Notify school of status change
      await this.notifySchool('ticket_status_update', notificationData)

      // Notify partner if escalated to urgent
      if (newStatus === 'urgent' && ticketData.partner) {
        await this.notifyPartner('ticket_escalated', notificationData)
      }

    } catch (error) {
      console.error('Failed to send ticket status update notifications:', error)
    }
  }

  /**
   * Send notification when new message is added
   */
  async notifyNewMessage(ticketId: string, messageId: string, senderType: 'client' | 'admin'): Promise<void> {
    try {
      const ticketData = await this.getTicketNotificationData(ticketId)
      if (!ticketData) return

      // Get message details
      const [message] = await db
        .select({
          message: ticketMessages.message,
          createdAt: ticketMessages.createdAt
        })
        .from(ticketMessages)
        .where(eq(ticketMessages.id, messageId))
        .limit(1)

      if (!message) return

      const notificationData = {
        ...ticketData,
        message: message.message,
        messageFrom: senderType,
        messageTime: message.createdAt
      }

      if (senderType === 'admin') {
        // Admin replied - notify school
        await this.notifySchool('ticket_admin_reply', notificationData)
      } else {
        // School replied - notify admin team
        await this.notifyAdminTeam('ticket_client_reply', notificationData)
        
        // Notify assigned admin specifically if ticket is assigned
        if (ticketData.assignedAdmin) {
          await this.notifyAssignedAdmin('ticket_client_reply', notificationData)
        }
      }

    } catch (error) {
      console.error('Failed to send new message notifications:', error)
    }
  }

  /**
   * Send notification when ticket is assigned to admin
   */
  async notifyTicketAssigned(ticketId: string, adminId: string): Promise<void> {
    try {
      const ticketData = await this.getTicketNotificationData(ticketId)
      if (!ticketData) return

      // Get admin details
      const [admin] = await db
        .select({
          name: adminUsers.name,
          email: adminUsers.email
        })
        .from(adminUsers)
        .where(eq(adminUsers.id, adminId))
        .limit(1)

      if (!admin) return

      const notificationData = {
        ...ticketData,
        assignedAdmin: admin
      }

      // Notify assigned admin
      await this.notifyAssignedAdmin('ticket_assigned', notificationData)

      // Notify school that ticket was assigned
      await this.notifySchool('ticket_assigned_notification', notificationData)

    } catch (error) {
      console.error('Failed to send ticket assignment notifications:', error)
    }
  }

  /**
   * Escalate ticket to partner for high-priority issues
   */
  async escalateToPartner(ticketId: string, escalationReason: string): Promise<void> {
    try {
      const ticketData = await this.getTicketNotificationData(ticketId)
      if (!ticketData || !ticketData.partner) return

      const notificationData = {
        ...ticketData,
        escalationReason
      }

      await this.notifyPartner('ticket_escalated', notificationData)

    } catch (error) {
      console.error('Failed to escalate ticket to partner:', error)
    }
  }

  /**
   * Get comprehensive ticket data for notifications
   */
  private async getTicketNotificationData(ticketId: string): Promise<TicketNotificationData | null> {
    try {
      // Get ticket with school and creator details
      const [ticketData] = await db
        .select({
          ticket: supportTickets,
          school: {
            name: clients.schoolName,
            email: clients.email
          },
          creator: {
            name: clientUsers.name,
            email: clientUsers.email
          }
        })
        .from(supportTickets)
        .leftJoin(clients, eq(supportTickets.clientId, clients.id))
        .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
        .where(eq(supportTickets.id, ticketId))
        .limit(1)

      if (!ticketData) return null

      // Get assigned admin if any
      let assignedAdmin = null
      if (ticketData.ticket.assignedTo) {
        const [admin] = await db
          .select({
            name: adminUsers.name,
            email: adminUsers.email
          })
          .from(adminUsers)
          .where(eq(adminUsers.id, ticketData.ticket.assignedTo))
          .limit(1)
        
        assignedAdmin = admin || null
      }

      // Get partner if school was referred
      let partner = null
      if (ticketData.ticket.clientId) {
        const [referral] = await db
          .select({
            partner: {
              name: partners.name,
              email: partners.email
            }
          })
          .from(schoolReferrals)
          .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
          .where(eq(schoolReferrals.clientId, ticketData.ticket.clientId))
          .limit(1)
        
        partner = referral?.partner || null
      }

      return {
        ticketId: ticketData.ticket.id,
        title: ticketData.ticket.title,
        description: ticketData.ticket.description,
        priority: ticketData.ticket.priority || 'medium',
        status: ticketData.ticket.status || 'open',
        category: ticketData.ticket.category || 'general',
        schoolName: ticketData.school?.name || 'Unknown School',
        schoolEmail: ticketData.school?.email || '',
        createdBy: {
          name: ticketData.creator?.name || 'Unknown User',
          email: ticketData.creator?.email || ''
        },
        assignedAdmin: assignedAdmin || undefined,
        partner: partner || undefined
      }

    } catch (error) {
      console.error('Failed to get ticket notification data:', error)
      return null
    }
  }

  /**
   * Send notification to admin team
   */
  private async notifyAdminTeam(type: string, data: TicketNotificationData): Promise<void> {
    // Get all admins with support permissions
    const admins = await db
      .select({
        name: adminUsers.name,
        email: adminUsers.email
      })
      .from(adminUsers)
      .where(and(
        eq(adminUsers.isActive, true),
        // Check if admin has support permissions
      ))

    for (const admin of admins) {
      await this.sendNotificationEmail(admin.email, type, data)
    }
  }

  /**
   * Send notification to assigned admin
   */
  private async notifyAssignedAdmin(type: string, data: TicketNotificationData): Promise<void> {
    if (data.assignedAdmin?.email) {
      await this.sendNotificationEmail(data.assignedAdmin.email, type, data)
    }
  }

  /**
   * Send notification to school
   */
  private async notifySchool(type: string, data: TicketNotificationData): Promise<void> {
    if (data.schoolEmail) {
      await this.sendNotificationEmail(data.schoolEmail, type, data)
    }
  }

  /**
   * Send notification to partner
   */
  private async notifyPartner(type: string, data: TicketNotificationData): Promise<void> {
    if (data.partner?.email) {
      await this.sendNotificationEmail(data.partner.email, type, data)
    }
  }

  /**
   * Send actual notification email
   */
  private async sendNotificationEmail(email: string, type: string, data: TicketNotificationData): Promise<void> {
    const { subject, html } = this.getEmailTemplate(type, data)
    
    await this.emailService.sendEmail({
      to: email,
      subject,
      html
    })
  }

  /**
   * Get email template for notification type
   */
  private getEmailTemplate(type: string, data: TicketNotificationData): { subject: string; html: string } {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://schopio.com'
    
    switch (type) {
      case 'ticket_created':
        return {
          subject: `New Support Ticket: ${data.title}`,
          html: this.getTicketCreatedTemplate(data, baseUrl)
        }
      
      case 'ticket_created_confirmation':
        return {
          subject: `Support Ticket Created - ${data.title}`,
          html: this.getTicketConfirmationTemplate(data, baseUrl)
        }
      
      case 'ticket_admin_reply':
        return {
          subject: `Support Team Reply - ${data.title}`,
          html: this.getAdminReplyTemplate(data, baseUrl)
        }
      
      case 'ticket_client_reply':
        return {
          subject: `Client Reply - ${data.title}`,
          html: this.getClientReplyTemplate(data, baseUrl)
        }
      
      case 'ticket_status_update':
        return {
          subject: `Ticket Status Update - ${data.title}`,
          html: this.getStatusUpdateTemplate(data, baseUrl)
        }
      
      case 'ticket_assigned':
        return {
          subject: `Ticket Assigned to You - ${data.title}`,
          html: this.getTicketAssignedTemplate(data, baseUrl)
        }
      
      case 'ticket_escalated':
        return {
          subject: `URGENT: Ticket Escalated - ${data.title}`,
          html: this.getTicketEscalatedTemplate(data, baseUrl)
        }
      
      default:
        return {
          subject: `Support Notification - ${data.title}`,
          html: this.getGenericTemplate(data, baseUrl)
        }
    }
  }

  // Email template methods will be added in the next part...
  private getTicketCreatedTemplate(data: TicketNotificationData, baseUrl: string): string {
    return `
      <h2 style="color: #2563eb;">New Support Ticket Created</h2>
      <p><strong>School:</strong> ${data.schoolName}</p>
      <p><strong>Title:</strong> ${data.title}</p>
      <p><strong>Priority:</strong> ${data.priority}</p>
      <p><strong>Category:</strong> ${data.category}</p>
      <p><strong>Created by:</strong> ${data.createdBy.name}</p>
      <div style="margin: 20px 0; padding: 15px; background: #f9fafb; border-radius: 8px;">
        <p><strong>Description:</strong></p>
        <p>${data.description}</p>
      </div>
      <a href="${baseUrl}/admin/support/tickets/${data.ticketId}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View Ticket</a>
    `
  }

  private getTicketConfirmationTemplate(data: TicketNotificationData, baseUrl: string): string {
    return `
      <h2 style="color: #10b981;">Support Ticket Created Successfully</h2>
      <p>Dear ${data.createdBy.name},</p>
      <p>Your support ticket has been created successfully. Our team will respond within 24 hours.</p>
      <div style="margin: 20px 0; padding: 15px; background: #f0f9ff; border-radius: 8px;">
        <p><strong>Ticket ID:</strong> ${data.ticketId}</p>
        <p><strong>Title:</strong> ${data.title}</p>
        <p><strong>Priority:</strong> ${data.priority}</p>
        <p><strong>Status:</strong> ${data.status}</p>
      </div>
      <a href="${baseUrl}/school/support/tickets/${data.ticketId}" style="background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Track Ticket</a>
    `
  }

  private getAdminReplyTemplate(data: TicketNotificationData, baseUrl: string): string {
    return `
      <h2 style="color: #2563eb;">Support Team Reply</h2>
      <p>Dear ${data.createdBy.name},</p>
      <p>Our support team has replied to your ticket:</p>
      <div style="margin: 20px 0; padding: 15px; background: #f0f9ff; border-radius: 8px;">
        <p><strong>Ticket:</strong> ${data.title}</p>
        <p><strong>Reply:</strong></p>
        <p>${data.message}</p>
      </div>
      <a href="${baseUrl}/school/support/tickets/${data.ticketId}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View & Reply</a>
    `
  }

  private getClientReplyTemplate(data: TicketNotificationData, baseUrl: string): string {
    return `
      <h2 style="color: #f59e0b;">Client Reply Received</h2>
      <p><strong>School:</strong> ${data.schoolName}</p>
      <p><strong>Ticket:</strong> ${data.title}</p>
      <p><strong>From:</strong> ${data.createdBy.name}</p>
      <div style="margin: 20px 0; padding: 15px; background: #fef3c7; border-radius: 8px;">
        <p><strong>Message:</strong></p>
        <p>${data.message}</p>
      </div>
      <a href="${baseUrl}/admin/support/tickets/${data.ticketId}" style="background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Reply Now</a>
    `
  }

  private getStatusUpdateTemplate(data: any, baseUrl: string): string {
    return `
      <h2 style="color: #2563eb;">Ticket Status Updated</h2>
      <p>Dear ${data.createdBy.name},</p>
      <p>Your support ticket status has been updated:</p>
      <div style="margin: 20px 0; padding: 15px; background: #f0f9ff; border-radius: 8px;">
        <p><strong>Ticket:</strong> ${data.title}</p>
        <p><strong>Previous Status:</strong> ${data.oldStatus}</p>
        <p><strong>New Status:</strong> ${data.newStatus}</p>
      </div>
      <a href="${baseUrl}/school/support/tickets/${data.ticketId}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View Ticket</a>
    `
  }

  private getTicketAssignedTemplate(data: TicketNotificationData, baseUrl: string): string {
    return `
      <h2 style="color: #10b981;">Ticket Assigned to You</h2>
      <p>Dear ${data.assignedAdmin?.name},</p>
      <p>A support ticket has been assigned to you:</p>
      <div style="margin: 20px 0; padding: 15px; background: #f0fdf4; border-radius: 8px;">
        <p><strong>School:</strong> ${data.schoolName}</p>
        <p><strong>Title:</strong> ${data.title}</p>
        <p><strong>Priority:</strong> ${data.priority}</p>
        <p><strong>Category:</strong> ${data.category}</p>
      </div>
      <a href="${baseUrl}/admin/support/tickets/${data.ticketId}" style="background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Handle Ticket</a>
    `
  }

  private getTicketEscalatedTemplate(data: TicketNotificationData, baseUrl: string): string {
    return `
      <h2 style="color: #ef4444;">URGENT: Ticket Escalated</h2>
      <p>Dear ${data.partner?.name},</p>
      <p>A support ticket for your referred school has been escalated:</p>
      <div style="margin: 20px 0; padding: 15px; background: #fef2f2; border-radius: 8px;">
        <p><strong>School:</strong> ${data.schoolName}</p>
        <p><strong>Title:</strong> ${data.title}</p>
        <p><strong>Priority:</strong> ${data.priority}</p>
        <p><strong>Reason:</strong> ${(data as any).escalationReason}</p>
      </div>
      <p>Please contact the school directly to provide additional support.</p>
      <a href="${baseUrl}/partner/support/tickets/${data.ticketId}" style="background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View Details</a>
    `
  }

  private getGenericTemplate(data: TicketNotificationData, baseUrl: string): string {
    return `
      <h2 style="color: #2563eb;">Support Notification</h2>
      <p>There has been an update to support ticket: ${data.title}</p>
      <a href="${baseUrl}/support/tickets/${data.ticketId}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View Ticket</a>
    `
  }
}

export const supportNotificationService = new SupportNotificationService()
