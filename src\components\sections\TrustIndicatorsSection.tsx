'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { Shield, Award, Users, TrendingUp, Star, CheckCircle } from 'lucide-react'

const TrustIndicatorsSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const valuePropositions = [
    {
      title: "AI-Powered Early Warning System",
      description: "Identify struggling students 6 weeks before traditional methods through predictive analytics and behavioral pattern recognition.",
      benefit: "Prevent academic failures before they happen",
      icon: TrendingUp,
      color: "text-emerald-600 bg-emerald-100"
    },
    {
      title: "Complete Administrative Automation",
      description: "Eliminate 60% of manual paperwork with intelligent workflows, automated attendance, and digital fee management.",
      benefit: "Transform 40-hour admin weeks into 16-hour efficiency",
      icon: Shield,
      color: "text-blue-600 bg-blue-100"
    },
    {
      title: "Real-Time Parent Engagement",
      description: "Instant notifications, progress tracking, and two-way communication through responsive web portal.",
      benefit: "Achieve 85% parent participation in school activities",
      icon: Users,
      color: "text-purple-600 bg-purple-100"
    }
  ]

  const systemCapabilities = [
    {
      number: "8+",
      label: "Complete Modules",
      description: "All features in basic plan",
      icon: Users,
      color: "text-blue-600 bg-blue-100"
    },
    {
      number: "99.9%",
      label: "System Uptime",
      description: "Enterprise-grade reliability",
      icon: Shield,
      color: "text-purple-600 bg-purple-100"
    },
    {
      number: "24/7",
      label: "System Availability",
      description: "Live data & notifications",
      icon: TrendingUp,
      color: "text-emerald-600 bg-emerald-100"
    },
    {
      number: "AI",
      label: "Powered Insights",
      description: "Gemma-3.27B predictions",
      icon: Star,
      color: "text-yellow-600 bg-yellow-100"
    }
  ]

  const securityBadges = [
    { name: "ISO 27001", description: "Information Security" },
    { name: "SSL Secured", description: "256-bit Encryption" },
    { name: "GDPR Compliant", description: "Data Protection" },
    { name: "SOC 2 Type II", description: "Security Audited" }
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Award className="w-4 h-4" />
            Enterprise-Grade School Management Platform
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Built for Modern Educational Excellence with
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> AI-Powered Intelligence</span>
          </h2>
        </motion.div>

        {/* System Capabilities */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {systemCapabilities.map((capability, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="text-center bg-gradient-to-br from-slate-50 to-blue-50 border-0 shadow-md hover:shadow-lg transition-all duration-300">
                <CardContent padding="lg">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 ${capability.color}`}>
                    <capability.icon className="w-6 h-6" />
                  </div>
                  <div className="text-2xl font-bold text-slate-900 mb-1">{capability.number}</div>
                  <div className="text-sm font-semibold text-slate-700 mb-1">{capability.label}</div>
                  <div className="text-xs text-slate-500">{capability.description}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>


      </div>
    </section>
  )
}

export default TrustIndicatorsSection
