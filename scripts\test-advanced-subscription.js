/**
 * Test Advanced Subscription Management System
 * Creates test data and tests the advanced subscription update functionality
 */

const { neon } = require('@neondatabase/serverless')

const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const sql = neon(connectionString)

async function testAdvancedSubscriptionSystem() {
  try {
    console.log('🧪 Testing Advanced Subscription Management System...')

    // 0. Get admin user for created_by fields
    const [adminUser] = await sql`SELECT id FROM admin_users LIMIT 1`
    if (!adminUser) {
      console.error('❌ No admin user found. Please ensure admin users exist.')
      process.exit(1)
    }
    console.log(`📋 Using admin user: ${adminUser.id}`)

    // 1. Create test client
    console.log('📋 Creating test client...')
    const timestamp = Date.now().toString().slice(-4) // Last 4 digits for 8-char limit
    const [testClient] = await sql`
      INSERT INTO clients (school_code, school_name, email, phone, status, actual_student_count)
      VALUES (${`TST${timestamp}`}, 'Test Advanced School', ${`test${timestamp}@advanced.edu`}, '9876543210', 'active', 100)
      RETURNING id, school_name
    `
    console.log(`✅ Created test client: ${testClient.school_name} (${testClient.id})`)

    // 2. Create test partner
    console.log('👥 Creating test partner...')
    const [testPartner] = await sql`
      INSERT INTO partners (partner_code, name, email, password_hash, company_name, phone, address, profit_share_percentage, is_active, created_by)
      VALUES (${`P${timestamp}`}, 'Test Partner', ${`partner${timestamp}@test.com`}, 'test_hash', 'Test Partner Co.', '9876543211', 'Test Address', 30.00, true, ${adminUser.id})
      RETURNING id, name
    `
    console.log(`✅ Created test partner: ${testPartner.name} (${testPartner.id})`)

    // 3. Create referral code
    console.log('🔗 Creating referral code...')
    const [referralCode] = await sql`
      INSERT INTO referral_codes (code, partner_id, is_active, max_usage)
      VALUES (${`REF${timestamp}`}, ${testPartner.id}, true, 100)
      RETURNING id, code
    `
    console.log(`✅ Created referral code: ${referralCode.code} (${referralCode.id})`)

    // 4. Create school referral
    console.log('🏫 Creating school referral...')
    await sql`
      INSERT INTO school_referrals (client_id, partner_id, referral_code_id, referral_source, is_active, verified_at, verified_by)
      VALUES (${testClient.id}, ${testPartner.id}, ${referralCode.id}, 'registration', true, NOW(), ${adminUser.id})
    `
    console.log(`✅ Created school referral`)

    // 5. Create test subscription
    console.log('📊 Creating test subscription...')
    const currentDate = new Date().toISOString().split('T')[0]
    const nextMonth = new Date()
    nextMonth.setMonth(nextMonth.getMonth() + 1)
    const nextMonthDate = nextMonth.toISOString().split('T')[0]

    const [testSubscription] = await sql`
      INSERT INTO billing_subscriptions (
        client_id, student_count, price_per_student, monthly_amount,
        status, current_period_start, current_period_end, next_billing_date,
        billing_cycle, grace_period_days,
        database_costs, website_maintenance, support_costs, infrastructure_costs, total_operational_expenses
      )
      VALUES (
        ${testClient.id}, 100, '25.00', '2500.00',
        'active', ${currentDate}, ${nextMonthDate}, ${nextMonthDate},
        'monthly', 3,
        '1000.00', '800.00', '2000.00', '1400.00', '5200.00'
      )
      RETURNING id, monthly_amount, total_operational_expenses
    `
    console.log(`✅ Created test subscription: ${testSubscription.id} (₹${testSubscription.monthly_amount}/month, ₹${testSubscription.total_operational_expenses} expenses)`)

    // 6. Test data summary
    console.log('\n📋 Test Data Summary:')
    console.log(`   Client: ${testClient.school_name} (${testClient.id})`)
    console.log(`   Partner: ${testPartner.name} (${testPartner.id}) - 30% commission`)
    console.log(`   Subscription: ${testSubscription.id}`)
    console.log(`   Current Amount: ₹${testSubscription.monthly_amount}/month`)
    console.log(`   Current Expenses: ₹${testSubscription.total_operational_expenses}/month`)
    console.log(`   Net Amount: ₹${parseFloat(testSubscription.monthly_amount) - parseFloat(testSubscription.total_operational_expenses)}`)
    console.log(`   Expected Commission: ₹${((parseFloat(testSubscription.monthly_amount) - parseFloat(testSubscription.total_operational_expenses)) * 0.30).toFixed(2)}`)

    console.log('\n🎯 Test Scenarios to Try:')
    console.log('1. Update price per student from ₹25 to ₹30 (immediate effect)')
    console.log('2. Update student count from 100 to 120 (next cycle)')
    console.log('3. Update operational expenses (reduce support costs)')
    console.log('4. Test prorated billing for mid-cycle changes')

    console.log('\n📝 API Endpoint to Test:')
    console.log(`PUT /api/admin/subscriptions/${testSubscription.id}/advanced-update`)
    console.log('Body example:')
    console.log(JSON.stringify({
      changes: {
        pricePerStudent: 30,
        operationalExpenses: {
          databaseCosts: 1000,
          websiteMaintenance: 800,
          supportCosts: 1500, // Reduced from 2000
          infrastructureCosts: 1400
        }
      },
      effectiveDate: new Date().toISOString(),
      reason: "Price increase and cost optimization"
    }, null, 2))

    console.log('\n✅ Advanced subscription test data created successfully!')
    console.log('🔗 Use the admin dashboard to test the advanced subscription management features')

  } catch (error) {
    console.error('❌ Test setup failed:', error)
    process.exit(1)
  }
}

testAdvancedSubscriptionSystem()
