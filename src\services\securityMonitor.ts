import { Context } from 'hono'
import { db } from '@/src/db'
import { securityEvents, auditLogs } from '@/src/db/schema'
import { eq, and, gte, count, desc } from 'drizzle-orm'
import { auditLogger, SecurityEvent } from './auditLogger'

export interface SecurityAlert {
  type: 'brute_force' | 'suspicious_activity' | 'data_breach' | 'unauthorized_access' | 'payment_fraud' | 'system_anomaly'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  details: Record<string, any>
  userId?: string
  adminId?: string
  ipAddress: string
  userAgent?: string
  timestamp: Date
}

export interface ThreatDetectionRule {
  name: string
  description: string
  enabled: boolean
  threshold: number
  timeWindowMs: number
  action: 'log' | 'block' | 'alert' | 'escalate'
  severity: 'low' | 'medium' | 'high' | 'critical'
}

class SecurityMonitor {
  private static instance: SecurityMonitor
  private alertQueue: SecurityAlert[] = []
  private isProcessing = false
  private detectionRules: Map<string, ThreatDetectionRule> = new Map()

  private constructor() {
    this.setupDetectionRules()
    
    // Process alerts every 10 seconds
    setInterval(() => {
      this.processAlertQueue()
    }, 10000)

    // Run threat detection every 30 seconds
    setInterval(() => {
      this.runThreatDetection()
    }, 30000)
  }

  static getInstance(): SecurityMonitor {
    if (!SecurityMonitor.instance) {
      SecurityMonitor.instance = new SecurityMonitor()
    }
    return SecurityMonitor.instance
  }

  private setupDetectionRules(): void {
    // Brute force detection
    this.detectionRules.set('brute_force_login', {
      name: 'Brute Force Login Detection',
      description: 'Detect multiple failed login attempts from same IP',
      enabled: true,
      threshold: 5, // 5 failed attempts
      timeWindowMs: 15 * 60 * 1000, // 15 minutes
      action: 'block',
      severity: 'high'
    })

    // Suspicious admin activity
    this.detectionRules.set('admin_mass_actions', {
      name: 'Admin Mass Actions',
      description: 'Detect admin performing too many actions in short time',
      enabled: true,
      threshold: 50, // 50 actions
      timeWindowMs: 5 * 60 * 1000, // 5 minutes
      action: 'alert',
      severity: 'medium'
    })

    // Payment fraud detection
    this.detectionRules.set('payment_fraud', {
      name: 'Payment Fraud Detection',
      description: 'Detect suspicious payment patterns',
      enabled: true,
      threshold: 3, // 3 failed payments
      timeWindowMs: 10 * 60 * 1000, // 10 minutes
      action: 'escalate',
      severity: 'critical'
    })

    // Unauthorized data access
    this.detectionRules.set('data_access_anomaly', {
      name: 'Data Access Anomaly',
      description: 'Detect unusual data access patterns',
      enabled: true,
      threshold: 100, // 100 data access events
      timeWindowMs: 60 * 60 * 1000, // 1 hour
      action: 'alert',
      severity: 'medium'
    })

    // Geographic anomaly
    this.detectionRules.set('geographic_anomaly', {
      name: 'Geographic Anomaly',
      description: 'Detect logins from unusual locations',
      enabled: true,
      threshold: 1, // 1 login from new country
      timeWindowMs: 24 * 60 * 60 * 1000, // 24 hours
      action: 'alert',
      severity: 'medium'
    })
  }

  /**
   * Log a security event
   */
  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      await db.insert(securityEvents).values({
        eventType: event.type,
        userId: event.userId,
        adminId: event.adminId,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        details: event.details,
        severity: event.severity,
        createdAt: event.timestamp || new Date()
      })

      // Also log to audit logger
      await auditLogger.logSecurity(event)

      // Check if this triggers any alerts
      await this.checkForThreats(event)

    } catch (error) {
      console.error('Failed to log security event:', error)
    }
  }

  /**
   * Create a security alert
   */
  async createAlert(alert: SecurityAlert): Promise<void> {
    this.alertQueue.push(alert)

    // For critical alerts, process immediately
    if (alert.severity === 'critical') {
      await this.processAlertQueue()
    }
  }

  /**
   * Check for security threats based on new event
   */
  private async checkForThreats(event: SecurityEvent): Promise<void> {
    try {
      // Check brute force attacks
      if (event.type === 'failed_login') {
        await this.checkBruteForce(event)
      }

      // Check payment fraud
      if (event.type === 'payment_activity') {
        await this.checkPaymentFraud(event)
      }

      // Check admin activity
      if (event.adminId) {
        await this.checkAdminActivity(event)
      }

    } catch (error) {
      console.error('Threat detection error:', error)
    }
  }

  /**
   * Check for brute force attacks
   */
  private async checkBruteForce(event: SecurityEvent): Promise<void> {
    const rule = this.detectionRules.get('brute_force_login')
    if (!rule || !rule.enabled) return

    const timeWindow = new Date(Date.now() - rule.timeWindowMs)

    try {
      const [result] = await db
        .select({ count: count() })
        .from(securityEvents)
        .where(
          and(
            eq(securityEvents.eventType, 'failed_login'),
            eq(securityEvents.ipAddress, event.ipAddress),
            gte(securityEvents.createdAt, timeWindow)
          )
        )

      if (result.count >= rule.threshold) {
        await this.createAlert({
          type: 'brute_force',
          severity: rule.severity,
          message: `Brute force attack detected from IP ${event.ipAddress}`,
          details: {
            ipAddress: event.ipAddress,
            failedAttempts: result.count,
            timeWindow: rule.timeWindowMs,
            rule: rule.name
          },
          ipAddress: event.ipAddress,
          userAgent: event.userAgent,
          timestamp: new Date()
        })
      }

    } catch (error) {
      console.error('Brute force check error:', error)
    }
  }

  /**
   * Check for payment fraud
   */
  private async checkPaymentFraud(event: SecurityEvent): Promise<void> {
    const rule = this.detectionRules.get('payment_fraud')
    if (!rule || !rule.enabled) return

    const timeWindow = new Date(Date.now() - rule.timeWindowMs)

    try {
      const [result] = await db
        .select({ count: count() })
        .from(auditLogs)
        .where(
          and(
            eq(auditLogs.category, 'payment'),
            eq(auditLogs.success, false),
            eq(auditLogs.ipAddress, event.ipAddress),
            gte(auditLogs.timestamp, timeWindow)
          )
        )

      if (result.count >= rule.threshold) {
        await this.createAlert({
          type: 'payment_fraud',
          severity: rule.severity,
          message: `Potential payment fraud detected from IP ${event.ipAddress}`,
          details: {
            ipAddress: event.ipAddress,
            failedPayments: result.count,
            timeWindow: rule.timeWindowMs,
            rule: rule.name
          },
          ipAddress: event.ipAddress,
          userAgent: event.userAgent,
          timestamp: new Date()
        })
      }

    } catch (error) {
      console.error('Payment fraud check error:', error)
    }
  }

  /**
   * Check for suspicious admin activity
   */
  private async checkAdminActivity(event: SecurityEvent): Promise<void> {
    if (!event.adminId) return

    const rule = this.detectionRules.get('admin_mass_actions')
    if (!rule || !rule.enabled) return

    const timeWindow = new Date(Date.now() - rule.timeWindowMs)

    try {
      const [result] = await db
        .select({ count: count() })
        .from(auditLogs)
        .where(
          and(
            eq(auditLogs.adminId, event.adminId),
            eq(auditLogs.category, 'admin'),
            gte(auditLogs.timestamp, timeWindow)
          )
        )

      if (result.count >= rule.threshold) {
        await this.createAlert({
          type: 'suspicious_activity',
          severity: rule.severity,
          message: `Suspicious admin activity detected`,
          details: {
            adminId: event.adminId,
            actionCount: result.count,
            timeWindow: rule.timeWindowMs,
            rule: rule.name
          },
          adminId: event.adminId,
          ipAddress: event.ipAddress,
          userAgent: event.userAgent,
          timestamp: new Date()
        })
      }

    } catch (error) {
      console.error('Admin activity check error:', error)
    }
  }

  /**
   * Run comprehensive threat detection
   */
  private async runThreatDetection(): Promise<void> {
    try {
      // This would run more comprehensive analysis
      // For now, just log that detection is running
      console.log('Running threat detection...')
    } catch (error) {
      console.error('Threat detection error:', error)
    }
  }

  /**
   * Process alert queue
   */
  private async processAlertQueue(): Promise<void> {
    if (this.isProcessing || this.alertQueue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      const alertsToProcess = [...this.alertQueue]
      this.alertQueue = []

      for (const alert of alertsToProcess) {
        await this.processAlert(alert)
      }

    } catch (error) {
      console.error('Failed to process security alerts:', error)
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Process individual alert
   */
  private async processAlert(alert: SecurityAlert): Promise<void> {
    try {
      // Log the alert
      console.log(`SECURITY ALERT [${alert.severity.toUpperCase()}]: ${alert.message}`, alert.details)

      // In production, you would:
      // 1. Send email notifications
      // 2. Send Slack/Teams notifications
      // 3. Create tickets in incident management system
      // 4. Trigger automated responses (blocking IPs, etc.)

      // For critical alerts, you might want to:
      if (alert.severity === 'critical') {
        // Send immediate notifications
        // Trigger emergency response procedures
        console.log('CRITICAL SECURITY ALERT - IMMEDIATE ATTENTION REQUIRED')
      }

    } catch (error) {
      console.error('Failed to process alert:', error)
    }
  }

  /**
   * Get security dashboard data
   */
  async getSecurityDashboard(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<any> {
    try {
      const timeRangeMs = {
        hour: 60 * 60 * 1000,
        day: 24 * 60 * 60 * 1000,
        week: 7 * 24 * 60 * 60 * 1000,
        month: 30 * 24 * 60 * 60 * 1000
      }

      const startTime = new Date(Date.now() - timeRangeMs[timeRange])

      // Get security events summary
      const events = await db
        .select()
        .from(securityEvents)
        .where(gte(securityEvents.createdAt, startTime))
        .orderBy(desc(securityEvents.createdAt))
        .limit(100)

      // Get threat detection summary
      const threatSummary = {
        totalEvents: events.length,
        criticalEvents: events.filter(e => e.severity === 'critical').length,
        highEvents: events.filter(e => e.severity === 'high').length,
        mediumEvents: events.filter(e => e.severity === 'medium').length,
        lowEvents: events.filter(e => e.severity === 'low').length,
        topIPs: this.getTopIPs(events),
        eventTypes: this.getEventTypeSummary(events)
      }

      return {
        summary: threatSummary,
        recentEvents: events.slice(0, 20),
        detectionRules: Array.from(this.detectionRules.values())
      }

    } catch (error) {
      console.error('Failed to get security dashboard:', error)
      return {
        summary: {},
        recentEvents: [],
        detectionRules: []
      }
    }
  }

  private getTopIPs(events: any[]): Array<{ ip: string; count: number }> {
    const ipCounts = new Map<string, number>()
    
    events.forEach(event => {
      const count = ipCounts.get(event.ipAddress) || 0
      ipCounts.set(event.ipAddress, count + 1)
    })

    return Array.from(ipCounts.entries())
      .map(([ip, count]) => ({ ip, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
  }

  private getEventTypeSummary(events: any[]): Array<{ type: string; count: number }> {
    const typeCounts = new Map<string, number>()
    
    events.forEach(event => {
      const count = typeCounts.get(event.eventType) || 0
      typeCounts.set(event.eventType, count + 1)
    })

    return Array.from(typeCounts.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
  }
}

export const securityMonitor = SecurityMonitor.getInstance()
