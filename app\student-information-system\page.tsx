import { Metadata } from 'next'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Breadcrumb, BreadcrumbStructuredData } from '@/components/ui/Breadcrumb'
import { 
  Users, 
  FileText, 
  Calendar, 
  BarChart3, 
  Shield, 
  CheckCircle,
  ArrowRight,
  Star,
  Database,
  UserCheck,
  BookOpen,
  Award
} from 'lucide-react'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Best Student Information System (SIS) in India | Complete Student Management | Schopio',
  description: 'Schopio offers the best Student Information System (SIS) in India. Complete student management with academic records, attendance tracking, and performance analytics. Trusted by 500+ schools.',
  keywords: 'student information system, SIS, student management system, student database, academic records management, student information management, school student system, student data management',
  openGraph: {
    title: 'Best Student Information System (SIS) in India | Complete Student Management | Schopio',
    description: 'Schopio offers the best Student Information System (SIS) in India. Complete student management with academic records, attendance tracking, and performance analytics. Trusted by 500+ schools.',
    type: 'website',
  },
}

export default function StudentInformationSystemPage() {
  const breadcrumbItems = [
    { label: 'Student Information System' }
  ]

  const sisFeatures = [
    {
      icon: Users,
      title: "Student Profiles",
      description: "Comprehensive student database with personal, academic, and contact information",
      features: ["Personal information", "Contact details", "Emergency contacts", "Medical records"]
    },
    {
      icon: FileText,
      title: "Academic Records",
      description: "Complete academic history with grades, transcripts, and performance tracking",
      features: ["Grade management", "Transcript generation", "Academic history", "Performance reports"]
    },
    {
      icon: Calendar,
      title: "Attendance Management",
      description: "Real-time attendance tracking with automated notifications and reports",
      features: ["Daily attendance", "Absence tracking", "Automated alerts", "Attendance reports"]
    },
    {
      icon: BarChart3,
      title: "Performance Analytics",
      description: "Advanced analytics for student performance insights and progress tracking",
      features: ["Performance metrics", "Progress tracking", "Comparative analysis", "Predictive insights"]
    },
    {
      icon: Database,
      title: "Data Management",
      description: "Secure data storage with backup, migration, and integration capabilities",
      features: ["Data backup", "Migration tools", "API integration", "Data security"]
    },
    {
      icon: Shield,
      title: "Privacy & Security",
      description: "Enterprise-grade security with role-based access and privacy compliance",
      features: ["Data encryption", "Access control", "Privacy compliance", "Audit trails"]
    }
  ]

  const benefits = [
    "Centralized student information management",
    "Real-time access to student data",
    "Automated attendance and grade tracking",
    "Comprehensive reporting and analytics",
    "Parent portal for student information access",
    "Mobile-friendly interface for all users",
    "Integration with other school systems",
    "Secure and compliant data management"
  ]

  const dataPoints = [
    { label: "Student Records", value: "50,000+", icon: Users },
    { label: "Academic Years", value: "10+", icon: Calendar },
    { label: "Data Points", value: "100+", icon: Database },
    { label: "Reports Generated", value: "1M+", icon: FileText }
  ]

  const testimonials = [
    {
      name: "Mrs. Sunita Verma",
      role: "Academic Coordinator, Modern Public School",
      content: "The best student information system we've used. Makes managing student data incredibly easy.",
      rating: 5
    },
    {
      name: "Dr. Amit Sharma",
      role: "Principal, Delhi International School",
      content: "Comprehensive SIS solution that has transformed our student management processes.",
      rating: 5
    }
  ]

  return (
    <main className="min-h-screen bg-white">
      <BreadcrumbStructuredData items={breadcrumbItems} />
      
      {/* Breadcrumb Navigation */}
      <section className="py-4 bg-white border-b">
        <div className="container mx-auto px-4">
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-indigo-100 text-indigo-700 border border-indigo-200 px-4 py-2 rounded-full text-sm font-bold mb-6">
              <Database className="w-4 h-4" />
              Complete Student Information System
            </div>
            <h1 className="text-5xl lg:text-7xl font-bold text-slate-900 mb-6">
              Best Student Information 
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent"> System</span>
            </h1>
            <p className="text-xl text-slate-600 leading-relaxed mb-8">
              Schopio offers the most comprehensive Student Information System (SIS) in India. 
              Complete student management with academic records, attendance tracking, performance analytics, 
              and secure data management. Trusted by 500+ schools nationwide.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo">
                <Button size="lg" className="bg-indigo-600 hover:bg-indigo-700">
                  Get SIS Demo <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/packages">
                <Button variant="outline" size="lg">
                  View SIS Pricing
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* SIS Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-4">
              Complete Student Information System Features
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Everything you need to manage student information efficiently and securely
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sisFeatures.map((feature, index) => (
              <Card key={index} className="border-2 hover:border-indigo-200 transition-colors">
                <CardHeader>
                  <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-indigo-600" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900">{feature.title}</h3>
                  <p className="text-slate-600">{feature.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <li key={idx} className="flex items-center gap-2 text-sm text-slate-600">
                        <CheckCircle className="w-4 h-4 text-emerald-500" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Data Points Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-900 mb-4">
              Trusted Student Information System
            </h2>
            <p className="text-xl text-slate-600">
              Managing student data at scale with reliability and security
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {dataPoints.map((point, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <point.icon className="w-8 h-8 text-indigo-600" />
                </div>
                <h3 className="text-3xl font-bold text-slate-900 mb-2">{point.value}</h3>
                <p className="text-slate-600">{point.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-slate-900 mb-4">
                Why Choose Our Student Information System?
              </h2>
              <p className="text-xl text-slate-600">
                Comprehensive benefits for schools, teachers, students, and parents
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-6 h-6 text-emerald-500 flex-shrink-0" />
                  <span className="text-slate-700">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-900 mb-4">
              What Schools Say About Our SIS
            </h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-2">
                <CardContent className="p-6">
                  <div className="flex gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-slate-700 mb-4">"{testimonial.content}"</p>
                  <div>
                    <p className="font-semibold text-slate-900">{testimonial.name}</p>
                    <p className="text-sm text-slate-600">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Implement the Best Student Information System?
            </h2>
            <p className="text-xl text-indigo-100 mb-8">
              Join 500+ schools using Schopio's comprehensive Student Information System. 
              Transform your student management today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo">
                <Button size="lg" className="bg-white text-indigo-600 hover:bg-gray-100">
                  Get SIS Demo <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/packages">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-indigo-600">
                  View SIS Pricing
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
