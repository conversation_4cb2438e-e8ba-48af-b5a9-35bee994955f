import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { pdfInvoiceService } from '../../../src/services/pdfInvoiceService'
import { db } from '../../../src/db'
import { billingInvoices, clients } from '../../../src/db/schema'
import { eq, and } from 'drizzle-orm'

const app = new Hono()

// Schema for invoice PDF generation
const invoicePDFSchema = z.object({
  invoiceId: z.string().uuid('Invalid invoice ID format')
})

// Schema for invoice access validation
const invoiceAccessSchema = z.object({
  invoiceId: z.string().uuid(),
  clientId: z.string().uuid().optional(), // For client access validation
  adminAccess: z.boolean().optional() // For admin access
})

/**
 * Generate and download PDF invoice
 * GET /api/billing/invoice/:invoiceId/pdf
 */
app.get('/invoice/:invoiceId/pdf', 
  zValidator('param', invoicePDFSchema),
  async (c) => {
    try {
      const { invoiceId } = c.req.valid('param')
      
      // Validate invoice exists and get basic info
      const [invoiceInfo] = await db.select({
        id: billingInvoices.id,
        invoiceNumber: billingInvoices.invoiceNumber,
        clientId: billingInvoices.clientId,
        status: billingInvoices.status,
        schoolName: clients.schoolName
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .where(eq(billingInvoices.id, invoiceId))
      .limit(1)

      if (!invoiceInfo) {
        return c.json({ error: 'Invoice not found' }, 404)
      }

      // Generate PDF
      const result = await pdfInvoiceService.generateInvoicePDF(invoiceId)
      
      if (!result.success) {
        console.error('PDF generation failed:', result.error)
        return c.json({ error: 'Failed to generate PDF invoice' }, 500)
      }

      // Set response headers for PDF download
      c.header('Content-Type', 'application/pdf')
      c.header('Content-Disposition', `attachment; filename="${result.fileName}"`)
      c.header('Cache-Control', 'no-cache, no-store, must-revalidate')
      c.header('Pragma', 'no-cache')
      c.header('Expires', '0')

      return c.body(result.pdfBuffer!)
    } catch (error) {
      console.error('Error in PDF invoice generation:', error)
      return c.json({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

/**
 * Preview PDF invoice in browser (inline display)
 * GET /api/billing/invoice/:invoiceId/preview
 */
app.get('/invoice/:invoiceId/preview',
  zValidator('param', invoicePDFSchema),
  async (c) => {
    try {
      const { invoiceId } = c.req.valid('param')
      
      // Validate invoice exists
      const [invoiceInfo] = await db.select({
        id: billingInvoices.id,
        invoiceNumber: billingInvoices.invoiceNumber,
        status: billingInvoices.status
      })
      .from(billingInvoices)
      .where(eq(billingInvoices.id, invoiceId))
      .limit(1)

      if (!invoiceInfo) {
        return c.json({ error: 'Invoice not found' }, 404)
      }

      // Generate PDF
      const result = await pdfInvoiceService.generateInvoicePDF(invoiceId)
      
      if (!result.success) {
        console.error('PDF generation failed:', result.error)
        return c.json({ error: 'Failed to generate PDF invoice' }, 500)
      }

      // Set response headers for inline PDF display
      c.header('Content-Type', 'application/pdf')
      c.header('Content-Disposition', `inline; filename="${result.fileName}"`)
      c.header('Cache-Control', 'public, max-age=3600') // Cache for 1 hour

      return c.body(result.pdfBuffer!)
    } catch (error) {
      console.error('Error in PDF invoice preview:', error)
      return c.json({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

/**
 * Get invoice metadata for PDF generation
 * GET /api/billing/invoice/:invoiceId/info
 */
app.get('/invoice/:invoiceId/info',
  zValidator('param', invoicePDFSchema),
  async (c) => {
    try {
      const { invoiceId } = c.req.valid('param')
      
      // Get invoice information
      const [invoiceInfo] = await db.select({
        id: billingInvoices.id,
        invoiceNumber: billingInvoices.invoiceNumber,
        monthlyAmount: billingInvoices.totalAmount,
        status: billingInvoices.status,
        issuedDate: billingInvoices.issuedDate,
        nextBillingDate: billingInvoices.dueDate,

        clientId: billingInvoices.clientId,
        schoolName: clients.schoolName,
        email: clients.email
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .where(eq(billingInvoices.id, invoiceId))
      .limit(1)

      if (!invoiceInfo) {
        return c.json({ error: 'Invoice not found' }, 404)
      }

      return c.json({
        success: true,
        invoice: {
          id: invoiceInfo.id,
          invoiceNumber: invoiceInfo.invoiceNumber,
          monthlyAmount: invoiceInfo.monthlyAmount,
          status: invoiceInfo.status,
          issuedDate: invoiceInfo.issuedDate,
          nextBillingDate: invoiceInfo.nextBillingDate,
          client: {
            id: invoiceInfo.clientId,
            schoolName: invoiceInfo.schoolName,
            email: invoiceInfo.email
          }
        }
      })
    } catch (error) {
      console.error('Error fetching invoice info:', error)
      return c.json({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

/**
 * Bulk PDF generation for multiple invoices (Admin only)
 * POST /api/billing/invoices/bulk-pdf
 */
app.post('/invoices/bulk-pdf',
  zValidator('json', z.object({
    invoiceIds: z.array(z.string().uuid()).min(1).max(50), // Limit to 50 invoices
    format: z.enum(['zip', 'merged', 'links']).default('zip') // zip = ZIP download, merged = single PDF, links = individual links
  })),
  async (c) => {
    try {
      const { invoiceIds, format } = c.req.valid('json')

      console.log(`📋 Bulk PDF request: ${invoiceIds.length} invoices, format: ${format}`)

      if (format === 'zip') {
        // Generate ZIP file with all PDFs
        const bulkResult = await pdfInvoiceService.generateBulkInvoicePDFs(invoiceIds)

        if (!bulkResult.success) {
          return c.json({
            success: false,
            error: bulkResult.error || 'Failed to generate bulk PDF ZIP'
          }, 500)
        }

        // Return ZIP file as download
        const headers = new Headers()
        headers.set('Content-Type', 'application/zip')
        headers.set('Content-Disposition', `attachment; filename="${bulkResult.fileName}"`)
        headers.set('Content-Length', bulkResult.zipBuffer!.length.toString())

        return new Response(bulkResult.zipBuffer, {
          status: 200,
          headers
        })

      } else if (format === 'merged') {
        // Generate single merged PDF with all invoices
        const mergedResult = await pdfInvoiceService.generateMergedInvoicePDF(invoiceIds)

        if (!mergedResult.success) {
          return c.json({
            success: false,
            error: mergedResult.error || 'Failed to generate merged PDF'
          }, 500)
        }

        // Return merged PDF as download
        const headers = new Headers()
        headers.set('Content-Type', 'application/pdf')
        headers.set('Content-Disposition', `attachment; filename="${mergedResult.fileName}"`)
        headers.set('Content-Length', mergedResult.pdfBuffer!.length.toString())

        return new Response(mergedResult.pdfBuffer, {
          status: 200,
          headers
        })

      } else {
        // Return individual PDF links (legacy format)
        const results = []

        for (const invoiceId of invoiceIds) {
          const [invoiceInfo] = await db.select({
            id: billingInvoices.id,
            invoiceNumber: billingInvoices.invoiceNumber,
            schoolName: clients.schoolName
          })
          .from(billingInvoices)
          .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
          .where(eq(billingInvoices.id, invoiceId))
          .limit(1)

          if (invoiceInfo) {
            results.push({
              invoiceId: invoiceInfo.id,
              invoiceNumber: invoiceInfo.invoiceNumber,
              schoolName: invoiceInfo.schoolName,
              downloadUrl: `/api/billing/invoice/${invoiceInfo.id}/pdf`,
              previewUrl: `/api/billing/invoice/${invoiceInfo.id}/preview`
            })
          }
        }

        return c.json({
          success: true,
          format,
          invoices: results,
          message: `Generated ${results.length} PDF invoice links`
        })
      }
    } catch (error) {
      console.error('Error in bulk PDF generation:', error)
      return c.json({
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

/**
 * Health check for PDF service
 * GET /api/billing/pdf/health
 */
app.get('/pdf/health', async (c) => {
  try {
    // Test PDF generation with minimal data
    const testResult = {
      pdfServiceAvailable: true,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }
    
    return c.json({
      success: true,
      status: 'healthy',
      ...testResult
    })
  } catch (error) {
    return c.json({
      success: false,
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

export default app
