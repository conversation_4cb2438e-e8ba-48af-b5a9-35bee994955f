import { db } from '@/src/db'
import {
  subscriptions,
  billingSubscriptions,
  billingInvoices,
  billingPaymentReminders,
  clients
} from '@/src/db/schema'
import { eq, and, lte, gte, sql } from 'drizzle-orm'
import { auditLogger } from './auditLogger'
import { billingMonitor } from './billingMonitor'
import { emailService } from './emailService'

export interface DunningAction {
  type: 'email_reminder' | 'phone_call' | 'suspension_warning' | 'service_suspension' | 'account_termination'
  daysOverdue: number
  description: string
  automated: boolean
  executed: boolean
  executedAt?: Date
}

export interface DunningCase {
  id: string
  clientId: string
  invoiceId: string
  daysOverdue: number
  monthlyAmount: number
  currentAction: DunningAction
  nextAction?: DunningAction
  status: 'active' | 'resolved' | 'escalated' | 'suspended'
  createdAt: Date
  updatedAt: Date
  client: {
    schoolName: string
    email: string
    contactPerson: string
    phone: string
  }
}

export interface DunningStats {
  totalCases: number
  activeCases: number
  resolvedCases: number
  escalatedCases: number
  suspendedCases: number
  averageResolutionDays: number
  recoveryRate: number
  totalRecovered: number
}

class DunningManager {
  private static instance: DunningManager
  private dunningSequence: DunningAction[] = [
    { type: 'email_reminder', daysOverdue: 1, description: 'First payment reminder email', automated: true, executed: false },
    { type: 'email_reminder', daysOverdue: 3, description: 'Second payment reminder email', automated: true, executed: false },
    { type: 'email_reminder', daysOverdue: 7, description: 'Final payment reminder email', automated: true, executed: false },
    { type: 'phone_call', daysOverdue: 10, description: 'Phone call to client', automated: false, executed: false },
    { type: 'suspension_warning', daysOverdue: 12, description: 'Service suspension warning', automated: true, executed: false },
    { type: 'service_suspension', daysOverdue: 15, description: 'Suspend service access', automated: true, executed: false },
    { type: 'account_termination', daysOverdue: 30, description: 'Terminate account', automated: false, executed: false }
  ]

  private constructor() {}

  public static getInstance(): DunningManager {
    if (!DunningManager.instance) {
      DunningManager.instance = new DunningManager()
    }
    return DunningManager.instance
  }

  /**
   * Process all overdue invoices and execute dunning actions
   */
  public async processDunningActions(): Promise<{
    processed: number
    actionsExecuted: number
    errors: number
    details: any[]
  }> {
    try {
      console.log('🔄 Processing dunning actions...')

      // Get all overdue invoices
      const overdueInvoices = await this.getOverdueInvoices()
      
      const results = {
        processed: 0,
        actionsExecuted: 0,
        errors: 0,
        details: [] as any[]
      }

      for (const invoice of overdueInvoices) {
        try {
          results.processed++
          
          const daysOverdue = this.calculateDaysOverdue(invoice.dueDate)
          const currentAction = this.getCurrentAction(daysOverdue)
          
          if (currentAction && invoice.id && !await this.isActionExecuted(invoice.id, currentAction.type)) {
            const executed = await this.executeAction(invoice, currentAction, daysOverdue)
            
            if (executed) {
              results.actionsExecuted++
              results.details.push({
                invoiceId: invoice.id,
                clientId: invoice.clientId,
                action: currentAction.type,
                daysOverdue,
                status: 'executed'
              })

              // Log the action
              await auditLogger.logAdmin('dunning_action_executed', {
                adminId: 'system',
                resource: 'dunning_management',
                resourceId: invoice.id || undefined,
                details: {
                  action: currentAction.type,
                  daysOverdue,
                  invoiceNumber: invoice.invoiceNumber,
                  totalAmount: invoice.totalAmount,
                  clientId: invoice.clientId
                },
                ipAddress: 'system',
                userAgent: 'dunning-manager',
                success: true
              })
            }
          }

        } catch (error) {
          results.errors++
          results.details.push({
            invoiceId: invoice.id,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          })

          console.error(`❌ Dunning action failed for invoice ${invoice.id}:`, error)
        }
      }

      console.log(`✅ Dunning processing completed: ${results.actionsExecuted} actions executed`)

      return results

    } catch (error) {
      console.error('❌ Dunning processing failed:', error)
      throw error
    }
  }

  /**
   * Get all overdue invoices with client information
   */
  private async getOverdueInvoices() {
    const today = new Date().toISOString().split('T')[0]

    return await db
      .select({
        id: billingInvoices.id,
        clientId: billingInvoices.clientId,
        invoiceNumber: billingInvoices.invoiceNumber,
        totalAmount: billingInvoices.totalAmount,
        dueDate: billingInvoices.dueDate,
        status: billingInvoices.status,
        // Client information
        schoolName: clients.schoolName,
        email: clients.email,
        contactPerson: clients.contactPerson,
        phone: clients.phone,
        // Subscription information
        subscriptionId: subscriptions.id,
        subscriptionStatus: subscriptions.status
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .leftJoin(billingSubscriptions, eq(billingInvoices.subscriptionId, billingSubscriptions.id))
      .leftJoin(subscriptions, eq(billingSubscriptions.id, subscriptions.id))
      .where(and(
        eq(billingInvoices.status, 'sent'),
        lte(billingInvoices.dueDate, today),
        eq(subscriptions.status, 'active') // Only process active subscriptions
      ))
      .orderBy(billingInvoices.dueDate)
  }

  /**
   * Calculate days overdue for an invoice
   */
  private calculateDaysOverdue(dueDate: string): number {
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = today.getTime() - due.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * Get the current action based on days overdue
   */
  private getCurrentAction(daysOverdue: number): DunningAction | null {
    // Find the most recent action that should be executed
    const applicableActions = this.dunningSequence.filter(action => daysOverdue >= action.daysOverdue)
    return applicableActions.length > 0 ? applicableActions[applicableActions.length - 1] : null
  }

  /**
   * Check if an action has already been executed for an invoice
   */
  private async isActionExecuted(invoiceId: string, actionType: string): Promise<boolean> {
    const [reminder] = await db
      .select()
      .from(billingPaymentReminders)
      .where(and(
        eq(billingPaymentReminders.invoiceId, invoiceId),
        eq(billingPaymentReminders.reminderType, actionType)
      ))
      .limit(1)

    return reminder !== undefined
  }

  /**
   * Execute a dunning action
   */
  private async executeAction(invoice: any, action: DunningAction, daysOverdue: number): Promise<boolean> {
    try {
      switch (action.type) {
        case 'email_reminder':
          return await this.sendEmailReminder(invoice, action, daysOverdue)
        
        case 'phone_call':
          return await this.schedulePhoneCall(invoice, action, daysOverdue)
        
        case 'suspension_warning':
          return await this.sendSuspensionWarning(invoice, action, daysOverdue)
        
        case 'service_suspension':
          return await this.suspendService(invoice, action, daysOverdue)
        
        case 'account_termination':
          return await this.scheduleAccountTermination(invoice, action, daysOverdue)
        
        default:
          console.warn(`Unknown dunning action type: ${action.type}`)
          return false
      }
    } catch (error) {
      console.error(`Failed to execute dunning action ${action.type}:`, error)
      return false
    }
  }

  /**
   * Send email reminder
   */
  private async sendEmailReminder(invoice: any, action: DunningAction, daysOverdue: number): Promise<boolean> {
    try {
      // Determine email type based on action and days overdue
      let emailResult

      if (daysOverdue >= 15) {
        // Final notice for accounts 15+ days overdue
        emailResult = await emailService.sendFinalNotice({
          schoolName: invoice.schoolName,
          contactPerson: invoice.contactPerson || 'Admin',
          email: invoice.email,
          invoiceNumber: invoice.invoiceNumber,
          amount: invoice.totalAmount,
          dueDate: invoice.dueDate,
          daysOverdue,
          paymentUrl: `${process.env.FRONTEND_URL || 'https://schopio.com'}/billing/pay/${invoice.id}`
        })
      } else if (daysOverdue > 0) {
        // Overdue notice for accounts past due
        emailResult = await emailService.sendOverdueNotice({
          schoolName: invoice.schoolName,
          contactPerson: invoice.contactPerson || 'Admin',
          email: invoice.email,
          invoiceNumber: invoice.invoiceNumber,
          amount: invoice.totalAmount,
          dueDate: invoice.dueDate,
          daysOverdue,
          paymentUrl: `${process.env.FRONTEND_URL || 'https://schopio.com'}/billing/pay/${invoice.id}`
        })
      } else {
        // Payment reminder for upcoming due dates
        emailResult = await emailService.sendPaymentReminder({
          schoolName: invoice.schoolName,
          contactPerson: invoice.contactPerson || 'Admin',
          email: invoice.email,
          invoiceNumber: invoice.invoiceNumber,
          amount: invoice.totalAmount,
          dueDate: invoice.dueDate,
          paymentUrl: `${process.env.FRONTEND_URL || 'https://schopio.com'}/billing/pay/${invoice.id}`
        })
      }

      if (!emailResult.success) {
        console.error(`❌ Failed to send email reminder: ${emailResult.error}`)
        return false
      }

      // Create payment reminder record
      await db.insert(billingPaymentReminders).values({
        invoiceId: invoice.id,
        clientId: invoice.clientId,
        reminderType: action.type,
        sentDate: new Date(),
        emailSent: true,
        smsSent: false
      })

      console.log(`✅ Email reminder sent to ${invoice.email} for invoice ${invoice.invoiceNumber} (ID: ${'id' in emailResult ? emailResult.id || 'N/A' : 'N/A'})`)
      return true
    } catch (error) {
      console.error('Failed to send email reminder:', error)
      return false
    }
  }

  /**
   * Schedule phone call (manual action)
   */
  private async schedulePhoneCall(invoice: any, action: DunningAction, daysOverdue: number): Promise<boolean> {
    try {
      // Create reminder record for manual follow-up
      await db.insert(billingPaymentReminders).values({
        invoiceId: invoice.id,
        clientId: invoice.clientId,
        reminderType: action.type,
        sentDate: new Date(),
        emailSent: false,
        smsSent: false
      })

      console.log(`📞 Phone call scheduled for ${invoice.contactPerson} (${invoice.phone}) - Invoice ${invoice.invoiceNumber}`)

      return true
    } catch (error) {
      console.error('Failed to schedule phone call:', error)
      return false
    }
  }

  /**
   * Send suspension warning
   */
  private async sendSuspensionWarning(invoice: any, action: DunningAction, daysOverdue: number): Promise<boolean> {
    try {
      // Send final notice email for suspension warning
      const emailResult = await emailService.sendFinalNotice({
        schoolName: invoice.schoolName,
        contactPerson: invoice.contactPerson || 'Admin',
        email: invoice.email,
        invoiceNumber: invoice.invoiceNumber,
        amount: invoice.totalAmount,
        dueDate: invoice.dueDate,
        daysOverdue,
        paymentUrl: `${process.env.FRONTEND_URL || 'https://schopio.com'}/billing/pay/${invoice.id}`
      })

      if (!emailResult.success) {
        console.error(`❌ Failed to send suspension warning email: ${emailResult.error}`)
        return false
      }

      // Create warning record
      await db.insert(billingPaymentReminders).values({
        invoiceId: invoice.id,
        clientId: invoice.clientId,
        reminderType: action.type,
        sentDate: new Date(),
        emailSent: true,
        smsSent: false
      })

      console.log(`⚠️ Suspension warning sent to ${invoice.email} for invoice ${invoice.invoiceNumber} (ID: ${'id' in emailResult ? emailResult.id || 'N/A' : 'N/A'})`)
      return true
    } catch (error) {
      console.error('Failed to send suspension warning:', error)
      return false
    }
  }

  /**
   * Suspend service
   */
  private async suspendService(invoice: any, action: DunningAction, daysOverdue: number): Promise<boolean> {
    try {
      // Update subscription status to suspended
      await db
        .update(subscriptions)
        .set({
          status: 'suspended',
          notes: `Service suspended due to payment overdue: ${daysOverdue} days. Invoice: ${invoice.invoiceNumber}`
        })
        .where(eq(subscriptions.id, invoice.subscriptionId))

      // Create suspension record
      await db.insert(billingPaymentReminders).values({
        invoiceId: invoice.id,
        clientId: invoice.clientId,
        reminderType: action.type,
        sentDate: new Date(),
        emailSent: true,
        smsSent: false
      })

      console.log(`🚫 Service suspended for client ${invoice.clientId} - Invoice ${invoice.invoiceNumber}`)

      return true
    } catch (error) {
      console.error('Failed to suspend service:', error)
      return false
    }
  }

  /**
   * Schedule account termination (manual action)
   */
  private async scheduleAccountTermination(invoice: any, action: DunningAction, daysOverdue: number): Promise<boolean> {
    try {
      // Create termination record for manual review
      await db.insert(billingPaymentReminders).values({
        invoiceId: invoice.id,
        clientId: invoice.clientId,
        reminderType: action.type,
        sentDate: new Date(),
        emailSent: false,
        smsSent: false
      })

      console.log(`🔴 Account termination scheduled for client ${invoice.clientId} - Manual review required`)

      return true
    } catch (error) {
      console.error('Failed to schedule account termination:', error)
      return false
    }
  }

  /**
   * Get dunning statistics
   */
  public async getDunningStats(): Promise<DunningStats> {
    try {
      const today = new Date()
      const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000))

      // Get overdue invoices count
      const [totalOverdue] = await db
        .select({ count: sql<number>`COUNT(*)` })
        .from(billingInvoices)
        .where(and(
          eq(billingInvoices.status, 'sent'),
          lte(billingInvoices.dueDate, today.toISOString().split('T')[0])
        ))

      // Get resolved cases (paid invoices that were overdue)
      const [resolvedCases] = await db
        .select({ count: sql<number>`COUNT(*)` })
        .from(billingInvoices)
        .where(and(
          eq(billingInvoices.status, 'paid'),
          gte(billingInvoices.paidDate, thirtyDaysAgo.toISOString())
        ))

      // Get suspended subscriptions
      const [suspendedCases] = await db
        .select({ count: sql<number>`COUNT(*)` })
        .from(subscriptions)
        .where(eq(subscriptions.status, 'suspended'))

      return {
        totalCases: totalOverdue.count,
        activeCases: totalOverdue.count,
        resolvedCases: resolvedCases.count,
        escalatedCases: 0, // TODO: Implement escalation tracking
        suspendedCases: suspendedCases.count,
        averageResolutionDays: 7, // TODO: Calculate actual average
        recoveryRate: totalOverdue.count > 0 ? (resolvedCases.count / totalOverdue.count) * 100 : 100,
        totalRecovered: 0 // TODO: Calculate total recovered amount
      }

    } catch (error) {
      console.error('Failed to get dunning stats:', error)
      throw error
    }
  }

  /**
   * Get active dunning cases
   */
  public async getActiveDunningCases(): Promise<DunningCase[]> {
    try {
      const overdueInvoices = await this.getOverdueInvoices()
      
      return overdueInvoices
        .filter(invoice => invoice.clientId !== null && invoice.subscriptionId !== null && invoice.id !== null)
        .map(invoice => {
        const daysOverdue = this.calculateDaysOverdue(invoice.dueDate)
        const currentAction = this.getCurrentAction(daysOverdue)
        const nextActionIndex = this.dunningSequence.findIndex(a => a.type === currentAction?.type) + 1
        const nextAction = nextActionIndex < this.dunningSequence.length ? this.dunningSequence[nextActionIndex] : undefined

        return {
          id: `dunning-${invoice.id}`,
          clientId: invoice.clientId!,
          invoiceId: invoice.id!,
          daysOverdue,
          monthlyAmount: parseFloat(invoice.totalAmount),
          currentAction: currentAction!,
          nextAction,
          status: invoice.subscriptionStatus === 'suspended' ? 'suspended' : 'active',
          createdAt: new Date(invoice.dueDate),
          updatedAt: new Date(),
          client: {
            schoolName: invoice.schoolName || 'Unknown School',
            email: invoice.email || '<EMAIL>',
            contactPerson: invoice.contactPerson || 'Unknown Contact',
            phone: invoice.phone || 'N/A'
          }
        }
      })

    } catch (error) {
      console.error('Failed to get active dunning cases:', error)
      throw error
    }
  }
}

export const dunningManager = DunningManager.getInstance()
