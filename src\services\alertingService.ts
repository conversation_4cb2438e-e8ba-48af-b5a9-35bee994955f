/**
 * Critical Event Alerting Service for Schopio
 * Monitors system health and sends alerts for critical events
 */

import { Resend } from 'resend';

// Alert severity levels
export enum AlertSeverity {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  INFO = 'info'
}

// Alert categories
export enum AlertCategory {
  PAYMENT = 'payment',
  SUBSCRIPTION = 'subscription',
  SECURITY = 'security',
  PERFORMANCE = 'performance',
  SYSTEM = 'system',
  DATABASE = 'database',
  API = 'api'
}

// Alert configuration
interface AlertConfig {
  enabled: boolean;
  channels: {
    email: boolean;
    webhook: boolean;
    sms: boolean;
  };
  recipients: {
    email: string[];
    webhook: string[];
    sms: string[];
  };
  throttling: {
    enabled: boolean;
    windowMinutes: number;
    maxAlertsPerWindow: number;
  };
}

// Alert event interface
interface AlertEvent {
  id: string;
  severity: AlertSeverity;
  category: AlertCategory;
  title: string;
  message: string;
  details: Record<string, any>;
  timestamp: Date;
  source: string;
  metadata?: Record<string, any>;
}

// Alert history for throttling
interface AlertHistory {
  [key: string]: {
    count: number;
    firstAlert: Date;
    lastAlert: Date;
  };
}

class AlertingService {
  private resend: Resend;
  private alertHistory: AlertHistory = {};
  private config: AlertConfig;

  constructor() {
    this.resend = new Resend(process.env.RESEND_API_KEY);
    this.config = this.loadConfiguration();
  }

  private loadConfiguration(): AlertConfig {
    return {
      enabled: process.env.ALERTING_ENABLED !== 'false',
      channels: {
        email: process.env.ALERT_EMAIL_ENABLED !== 'false',
        webhook: process.env.ALERT_WEBHOOK_ENABLED === 'true',
        sms: process.env.ALERT_SMS_ENABLED === 'true'
      },
      recipients: {
        email: process.env.ALERT_EMAIL_RECIPIENTS?.split(',') || ['<EMAIL>'],
        webhook: process.env.ALERT_WEBHOOK_URLS?.split(',') || [],
        sms: process.env.ALERT_SMS_RECIPIENTS?.split(',') || []
      },
      throttling: {
        enabled: process.env.ALERT_THROTTLING_ENABLED !== 'false',
        windowMinutes: parseInt(process.env.ALERT_THROTTLING_WINDOW || '60'),
        maxAlertsPerWindow: parseInt(process.env.ALERT_THROTTLING_MAX || '10')
      }
    };
  }

  /**
   * Send an alert for a critical event
   */
  async sendAlert(event: Omit<AlertEvent, 'id' | 'timestamp'>): Promise<void> {
    if (!this.config.enabled) {
      console.log('Alerting disabled, skipping alert:', event.title);
      return;
    }

    const alertEvent: AlertEvent = {
      ...event,
      id: this.generateAlertId(),
      timestamp: new Date()
    };

    // Check throttling
    if (this.isThrottled(alertEvent)) {
      console.log('Alert throttled:', alertEvent.title);
      return;
    }

    // Update alert history
    this.updateAlertHistory(alertEvent);

    // Send alerts through configured channels
    const promises: Promise<void>[] = [];

    if (this.config.channels.email) {
      promises.push(this.sendEmailAlert(alertEvent));
    }

    if (this.config.channels.webhook) {
      promises.push(this.sendWebhookAlert(alertEvent));
    }

    if (this.config.channels.sms) {
      promises.push(this.sendSMSAlert(alertEvent));
    }

    try {
      await Promise.allSettled(promises);
      console.log(`Alert sent successfully: ${alertEvent.title}`);
    } catch (error) {
      console.error('Failed to send alert:', error);
    }
  }

  /**
   * Payment-related alerts
   */
  async alertPaymentFailure(subscriptionId: string, schoolId: string, error: string): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.CRITICAL,
      category: AlertCategory.PAYMENT,
      title: 'Payment Processing Failure',
      message: `Payment failed for subscription ${subscriptionId}`,
      details: {
        subscriptionId,
        schoolId,
        error,
        action: 'immediate_investigation_required'
      },
      source: 'payment_processor'
    });
  }

  async alertPaymentMethodExpiring(schoolId: string, expiryDate: string): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.HIGH,
      category: AlertCategory.PAYMENT,
      title: 'Payment Method Expiring Soon',
      message: `Payment method for school ${schoolId} expires on ${expiryDate}`,
      details: {
        schoolId,
        expiryDate,
        action: 'notify_customer'
      },
      source: 'payment_monitor'
    });
  }

  async alertChargebackReceived(paymentId: string, amount: number): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.CRITICAL,
      category: AlertCategory.PAYMENT,
      title: 'Chargeback Received',
      message: `Chargeback received for payment ${paymentId} - Amount: ₹${amount}`,
      details: {
        paymentId,
        amount,
        action: 'dispute_management_required'
      },
      source: 'payment_processor'
    });
  }

  /**
   * Subscription-related alerts
   */
  async alertSubscriptionSuspended(subscriptionId: string, reason: string): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.HIGH,
      category: AlertCategory.SUBSCRIPTION,
      title: 'Subscription Suspended',
      message: `Subscription ${subscriptionId} has been suspended`,
      details: {
        subscriptionId,
        reason,
        action: 'customer_notification_sent'
      },
      source: 'subscription_manager'
    });
  }

  async alertHighChurnRate(churnRate: number, threshold: number): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.MEDIUM,
      category: AlertCategory.SUBSCRIPTION,
      title: 'High Customer Churn Rate Detected',
      message: `Churn rate (${churnRate}%) exceeds threshold (${threshold}%)`,
      details: {
        churnRate,
        threshold,
        action: 'retention_strategy_review'
      },
      source: 'analytics_engine'
    });
  }

  /**
   * Security-related alerts
   */
  async alertSecurityBreach(details: Record<string, any>): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.CRITICAL,
      category: AlertCategory.SECURITY,
      title: 'Security Breach Detected',
      message: 'Potential security breach detected in the system',
      details: {
        ...details,
        action: 'immediate_security_response_required'
      },
      source: 'security_monitor'
    });
  }

  async alertSuspiciousActivity(userId: string, activity: string, ipAddress: string): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.HIGH,
      category: AlertCategory.SECURITY,
      title: 'Suspicious Activity Detected',
      message: `Suspicious activity detected for user ${userId}`,
      details: {
        userId,
        activity,
        ipAddress,
        action: 'account_monitoring_enabled'
      },
      source: 'security_monitor'
    });
  }

  async alertRateLimitExceeded(endpoint: string, ipAddress: string, attempts: number): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.MEDIUM,
      category: AlertCategory.SECURITY,
      title: 'Rate Limit Exceeded',
      message: `Rate limit exceeded for endpoint ${endpoint}`,
      details: {
        endpoint,
        ipAddress,
        attempts,
        action: 'ip_temporarily_blocked'
      },
      source: 'rate_limiter'
    });
  }

  /**
   * Performance-related alerts
   */
  async alertHighResponseTime(endpoint: string, responseTime: number, threshold: number): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.MEDIUM,
      category: AlertCategory.PERFORMANCE,
      title: 'High API Response Time',
      message: `Endpoint ${endpoint} response time (${responseTime}ms) exceeds threshold (${threshold}ms)`,
      details: {
        endpoint,
        responseTime,
        threshold,
        action: 'performance_investigation_required'
      },
      source: 'performance_monitor'
    });
  }

  async alertHighErrorRate(endpoint: string, errorRate: number, threshold: number): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.HIGH,
      category: AlertCategory.PERFORMANCE,
      title: 'High API Error Rate',
      message: `Endpoint ${endpoint} error rate (${errorRate}%) exceeds threshold (${threshold}%)`,
      details: {
        endpoint,
        errorRate,
        threshold,
        action: 'immediate_investigation_required'
      },
      source: 'error_monitor'
    });
  }

  async alertResourceExhaustion(resource: string, usage: number, threshold: number): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.CRITICAL,
      category: AlertCategory.SYSTEM,
      title: 'Resource Exhaustion Warning',
      message: `${resource} usage (${usage}%) exceeds critical threshold (${threshold}%)`,
      details: {
        resource,
        usage,
        threshold,
        action: 'immediate_scaling_required'
      },
      source: 'system_monitor'
    });
  }

  /**
   * Database-related alerts
   */
  async alertDatabaseConnectionFailure(error: string): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.CRITICAL,
      category: AlertCategory.DATABASE,
      title: 'Database Connection Failure',
      message: 'Failed to connect to the database',
      details: {
        error,
        action: 'immediate_database_investigation_required'
      },
      source: 'database_monitor'
    });
  }

  async alertSlowQuery(query: string, duration: number, threshold: number): Promise<void> {
    await this.sendAlert({
      severity: AlertSeverity.MEDIUM,
      category: AlertCategory.DATABASE,
      title: 'Slow Database Query Detected',
      message: `Query execution time (${duration}ms) exceeds threshold (${threshold}ms)`,
      details: {
        query: query.substring(0, 200) + '...', // Truncate for readability
        duration,
        threshold,
        action: 'query_optimization_required'
      },
      source: 'database_monitor'
    });
  }

  /**
   * Private helper methods
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private isThrottled(event: AlertEvent): boolean {
    if (!this.config.throttling.enabled) return false;

    const key = `${event.category}_${event.severity}`;
    const now = new Date();
    const windowStart = new Date(now.getTime() - this.config.throttling.windowMinutes * 60 * 1000);

    const history = this.alertHistory[key];
    if (!history) return false;

    // Check if we're within the throttling window
    if (history.firstAlert > windowStart) {
      return history.count >= this.config.throttling.maxAlertsPerWindow;
    }

    return false;
  }

  private updateAlertHistory(event: AlertEvent): void {
    const key = `${event.category}_${event.severity}`;
    const now = new Date();
    const windowStart = new Date(now.getTime() - this.config.throttling.windowMinutes * 60 * 1000);

    if (!this.alertHistory[key] || this.alertHistory[key].firstAlert < windowStart) {
      // Start new window
      this.alertHistory[key] = {
        count: 1,
        firstAlert: now,
        lastAlert: now
      };
    } else {
      // Update existing window
      this.alertHistory[key].count++;
      this.alertHistory[key].lastAlert = now;
    }
  }

  private async sendEmailAlert(event: AlertEvent): Promise<void> {
    try {
      const subject = `[${event.severity.toUpperCase()}] ${event.title}`;
      const htmlContent = this.generateEmailHTML(event);

      await this.resend.emails.send({
        from: process.env.FROM_EMAIL || '<EMAIL>',
        to: this.config.recipients.email,
        subject,
        html: htmlContent
      });
    } catch (error) {
      console.error('Failed to send email alert:', error);
    }
  }

  private async sendWebhookAlert(event: AlertEvent): Promise<void> {
    const payload = {
      alert: event,
      timestamp: event.timestamp.toISOString(),
      environment: process.env.NODE_ENV || 'development'
    };

    const promises = this.config.recipients.webhook.map(async (url) => {
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Schopio-Alerting-Service/1.0'
          },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          throw new Error(`Webhook failed with status ${response.status}`);
        }
      } catch (error) {
        console.error(`Failed to send webhook alert to ${url}:`, error);
      }
    });

    await Promise.allSettled(promises);
  }

  private async sendSMSAlert(event: AlertEvent): Promise<void> {
    // SMS implementation would go here
    // This could integrate with services like Twilio, AWS SNS, etc.
    console.log('SMS alert (not implemented):', event.title);
  }

  private generateEmailHTML(event: AlertEvent): string {
    const severityColor = {
      [AlertSeverity.CRITICAL]: '#dc2626',
      [AlertSeverity.HIGH]: '#ea580c',
      [AlertSeverity.MEDIUM]: '#d97706',
      [AlertSeverity.LOW]: '#65a30d',
      [AlertSeverity.INFO]: '#2563eb'
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Schopio Alert</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: ${severityColor[event.severity]}; color: white; padding: 15px; border-radius: 5px 5px 0 0;">
            <h2 style="margin: 0;">[${event.severity.toUpperCase()}] ${event.title}</h2>
          </div>
          
          <div style="background: #f9f9f9; padding: 20px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 5px 5px;">
            <p><strong>Message:</strong> ${event.message}</p>
            <p><strong>Category:</strong> ${event.category}</p>
            <p><strong>Source:</strong> ${event.source}</p>
            <p><strong>Time:</strong> ${event.timestamp.toISOString()}</p>
            
            ${Object.keys(event.details).length > 0 ? `
              <h3>Details:</h3>
              <ul>
                ${Object.entries(event.details).map(([key, value]) => 
                  `<li><strong>${key}:</strong> ${value}</li>`
                ).join('')}
              </ul>
            ` : ''}
            
            <hr style="margin: 20px 0;">
            <p style="font-size: 12px; color: #666;">
              This is an automated alert from the Schopio monitoring system.<br>
              Alert ID: ${event.id}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

// Export singleton instance
export const alertingService = new AlertingService();

// Export types for use in other modules
export type { AlertEvent, AlertConfig };

// Monitoring middleware for API endpoints
export function createMonitoringMiddleware() {
  const requestTimes = new Map<string, number>();
  const errorCounts = new Map<string, number>();
  const requestCounts = new Map<string, number>();

  return async (c: any, next: any) => {
    const startTime = Date.now();
    const endpoint = `${c.req.method} ${c.req.path}`;

    // Track request count
    requestCounts.set(endpoint, (requestCounts.get(endpoint) || 0) + 1);

    try {
      await next();

      // Track response time
      const responseTime = Date.now() - startTime;
      requestTimes.set(endpoint, responseTime);

      // Alert on high response time
      if (responseTime > 5000) { // 5 seconds threshold
        await alertingService.alertHighResponseTime(endpoint, responseTime, 5000);
      }

    } catch (error) {
      // Track error count
      errorCounts.set(endpoint, (errorCounts.get(endpoint) || 0) + 1);

      // Calculate error rate
      const totalRequests = requestCounts.get(endpoint) || 1;
      const errors = errorCounts.get(endpoint) || 0;
      const errorRate = (errors / totalRequests) * 100;

      // Alert on high error rate
      if (errorRate > 10) { // 10% threshold
        await alertingService.alertHighErrorRate(endpoint, errorRate, 10);
      }

      throw error;
    }
  };
}

// System health monitoring
export class SystemHealthMonitor {
  private static instance: SystemHealthMonitor;
  private monitoringInterval: NodeJS.Timeout | null = null;

  static getInstance(): SystemHealthMonitor {
    if (!SystemHealthMonitor.instance) {
      SystemHealthMonitor.instance = new SystemHealthMonitor();
    }
    return SystemHealthMonitor.instance;
  }

  startMonitoring(): void {
    if (this.monitoringInterval) return;

    // Monitor every 5 minutes
    this.monitoringInterval = setInterval(async () => {
      await this.checkSystemHealth();
    }, 5 * 60 * 1000);

    console.log('System health monitoring started');
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('System health monitoring stopped');
    }
  }

  private async checkSystemHealth(): Promise<void> {
    try {
      // Check memory usage
      const memUsage = process.memoryUsage();
      const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;

      if (memUsagePercent > 85) {
        await alertingService.alertResourceExhaustion('Memory', memUsagePercent, 85);
      }

      // Check CPU usage (simplified)
      const cpuUsage = process.cpuUsage();
      // Note: This is a simplified check. In production, you'd want more sophisticated CPU monitoring

      // Check database connectivity
      await this.checkDatabaseHealth();

    } catch (error) {
      console.error('Health check failed:', error);
    }
  }

  private async checkDatabaseHealth(): Promise<void> {
    try {
      // This would typically use your database connection
      // For now, we'll simulate a database check
      const startTime = Date.now();

      // Simulate database query
      // In real implementation: await db.query('SELECT 1');

      const queryTime = Date.now() - startTime;

      if (queryTime > 1000) { // 1 second threshold
        await alertingService.alertSlowQuery('SELECT 1', queryTime, 1000);
      }

    } catch (error) {
      await alertingService.alertDatabaseConnectionFailure(error instanceof Error ? error.message : 'Unknown error');
    }
  }
}

// Initialize monitoring on module load
if (process.env.NODE_ENV === 'production') {
  SystemHealthMonitor.getInstance().startMonitoring();
}
