'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Clock, 
  TrendingUp, 
  Users, 
  CheckCircle, 
  ArrowRight, 
  Zap, 
  Target, 
  BarChart3,
  Timer,
  User<PERSON>heck,
  Award
} from 'lucide-react'

const SavingsEstimatorSection = () => {
  const [students, setStudents] = useState(500)
  const [adminStaff, setAdminStaff] = useState(8)
  const [showResults, setShowResults] = useState(false)

  const calculateSavings = () => {
    // Calculate time savings based on student count and admin staff
    const weeklyHoursSaved = Math.round((students * 0.15) + (adminStaff * 8))
    const adminTaskReduction = Math.min(Math.round(35 + (students / 50)), 75)
    const monthlyHoursSaved = weeklyHoursSaved * 4
    const yearlyHoursSaved = weeklyHoursSaved * 52
    const efficiencyImprovement = Math.min(Math.round(40 + (students / 100)), 85)
    const parentSatisfactionIncrease = Math.min(Math.round(25 + (students / 80)), 60)

    return {
      weeklyHoursSaved,
      adminTaskReduction,
      monthlyHoursSaved,
      yearlyHoursSaved,
      efficiencyImprovement,
      parentSatisfactionIncrease
    }
  }

  const handleCalculate = () => {
    setShowResults(true)
  }

  const results = calculateSavings()

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const benefits = [
    {
      icon: Clock,
      title: "Automated Administrative Tasks",
      description: "Eliminate manual data entry, report generation, and routine communications"
    },
    {
      icon: Users,
      title: "Streamlined Staff Workflows",
      description: "Reduce time spent on attendance, fee collection, and parent communications"
    },
    {
      icon: Target,
      title: "Improved Decision Making",
      description: "Real-time insights help administrators make faster, data-driven decisions"
    },
    {
      icon: UserCheck,
      title: "Enhanced Parent Engagement",
      description: "Automated updates and self-service portals reduce support requests"
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-emerald-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Timer className="w-4 h-4" />
            Efficiency Savings Calculator
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Discover Your School&apos;s
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Time Savings Potential</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            See how much time your administrative staff could save with automated school management processes.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Calculator */}
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            <Card className="bg-white border-0 shadow-xl">
              <CardHeader padding="lg">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-xl flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-slate-900">Savings Estimator</h3>
                    <p className="text-slate-600">Calculate your efficiency improvements</p>
                  </div>
                </div>
              </CardHeader>

              <CardContent padding="lg">
                <div className="space-y-6">
                  {/* Student Count Input */}
                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Current Student Count
                    </label>
                    <div className="relative">
                      <input
                        type="range"
                        min="100"
                        max="5000"
                        step="50"
                        value={students}
                        onChange={(e) => setStudents(Number(e.target.value))}
                        className="w-full h-3 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <div className="flex justify-between text-sm text-slate-500 mt-2">
                        <span>100</span>
                        <span className="font-bold text-blue-600">{students} students</span>
                        <span>5000+</span>
                      </div>
                    </div>
                  </div>

                  {/* Admin Staff Input */}
                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Administrative Staff Count
                    </label>
                    <div className="relative">
                      <input
                        type="range"
                        min="2"
                        max="50"
                        step="1"
                        value={adminStaff}
                        onChange={(e) => setAdminStaff(Number(e.target.value))}
                        className="w-full h-3 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <div className="flex justify-between text-sm text-slate-500 mt-2">
                        <span>2</span>
                        <span className="font-bold text-emerald-600">{adminStaff} staff members</span>
                        <span>50+</span>
                      </div>
                    </div>
                  </div>

                  {/* Calculate Button */}
                  <Button
                    onClick={handleCalculate}
                    size="lg"
                    icon={ArrowRight}
                    iconPosition="right"
                    className="w-full bg-gradient-to-r from-blue-600 to-emerald-600 hover:opacity-90 text-white font-bold py-4"
                  >
                    Calculate My Savings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Results */}
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
          >
            {showResults ? (
              <Card className="bg-gradient-to-br from-emerald-500 to-blue-600 border-0 shadow-xl text-white">
                <CardHeader padding="lg">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                      <Award className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold">Your Efficiency Gains</h3>
                      <p className="text-blue-100">Potential improvements for your school</p>
                    </div>
                  </div>
                </CardHeader>

                <CardContent padding="lg">
                  <div className="grid grid-cols-2 gap-6 mb-8">
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                      className="text-center"
                    >
                      <div className="text-4xl font-bold text-yellow-300 mb-2">
                        {results.weeklyHoursSaved}
                      </div>
                      <div className="text-blue-100 text-sm">Hours Saved Per Week</div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="text-center"
                    >
                      <div className="text-4xl font-bold text-yellow-300 mb-2">
                        {results.adminTaskReduction}%
                      </div>
                      <div className="text-blue-100 text-sm">Admin Task Reduction</div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      className="text-center"
                    >
                      <div className="text-4xl font-bold text-yellow-300 mb-2">
                        {results.efficiencyImprovement}%
                      </div>
                      <div className="text-blue-100 text-sm">Efficiency Improvement</div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                      className="text-center"
                    >
                      <div className="text-4xl font-bold text-yellow-300 mb-2">
                        {results.parentSatisfactionIncrease}%
                      </div>
                      <div className="text-blue-100 text-sm">Parent Satisfaction Boost</div>
                    </motion.div>
                  </div>

                  <div className="bg-white/10 rounded-lg p-4 mb-6">
                    <h4 className="font-bold text-lg mb-3">Annual Impact Summary</h4>
                    <div className="space-y-2 text-blue-100">
                      <div className="flex justify-between">
                        <span>Total Hours Saved Per Year:</span>
                        <span className="font-bold text-yellow-300">{results.yearlyHoursSaved.toLocaleString()} hours</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Equivalent to Full-Time Staff:</span>
                        <span className="font-bold text-yellow-300">{Math.round(results.yearlyHoursSaved / 2080)} people</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Administrative Efficiency Gain:</span>
                        <span className="font-bold text-yellow-300">{results.efficiencyImprovement}%</span>
                      </div>
                    </div>
                  </div>

                  <Button
                    size="lg"
                    icon={ArrowRight}
                    iconPosition="right"
                    className="w-full bg-white text-blue-600 hover:bg-blue-50 font-bold py-4"
                  >
                    Get Personalized Implementation Plan
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <Card className="bg-white border-0 shadow-lg">
                <CardContent padding="lg">
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gradient-to-r from-slate-200 to-blue-200 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <BarChart3 className="w-8 h-8 text-slate-500" />
                    </div>
                    <h3 className="text-xl font-bold text-slate-900 mb-2">Ready to See Your Results?</h3>
                    <p className="text-slate-600">
                      Enter your school details and calculate your potential efficiency savings.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </motion.div>
        </div>

        {/* Benefits Section */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="mt-16"
        >
          <h3 className="text-2xl font-bold text-slate-900 text-center mb-8">
            How We Help You Save Time
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="h-full bg-white border border-slate-200 shadow-md hover:shadow-lg transition-all duration-300">
                  <CardContent padding="lg">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-emerald-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <benefit.icon className="w-6 h-6 text-blue-600" />
                      </div>
                      <h4 className="font-bold text-slate-900 mb-2">{benefit.title}</h4>
                      <p className="text-sm text-slate-600">{benefit.description}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="text-center mt-16"
        >
          <Card className="bg-gradient-to-r from-slate-900 to-blue-900 border-0 shadow-xl max-w-3xl mx-auto">
            <CardContent padding="xl">
              <div className="text-white">
                <h3 className="text-2xl font-bold mb-4">
                  Ready to Transform Your School&apos;s Efficiency?
                </h3>
                <p className="text-blue-200 mb-6">
                  Get a personalized consultation to discuss how these time savings can be achieved at your school.
                </p>
                <Button
                  size="lg"
                  icon={ArrowRight}
                  iconPosition="right"
                  className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:opacity-90 text-white font-bold px-8 py-4"
                >
                  Schedule Your Efficiency Consultation
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default SavingsEstimatorSection
