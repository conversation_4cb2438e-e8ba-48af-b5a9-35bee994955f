'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Wallet,
  TrendingUp,
  Download,
  Calendar,
  Banknote,
  ArrowUpRight,
  ArrowDownRight,
  CreditCard,
  Building,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'

interface EarningsData {
  summary: {
    totalEarnings: string
    thisMonthEarnings: string
    lastMonthEarnings: string
    yearToDateEarnings: string
    availableForWithdrawal: string
    pendingWithdrawals: string
    growth: number
  }
  recentEarnings: Array<{
    id: string
    clientName: string
    amount: string
    month: string
    year: number
    createdAt: string
  }>
  withdrawalHistory: Array<{
    id: string
    amount: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    requestDate: string
    processedDate: string | null
    bankDetails: {
      accountNumber: string
      ifscCode: string
      accountHolderName: string
    }
    transactionId: string | null
    notes: string | null
  }>
}

export default function PartnerEarningsPage() {
  const [earningsData, setEarningsData] = useState<EarningsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showWithdrawalForm, setShowWithdrawalForm] = useState(false)
  const [withdrawalForm, setWithdrawalForm] = useState({
    amount: '',
    accountNumber: '',
    ifscCode: '',
    accountHolderName: ''
  })
  const [submittingWithdrawal, setSubmittingWithdrawal] = useState(false)
  const router = useRouter()

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem('partnerToken')
    if (!token) {
      router.push('/partner/login')
      return
    }

    fetchEarningsData()
  }, [router])

  const fetchEarningsData = async () => {
    try {
      const token = localStorage.getItem('partnerToken')
      const response = await fetch('/api/partner/earnings', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('partnerToken')
          localStorage.removeItem('partner')
          router.push('/partner/login')
          return
        }
        throw new Error('Failed to fetch earnings data')
      }

      const data = await response.json()
      setEarningsData(data.data)
    } catch (error) {
      console.error('Earnings error:', error)
      setError('Failed to load earnings data')
    } finally {
      setLoading(false)
    }
  }

  const handleWithdrawalSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmittingWithdrawal(true)
    setError('')

    try {
      const token = localStorage.getItem('partnerToken')
      const response = await fetch('/api/partner/earnings/withdraw', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount: parseFloat(withdrawalForm.amount),
          bankDetails: {
            accountNumber: withdrawalForm.accountNumber,
            ifscCode: withdrawalForm.ifscCode,
            accountHolderName: withdrawalForm.accountHolderName
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit withdrawal request')
      }

      // Reset form and refresh data
      setWithdrawalForm({
        amount: '',
        accountNumber: '',
        ifscCode: '',
        accountHolderName: ''
      })
      setShowWithdrawalForm(false)
      await fetchEarningsData()
    } catch (error: any) {
      setError(error.message)
    } finally {
      setSubmittingWithdrawal(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      pending: 'outline',
      processing: 'default',
      completed: 'secondary',
      failed: 'destructive'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'processing':
        return <Clock className="w-4 h-4 text-blue-500" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading earnings data...</p>
        </div>
      </div>
    )
  }

  if (error && !earningsData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchEarningsData}>Try Again</Button>
        </div>
      </div>
    )
  }

  if (!earningsData) return null

  const {
    summary,
    recentEarnings = [],
    withdrawalHistory = []
  } = earningsData

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Earnings & Withdrawals</h1>
          <p className="text-gray-600 mt-1">Track your earnings and manage withdrawal requests</p>
        </div>
        <Button 
          onClick={() => setShowWithdrawalForm(true)}
          disabled={parseFloat(summary.availableForWithdrawal) <= 0}
        >
          <Download className="w-4 h-4 mr-2" />
          Request Withdrawal
        </Button>
      </div>

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-700">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Enhanced Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Lifetime Earnings</p>
                <p className="text-3xl font-bold text-gray-900">₹{summary.totalEarnings}</p>
                <div className="flex items-center mt-2">
                  {summary.growth >= 0 ? (
                    <ArrowUpRight className="w-4 h-4 text-green-600" />
                  ) : (
                    <ArrowDownRight className="w-4 h-4 text-red-600" />
                  )}
                  <span className={`text-sm ml-1 ${summary.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.abs(summary.growth)}% this month
                  </span>
                </div>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">This Month</p>
                <p className="text-3xl font-bold text-gray-900">₹{summary.thisMonthEarnings}</p>
                <p className="text-sm text-gray-500 mt-2">
                  Last month: ₹{summary.lastMonthEarnings}
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  YTD: ₹{summary.yearToDateEarnings}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Calendar className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Available for Withdrawal</p>
                <p className="text-3xl font-bold text-green-600">₹{summary.availableForWithdrawal}</p>
                <p className="text-sm text-gray-500 mt-2">
                  Ready to withdraw
                </p>
                <Button
                  size="sm"
                  className="mt-2 h-7 text-xs"
                  onClick={() => setShowWithdrawalForm(true)}
                  disabled={parseFloat(summary.availableForWithdrawal) <= 0}
                >
                  Withdraw Now
                </Button>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Wallet className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Withdrawals</p>
                <p className="text-3xl font-bold text-yellow-600">₹{summary.pendingWithdrawals}</p>
                <p className="text-sm text-gray-500 mt-2">
                  Being processed
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  {withdrawalHistory.filter(w => w.status === 'pending' || w.status === 'processing').length} requests
                </p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Withdrawal Form Modal */}
      {showWithdrawalForm && (
        <Card className="border-emerald-200 bg-emerald-50">
          <CardHeader>
            <CardTitle>Request Withdrawal</CardTitle>
            <CardDescription>
              Submit a withdrawal request for your available earnings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleWithdrawalSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="amount">Withdrawal Amount (₹)</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    max={summary.availableForWithdrawal}
                    value={withdrawalForm.amount}
                    onChange={(e) => setWithdrawalForm(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder="Enter amount"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Maximum available: ₹{summary.availableForWithdrawal}
                  </p>
                </div>
                <div>
                  <Label htmlFor="accountHolderName">Account Holder Name</Label>
                  <Input
                    id="accountHolderName"
                    value={withdrawalForm.accountHolderName}
                    onChange={(e) => setWithdrawalForm(prev => ({ ...prev, accountHolderName: e.target.value }))}
                    placeholder="Full name as per bank account"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="accountNumber">Account Number</Label>
                  <Input
                    id="accountNumber"
                    value={withdrawalForm.accountNumber}
                    onChange={(e) => setWithdrawalForm(prev => ({ ...prev, accountNumber: e.target.value }))}
                    placeholder="Bank account number"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="ifscCode">IFSC Code</Label>
                  <Input
                    id="ifscCode"
                    value={withdrawalForm.ifscCode}
                    onChange={(e) => setWithdrawalForm(prev => ({ ...prev, ifscCode: e.target.value.toUpperCase() }))}
                    placeholder="IFSC code"
                    required
                  />
                </div>
              </div>
              <div className="flex gap-3">
                <Button type="submit" disabled={submittingWithdrawal}>
                  {submittingWithdrawal ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </div>
                  ) : (
                    'Submit Request'
                  )}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowWithdrawalForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Enhanced Recent Earnings */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Recent Earnings</CardTitle>
                <CardDescription>Your latest commission earnings with detailed breakdown</CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentEarnings && recentEarnings.length > 0 ? (
                recentEarnings.map((earning) => (
                  <div key={earning.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-green-100 rounded-full">
                          <Building className="w-4 h-4 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{earning.clientName}</p>
                          <p className="text-sm text-gray-600">
                            {earning.month} {earning.year}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-600 text-lg">₹{earning.amount}</p>
                        <p className="text-xs text-gray-500">
                          {new Date(earning.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    {/* Earnings Breakdown */}
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Commission Rate:</span>
                          <span className="ml-2 font-medium">25%</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Payment Status:</span>
                          <span className="ml-2">
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                              Paid
                            </Badge>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Banknote className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No recent earnings</p>
                  <p className="text-sm text-gray-400 mt-1">Earnings will appear here once schools make payments</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Withdrawal History */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Withdrawal History</CardTitle>
                <CardDescription>Track your withdrawal requests and payment status</CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {withdrawalHistory && withdrawalHistory.length > 0 ? (
                withdrawalHistory.map((withdrawal) => (
                  <div key={withdrawal.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${
                          withdrawal.status === 'completed' ? 'bg-green-100' :
                          withdrawal.status === 'processing' ? 'bg-blue-100' :
                          withdrawal.status === 'failed' ? 'bg-red-100' :
                          'bg-yellow-100'
                        }`}>
                          {getStatusIcon(withdrawal.status)}
                        </div>
                        <div>
                          <span className="font-semibold text-gray-900 text-lg">₹{withdrawal.amount}</span>
                          <p className="text-sm text-gray-600">
                            Request #{withdrawal.id.slice(-8)}
                          </p>
                        </div>
                      </div>
                      {getStatusBadge(withdrawal.status)}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="space-y-2">
                        <div>
                          <span className="text-gray-500">Account:</span>
                          <span className="ml-2 font-mono">{withdrawal.bankDetails.accountNumber.replace(/\d(?=\d{4})/g, '*')}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">IFSC:</span>
                          <span className="ml-2 font-mono">{withdrawal.bankDetails.ifscCode}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Account Holder:</span>
                          <span className="ml-2">{withdrawal.bankDetails.accountHolderName}</span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div>
                          <span className="text-gray-500">Requested:</span>
                          <span className="ml-2">{new Date(withdrawal.requestDate).toLocaleDateString()}</span>
                        </div>
                        {withdrawal.processedDate && (
                          <div>
                            <span className="text-gray-500">Processed:</span>
                            <span className="ml-2">{new Date(withdrawal.processedDate).toLocaleDateString()}</span>
                          </div>
                        )}
                        {withdrawal.transactionId && (
                          <div>
                            <span className="text-gray-500">Transaction ID:</span>
                            <span className="ml-2 font-mono text-xs">{withdrawal.transactionId}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {withdrawal.notes && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Note:</span> {withdrawal.notes}
                        </p>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No withdrawal history</p>
                  <p className="text-sm text-gray-400 mt-1">Your withdrawal requests will appear here</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
