-- Migration script for enhanced discount system fields
-- Add missing discount fields to billing_subscriptions table

-- Add discount_start_date field
ALTER TABLE billing_subscriptions 
ADD COLUMN IF NOT EXISTS discount_start_date DATE;

-- Add original_monthly_amount field to store pre-discount amount
ALTER TABLE billing_subscriptions 
ADD COLUMN IF NOT EXISTS original_monthly_amount DECIMAL(10,2);

-- Add discount_reason field for admin justification
ALTER TABLE billing_subscriptions 
ADD COLUMN IF NOT EXISTS discount_reason TEXT;

-- Add comments for documentation
COMMENT ON COLUMN billing_subscriptions.discount_start_date IS 'Date when the discount period begins';
COMMENT ON COLUMN billing_subscriptions.original_monthly_amount IS 'Original monthly amount before discount application';
COMMENT ON COLUMN billing_subscriptions.discount_reason IS 'Admin justification for applying the discount';

-- Create index for efficient discount queries
CREATE INDEX IF NOT EXISTS idx_billing_subscriptions_discount_dates 
ON billing_subscriptions(discount_start_date, discount_end_date) 
WHERE has_active_discount = true;

-- Create index for discount expiration queries
CREATE INDEX IF NOT EXISTS idx_billing_subscriptions_discount_expiration 
ON billing_subscriptions(discount_end_date) 
WHERE has_active_discount = true;

-- Verify the migration
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'billing_subscriptions' 
AND column_name IN ('discount_start_date', 'original_monthly_amount', 'discount_reason')
ORDER BY column_name;
