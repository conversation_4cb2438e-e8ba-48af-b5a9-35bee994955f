import { drizzle } from 'drizzle-orm/neon-http'
import { neon } from '@neondatabase/serverless'
import * as schema from './schema'

// Get database URL from environment variables
const databaseUrl = process.env.DATABASE_URL

if (!databaseUrl) {
  throw new Error('DATABASE_URL environment variable is required')
}

// Create Neon HTTP client
const sql = neon(databaseUrl)

// Create Drizzle database instance with schema
export const db = drizzle(sql, { schema })

// Export schema for use in other files
export * from './schema'

// Database connection test function
export async function testConnection() {
  try {
    const result = await sql`SELECT 1 as test`
    console.log('✅ Database connection successful:', result)
    return true
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    return false
  }
}

// Helper function to check if database is ready
export async function isDatabaseReady() {
  try {
    // Try to query a simple table to check if schema exists
    await sql`SELECT 1 FROM information_schema.tables WHERE table_name = 'leads' LIMIT 1`
    return true
  } catch (error) {
    console.log('Database schema not ready, may need migration')
    return false
  }
}
