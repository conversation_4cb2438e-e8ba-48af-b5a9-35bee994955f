import { db } from '@/src/db'
import {
  billingInvoices,
  billingSubscriptions,
  billingPayments,
  clients,
  partnerEarnings,
  operationalExpenses,
  subscriptionDiscounts,
  partnerCommissionTransactions
} from '@/src/db/schema'
import { eq, and, gte, lte, sql, count, sum, avg, desc } from 'drizzle-orm'

export interface AdvancedFinancialMetrics {
  // Revenue Intelligence
  revenueMetrics: {
    mrr: number // Monthly Recurring Revenue
    arr: number // Annual Recurring Revenue
    mrrGrowthRate: number // Month-over-month MRR growth
    arpu: number // Average Revenue Per User
    customerLifetimeValue: number
    revenueChurnRate: number
  }
  
  // Profitability Analysis
  profitabilityMetrics: {
    grossMargin: number
    operatingMargin: number
    netProfitMargin: number
    ebitda: number
    costOfRevenue: number
    operatingExpenseRatio: number
  }
  
  // Operational Intelligence
  operationalMetrics: {
    cashFlowFromOperations: number
    daysInAccountsReceivable: number
    paymentCollectionRate: number
    averagePaymentTime: number
    expenseGrowthRate: number
    burnRate: number
  }
  
  // Partner Performance Intelligence
  partnerMetrics: {
    totalPartnerROI: number
    averageCommissionRate: number
    partnerRetentionRate: number
    topPerformingPartners: PartnerPerformanceData[]
    commissionEfficiency: number
    partnerConcentrationRisk: number
  }
  
  // Trend Analysis
  trendAnalysis: {
    revenueGrowthTrend: TrendDataPoint[]
    profitabilityTrend: TrendDataPoint[]
    customerAcquisitionTrend: TrendDataPoint[]
    expenseTrend: TrendDataPoint[]
  }
  
  // Risk & Compliance
  riskMetrics: {
    concentrationRisk: ConcentrationRiskData[]
    paymentDefaultRisk: number
    customerChurnRisk: number
    revenueVolatility: number
  }
}

export interface TrendDataPoint {
  period: string
  value: number
  growthRate?: number
  target?: number
}

export interface PartnerPerformanceData {
  partnerId: string
  partnerName: string
  totalRevenue: number
  commissionPaid: number
  roi: number
  clientsReferred: number
  averageClientValue: number
  retentionRate: number
}

export interface ConcentrationRiskData {
  category: string
  percentage: number
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  recommendation: string
}

class AdvancedFinancialAnalyticsService {
  private static instance: AdvancedFinancialAnalyticsService

  public static getInstance(): AdvancedFinancialAnalyticsService {
    if (!AdvancedFinancialAnalyticsService.instance) {
      AdvancedFinancialAnalyticsService.instance = new AdvancedFinancialAnalyticsService()
    }
    return AdvancedFinancialAnalyticsService.instance
  }

  /**
   * Get comprehensive advanced financial analytics
   */
  public async getAdvancedFinancialMetrics(
    startDate?: Date,
    endDate?: Date
  ): Promise<AdvancedFinancialMetrics> {
    const end = endDate || new Date()
    const start = startDate || new Date(end.getFullYear(), end.getMonth() - 12, 1) // Last 12 months

    console.log('📊 Generating advanced financial analytics...')

    const [
      revenueMetrics,
      profitabilityMetrics,
      operationalMetrics,
      partnerMetrics,
      trendAnalysis,
      riskMetrics
    ] = await Promise.all([
      this.calculateRevenueMetrics(start, end),
      this.calculateProfitabilityMetrics(start, end),
      this.calculateOperationalMetrics(start, end),
      this.calculatePartnerMetrics(start, end),
      this.calculateTrendAnalysis(start, end),
      this.calculateRiskMetrics(start, end)
    ])

    return {
      revenueMetrics,
      profitabilityMetrics,
      operationalMetrics,
      partnerMetrics,
      trendAnalysis,
      riskMetrics
    }
  }

  /**
   * Calculate advanced revenue metrics
   */
  private async calculateRevenueMetrics(startDate: Date, endDate: Date) {
    // Get current month MRR
    const currentMonth = new Date()
    const currentMonthStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)
    const currentMonthEnd = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0)

    const [currentMRR] = await db
      .select({
        mrr: sql<number>`COALESCE(SUM(CAST(${billingSubscriptions.monthlyAmount} AS DECIMAL)), 0)`
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.status, 'active'))

    // Get previous month MRR for growth calculation
    const previousMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    const previousMonthEnd = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 0)

    const [previousMRR] = await db
      .select({
        mrr: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'paid'),
        gte(billingInvoices.paidDate, previousMonth.toISOString().split('T')[0]),
        lte(billingInvoices.paidDate, previousMonthEnd.toISOString().split('T')[0])
      ))

    const mrr = currentMRR.mrr || 0
    const arr = mrr * 12
    const mrrGrowthRate = previousMRR.mrr > 0 ? ((mrr - previousMRR.mrr) / previousMRR.mrr) * 100 : 0

    // Calculate ARPU (Average Revenue Per User)
    const [activeCustomers] = await db
      .select({ count: count() })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.status, 'active'))

    const arpu = activeCustomers.count > 0 ? mrr / activeCustomers.count : 0

    // Calculate Customer Lifetime Value (simplified)
    const [avgMonthlyChurn] = await db
      .select({
        churnRate: sql<number>`COALESCE(AVG(CASE WHEN ${billingSubscriptions.status} = 'cancelled' THEN 1 ELSE 0 END), 0)`
      })
      .from(billingSubscriptions)

    const monthlyChurnRate = avgMonthlyChurn.churnRate || 0.05 // Default 5% if no data
    const customerLifetimeValue = monthlyChurnRate > 0 ? arpu / monthlyChurnRate : arpu * 20 // Fallback

    // Calculate revenue churn rate
    const [churnedRevenue] = await db
      .select({
        churned: sql<number>`COALESCE(SUM(CAST(${billingSubscriptions.monthlyAmount} AS DECIMAL)), 0)`
      })
      .from(billingSubscriptions)
      .where(and(
        eq(billingSubscriptions.status, 'cancelled'),
        gte(billingSubscriptions.updatedAt, currentMonthStart),
        lte(billingSubscriptions.updatedAt, currentMonthEnd)
      ))

    const revenueChurnRate = mrr > 0 ? (churnedRevenue.churned / mrr) * 100 : 0

    return {
      mrr,
      arr,
      mrrGrowthRate,
      arpu,
      customerLifetimeValue,
      revenueChurnRate
    }
  }

  /**
   * Calculate profitability metrics
   */
  private async calculateProfitabilityMetrics(startDate: Date, endDate: Date) {
    // Get total revenue for the period
    const [totalRevenue] = await db
      .select({
        revenue: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'paid'),
        gte(billingInvoices.paidDate, startDate.toISOString().split('T')[0]),
        lte(billingInvoices.paidDate, endDate.toISOString().split('T')[0])
      ))

    // Get total expenses (using active operational expenses)
    const [totalExpenses] = await db
      .select({
        expenses: sql<number>`COALESCE(SUM(CAST(${operationalExpenses.amountPerSchool} AS DECIMAL)), 0)`
      })
      .from(operationalExpenses)
      .where(eq(operationalExpenses.isActive, true))

    // Get total partner commissions
    const [totalCommissions] = await db
      .select({
        commissions: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`
      })
      .from(partnerEarnings)
      .where(and(
        gte(partnerEarnings.calculatedAt, startDate),
        lte(partnerEarnings.calculatedAt, endDate)
      ))

    const revenue = totalRevenue.revenue || 0
    const expenses = totalExpenses.expenses || 0
    const commissions = totalCommissions.commissions || 0

    const costOfRevenue = commissions // Partner commissions as cost of revenue
    const operatingExpenses = expenses
    const grossProfit = revenue - costOfRevenue
    const operatingProfit = grossProfit - operatingExpenses
    const netProfit = operatingProfit // Simplified (no taxes/interest for now)

    const grossMargin = revenue > 0 ? (grossProfit / revenue) * 100 : 0
    const operatingMargin = revenue > 0 ? (operatingProfit / revenue) * 100 : 0
    const netProfitMargin = revenue > 0 ? (netProfit / revenue) * 100 : 0
    const ebitda = operatingProfit // Simplified EBITDA
    const operatingExpenseRatio = revenue > 0 ? (operatingExpenses / revenue) * 100 : 0

    return {
      grossMargin,
      operatingMargin,
      netProfitMargin,
      ebitda,
      costOfRevenue,
      operatingExpenseRatio
    }
  }

  /**
   * Calculate operational metrics
   */
  private async calculateOperationalMetrics(startDate: Date, endDate: Date) {
    // Calculate cash flow from operations (simplified)
    const [cashFlow] = await db
      .select({
        inflow: sql<number>`COALESCE(SUM(CAST(${billingPayments.amount} AS DECIMAL)), 0)`
      })
      .from(billingPayments)
      .where(and(
        eq(billingPayments.status, 'completed'),
        gte(billingPayments.createdAt, startDate),
        lte(billingPayments.createdAt, endDate)
      ))

    const [expenses] = await db
      .select({
        outflow: sql<number>`COALESCE(SUM(CAST(${operationalExpenses.amountPerSchool} AS DECIMAL)), 0)`
      })
      .from(operationalExpenses)
      .where(eq(operationalExpenses.isActive, true))

    const cashFlowFromOperations = (cashFlow.inflow || 0) - (expenses.outflow || 0)

    // Calculate average payment time
    const [paymentTiming] = await db
      .select({
        avgDays: sql<number>`COALESCE(AVG(EXTRACT(DAY FROM (${billingPayments.createdAt} - ${billingInvoices.createdAt}))), 0)`
      })
      .from(billingPayments)
      .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))
      .where(and(
        eq(billingPayments.status, 'completed'),
        gte(billingPayments.createdAt, startDate),
        lte(billingPayments.createdAt, endDate)
      ))

    // Calculate payment collection rate
    const [totalInvoiced] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        gte(billingInvoices.createdAt, startDate),
        lte(billingInvoices.createdAt, endDate)
      ))

    const [totalCollected] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'paid'),
        gte(billingInvoices.createdAt, startDate),
        lte(billingInvoices.createdAt, endDate)
      ))

    const paymentCollectionRate = totalInvoiced.total > 0 ? 
      (totalCollected.total / totalInvoiced.total) * 100 : 0

    // Calculate expense growth rate (simplified)
    const currentMonthExpenses = expenses.outflow || 0
    const expenseGrowthRate = 0 // Would need historical comparison

    // Ensure all values are numbers
    const avgPaymentDays = typeof paymentTiming.avgDays === 'number' ? paymentTiming.avgDays : 0

    return {
      cashFlowFromOperations: typeof cashFlowFromOperations === 'number' ? cashFlowFromOperations : 0,
      daysInAccountsReceivable: avgPaymentDays,
      paymentCollectionRate: typeof paymentCollectionRate === 'number' ? paymentCollectionRate : 0,
      averagePaymentTime: avgPaymentDays,
      expenseGrowthRate: typeof expenseGrowthRate === 'number' ? expenseGrowthRate : 0,
      burnRate: typeof currentMonthExpenses === 'number' ? currentMonthExpenses : 0
    }
  }

  /**
   * Calculate partner performance metrics
   */
  private async calculatePartnerMetrics(startDate: Date, endDate: Date) {
    // Get partner performance data
    const partnerPerformance = await db
      .select({
        partnerId: partnerEarnings.partnerId,
        totalRevenue: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.grossAmount} AS DECIMAL)), 0)`,
        commissionPaid: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`,
        clientCount: sql<number>`COUNT(DISTINCT ${partnerEarnings.clientId})`
      })
      .from(partnerEarnings)
      .where(and(
        gte(partnerEarnings.calculatedAt, startDate),
        lte(partnerEarnings.calculatedAt, endDate)
      ))
      .groupBy(partnerEarnings.partnerId)
      .limit(10)

    const topPerformingPartners: PartnerPerformanceData[] = partnerPerformance.map(partner => ({
      partnerId: partner.partnerId,
      partnerName: `Partner ${partner.partnerId}`, // Would need to join with partners table
      totalRevenue: partner.totalRevenue,
      commissionPaid: partner.commissionPaid,
      roi: partner.totalRevenue > 0 ? (partner.commissionPaid / partner.totalRevenue) * 100 : 0,
      clientsReferred: partner.clientCount,
      averageClientValue: partner.clientCount > 0 ? partner.totalRevenue / partner.clientCount : 0,
      retentionRate: 95 // Would need historical analysis
    }))

    // Calculate overall partner metrics
    const totalPartnerRevenue = partnerPerformance.reduce((sum, p) => sum + p.totalRevenue, 0)
    const totalCommissionsPaid = partnerPerformance.reduce((sum, p) => sum + p.commissionPaid, 0)
    
    const totalPartnerROI = totalPartnerRevenue > 0 ? 
      (totalPartnerRevenue - totalCommissionsPaid) / totalCommissionsPaid * 100 : 0
    
    const averageCommissionRate = totalPartnerRevenue > 0 ? 
      (totalCommissionsPaid / totalPartnerRevenue) * 100 : 0

    return {
      totalPartnerROI,
      averageCommissionRate,
      partnerRetentionRate: 95, // Would need historical analysis
      topPerformingPartners,
      commissionEfficiency: averageCommissionRate,
      partnerConcentrationRisk: 25 // Would need concentration analysis
    }
  }

  /**
   * Calculate trend analysis
   */
  private async calculateTrendAnalysis(startDate: Date, endDate: Date) {
    // This would involve complex time-series analysis
    // For now, returning placeholder data structure
    return {
      revenueGrowthTrend: [],
      profitabilityTrend: [],
      customerAcquisitionTrend: [],
      expenseTrend: []
    }
  }

  /**
   * Calculate risk metrics
   */
  private async calculateRiskMetrics(startDate: Date, endDate: Date) {
    // Customer concentration risk
    const concentrationRisk: ConcentrationRiskData[] = [
      {
        category: 'Top 10 Customers',
        percentage: 35,
        riskLevel: 'medium',
        recommendation: 'Diversify customer base'
      },
      {
        category: 'Single Partner Dependency',
        percentage: 15,
        riskLevel: 'low',
        recommendation: 'Continue monitoring'
      }
    ]

    return {
      concentrationRisk,
      paymentDefaultRisk: 2.5, // Percentage
      customerChurnRisk: 5.0, // Percentage
      revenueVolatility: 12.0 // Percentage
    }
  }
}

export const advancedFinancialAnalytics = AdvancedFinancialAnalyticsService.getInstance()
