/**
 * Standardized API Response Utilities for Schopio Platform
 * Provides consistent response formats across all API endpoints
 */

import { Context } from 'hono'

// Standard error codes
export const ERROR_CODES = {
  // Authentication & Authorization
  UNAUTHORIZED: 'UNAUTHORIZED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  FORBIDDEN: 'FORBIDDEN',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Resource Management
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  CONFLICT: 'CONFLICT',
  
  // Business Logic
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  SUBSCRIPTION_INACTIVE: 'SUBSCRIPTION_INACTIVE',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  BILLING_ERROR: 'BILLING_ERROR',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // System Errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR'
} as const

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

// Standard response interfaces
export interface APISuccessResponse<T = any> {
  success: true
  data: T
  message?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  timestamp: string
}

export interface APIErrorResponse {
  success: false
  error: {
    code: ErrorCode
    message: string
    details?: any
    field?: string // For validation errors
  }
  timestamp: string
  path: string
  requestId?: string
}

// Pagination interface
export interface PaginationParams {
  page: number
  limit: number
  total: number
}

/**
 * Create standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  pagination?: PaginationParams
): APISuccessResponse<T> {
  const response: APISuccessResponse<T> = {
    success: true,
    data,
    timestamp: new Date().toISOString()
  }

  if (message) {
    response.message = message
  }

  if (pagination) {
    response.pagination = {
      ...pagination,
      totalPages: Math.ceil(pagination.total / pagination.limit)
    }
  }

  return response
}

/**
 * Create standardized error response
 */
export function createErrorResponse(
  code: ErrorCode,
  message: string,
  path: string,
  details?: any,
  field?: string,
  requestId?: string
): APIErrorResponse {
  return {
    success: false,
    error: {
      code,
      message,
      ...(details && { details }),
      ...(field && { field })
    },
    timestamp: new Date().toISOString(),
    path,
    ...(requestId && { requestId })
  }
}

/**
 * Send success response with proper status code
 */
export function sendSuccess<T>(
  c: Context,
  data: T,
  message?: string,
  pagination?: PaginationParams,
  statusCode: number = 200
) {
  const response = createSuccessResponse(data, message, pagination)
  return c.json(response, statusCode as any)
}

/**
 * Send error response with proper status code
 */
export function sendError(
  c: Context,
  code: ErrorCode,
  message: string,
  statusCode: number = 400,
  details?: any,
  field?: string
) {
  const requestId = c.get('requestId') || crypto.randomUUID()
  const response = createErrorResponse(
    code,
    message,
    c.req.path,
    details,
    field,
    requestId
  )
  return c.json(response, statusCode as any)
}

/**
 * Common error response helpers
 */
export const ErrorResponses = {
  // 400 Bad Request
  validationError: (c: Context, message: string, details?: any, field?: string) =>
    sendError(c, ERROR_CODES.VALIDATION_ERROR, message, 400, details, field),
  
  invalidInput: (c: Context, message: string = 'Invalid input provided') =>
    sendError(c, ERROR_CODES.INVALID_INPUT, message, 400),
  
  missingField: (c: Context, field: string) =>
    sendError(c, ERROR_CODES.MISSING_REQUIRED_FIELD, `Missing required field: ${field}`, 400, undefined, field),

  // 401 Unauthorized
  unauthorized: (c: Context, message: string = 'Authorization token required') =>
    sendError(c, ERROR_CODES.UNAUTHORIZED, message, 401),
  
  invalidToken: (c: Context, message: string = 'Invalid or expired token') =>
    sendError(c, ERROR_CODES.INVALID_TOKEN, message, 401),
  
  tokenExpired: (c: Context, message: string = 'Token has expired') =>
    sendError(c, ERROR_CODES.TOKEN_EXPIRED, message, 401),

  // 403 Forbidden
  forbidden: (c: Context, message: string = 'Access denied') =>
    sendError(c, ERROR_CODES.FORBIDDEN, message, 403),
  
  insufficientPermissions: (c: Context, message: string = 'Insufficient permissions') =>
    sendError(c, ERROR_CODES.INSUFFICIENT_PERMISSIONS, message, 403),

  // 404 Not Found
  notFound: (c: Context, resource: string = 'Resource') =>
    sendError(c, ERROR_CODES.NOT_FOUND, `${resource} not found`, 404),

  // 409 Conflict
  alreadyExists: (c: Context, resource: string = 'Resource') =>
    sendError(c, ERROR_CODES.ALREADY_EXISTS, `${resource} already exists`, 409),
  
  conflict: (c: Context, message: string) =>
    sendError(c, ERROR_CODES.CONFLICT, message, 409),

  // 429 Too Many Requests
  rateLimitExceeded: (c: Context, retryAfter?: number) =>
    sendError(c, ERROR_CODES.RATE_LIMIT_EXCEEDED, 'Too many requests. Please try again later.', 429, 
      retryAfter ? { retryAfter } : undefined),

  // 500 Internal Server Error
  internalError: (c: Context, message: string = 'Internal server error') =>
    sendError(c, ERROR_CODES.INTERNAL_ERROR, message, 500),
  
  databaseError: (c: Context, message: string = 'Database operation failed') =>
    sendError(c, ERROR_CODES.DATABASE_ERROR, message, 500),
  
  serviceUnavailable: (c: Context, service: string = 'Service') =>
    sendError(c, ERROR_CODES.SERVICE_UNAVAILABLE, `${service} is currently unavailable`, 503),

  // Business Logic Errors
  subscriptionInactive: (c: Context, message: string = 'Subscription is not active') =>
    sendError(c, ERROR_CODES.SUBSCRIPTION_INACTIVE, message, 400),
  
  paymentFailed: (c: Context, message: string = 'Payment processing failed') =>
    sendError(c, ERROR_CODES.PAYMENT_FAILED, message, 400),
  
  billingError: (c: Context, message: string = 'Billing operation failed') =>
    sendError(c, ERROR_CODES.BILLING_ERROR, message, 400),
  
  insufficientBalance: (c: Context, message: string = 'Insufficient balance') =>
    sendError(c, ERROR_CODES.INSUFFICIENT_BALANCE, message, 400)
}

/**
 * Success response helpers
 */
export const SuccessResponses = {
  // 200 OK
  ok: <T>(c: Context, data: T, message?: string, pagination?: PaginationParams) =>
    sendSuccess(c, data, message, pagination, 200),

  // 201 Created
  created: <T>(c: Context, data: T, message?: string) =>
    sendSuccess(c, data, message, undefined, 201),

  // 202 Accepted
  accepted: <T>(c: Context, data: T, message?: string) =>
    sendSuccess(c, data, message, undefined, 202),

  // 204 No Content (for updates/deletes)
  noContent: (c: Context) =>
    c.json(null, 204 as any)
}

/**
 * Validation error formatter for Zod errors
 */
export function formatZodError(error: any): { message: string; details: any } {
  if (error.errors && Array.isArray(error.errors)) {
    const firstError = error.errors[0]
    return {
      message: firstError.message || 'Validation failed',
      details: error.errors.map((err: any) => ({
        field: err.path?.join('.') || 'unknown',
        message: err.message,
        code: err.code
      }))
    }
  }
  
  return {
    message: 'Validation failed',
    details: error
  }
}

/**
 * Database error handler
 */
export function handleDatabaseError(c: Context, error: any) {
  console.error('Database error:', error)
  
  // Handle specific database errors
  if (error.code === '23505') { // Unique constraint violation
    return ErrorResponses.alreadyExists(c, 'Record already exists')
  }
  
  if (error.code === '23503') { // Foreign key constraint violation
    return ErrorResponses.validationError(c, 'Referenced record does not exist')
  }
  
  if (error.code === '23502') { // Not null constraint violation
    return ErrorResponses.validationError(c, 'Required field is missing')
  }
  
  // Generic database error
  return ErrorResponses.databaseError(c)
}

/**
 * Async error handler wrapper
 */
export function asyncHandler(fn: Function) {
  return async (c: Context, next?: any) => {
    try {
      return await fn(c, next)
    } catch (error: any) {
      console.error('Async handler error:', error)

      // Handle known error types
      if (error?.name === 'ZodError') {
        const { message, details } = formatZodError(error)
        return ErrorResponses.validationError(c, message, details)
      }

      // Handle database errors
      if (error?.code && typeof error.code === 'string') {
        return handleDatabaseError(c, error)
      }

      // Generic error
      return ErrorResponses.internalError(c)
    }
  }
}
