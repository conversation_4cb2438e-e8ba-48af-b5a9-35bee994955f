'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  ArrowLeft,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { FileUpload, type UploadedFile } from '@/components/ui/FileUpload'
import Link from 'next/link'
import { useRouter } from 'next/navigation'



export default function CreateTicketPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [authenticated, setAuthenticated] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
    category: 'general' as 'billing' | 'technical' | 'feature_request' | 'bug' | 'general'
  })
  const [attachments, setAttachments] = useState<UploadedFile[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Check authentication on page load
  useEffect(() => {
    const token = localStorage.getItem('schoolToken')
    const userData = localStorage.getItem('user')

    console.log('Debug - Token exists:', !!token)
    console.log('Debug - User data exists:', !!userData)

    if (!token || !userData) {
      console.log('Debug - Redirecting to auth due to missing token or user data')
      router.push('/auth')
      return
    }

    try {
      const user = JSON.parse(userData)
      console.log('Debug - User data:', user)
      setAuthenticated(true)
    } catch (error) {
      console.error('Debug - Error parsing user data:', error)
      router.push('/auth')
    }
  }, [router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleFilesChange = (files: UploadedFile[]) => {
    setAttachments(files)
    // Clear any previous file-related errors
    if (errors.attachments) {
      setErrors(prev => ({ ...prev, attachments: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    } else if (formData.title.length < 5) {
      newErrors.title = 'Title must be at least 5 characters'
    } else if (formData.title.length > 255) {
      newErrors.title = 'Title must be less than 255 characters'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required'
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const uploadAttachments = async (): Promise<any[]> => {
    if (attachments.length === 0) return []

    // In a real implementation, you would upload files to a storage service
    // For now, we'll simulate this by creating file URLs
    return attachments.map(attachment => ({
      fileName: attachment.fileName,
      fileUrl: `https://example.com/uploads/${attachment.fileName}`, // Placeholder URL
      fileSize: attachment.fileSize,
      fileType: attachment.fileType
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    setErrors({})

    try {
      const token = localStorage.getItem('schoolToken')
      console.log('Debug - Token from localStorage:', token ? 'EXISTS' : 'NULL')

      if (!token) {
        console.error('Debug - No schoolToken found in localStorage')
        console.log('Debug - All localStorage keys:', Object.keys(localStorage))
        throw new Error('No authentication token found. Please log in again.')
      }

      // Upload attachments first
      const uploadedAttachments = await uploadAttachments()

      const response = await fetch('/api/school/support/tickets', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          attachments: uploadedAttachments
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create support ticket')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to create support ticket')
      }

      // Redirect to the created ticket
      router.push(`/profile/support/${result.data.ticket.id}`)
    } catch (error) {
      console.error('Error creating support ticket:', error)
      setErrors({ submit: error instanceof Error ? error.message : 'Failed to create support ticket' })
    } finally {
      setLoading(false)
    }
  }

  // Show loading while checking authentication
  if (!authenticated) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Checking authentication...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Link href="/profile/support">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Tickets
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create Support Ticket</h1>
          <p className="text-gray-600">Describe your issue and our support team will help you</p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Ticket Information</CardTitle>
              <CardDescription>
                Provide a clear title and detailed description of your issue
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Brief description of your issue"
                  className={errors.title ? 'border-red-500' : ''}
                />
                {errors.title && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.title}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Provide detailed information about your issue, including steps to reproduce if applicable"
                  rows={6}
                  className={errors.description ? 'border-red-500' : ''}
                />
                {errors.description && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.description}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Priority and Category */}
          <Card>
            <CardHeader>
              <CardTitle>Classification</CardTitle>
              <CardDescription>
                Help us prioritize and route your ticket appropriately
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="priority">Priority</Label>
                  <select
                    id="priority"
                    value={formData.priority}
                    onChange={(e) => handleInputChange('priority', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="low">Low - General inquiry</option>
                    <option value="medium">Medium - Standard issue</option>
                    <option value="high">High - Important issue</option>
                    <option value="urgent">Urgent - Critical issue</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="category">Category</Label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="general">General Support</option>
                    <option value="billing">Billing & Payments</option>
                    <option value="technical">Technical Issue</option>
                    <option value="feature_request">Feature Request</option>
                    <option value="bug">Bug Report</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* File Attachments */}
          <Card>
            <CardHeader>
              <CardTitle>Attachments</CardTitle>
              <CardDescription>
                Upload screenshots, documents, or other files to help explain your issue
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileUpload
                onFilesChange={handleFilesChange}
                maxFiles={5}
                maxSize={10 * 1024 * 1024} // 10MB
                multiple={true}
                showPreview={true}
              />
              {errors.attachments && (
                <p className="text-sm text-red-600 mt-2 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.attachments}
                </p>
              )}
            </CardContent>
          </Card>

          {/* Submit */}
          <div className="flex justify-end gap-4">
            <Link href="/profile/support">
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={loading} className="bg-blue-600 hover:bg-blue-700">
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Create Ticket
                </>
              )}
            </Button>
          </div>

          {errors.submit && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.submit}
              </p>
            </div>
          )}
        </div>
      </form>
    </div>
  )
}
