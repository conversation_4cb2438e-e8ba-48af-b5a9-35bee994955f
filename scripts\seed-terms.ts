import { config } from 'dotenv'
import { resolve } from 'path'

// Load environment variables from .env.local
config({ path:'.env.local'})

import { db } from '../src/db'
import { termsConditions } from '../src/db/schema'

const defaultTermsContent = `
# SCHOPIO SCHOOL MANAGEMENT SYSTEM
## TERMS AND CONDITIONS OF SERVICE

**Effective Date:** ${new Date().toLocaleDateString('en-IN')}
**Version:** 1.0
**Last Updated:** ${new Date().toLocaleDateString('en-IN')}

---

## 1. ACCEPTANCE OF TERMS

By accessing, registering for, or using the Schopio School Management System ("Service"), you ("Customer", "School", "User") agree to be bound by these Terms and Conditions ("Terms"). If you are entering into this agreement on behalf of a school or educational institution, you represent that you have the authority to bind such entity to these Terms.

**IF YOU DO NOT AGREE TO THESE TERMS, DO NOT USE THE SERVICE.**

---

## 2. DEFINITIONS

- **"Service"** means the Schopio School Management System software platform and related services
- **"Customer"** means the educational institution or school using the Service
- **"Data"** means all information, content, and data submitted to or generated by the Service
- **"Subscription"** means the paid access to the Service under these Terms
- **"Force Majeure"** means circumstances beyond reasonable control including natural disasters, government actions, internet failures, or cyber attacks

---

## 3. SERVICE DESCRIPTION

Schopio provides a cloud-based school management system including but not limited to:
- Student information management
- Fee collection and billing
- Academic record keeping
- Communication tools
- Reporting and analytics
- Administrative functions

The Service is provided on a Software-as-a-Service (SaaS) basis.

---

## 4. SUBSCRIPTION AND BILLING TERMS

### 4.1 Subscription Plans
- **Pricing:** Per-student monthly billing as specified in your subscription agreement
- **Minimum Billing:** 50 students or actual student count, whichever is higher
- **Billing Cycle:** Monthly on the same date each month
- **Currency:** Indian Rupees (₹)

### 4.2 Payment Terms
- Payment is due on the billing date specified in your account
- **Grace Period:** 3 calendar days after due date without penalty
- **Late Payment Penalty:** 2% per day after grace period expires
- **Suspension:** Service may be suspended after 15 days of non-payment
- **Termination:** Account may be terminated after 30 days of non-payment
- **Reactivation Fee:** ₹500 if service is suspended due to non-payment

### 4.3 Advance Payments
- Multi-month advance payments are accepted
- Advance payments are non-refundable except as required by law
- Unused advance payments will be applied to future billing cycles

### 4.4 Price Changes
- We reserve the right to modify pricing with 30 days written notice
- Price changes will not affect your current billing cycle
- Continued use after notice constitutes acceptance of new pricing

---

## 5. DATA PROTECTION AND PRIVACY

### 5.1 Data Security
- We implement industry-standard security measures to protect your data
- Data is encrypted in transit and at rest
- Regular security audits and monitoring are performed
- Access controls and authentication mechanisms are maintained

### 5.2 Data Ownership
- You retain ownership of all data you submit to the Service
- We do not claim ownership of your educational or student data
- You grant us a limited license to process data solely to provide the Service

### 5.3 Data Backup and Retention
- Regular automated backups are performed
- During active subscription: Continuous data protection
- During suspension: Data retained for 30 days
- After termination: Data retained for 90 days for recovery purposes
- Data export available upon request during retention period

### 5.4 Compliance
- Service complies with applicable Indian data protection laws
- We maintain appropriate privacy policies and procedures
- Student data is handled in accordance with educational privacy standards

---

## 6. ACCEPTABLE USE POLICY

### 6.1 Permitted Use
- Use the Service only for legitimate educational and administrative purposes
- Comply with all applicable laws and regulations
- Maintain the security of your account credentials
- Use the Service in accordance with its intended functionality

### 6.2 Prohibited Activities
- Attempting to gain unauthorized access to the Service or other accounts
- Interfering with or disrupting the Service or servers
- Using the Service for illegal activities or to violate third-party rights
- Reverse engineering, decompiling, or attempting to extract source code
- Sharing account credentials with unauthorized users
- Uploading malicious software or harmful content

---

## 7. SERVICE AVAILABILITY AND SUPPORT

### 7.1 Uptime Commitment
- We target 99.5% uptime availability
- Scheduled maintenance will be performed during off-peak hours when possible
- Advance notice will be provided for planned maintenance

### 7.2 Support Services
- Technical support available during business hours (9 AM - 6 PM IST)
- Email support for non-urgent issues
- Training and onboarding assistance for new customers
- Documentation and user guides provided

### 7.3 Service Limitations
- Service availability may be affected by internet connectivity issues
- Performance may vary based on usage patterns and data volume
- Some features may require specific browser versions or configurations

---

## 8. INTELLECTUAL PROPERTY RIGHTS

### 8.1 Our Rights
- We retain all rights, title, and interest in the Service and its technology
- Our trademarks, logos, and branding remain our exclusive property
- The Service is protected by copyright, trademark, and other intellectual property laws

### 8.2 Your Rights
- You retain ownership of your data and content
- You may use the Service in accordance with these Terms
- No rights are granted beyond those explicitly stated in these Terms

---

## 9. WARRANTY DISCLAIMERS

### 9.1 Service Provided "AS IS"
THE SERVICE IS PROVIDED ON AN "AS IS" AND "AS AVAILABLE" BASIS. WE MAKE NO WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO:
- MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE
- UNINTERRUPTED OR ERROR-FREE OPERATION
- ACCURACY OR RELIABILITY OF DATA OR RESULTS
- SECURITY OR FREEDOM FROM VIRUSES OR HARMFUL COMPONENTS

### 9.2 No Guarantee of Results
- We do not guarantee specific outcomes from using the Service
- Educational results depend on many factors beyond our control
- The Service is a tool to assist with school management, not a guarantee of success

---

## 10. LIMITATION OF LIABILITY

### 10.1 Liability Limits
TO THE MAXIMUM EXTENT PERMITTED BY LAW, OUR TOTAL LIABILITY FOR ANY CLAIMS ARISING FROM OR RELATED TO THE SERVICE SHALL NOT EXCEED THE AMOUNT PAID BY YOU FOR THE SERVICE IN THE 12 MONTHS PRECEDING THE CLAIM.

### 10.2 Excluded Damages
WE SHALL NOT BE LIABLE FOR:
- INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES
- LOSS OF PROFITS, REVENUE, DATA, OR USE
- BUSINESS INTERRUPTION OR LOST OPPORTUNITIES
- DAMAGES RESULTING FROM THIRD-PARTY ACTIONS
- DAMAGES BEYOND OUR REASONABLE CONTROL

### 10.3 Essential Terms
YOU ACKNOWLEDGE THAT THESE LIMITATIONS ARE ESSENTIAL TERMS AND THAT WE WOULD NOT PROVIDE THE SERVICE WITHOUT THESE LIMITATIONS.

---

## 11. INDEMNIFICATION

You agree to indemnify, defend, and hold harmless Schopio, its officers, directors, employees, and agents from any claims, damages, losses, or expenses (including reasonable attorney fees) arising from:
- Your use of the Service
- Your violation of these Terms
- Your violation of applicable laws or third-party rights
- Your data or content submitted to the Service

---

## 12. TERMINATION

### 12.1 Termination by You
- You may terminate your subscription with 30 days written notice
- No refunds for partial billing periods
- Data export available for 30 days after termination

### 12.2 Termination by Us
We may terminate your access immediately for:
- Material breach of these Terms
- Non-payment after applicable grace periods
- Illegal use of the Service
- Violation of acceptable use policies

### 12.3 Effect of Termination
- Access to the Service will cease
- Data will be retained according to our retention policy
- Outstanding fees remain due and payable
- Provisions that should survive termination will remain in effect

---

## 13. FORCE MAJEURE

We shall not be liable for any failure or delay in performance due to circumstances beyond our reasonable control, including but not limited to:
- Natural disasters, pandemics, or acts of God
- Government actions, laws, or regulations
- Internet or telecommunications failures
- Cyber attacks or security breaches affecting infrastructure
- Labor disputes or supplier failures

---

## 14. GOVERNING LAW AND DISPUTE RESOLUTION

### 14.1 Governing Law
These Terms shall be governed by and construed in accordance with the laws of India, without regard to conflict of law principles.

### 14.2 Jurisdiction
Any disputes arising from these Terms shall be subject to the exclusive jurisdiction of the courts in [Your City], India.

### 14.3 Dispute Resolution
- Initial disputes should be addressed through good faith negotiations
- Mediation may be pursued before formal legal proceedings
- Each party bears its own costs unless otherwise determined by a court

---

## 15. MODIFICATIONS TO TERMS

### 15.1 Right to Modify
We reserve the right to modify these Terms at any time with 30 days advance notice.

### 15.2 Notice of Changes
- Notice will be provided via email to your registered address
- Updated Terms will be posted on our website
- Continued use after the effective date constitutes acceptance

### 15.3 Material Changes
For material changes that significantly affect your rights, we may require explicit acceptance.

---

## 16. GENERAL PROVISIONS

### 16.1 Entire Agreement
These Terms constitute the entire agreement between you and Schopio regarding the Service.

### 16.2 Severability
If any provision is found unenforceable, the remainder of these Terms shall remain in effect.

### 16.3 Waiver
Failure to enforce any provision does not constitute a waiver of that provision.

### 16.4 Assignment
You may not assign these Terms without our written consent. We may assign these Terms without restriction.

### 16.5 Survival
Provisions that should reasonably survive termination will continue in effect.

---

## 17. CONTACT INFORMATION

For questions about these Terms or the Service, contact us at:

**Schopio Support Team**
Email: <EMAIL>
Phone: +91-XXXX-XXXXXX
Address: [Your Business Address]

---

**BY CLICKING "I ACCEPT" OR USING THE SERVICE, YOU ACKNOWLEDGE THAT YOU HAVE READ, UNDERSTOOD, AND AGREE TO BE BOUND BY THESE TERMS AND CONDITIONS.**

*This document was last updated on ${new Date().toLocaleDateString('en-IN')} and is effective immediately.*
`

async function seedTerms() {
  try {
    console.log('Seeding default terms & conditions...')
    
    // Check if terms already exist
    const existingTerms = await db.select().from(termsConditions).limit(1)
    
    if (existingTerms.length > 0) {
      console.log('Terms & conditions already exist, skipping seed.')
      return
    }
    
    // Insert default terms
    await db.insert(termsConditions).values({
      version: 'v1.0',
      title: 'Schopio School Management System - Terms & Conditions',
      content: defaultTermsContent.trim(),
      effectiveDate: new Date().toISOString().split('T')[0], // Today's date
      isActive: true
    })
    
    console.log('✅ Default terms & conditions seeded successfully!')
    
  } catch (error) {
    console.error('❌ Error seeding terms & conditions:', error)
  }
}

// Run if called directly
if (require.main === module) {
  seedTerms().then(() => process.exit(0))
}

export { seedTerms }
