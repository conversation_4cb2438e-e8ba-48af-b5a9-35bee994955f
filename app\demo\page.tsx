import DemoSchedulerSection from '@/components/sections/DemoSchedulerSection'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Book Your Personalized Demo | Schopio - AI-Powered School Management',
  description: 'Schedule a personalized 30-minute demo of Schopio. See how our AI-powered school management system can transform your educational institution.',
  keywords: 'school management demo, educational software demo, AI school system demo, Schopio demo booking',
  openGraph: {
    title: 'Book Your Personalized Demo | Schopio',
    description: 'Schedule a personalized 30-minute demo of Schopio. See how our AI-powered school management system can transform your educational institution.',
    type: 'website',
  },
}

export default function DemoPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Page Header */}
      <section className="py-12 bg-gradient-to-br from-blue-50 to-emerald-50 border-b border-slate-200">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-4">
              Experience Schopio in Action
            </h1>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Book a personalized 30-minute demo and see how Schopio can transform your school&apos;s operations with AI-powered intelligence.
            </p>
          </div>
        </div>
      </section>

      {/* Demo Scheduler Section */}
      <DemoSchedulerSection />
    </main>
  )
}
