import { db } from '@/src/db'
import { partnerFundAccounts, partnerCommissionEscrow, commissionReleaseAudit } from '@/src/db/schema'
import { eq } from 'drizzle-orm'

/**
 * Razorpay Route Service
 * Handles fund transfers to partner accounts using Razorpay Route
 * with comprehensive validation and error handling
 */

export interface FundAccountDetails {
  id: string
  partnerId: string
  accountType: 'bank_account' | 'vpa'
  accountNumber?: string
  ifscCode?: string
  vpaAddress?: string
  accountHolderName: string
  isActive: boolean
  isVerified: boolean
  razorpayFundAccountId?: string
}

export interface TransferRequest {
  escrowId: string
  partnerId: string
  amount: number
  currency: string
  notes?: Record<string, any>
  mode?: 'IMPS' | 'NEFT' | 'RTGS' | 'UPI'
}

export interface TransferResult {
  success: boolean
  transferId?: string
  razorpayTransferId?: string
  amount: number
  status: 'queued' | 'pending' | 'processed' | 'failed'
  error?: string
  estimatedSettlement?: Date
  fees?: number
  tax?: number
}

class RazorpayRouteService {
  private readonly baseUrl: string
  private readonly keyId: string
  private readonly keySecret: string

  constructor() {
    this.baseUrl = process.env.RAZORPAY_BASE_URL || 'https://api.razorpay.com/v1'
    this.keyId = process.env.RAZORPAY_KEY_ID || ''
    this.keySecret = process.env.RAZORPAY_KEY_SECRET || ''

    if (!this.keyId || !this.keySecret) {
      console.warn('⚠️ Razorpay credentials not configured for Route service')
    }
  }

  /**
   * Create fund account for partner
   */
  async createFundAccount(partnerId: string, accountDetails: Partial<FundAccountDetails>): Promise<FundAccountDetails> {
    try {
      // Validate partner exists
      const partner = await this.validatePartner(partnerId)
      if (!partner) {
        throw new Error('Partner not found')
      }

      // Create fund account in Razorpay (if not in test mode)
      let razorpayFundAccountId: string | undefined

      if (process.env.NODE_ENV === 'production') {
        const razorpayResponse = await this.callRazorpayAPI('/fund_accounts', 'POST', {
          contact_id: partner.razorpayContactId,
          account_type: accountDetails.accountType,
          bank_account: accountDetails.accountType === 'bank_account' ? {
            name: accountDetails.accountHolderName,
            account_number: accountDetails.accountNumber,
            ifsc: accountDetails.ifscCode
          } : undefined,
          vpa: accountDetails.accountType === 'vpa' ? {
            address: accountDetails.vpaAddress
          } : undefined
        })

        razorpayFundAccountId = razorpayResponse.id
      } else {
        // Test mode - generate mock ID
        razorpayFundAccountId = `fa_test_${Date.now()}`
      }

      // Store in database
      const [fundAccount] = await db.insert(partnerFundAccounts).values({
        partnerId,
        accountNumber: accountDetails.accountNumber || '',
        ifscCode: accountDetails.ifscCode || '',
        accountHolderName: accountDetails.accountHolderName || '',
        isActive: true,
        validationStatus: 'pending',
        razorpayFundAccountId
      }).returning()

      console.log(`✅ Fund account created for partner ${partnerId}: ${fundAccount.id}`)

      return {
        id: fundAccount.id,
        partnerId: fundAccount.partnerId,
        accountType: 'bank_account', // Only bank accounts supported for now
        accountNumber: fundAccount.accountNumber || undefined,
        ifscCode: fundAccount.ifscCode || undefined,
        accountHolderName: fundAccount.accountHolderName,
        isActive: fundAccount.isActive || false,
        isVerified: fundAccount.validationStatus === 'verified',
        razorpayFundAccountId: fundAccount.razorpayFundAccountId || undefined
      }

    } catch (error) {
      console.error('Fund account creation failed:', error)
      throw new Error(`Failed to create fund account: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Transfer funds to partner account
   */
  async transferFunds(request: TransferRequest): Promise<TransferResult> {
    try {
      // Get partner fund account
      const fundAccount = await this.getPartnerFundAccount(request.partnerId)
      if (!fundAccount) {
        throw new Error('Partner fund account not found')
      }

      if (!fundAccount.isVerified) {
        throw new Error('Partner fund account not verified')
      }

      // Validate amount
      if (request.amount <= 0) {
        throw new Error('Transfer amount must be positive')
      }

      // Create transfer in Razorpay (if not in test mode)
      let razorpayTransferId: string
      let transferStatus: 'queued' | 'pending' | 'processed' | 'failed' = 'queued'

      if (process.env.NODE_ENV === 'production') {
        const razorpayResponse = await this.callRazorpayAPI('/transfers', 'POST', {
          fund_account_id: fundAccount.razorpayFundAccountId,
          amount: Math.round(request.amount * 100), // Convert to paise
          currency: request.currency || 'INR',
          mode: request.mode || 'IMPS',
          notes: {
            escrow_id: request.escrowId,
            partner_id: request.partnerId,
            purpose: 'commission_payout',
            ...request.notes
          }
        })

        razorpayTransferId = razorpayResponse.id
        transferStatus = razorpayResponse.status
      } else {
        // Test mode - generate mock transfer
        razorpayTransferId = `trf_test_${Date.now()}`
        transferStatus = 'processed'
      }

      // Calculate estimated settlement time
      const estimatedSettlement = this.calculateSettlementTime(request.mode || 'IMPS')

      console.log(`💸 Transfer initiated: ₹${request.amount} to partner ${request.partnerId}`)

      return {
        success: true,
        transferId: `transfer_${Date.now()}`,
        razorpayTransferId,
        amount: request.amount,
        status: transferStatus,
        estimatedSettlement,
        fees: this.calculateTransferFees(request.amount, request.mode || 'IMPS'),
        tax: this.calculateTax(request.amount)
      }

    } catch (error) {
      console.error('Fund transfer failed:', error)
      return {
        success: false,
        amount: request.amount,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Validate fund account
   */
  async validateFundAccount(partnerId: string, accountDetails: Partial<FundAccountDetails>): Promise<boolean> {
    try {
      if (process.env.NODE_ENV !== 'production') {
        // Test mode - always return true
        return true
      }

      // Use Razorpay Fund Account Validation API
      const response = await this.callRazorpayAPI('/fund_accounts/validations', 'POST', {
        fund_account: {
          account_type: accountDetails.accountType,
          bank_account: accountDetails.accountType === 'bank_account' ? {
            name: accountDetails.accountHolderName,
            account_number: accountDetails.accountNumber,
            ifsc: accountDetails.ifscCode
          } : undefined,
          vpa: accountDetails.accountType === 'vpa' ? {
            address: accountDetails.vpaAddress
          } : undefined
        },
        amount: 100, // ₹1 for validation
        currency: 'INR',
        notes: {
          purpose: 'fund_account_validation',
          partner_id: partnerId
        }
      })

      return response.status === 'completed'

    } catch (error) {
      console.error('Fund account validation failed:', error)
      return false
    }
  }

  /**
   * Get partner fund account
   */
  private async getPartnerFundAccount(partnerId: string): Promise<FundAccountDetails | null> {
    const [fundAccount] = await db
      .select()
      .from(partnerFundAccounts)
      .where(eq(partnerFundAccounts.partnerId, partnerId))
      .limit(1)

    if (!fundAccount) return null

    return {
      id: fundAccount.id,
      partnerId: fundAccount.partnerId,
      accountType: 'bank_account', // Only bank accounts supported for now
      accountNumber: fundAccount.accountNumber || undefined,
      ifscCode: fundAccount.ifscCode || undefined,
      accountHolderName: fundAccount.accountHolderName,
      isActive: fundAccount.isActive || false,
      isVerified: fundAccount.validationStatus === 'verified',
      razorpayFundAccountId: fundAccount.razorpayFundAccountId || undefined
    }
  }

  /**
   * Validate partner exists
   */
  private async validatePartner(partnerId: string) {
    // This would check the partners table
    // For now, return a mock partner
    return {
      id: partnerId,
      razorpayContactId: `contact_${partnerId}`
    }
  }

  /**
   * Call Razorpay API
   */
  private async callRazorpayAPI(endpoint: string, method: string, data?: any) {
    const auth = Buffer.from(`${this.keyId}:${this.keySecret}`).toString('base64')
    
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method,
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json'
      },
      body: data ? JSON.stringify(data) : undefined
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(`Razorpay API error: ${error.error?.description || 'Unknown error'}`)
    }

    return await response.json()
  }

  /**
   * Calculate settlement time based on transfer mode
   */
  private calculateSettlementTime(mode: string): Date {
    const now = new Date()
    const hours = mode === 'IMPS' ? 0.5 : mode === 'NEFT' ? 2 : 4
    return new Date(now.getTime() + hours * 60 * 60 * 1000)
  }

  /**
   * Calculate transfer fees
   */
  private calculateTransferFees(amount: number, mode: string): number {
    // Razorpay Route fees (approximate)
    const baseFee = mode === 'IMPS' ? 5 : mode === 'NEFT' ? 2 : 10
    const percentageFee = amount * 0.001 // 0.1%
    return Math.min(baseFee + percentageFee, 25) // Max ₹25
  }

  /**
   * Calculate tax on fees
   */
  private calculateTax(amount: number): number {
    const fees = this.calculateTransferFees(amount, 'IMPS')
    return fees * 0.18 // 18% GST
  }
}

// Export singleton instance
export const razorpayRouteService = new RazorpayRouteService()
