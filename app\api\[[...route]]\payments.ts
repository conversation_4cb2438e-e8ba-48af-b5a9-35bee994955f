import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { db } from '../../../src/db'
import {
  billingInvoices,
  billingPayments,
  clients,
  subscriptions,
  billingSubscriptions,
  schoolReferrals
} from '../../../src/db/schema'
import { eq, and, desc } from 'drizzle-orm'
import { razorpayService } from '../../../src/services/razorpayService'

const app = new Hono()

// Create payment order for client invoice
app.post('/create-order', zValidator('json', z.object({
  invoiceId: z.string().uuid(),
  clientEmail: z.string().email()
})), async (c) => {
  try {
    const { invoiceId, clientEmail } = c.req.valid('json')

    // Verify invoice and client
    const invoiceData = await db.select({
      id: billingInvoices.id,
      invoiceNumber: billingInvoices.invoiceNumber,
      monthlyAmount: billingInvoices.totalAmount,
      status: billingInvoices.status,
      clientId: billingInvoices.clientId,
      client: {
        schoolName: clients.schoolName,
        email: clients.email
      }
    })
    .from(billingInvoices)
    .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
    .where(and(
      eq(billingInvoices.id, invoiceId),
      eq(clients.email, clientEmail)
    ))
    .limit(1)

    if (invoiceData.length === 0) {
      return c.json({ 
        success: false, 
        error: 'Invoice not found or unauthorized access' 
      }, 404)
    }

    const invoice = invoiceData[0]

    // Check if invoice is already paid
    if (invoice.status === 'paid') {
      return c.json({ 
        success: false, 
        error: 'Invoice is already paid' 
      }, 400)
    }

    // Create Razorpay order
    const amount = Math.round(parseFloat(invoice.monthlyAmount) * 100) // Convert to paise
    const orderResult = await razorpayService.createOrder({
      amount,
      currency: 'INR',
      receipt: `PAY-${invoice.invoiceNumber}`,
      notes: {
        invoice_id: invoiceId,
        client_id: invoice.clientId || '',
        school_name: invoice.client?.schoolName || '',
        invoice_number: invoice.invoiceNumber
      }
    })

    if (!orderResult.success) {
      return c.json({ 
        success: false, 
        error: 'Failed to create payment order' 
      }, 500)
    }

    return c.json({
      success: true,
      order: {
        id: orderResult.order.id,
        monthlyAmount: orderResult.order.amount,
        currency: orderResult.order.currency,
        receipt: orderResult.order.receipt
      },
      invoice: {
        id: invoice.id,
        number: invoice.invoiceNumber,
        monthlyAmount: invoice.monthlyAmount,
        schoolName: invoice.client?.schoolName
      }
    })

  } catch (error) {
    console.error('Create payment order error:', error)
    return c.json({ 
      success: false, 
      error: 'Failed to create payment order' 
    }, 500)
  }
})

// Verify and complete payment
app.post('/verify', zValidator('json', z.object({
  razorpayOrderId: z.string(),
  razorpayPaymentId: z.string(),
  razorpaySignature: z.string(),
  invoiceId: z.string().uuid(),
  clientEmail: z.string().email()
})), async (c) => {
  try {
    const { 
      razorpayOrderId, 
      razorpayPaymentId, 
      razorpaySignature, 
      invoiceId, 
      clientEmail 
    } = c.req.valid('json')

    // Verify invoice and client authorization
    const invoiceData = await db.select({
      id: billingInvoices.id,
      subscriptionId: billingInvoices.subscriptionId,
      clientId: billingInvoices.clientId,
      monthlyAmount: billingInvoices.totalAmount,
      status: billingInvoices.status,
      client: {
        email: clients.email,
        schoolName: clients.schoolName
      }
    })
    .from(billingInvoices)
    .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
    .where(and(
      eq(billingInvoices.id, invoiceId),
      eq(clients.email, clientEmail)
    ))
    .limit(1)

    if (invoiceData.length === 0) {
      return c.json({ 
        success: false, 
        error: 'Invoice not found or unauthorized access' 
      }, 404)
    }

    const invoice = invoiceData[0]

    // Check if already paid
    if (invoice.status === 'paid') {
      return c.json({ 
        success: false, 
        error: 'Invoice is already paid' 
      }, 400)
    }

    // Verify payment signature
    const isValid = razorpayService.verifyPaymentSignature({
      razorpayOrderId,
      razorpayPaymentId,
      razorpaySignature
    })

    if (!isValid) {
      return c.json({ 
        success: false, 
        error: 'Invalid payment signature' 
      }, 400)
    }

    // Get payment details from Razorpay
    const paymentResult = await razorpayService.getPayment(razorpayPaymentId)
    if (!paymentResult.success) {
      return c.json({ 
        success: false, 
        error: 'Failed to verify payment with gateway' 
      }, 500)
    }

    const payment = paymentResult.payment

    // Update invoice status
    await db.update(billingInvoices)
      .set({
        status: 'paid',
        paidDate: new Date().toISOString().split('T')[0]
      })
      .where(eq(billingInvoices.id, invoiceId))

    // Create payment record
    const [newPayment] = await db.insert(billingPayments).values({
      invoiceId,
      subscriptionId: invoice.subscriptionId,
      clientId: invoice.clientId || '',
      razorpayPaymentId,
      amount: (payment.amount / 100).toString(), // Convert from paise
      status: 'succeeded',
      paymentMethod: payment.method
    }).returning()

    // Update billing cycle status if applicable
    const billingCycle = await db.select()
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, invoice.id))
      .limit(1)

    if (billingCycle.length > 0) {
      await db.update(billingSubscriptions)
        .set({ status: 'paid' })
        .where(eq(billingSubscriptions.id, billingCycle[0].id))
    }

    // Process partner commission after successful payment
    try {
      // Check if this school has a partner referral
      const [partnerReferral] = await db
        .select({
          partnerId: schoolReferrals.partnerId,
          isActive: schoolReferrals.isActive
        })
        .from(schoolReferrals)
        .where(and(
          eq(schoolReferrals.clientId, invoice.clientId || ''),
          eq(schoolReferrals.isActive, true)
        ))
        .limit(1)

      if (partnerReferral && partnerReferral.partnerId) {
        console.log(`Processing commission for partner ${partnerReferral.partnerId} on payment ${newPayment.id}`)

        const grossAmount = parseFloat(newPayment.amount)

        // Import commission processor
        const { commissionProcessor } = await import('@/src/services/commissionProcessor')

        await commissionProcessor.processCommissionForPayment(
          newPayment.id,
          partnerReferral.partnerId,
          invoice.clientId || '',
          grossAmount
        )

        console.log(`Commission processing completed for payment ${newPayment.id}`)
      } else {
        console.log(`No active partner referral found for client ${invoice.clientId}`)
      }
    } catch (commissionError) {
      console.error('Commission processing error:', commissionError)
      // Don't fail the payment if commission processing fails
    }

    return c.json({
      success: true,
      message: 'Payment completed successfully',
      payment: {
        id: newPayment.id,
        monthlyAmount: newPayment.amount,
        status: newPayment.status,
        createdAt: newPayment.createdAt
      }
    })

  } catch (error) {
    console.error('Verify payment error:', error)
    return c.json({ 
      success: false, 
      error: 'Failed to verify payment' 
    }, 500)
  }
})

// Get client's pending invoices
app.get('/invoices/:clientEmail', async (c) => {
  try {
    const clientEmail = c.req.param('clientEmail')

    const pendingInvoices = await db.select({
      id: billingInvoices.id,
      invoiceNumber: billingInvoices.invoiceNumber,
      monthlyAmount: billingInvoices.totalAmount,
      taxAmount: billingInvoices.taxAmount,
      status: billingInvoices.status,
      issuedDate: billingInvoices.issuedDate,
      nextBillingDate: billingInvoices.dueDate,
      billingCycle: {
        currentPeriodStart: billingSubscriptions.currentPeriodStart,
        currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
        studentCount: billingSubscriptions.studentCount
      }
    })
    .from(billingInvoices)
    .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
    .leftJoin(billingSubscriptions, eq(billingInvoices.subscriptionId, billingSubscriptions.id))
    .where(and(
      eq(clients.email, clientEmail),
      eq(billingInvoices.status, 'sent')
    ))
    .orderBy(desc(billingInvoices.issuedDate))

    return c.json({
      success: true,
      invoices: pendingInvoices
    })

  } catch (error) {
    console.error('Get client invoices error:', error)
    return c.json({ 
      success: false, 
      error: 'Failed to fetch invoices' 
    }, 500)
  }
})

// Get client's payment history
app.get('/history/:clientEmail', async (c) => {
  try {
    const clientEmail = c.req.param('clientEmail')

    const paymentHistory = await db.select({
      id: billingPayments.id,
      monthlyAmount: billingPayments.amount,
      currency: billingPayments.currency,
      status: billingPayments.status,
      paymentMethod: billingPayments.paymentMethod,
      createdAt: billingPayments.createdAt,
      invoice: {
        invoiceNumber: billingInvoices.invoiceNumber,
        issuedDate: billingInvoices.issuedDate,
        monthlyAmount: billingInvoices.totalAmount
      }
    })
    .from(billingPayments)
    .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))
    .leftJoin(clients, eq(billingPayments.clientId, clients.id))
    .where(eq(clients.email, clientEmail))
    .orderBy(desc(billingPayments.createdAt))

    return c.json({
      success: true,
      payments: paymentHistory
    })

  } catch (error) {
    console.error('Get payment history error:', error)
    return c.json({ 
      success: false, 
      error: 'Failed to fetch payment history' 
    }, 500)
  }
})

export default app
