'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Calendar, 
  Video, 
  Users, 
  CheckCircle,
  ArrowRight,
  Clock,
  Star
} from 'lucide-react'

const SimpleDemoRedirect = () => {
  const demoFeatures = [
    {
      icon: Video,
      title: "Live System Demo",
      description: "See Schopio in action with real school data"
    },
    {
      icon: Users,
      title: "Role-Based Walkthrough", 
      description: "Experience different user perspectives"
    },
    {
      icon: CheckCircle,
      title: "Custom Use Cases",
      description: "Tailored scenarios for your school type"
    },
    {
      icon: Clock,
      title: "30-Minute Session",
      description: "Quick but comprehensive overview"
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-emerald-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Calendar className="w-4 h-4" />
            Schedule Demo
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Ready to See Schopio in Action?
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Book a personalized demo and discover how Schopio can transform your school&apos;s operations.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-white border-0 shadow-xl">
              <CardContent padding="xl">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                  {/* Demo Features */}
                  <div>
                    <h3 className="text-2xl font-bold text-slate-900 mb-6">
                      What You&apos;ll Experience
                    </h3>
                    <div className="space-y-4">
                      {demoFeatures.map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          className="flex items-start gap-4"
                        >
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-emerald-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <feature.icon className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-slate-900 mb-1">
                              {feature.title}
                            </h4>
                            <p className="text-slate-600 text-sm">
                              {feature.description}
                            </p>
                          </div>
                        </motion.div>
                      ))}
                    </div>

                    {/* Trust Indicator */}
                    <div className="mt-8 p-4 bg-gradient-to-r from-blue-600 to-emerald-600 rounded-lg text-white">
                      <div className="flex items-center gap-2 mb-2">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <p className="text-blue-100 text-sm italic">
                        &quot;The demo was incredibly helpful. They showed exactly how Schopio would work for our school.&quot;
                      </p>
                      <div className="text-xs mt-2 text-blue-200">
                        - School Administrator
                      </div>
                    </div>
                  </div>

                  {/* CTA Section */}
                  <div className="text-center">
                    <div className="bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl p-8">
                      <Calendar className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                      <h3 className="text-2xl font-bold text-slate-900 mb-4">
                        Book Your Demo Now
                      </h3>
                      <p className="text-slate-600 mb-6">
                        Choose your preferred date and time for a personalized 30-minute demo session.
                      </p>
                      
                      <Button
                        size="xl"
                        icon={ArrowRight}
                        iconPosition="right"
                        className="w-full bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 text-white font-bold py-4 mb-4"
                        onClick={() => window.location.href = '/demo'}
                      >
                        Schedule My Demo
                      </Button>
                      
                      <p className="text-xs text-slate-500">
                        No commitment required • Free consultation • 30-minute session
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default SimpleDemoRedirect
