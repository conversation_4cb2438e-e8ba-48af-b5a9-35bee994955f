'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  Mail, 
  Building, 
  Users, 
  CheckCircle,
  ArrowRight,
  Video,
  MapPin,
  Star
} from 'lucide-react'

const DemoSchedulerSection = () => {
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitMessage, setSubmitMessage] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    schoolName: '',
    studentCount: '',
    currentSystem: '',
    specificInterests: ''
  })

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const availableDates = [
    { date: '2024-01-15', day: 'Mon', dayNum: '15' },
    { date: '2024-01-16', day: 'Tue', dayNum: '16' },
    { date: '2024-01-17', day: 'Wed', dayNum: '17' },
    { date: '2024-01-18', day: 'Thu', dayNum: '18' },
    { date: '2024-01-19', day: 'Fri', dayNum: '19' }
  ]

  const availableTimes = [
    '10:00 AM', '11:00 AM', '12:00 PM', '2:00 PM', '3:00 PM', '4:00 PM'
  ]

  const studentCountOptions = [
    '100-500 students',
    '500-1000 students', 
    '1000-2000 students',
    '2000+ students'
  ]

  const currentSystemOptions = [
    'Manual/Paper-based',
    'Basic Software',
    'Multiple Systems',
    'Legacy System'
  ]

  const demoFeatures = [
    {
      icon: Video,
      title: "Live System Demo",
      description: "See Schopio in action with real school data"
    },
    {
      icon: Users,
      title: "Role-Based Walkthrough", 
      description: "Experience different user perspectives"
    },
    {
      icon: CheckCircle,
      title: "Custom Use Cases",
      description: "Tailored scenarios for your school type"
    },
    {
      icon: Calendar,
      title: "Implementation Planning",
      description: "Discuss timeline and next steps"
    }
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitMessage('')

    try {
      // Combine date and time into ISO datetime string
      const scheduledDateTime = new Date(`${selectedDate}T${convertTo24Hour(selectedTime)}:00.000Z`).toISOString()

      const demoRequestData = {
        contactPerson: formData.name,
        email: formData.email,
        phone: formData.phone,
        schoolName: formData.schoolName,
        estimatedStudents: formData.studentCount ? parseInt(formData.studentCount) : undefined,
        scheduledDate: scheduledDateTime,
        demoType: "online" as const,
        notes: `Current System: ${formData.currentSystem}. Specific Interests: ${formData.specificInterests}`
      }

      const response = await fetch('/api/demo/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(demoRequestData)
      })

      const result = await response.json()

      if (result.success) {
        setSubmitMessage('Demo scheduled successfully! You\'ll receive a confirmation email shortly.')
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          schoolName: '',
          studentCount: '',
          currentSystem: '',
          specificInterests: ''
        })
        setSelectedDate('')
        setSelectedTime('')
      } else {
        setSubmitMessage(result.error || 'Failed to schedule demo. Please try again.')
      }
    } catch (error) {
      console.error('Error submitting demo request:', error)
      setSubmitMessage('Failed to schedule demo. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Helper function to convert 12-hour time to 24-hour format
  const convertTo24Hour = (time12h: string) => {
    const [time, modifier] = time12h.split(' ')
    let [hours, minutes] = time.split(':')
    if (hours === '12') {
      hours = '00'
    }
    if (modifier === 'PM') {
      hours = (parseInt(hours, 10) + 12).toString()
    }
    return `${hours}:${minutes}`
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Calendar className="w-4 h-4" />
            Schedule Demo
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Book Your Personalized 
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Live Demo</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            See exactly how Schopio will work for your school. Our education specialists will customize the demo based on your specific needs.
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Demo Information */}
            <motion.div
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
            >
              <div className="space-y-8">
                {/* What to Expect */}
                <Card className="bg-gradient-to-br from-blue-50 to-emerald-50 border border-blue-200">
                  <CardHeader padding="lg">
                    <h3 className="text-2xl font-bold text-slate-900 mb-2">What to Expect in Your Demo</h3>
                    <p className="text-slate-600">30-minute personalized session tailored to your school</p>
                  </CardHeader>
                  <CardContent padding="lg">
                    <div className="space-y-4">
                      {demoFeatures.map((feature, index) => (
                        <div key={index} className="flex items-start gap-4">
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <feature.icon className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h4 className="font-bold text-slate-900 mb-1">{feature.title}</h4>
                            <p className="text-sm text-slate-600">{feature.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Benefits */}
                <Card className="bg-white border border-slate-200">
                  <CardContent padding="lg">
                    <h4 className="text-lg font-bold text-slate-900 mb-4">Why Book a Demo?</h4>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-emerald-500" />
                        <span className="text-slate-700">See real ROI calculations for your school size</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-emerald-500" />
                        <span className="text-slate-700">Get answers to your specific questions</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-emerald-500" />
                        <span className="text-slate-700">Understand implementation timeline</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-emerald-500" />
                        <span className="text-slate-700">No pressure - just information</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Testimonial */}
                <Card className="bg-gradient-to-r from-blue-600 to-emerald-600 border-0 text-white">
                  <CardContent padding="lg">
                    <div className="flex items-center gap-1 mb-3">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <p className="text-blue-100 italic mb-4">
                      &quot;The demo was incredibly helpful. They showed exactly how Schopio would work for our 1,500 students and answered all our technical questions.&quot;
                    </p>
                    <div className="text-sm">
                      <div className="font-semibold">Dr. Priya Sharma</div>
                      <div className="text-blue-200">Principal, DISCLOSED School</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>

            {/* Booking Form */}
            <motion.div
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
            >
              <Card className="bg-white border-0 shadow-xl">
                <CardHeader padding="lg">
                  <h3 className="text-2xl font-bold text-slate-900 mb-2">Schedule Your Demo</h3>
                  <p className="text-slate-600">Choose your preferred date and time</p>
                </CardHeader>
                <CardContent padding="lg">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Date Selection */}
                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-3">
                        Select Date
                      </label>
                      <div className="grid grid-cols-5 gap-2">
                        {availableDates.map((date) => (
                          <button
                            key={date.date}
                            type="button"
                            onClick={() => setSelectedDate(date.date)}
                            className={`p-3 rounded-lg border text-center transition-all ${
                              selectedDate === date.date
                                ? 'bg-blue-600 text-white border-blue-600'
                                : 'bg-white text-slate-700 border-slate-300 hover:border-blue-400'
                            }`}
                          >
                            <div className="text-xs">{date.day}</div>
                            <div className="text-lg font-bold">{date.dayNum}</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Time Selection */}
                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-3">
                        Select Time (IST)
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {availableTimes.map((time) => (
                          <button
                            key={time}
                            type="button"
                            onClick={() => setSelectedTime(time)}
                            className={`p-2 rounded-lg border text-sm transition-all ${
                              selectedTime === time
                                ? 'bg-blue-600 text-white border-blue-600'
                                : 'bg-white text-slate-700 border-slate-300 hover:border-blue-400'
                            }`}
                          >
                            {time}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-2">
                          Full Name *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Your name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-2">
                          Email *
                        </label>
                        <input
                          type="email"
                          required
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-2">
                          Phone Number
                        </label>
                        <input
                          type="tel"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="+91 98765 43210"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-2">
                          School Name *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.schoolName}
                          onChange={(e) => handleInputChange('schoolName', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Your school name"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-2">
                          Student Count
                        </label>
                        <select
                          value={formData.studentCount}
                          onChange={(e) => handleInputChange('studentCount', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">Select range</option>
                          {studentCountOptions.map((option) => (
                            <option key={option} value={option}>{option}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-2">
                          Current System
                        </label>
                        <select
                          value={formData.currentSystem}
                          onChange={(e) => handleInputChange('currentSystem', e.target.value)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">Select current system</option>
                          {currentSystemOptions.map((option) => (
                            <option key={option} value={option}>{option}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-2">
                        Specific Interests (Optional)
                      </label>
                      <textarea
                        value={formData.specificInterests}
                        onChange={(e) => handleInputChange('specificInterests', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Any specific features or questions you&apos;d like us to focus on..."
                      />
                    </div>

                    {submitMessage && (
                      <div className={`p-3 rounded-lg text-sm text-center ${
                        submitMessage.includes('successfully')
                          ? 'bg-green-100 text-green-700 border border-green-200'
                          : 'bg-red-100 text-red-700 border border-red-200'
                      }`}>
                        {submitMessage}
                      </div>
                    )}

                    <Button
                      type="submit"
                      size="lg"
                      icon={ArrowRight}
                      iconPosition="right"
                      disabled={isSubmitting || !selectedDate || !selectedTime || !formData.name || !formData.email || !formData.schoolName}
                      className="w-full bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 text-white font-bold py-4 disabled:opacity-50"
                    >
                      {isSubmitting ? 'Scheduling...' : 'Schedule My Demo'}
                    </Button>

                    <p className="text-xs text-slate-500 text-center">
                      By scheduling a demo, you agree to receive follow-up communications. No spam, unsubscribe anytime.
                    </p>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default DemoSchedulerSection
