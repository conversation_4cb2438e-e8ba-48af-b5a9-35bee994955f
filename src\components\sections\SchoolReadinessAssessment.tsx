'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, <PERSON>Content, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Target, 
  BarChart3,
  Award,
  AlertTriangle,
  TrendingUp,
  Users,
  Clock,
  FileText,
  Shield
} from 'lucide-react'

interface Question {
  id: string
  question: string
  options: { value: number; label: string; description: string }[]
}

const SchoolReadinessAssessment = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<string, number>>({})
  const [showResults, setShowResults] = useState(false)

  const questions: Question[] = [
    {
      id: 'current_system',
      question: 'How do you currently manage student information and records?',
      options: [
        { value: 1, label: 'Paper-based records', description: 'Physical files and manual documentation' },
        { value: 2, label: 'Basic spreadsheets', description: 'Excel/Google Sheets for basic tracking' },
        { value: 3, label: 'Multiple software tools', description: 'Different systems for different functions' },
        { value: 4, label: 'Integrated school software', description: 'Single comprehensive system' }
      ]
    },
    {
      id: 'communication',
      question: 'How do you communicate with parents and students?',
      options: [
        { value: 1, label: 'Phone calls and paper notices', description: 'Traditional communication methods' },
        { value: 2, label: 'Email and WhatsApp groups', description: 'Basic digital communication' },
        { value: 3, label: 'School website and SMS', description: 'Multiple digital channels' },
        { value: 4, label: 'Integrated parent portal', description: 'Centralized communication platform' }
      ]
    },
    {
      id: 'attendance',
      question: 'How do you track student attendance?',
      options: [
        { value: 1, label: 'Paper registers', description: 'Manual attendance marking' },
        { value: 2, label: 'Digital registers', description: 'Basic digital attendance' },
        { value: 3, label: 'Biometric systems', description: 'Automated attendance tracking' },
        { value: 4, label: 'Smart card/RFID system', description: 'Advanced automated tracking' }
      ]
    },
    {
      id: 'fee_management',
      question: 'How do you handle fee collection and financial management?',
      options: [
        { value: 1, label: 'Cash and manual receipts', description: 'Traditional payment methods' },
        { value: 2, label: 'Bank transfers and basic tracking', description: 'Semi-digital payment tracking' },
        { value: 3, label: 'Online payment gateway', description: 'Digital payment options' },
        { value: 4, label: 'Integrated financial system', description: 'Complete financial automation' }
      ]
    },
    {
      id: 'reporting',
      question: 'How do you generate reports and analytics?',
      options: [
        { value: 1, label: 'Manual compilation', description: 'Creating reports by hand' },
        { value: 2, label: 'Basic spreadsheet reports', description: 'Simple Excel-based reporting' },
        { value: 3, label: 'Software-generated reports', description: 'Automated basic reporting' },
        { value: 4, label: 'Real-time analytics dashboard', description: 'Advanced analytics and insights' }
      ]
    },
    {
      id: 'staff_efficiency',
      question: 'How much time does your administrative staff spend on routine tasks?',
      options: [
        { value: 1, label: 'More than 80% of their time', description: 'Mostly manual administrative work' },
        { value: 2, label: '60-80% of their time', description: 'Significant manual work' },
        { value: 3, label: '40-60% of their time', description: 'Balanced manual and strategic work' },
        { value: 4, label: 'Less than 40% of their time', description: 'Mostly strategic and student-focused work' }
      ]
    },
    {
      id: 'growth_challenges',
      question: 'What is your biggest challenge in managing school operations?',
      options: [
        { value: 1, label: 'Time-consuming manual processes', description: 'Too much paperwork and manual tasks' },
        { value: 2, label: 'Poor communication with parents', description: 'Difficulty keeping parents informed' },
        { value: 3, label: 'Lack of real-time insights', description: 'Cannot track performance effectively' },
        { value: 4, label: 'Scaling operations efficiently', description: 'Growing without proportional admin burden' }
      ]
    }
  ]

  const calculateScore = () => {
    const totalScore = Object.values(answers).reduce((sum, score) => sum + score, 0)
    const maxScore = questions.length * 4
    return Math.round((totalScore / maxScore) * 100)
  }

  const getScoreCategory = (score: number) => {
    if (score >= 80) return { level: 'Advanced', color: 'emerald', icon: Award }
    if (score >= 60) return { level: 'Intermediate', color: 'blue', icon: TrendingUp }
    if (score >= 40) return { level: 'Developing', color: 'yellow', icon: Target }
    return { level: 'Basic', color: 'red', icon: AlertTriangle }
  }

  const getRecommendations = (score: number) => {
    if (score >= 80) {
      return [
        'Your school is already well-digitized! Focus on advanced analytics and AI-powered insights.',
        'Consider implementing predictive analytics for student performance and attendance.',
        'Explore advanced parent engagement features and automated communications.',
        'Look into biometric integration for enhanced attendance management (available on demand).'
      ]
    }
    if (score >= 60) {
      return [
        'Good foundation! Focus on integrating your existing systems for better efficiency.',
        'Implement automated reporting and real-time analytics dashboards.',
        'Enhance parent communication with integrated web portals.',
        'Consider biometric attendance (available on demand) and digital fee management.'
      ]
    }
    if (score >= 40) {
      return [
        'Significant improvement opportunities! Start with core digitization.',
        'Implement integrated student information management system.',
        'Digitize attendance tracking and fee collection processes.',
        'Establish basic parent communication portals.'
      ]
    }
    return [
      'Great potential for transformation! Begin with fundamental digital systems.',
      'Start with basic student information management and digital records.',
      'Implement digital attendance and basic parent communication.',
      'Focus on eliminating paper-based processes step by step.'
    ]
  }

  const handleAnswer = (value: number) => {
    setAnswers({ ...answers, [questions[currentQuestion].id]: value })
    
    if (currentQuestion < questions.length - 1) {
      setTimeout(() => setCurrentQuestion(currentQuestion + 1), 300)
    } else {
      setTimeout(() => setShowResults(true), 300)
    }
  }

  const goBack = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const resetAssessment = () => {
    setCurrentQuestion(0)
    setAnswers({})
    setShowResults(false)
  }

  const score = calculateScore()
  const scoreCategory = getScoreCategory(score)
  const recommendations = getRecommendations(score)

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Target className="w-4 h-4" />
            School Readiness Assessment
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            How Ready Is Your School for 
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Digital Transformation?</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Take our quick 7-question assessment to discover your school&apos;s efficiency score and get personalized recommendations.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            {!showResults ? (
              <motion.div
                key="assessment"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="bg-white border-0 shadow-xl">
                  <CardHeader padding="lg">
                    {/* Progress Bar */}
                    <div className="mb-6">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-slate-600">
                          Question {currentQuestion + 1} of {questions.length}
                        </span>
                        <span className="text-sm font-medium text-blue-600">
                          {Math.round(((currentQuestion + 1) / questions.length) * 100)}% Complete
                        </span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-2">
                        <motion.div
                          className="bg-gradient-to-r from-blue-500 to-emerald-500 h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                    </div>

                    <h3 className="text-2xl font-bold text-slate-900 mb-2">
                      {questions[currentQuestion].question}
                    </h3>
                    <p className="text-slate-600">
                      Select the option that best describes your current situation.
                    </p>
                  </CardHeader>

                  <CardContent padding="lg">
                    <div className="space-y-4">
                      {questions[currentQuestion].options.map((option, index) => (
                        <motion.button
                          key={index}
                          onClick={() => handleAnswer(option.value)}
                          className="w-full text-left p-4 border-2 border-slate-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="flex items-start gap-4">
                            <div className="w-6 h-6 border-2 border-slate-300 rounded-full group-hover:border-blue-500 transition-colors duration-200 flex-shrink-0 mt-1" />
                            <div>
                              <div className="font-semibold text-slate-900 mb-1">
                                {option.label}
                              </div>
                              <div className="text-sm text-slate-600">
                                {option.description}
                              </div>
                            </div>
                          </div>
                        </motion.button>
                      ))}
                    </div>

                    {/* Navigation */}
                    <div className="flex justify-between mt-8">
                      <Button
                        onClick={goBack}
                        variant="outline"
                        icon={ArrowLeft}
                        iconPosition="left"
                        disabled={currentQuestion === 0}
                        className="disabled:opacity-50"
                      >
                        Previous
                      </Button>
                      <div className="text-sm text-slate-500 self-center">
                        Click an option to continue
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ) : (
              <motion.div
                key="results"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-8"
              >
                {/* Score Card */}
                <Card className={`bg-gradient-to-br from-${scoreCategory.color}-500 to-${scoreCategory.color}-600 border-0 shadow-xl text-white`}>
                  <CardContent padding="xl">
                    <div className="text-center">
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                        className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6"
                      >
                        <scoreCategory.icon className="w-10 h-10 text-white" />
                      </motion.div>
                      
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.4 }}
                      >
                        <h3 className="text-3xl font-bold mb-2">Your School Management Efficiency Score</h3>
                        <div className="text-6xl font-bold mb-4">{score}/100</div>
                        <div className="text-xl font-semibold mb-2">{scoreCategory.level} Level</div>
                        <p className="text-lg opacity-90 max-w-2xl mx-auto">
                          {score >= 80 ? 'Excellent! Your school is highly digitized and efficient.' :
                           score >= 60 ? 'Good progress! There are opportunities to enhance efficiency.' :
                           score >= 40 ? 'Developing well! Significant improvements are possible.' :
                           'Great potential! Your school can benefit tremendously from digital transformation.'}
                        </p>
                      </motion.div>
                    </div>
                  </CardContent>
                </Card>

                {/* Recommendations */}
                <Card className="bg-white border-0 shadow-xl">
                  <CardHeader padding="lg">
                    <h3 className="text-2xl font-bold text-slate-900 mb-2">
                      Personalized Recommendations for Your School
                    </h3>
                    <p className="text-slate-600">
                      Based on your assessment, here&apos;s how Schopio can help transform your school operations:
                    </p>
                  </CardHeader>

                  <CardContent padding="lg">
                    <div className="grid md:grid-cols-2 gap-6">
                      {recommendations.map((recommendation, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          className="flex items-start gap-3 p-4 bg-blue-50 rounded-lg"
                        >
                          <CheckCircle className="w-6 h-6 text-emerald-500 flex-shrink-0 mt-0.5" />
                          <p className="text-slate-700">{recommendation}</p>
                        </motion.div>
                      ))}
                    </div>

                    <div className="mt-8 p-6 bg-gradient-to-r from-blue-600 to-emerald-600 rounded-xl text-white text-center">
                      <h4 className="text-xl font-bold mb-2">Ready to Transform Your School?</h4>
                      <p className="mb-4 opacity-90">
                        Get a custom solution recommendation tailored to your specific needs and current systems.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button
                          size="lg"
                          icon={ArrowRight}
                          iconPosition="right"
                          className="bg-white text-blue-600 hover:bg-blue-50 font-bold"
                        >
                          Get Custom Solution Recommendations
                        </Button>
                        <Button
                          onClick={resetAssessment}
                          variant="outline"
                          size="lg"
                          className="border-white text-white hover:bg-white/10"
                        >
                          Retake Assessment
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </section>
  )
}

export default SchoolReadinessAssessment
