'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { CheckCircle, XCircle, RefreshCw } from 'lucide-react'
import Link from 'next/link'

export default function DebugAuthPage() {
  const [authData, setAuthData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  const checkAuth = () => {
    setLoading(true)
    
    // Check localStorage
    const schoolToken = localStorage.getItem('schoolToken')
    const authToken = localStorage.getItem('authToken')
    const userData = localStorage.getItem('user')
    
    // Get all localStorage keys
    const allKeys = Object.keys(localStorage)
    
    let parsedUser = null
    try {
      if (userData) {
        parsedUser = JSON.parse(userData)
      }
    } catch (error) {
      console.error('Error parsing user data:', error)
    }

    setAuthData({
      schoolToken: schoolToken ? 'EXISTS' : 'NOT_FOUND',
      authToken: authToken ? 'EXISTS' : 'NOT_FOUND',
      userData: userData ? 'EXISTS' : 'NOT_FOUND',
      parsedUser,
      allLocalStorageKeys: allKeys,
      timestamp: new Date().toISOString()
    })
    
    setLoading(false)
  }

  useEffect(() => {
    checkAuth()
  }, [])

  const clearAuth = () => {
    localStorage.removeItem('schoolToken')
    localStorage.removeItem('authToken')
    localStorage.removeItem('user')
    checkAuth()
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center gap-4 mb-8">
        <Link href="/profile">
          <Button variant="outline" size="sm">
            ← Back to Profile
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Authentication Debug</h1>
          <p className="text-gray-600">Check your authentication status and tokens</p>
        </div>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Authentication Status
              <Button 
                variant="outline" 
                size="sm" 
                onClick={checkAuth}
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span>Checking...</span>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2">
                    {authData?.schoolToken === 'EXISTS' ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                    <span className="font-medium">School Token:</span>
                    <span className={authData?.schoolToken === 'EXISTS' ? 'text-green-600' : 'text-red-600'}>
                      {authData?.schoolToken}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {authData?.authToken === 'EXISTS' ? (
                      <CheckCircle className="w-5 h-5 text-yellow-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-gray-400" />
                    )}
                    <span className="font-medium">Auth Token:</span>
                    <span className={authData?.authToken === 'EXISTS' ? 'text-yellow-600' : 'text-gray-400'}>
                      {authData?.authToken}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {authData?.userData === 'EXISTS' ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                    <span className="font-medium">User Data:</span>
                    <span className={authData?.userData === 'EXISTS' ? 'text-green-600' : 'text-red-600'}>
                      {authData?.userData}
                    </span>
                  </div>
                </div>

                {authData?.parsedUser && (
                  <div>
                    <h3 className="font-medium mb-2">User Information:</h3>
                    <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                      {JSON.stringify(authData.parsedUser, null, 2)}
                    </pre>
                  </div>
                )}

                <div>
                  <h3 className="font-medium mb-2">All localStorage Keys:</h3>
                  <div className="bg-gray-100 p-3 rounded text-sm">
                    {authData?.allLocalStorageKeys?.length > 0 ? (
                      authData.allLocalStorageKeys.join(', ')
                    ) : (
                      'No keys found'
                    )}
                  </div>
                </div>

                <div className="text-xs text-gray-500">
                  Last checked: {authData?.timestamp}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button 
                variant="destructive" 
                onClick={clearAuth}
              >
                Clear All Auth Data
              </Button>
              
              <Link href="/auth">
                <Button variant="outline">
                  Go to Login
                </Button>
              </Link>
              
              <Link href="/profile/support/create">
                <Button>
                  Test Ticket Creation
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Expected Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>✅ School Token:</strong> Should be &quot;EXISTS&quot; for school portal access</p>
              <p><strong>⚠️ Auth Token:</strong> Should be &quot;NOT_FOUND&quot; (old token type)</p>
              <p><strong>✅ User Data:</strong> Should be &quot;EXISTS&quot; with school information</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
