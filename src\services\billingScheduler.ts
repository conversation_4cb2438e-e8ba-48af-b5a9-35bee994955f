import * as cron from 'node-cron'
import { db } from '@/db'
import { subscriptions, billingSubscriptions, billingInvoices, clients, supportTickets } from '@/db/schema'
import { eq, and, lte, gte, count } from 'drizzle-orm'
import { DueDateManager } from './dueDateManager'
import { billingMonitor } from './billingMonitor'
import { dunningManager } from './dunningManager'
import { paymentFailureHandler } from './paymentFailureHandler'
import { auditLogger } from './auditLogger'
import { securityMonitor } from './securityMonitor'
import { emailService } from './emailService'
import { discountExpirationService } from './discountExpirationService'

interface BillingSchedulerConfig {
  enabled: boolean
  timezone: string
  dryRun: boolean
}

class BillingScheduler {
  private config: BillingSchedulerConfig
  private tasks: Map<string, cron.ScheduledTask> = new Map()

  constructor(config: BillingSchedulerConfig = {
    enabled: true,
    timezone: 'Asia/Kolkata',
    dryRun: false
  }) {
    this.config = config
  }

  /**
   * Initialize all billing schedulers
   */
  public init(): void {
    if (!this.config.enabled) {
      console.log('🔄 Billing Scheduler: Disabled')
      return
    }

    console.log('🚀 Billing Scheduler: Initializing...')

    // Monthly billing generation - runs on 1st of every month at 6:00 AM
    this.scheduleMonthlyBilling()

    // Daily overdue check - runs every day at 9:00 AM
    this.scheduleOverdueCheck()

    // Daily due date processing - runs every day at 8:00 AM
    this.scheduleDueDateProcessing()

    // Daily discount expiration check - runs every day at 7:00 AM
    this.scheduleDiscountExpirationCheck()

    // Weekly payment reminder - runs every Monday at 10:00 AM
    this.schedulePaymentReminders()

    // Billing health monitoring - runs every 6 hours
    this.scheduleBillingHealthMonitoring()

    // Dunning management - runs daily at 11:00 AM
    this.scheduleDunningManagement()

    // Payment failure processing - runs every 2 hours
    this.schedulePaymentFailureProcessing()

    console.log('✅ Billing Scheduler: All tasks scheduled')
  }

  /**
   * Schedule monthly billing generation
   * Runs on 1st of every month at 6:00 AM IST
   */
  private scheduleMonthlyBilling(): void {
    const task = cron.schedule('0 6 1 * *', async () => {
      console.log('🔄 Starting monthly billing generation...')

      try {
        const result = await this.generateMonthlyBilling()
        console.log('✅ Monthly billing completed:', result)

        // Log successful billing generation
        await auditLogger.logAdmin('monthly_billing_generated', {
          adminId: 'system',
          resource: 'billing_scheduler',
          details: {
            processed: result.processed,
            created: result.created,
            errors: result.errors
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Monthly billing failed:', error)
        await this.handleBillingFailure('monthly-billing', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('monthly-billing', task)
    task.start()
    console.log('📅 Monthly billing scheduled: 1st of every month at 6:00 AM IST')
  }

  /**
   * Schedule daily overdue invoice check
   * Runs every day at 9:00 AM IST
   */
  private scheduleOverdueCheck(): void {
    const task = cron.schedule('0 9 * * *', async () => {
      console.log('🔄 Checking overdue billingInvoices...')

      try {
        const result = await this.checkOverdueInvoices()
        console.log('✅ Overdue check completed:', result)
      } catch (error) {
        console.error('❌ Overdue check failed:', error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('overdue-check', task)
    task.start()
    console.log('📅 Overdue check scheduled: Daily at 9:00 AM IST')
  }

  /**
   * Schedule daily due date processing
   * Runs every day at 8:00 AM IST
   */
  private scheduleDueDateProcessing(): void {
    const task = cron.schedule('0 8 * * *', async () => {
      console.log('🔄 Processing due dates and penalties...')

      try {
        const result = await DueDateManager.processAllDueDateUpdates()
        console.log('✅ Due date processing completed:', result)

        // Log summary if there were updates
        if (result.billingCycleUpdates.length > 0) {
          console.log(`📊 Updated ${result.billingCycleUpdates.length} billing cycles:`)
          result.billingCycleUpdates.forEach(update => {
            console.log(`  - Cycle ${update.id}: ${update.status} (${update.daysOverdue} days overdue, ₹${update.penaltyAmount} penalty)`)
          })
        }

        // Log successful due date processing
        await auditLogger.logAdmin('due_date_processing', {
          adminId: 'system',
          resource: 'billing_scheduler',
          details: {
            billingCycleUpdates: result.billingCycleUpdates.length,
            subscriptionUpdates: result.subscriptionsUpdated
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Due date processing failed:', error)
        await this.handleBillingFailure('due-date-processing', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('due-date-processing', task)
    task.start()
    console.log('📅 Due date processing scheduled: Daily at 8:00 AM IST')
  }

  /**
   * Schedule weekly payment reminders
   * Runs every Monday at 10:00 AM IST
   */
  private schedulePaymentReminders(): void {
    const task = cron.schedule('0 10 * * 1', async () => {
      console.log('🔄 Sending payment reminders...')

      try {
        const result = await this.sendPaymentReminders()
        console.log('✅ Payment reminders sent:', result)
      } catch (error) {
        console.error('❌ Payment reminders failed:', error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('payment-reminders', task)
    task.start()
    console.log('📅 Payment reminders scheduled: Every Monday at 10:00 AM IST')
  }

  /**
   * Generate monthly billing for all active subscriptions
   */
  public async generateMonthlyBilling(): Promise<{
    processed: number
    created: number
    errors: number
    details: any[]
  }> {
    const now = new Date()
    const currentMonth = now.getMonth() + 1
    const currentYear = now.getFullYear()

    console.log(`🔄 Generating billing for ${currentMonth}/${currentYear}`)

    if (this.config.dryRun) {
      console.log('🧪 DRY RUN MODE - No actual billing will be generated')
    }

    // Get all active subscriptions that are due for billing
    const activeSubscriptions = await db.select({
      id: subscriptions.id,
      clientId: subscriptions.clientId,
      planName: subscriptions.planName,
      studentCount: subscriptions.studentCount,
      monthlyAmount: subscriptions.monthlyAmount,
      nextBillingDate: subscriptions.nextBillingDate,
      billingCycle: subscriptions.billingCycle,
      client: {
        schoolName: clients.schoolName,
        email: clients.email,
        contactPerson: clients.contactPerson
      }
    })
    .from(subscriptions)
    .leftJoin(clients, eq(subscriptions.clientId, clients.id))
    .where(and(
      eq(subscriptions.status, 'active'),
      lte(subscriptions.nextBillingDate, now.toISOString().split('T')[0])
    ))

    const results = {
      processed: 0,
      created: 0,
      errors: 0,
      details: [] as any[]
    }

    for (const subscription of activeSubscriptions) {
      try {
        results.processed++

        // Check if billing cycle already exists for this month
        const existingCycle = await db.select()
          .from(billingSubscriptions)
          .where(and(
            eq(billingSubscriptions.id, subscription.id),
            gte(billingSubscriptions.currentPeriodStart, `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`),
            lte(billingSubscriptions.currentPeriodStart, `${currentYear}-${currentMonth.toString().padStart(2, '0')}-31`)
          ))
          .limit(1)

        if (existingCycle.length > 0) {
          console.log(`⏭️  Billing cycle already exists for subscription ${subscription.id}`)
          results.details.push({
            id: subscription.id,
            status: 'skipped',
            reason: 'Billing cycle already exists'
          })
          continue
        }

        if (!this.config.dryRun) {
          // Create billing cycle and invoice
          const billingResult = await this.createBillingCycleAndInvoice(subscription, currentMonth, currentYear)
          
          if (billingResult.success) {
            results.created++
            results.details.push({
              id: subscription.id,
              status: 'created',
              billingCycleId: billingResult.billingCycleId,
              invoiceId: billingResult.invoiceId
            })

            // Update next billing date
            await this.updateNextBillingDate(subscription.id, subscription.billingCycle || 'monthly')
          } else {
            results.errors++
            results.details.push({
              id: subscription.id,
              status: 'error',
              error: billingResult.error
            })
          }
        } else {
          console.log(`🧪 DRY RUN: Would create billing for subscription ${subscription.id}`)
          results.details.push({
            id: subscription.id,
            status: 'dry-run',
            clientName: subscription.client?.schoolName
          })
        }

      } catch (error) {
        results.errors++
        console.error(`❌ Error processing subscription ${subscription.id}:`, error)
        results.details.push({
          id: subscription.id,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return results
  }

  /**
   * Create billing cycle and invoice for a subscription
   */
  private async createBillingCycleAndInvoice(
    subscription: any,
    month: number,
    year: number
  ): Promise<{ success: boolean; billingCycleId?: string; invoiceId?: string; error?: string }> {
    try {
      // Calculate cycle dates
      const cycleStart = `${year}-${month.toString().padStart(2, '0')}-01`
      const cycleEndDate = new Date(year, month, 0) // Last day of month
      const cycleEnd = cycleEndDate.toISOString().split('T')[0]

      // Get subscription details for due date calculation
      const [subscriptionDetails] = await db.select({
        nextBillingDate: subscriptions.nextBillingDate,
        dueDate: subscriptions.dueDate,
        gracePeriodDays: subscriptions.gracePeriodDays,
        billingCycle: subscriptions.billingCycle,
        pricePerStudent: subscriptions.pricePerStudent,
        yearlyDiscountPercentage: subscriptions.yearlyDiscountPercentage
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, subscription.id))
      .limit(1)

      // Calculate due date using DueDateManager
      const cycleStartDate = new Date(cycleStart)
      const dueDateCalc = DueDateManager.calculateDueDate(
        cycleStartDate,
        (subscriptionDetails?.billingCycle as 'monthly' | 'yearly') || 'monthly',
        subscriptionDetails?.dueDate || 15,
        subscriptionDetails?.gracePeriodDays || 3
      )
      const dueDateStr = dueDateCalc.dueDate.toISOString().split('T')[0]

      // ✅ FIXED: Calculate billing amount with active discount integration
      let baseAmount: number
      let discountAmount = 0

      if (subscriptionDetails?.billingCycle === 'yearly') {
        // For yearly billing, calculate annual amount with discount
        const monthlyAmount = parseFloat(subscription.originalMonthlyAmount || subscription.monthlyAmount)
        const annualAmountBeforeDiscount = monthlyAmount * 12
        const discountPercentage = parseFloat(subscriptionDetails.yearlyDiscountPercentage?.toString() || '16.67')
        discountAmount = (annualAmountBeforeDiscount * discountPercentage) / 100
        baseAmount = annualAmountBeforeDiscount - discountAmount
      } else {
        // For monthly billing, check for active discount
        if (subscription.hasActiveDiscount && subscription.originalMonthlyAmount) {
          const originalAmount = parseFloat(subscription.originalMonthlyAmount)
          const discountedAmount = parseFloat(subscription.monthlyAmount)
          baseAmount = discountedAmount
          discountAmount = originalAmount - discountedAmount
        } else {
          baseAmount = parseFloat(subscription.monthlyAmount)
        }
      }

      const taxAmount = baseAmount * 0.18 // 18% GST
      const totalAmount = baseAmount + taxAmount

      // Create billing cycle
      const [newCycle] = await db.insert(billingSubscriptions).values({
        currentPeriodStart: cycleStart,
        currentPeriodEnd: cycleEnd,
        studentCount: subscription.studentCount,
        pricePerStudent: subscriptionDetails?.pricePerStudent || subscription.pricePerStudent,
        monthlyAmount: totalAmount.toString(),
        status: 'active', // Start as active, will be updated by due date processor
        nextBillingDate: dueDateStr,
        gracePeriodDays: subscriptionDetails?.gracePeriodDays || 3
      }).returning()

      // Generate invoice number
      const invoiceNumber = `INV-${year}${month.toString().padStart(2, '0')}-${subscription.clientId.slice(-6).toUpperCase()}`

      // ✅ FIXED: Create invoice with discount information
      const [newInvoice] = await db.insert(billingInvoices).values({
        subscriptionId: newCycle.id,
        clientId: subscription.clientId,
        invoiceNumber,
        subtotal: (baseAmount + discountAmount).toString(), // Original amount before discount
        discountAmount: discountAmount.toString(),
        taxAmount: taxAmount.toString(),
        totalAmount: totalAmount.toString(),
        status: 'open',
        issuedDate: new Date().toISOString().split('T')[0],
        dueDate: dueDateStr,
        periodStart: cycleStart,
        periodEnd: cycleEnd,
        // Add discount information to notes if applicable
        notes: subscription.hasActiveDiscount ?
          `Discount applied: ${subscription.currentDiscountPercentage}% (₹${discountAmount.toFixed(2)} savings)` :
          undefined
      }).returning()

      // Send invoice generated email notification
      try {
        // Get client information for email
        const [client] = await db.select({
          schoolName: clients.schoolName,
          email: clients.email,
          contactPerson: clients.contactPerson
        })
        .from(clients)
        .where(eq(clients.id, subscription.clientId))
        .limit(1)

        if (client && client.email) {
          const emailResult = await emailService.sendInvoiceWithPDF({
            schoolName: client.schoolName,
            contactPerson: client.contactPerson || 'Admin',
            email: client.email,
            invoiceNumber,
            amount: totalAmount.toString(),
            dueDate: dueDateStr,
            paymentUrl: `${process.env.FRONTEND_URL || 'https://schopio.com'}/billing/pay/${newInvoice.id}`,
            invoiceId: newInvoice.id
          })

          if (emailResult.success) {
            console.log(`✅ Invoice generated email sent to ${client.email} (ID: ${'id' in emailResult ? emailResult.id : 'unknown'})`)
          } else {
            console.error(`❌ Failed to send invoice generated email: ${emailResult.error}`)
          }
        }
      } catch (emailError) {
        console.error('❌ Error sending invoice generated email:', emailError)
        // Don't fail the billing process if email fails
      }

      return {
        success: true,
        billingCycleId: newCycle.id,
        invoiceId: newInvoice.id
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Update next billing date for subscription
   */
  private async updateNextBillingDate(id: string, billingCycle: string): Promise<void> {
    const nextBillingDate = new Date()
    
    if (billingCycle === 'monthly') {
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1)
    } else if (billingCycle === 'yearly') {
      nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1)
    }

    await db.update(subscriptions)
      .set({
        nextBillingDate: nextBillingDate.toISOString().split('T')[0]
      })
      .where(eq(subscriptions.id, id))
  }

  /**
   * Check for overdue invoices and update status
   */
  public async checkOverdueInvoices(): Promise<{ updated: number; details: any[] }> {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      console.log('🔍 Checking for overdue billingInvoices...')

      // Get all unpaid invoices that are past due date
      const overdueInvoices = await db.select({
        id: billingInvoices.id,
        clientId: billingInvoices.clientId,
        invoiceNumber: billingInvoices.invoiceNumber,
        amount: billingInvoices.totalAmount,
        dueDate: billingInvoices.dueDate,
        status: billingInvoices.status,
        subscriptionId: billingInvoices.subscriptionId,
        // Client information
        schoolName: clients.schoolName,
        email: clients.email,
        contactPerson: clients.contactPerson
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .where(and(
        eq(billingInvoices.status, 'sent'),
        lte(billingInvoices.dueDate, today.toISOString().split('T')[0])
      ))

      const results = {
        updated: 0,
        details: [] as any[]
      }

      for (const invoice of overdueInvoices) {
        try {
          if (!this.config.dryRun) {
            // Use transaction with row-level locking for atomic invoice updates
            await db.transaction(async (tx) => {
              // Lock the invoice to prevent concurrent updates
              const [lockedInvoice] = await tx.select()
                .from(billingInvoices)
                .where(eq(billingInvoices.id, invoice.id))
                .for('update')
                .limit(1)

              if (lockedInvoice) {
                // Update invoice status to overdue
                await tx.update(billingInvoices)
                  .set({
                    status: 'overdue'
                  })
                  .where(eq(billingInvoices.id, invoice.id))

                // Update corresponding billing cycle status (with row-level locking)
                if (invoice.subscriptionId) {
                  const [billingCycle] = await tx.select()
                    .from(billingSubscriptions)
                    .where(eq(billingSubscriptions.id, invoice.subscriptionId))
                    .for('update')
                    .limit(1)

                  if (billingCycle) {
                    await tx.update(billingSubscriptions)
                      .set({
                        status: 'overdue'
                      })
                      .where(eq(billingSubscriptions.id, invoice.subscriptionId))
                  }
                }
              }
            })

            // Send overdue notice email
            try {
              if (invoice.email) {
                const today = new Date()
                const dueDate = new Date(invoice.dueDate)
                const daysOverdue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))

                const emailResult = await emailService.sendOverdueNotice({
                  schoolName: invoice.schoolName,
                  contactPerson: invoice.contactPerson || 'Admin',
                  email: invoice.email,
                  invoiceNumber: invoice.invoiceNumber,
                  amount: invoice.amount,
                  dueDate: invoice.dueDate,
                  daysOverdue,
                  paymentUrl: `${process.env.FRONTEND_URL || 'https://schopio.com'}/billing/pay/${invoice.id}`
                })

                if (emailResult.success) {
                  console.log(`✅ Overdue notice email sent to ${invoice.email} (ID: ${'id' in emailResult ? emailResult.id : 'unknown'})`)
                } else {
                  console.error(`❌ Failed to send overdue notice email: ${emailResult.error}`)
                }
              }
            } catch (emailError) {
              console.error('❌ Error sending overdue notice email:', emailError)
              // Don't fail the process if email fails
            }

            results.updated++
            results.details.push({
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              clientName: invoice.schoolName,
              amount: invoice.amount,
              status: 'updated_to_overdue'
            })

            console.log(`📋 Updated invoice ${invoice.invoiceNumber} to overdue status`)
          } else {
            console.log(`🧪 DRY RUN: Would mark invoice ${invoice.invoiceNumber} as overdue`)
            results.details.push({
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              clientName: invoice.schoolName,
              amount: invoice.amount,
              status: 'dry_run_overdue'
            })
          }

        } catch (error) {
          console.error(`❌ Error updating invoice ${invoice.id}:`, error)
          results.details.push({
            invoiceId: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      console.log(`✅ Overdue check completed. Updated ${results.updated} invoices`)
      return results

    } catch (error) {
      console.error('❌ Error in overdue invoice check:', error)
      throw error
    }
  }

  /**
   * Send payment reminders for pending invoices
   */
  public async sendPaymentReminders(): Promise<{ sent: number; details: any[] }> {
    try {
      const today = new Date()
      const reminderDate = new Date(today)
      reminderDate.setDate(reminderDate.getDate() + 3) // Remind 3 days before due date

      console.log('📧 Sending payment reminders...')

      // Get invoices that are due within 3 days and haven't been paid
      const upcomingInvoices = await db.select({
        id: billingInvoices.id,
        clientId: billingInvoices.clientId,
        invoiceNumber: billingInvoices.invoiceNumber,
        amount: billingInvoices.totalAmount,
        dueDate: billingInvoices.dueDate,
        status: billingInvoices.status,
        // Client information
        schoolName: clients.schoolName,
        email: clients.email,
        contactPerson: clients.contactPerson,
        phone: clients.phone
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .where(and(
        eq(billingInvoices.status, 'sent'),
        lte(billingInvoices.dueDate, reminderDate.toISOString().split('T')[0]),
        gte(billingInvoices.dueDate, today.toISOString().split('T')[0])
      ))

      const results = {
        sent: 0,
        details: [] as any[]
      }

      for (const invoice of upcomingInvoices) {
        try {
          if (!this.config.dryRun) {
            console.log(`📧 Sending payment reminder for invoice ${invoice.invoiceNumber} to ${invoice.email}`)

            // Send payment reminder email using email service
            const emailResult = await emailService.sendPaymentReminder({
              schoolName: invoice.schoolName,
              contactPerson: invoice.contactPerson || 'Admin',
              email: invoice.email,
              invoiceNumber: invoice.invoiceNumber,
              amount: invoice.amount,
              dueDate: invoice.dueDate,
              paymentUrl: `${process.env.FRONTEND_URL || 'https://schopio.com'}/billing/pay/${invoice.id}`
            })

            if (!emailResult.success) {
              console.error(`❌ Failed to send payment reminder email: ${emailResult.error}`)
              throw new Error(`Email sending failed: ${emailResult.error}`)
            }

            console.log(`✅ Payment reminder email sent successfully (ID: ${'id' in emailResult ? emailResult.id : 'unknown'})`)

            results.sent++
            results.details.push({
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              clientName: invoice.schoolName,
              email: invoice.email,
              amount: invoice.amount,
              dueDate: invoice.dueDate,
              status: 'reminder_sent'
            })

          } else {
            console.log(`🧪 DRY RUN: Would send reminder for invoice ${invoice.invoiceNumber} to ${invoice.email}`)
            results.details.push({
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              clientName: invoice.schoolName,
              email: invoice.email,
              amount: invoice.amount,
              dueDate: invoice.dueDate,
              status: 'dry_run_reminder'
            })
          }

        } catch (error) {
          console.error(`❌ Error sending reminder for invoice ${invoice.id}:`, error)
          results.details.push({
            invoiceId: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      console.log(`✅ Payment reminders completed. Sent ${results.sent} reminders`)
      return results

    } catch (error) {
      console.error('❌ Error in payment reminders:', error)
      throw error
    }
  }

  /**
   * Stop all scheduled tasks
   */
  public stop(): void {
    this.tasks.forEach((task, name) => {
      task.stop()
      console.log(`🛑 Stopped task: ${name}`)
    })
    this.tasks.clear()
  }

  /**
   * Get status of all scheduled tasks
   */
  public getStatus(): { [key: string]: boolean } {
    const status: { [key: string]: boolean } = {}
    this.tasks.forEach((task, name) => {
      status[name] = task.getStatus() === 'scheduled'
    })
    return status
  }

  /**
   * Handle billing failures and send alerts
   */
  private async handleBillingFailure(taskName: string, error: Error, details?: any): Promise<void> {
    const errorInfo = {
      task: taskName,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      details
    }

    console.error(`🚨 BILLING FAILURE - ${taskName}:`, errorInfo)

    try {
      // Log security event for billing failure
      await securityMonitor.logSecurityEvent({
        type: 'billing_system_error',
        severity: 'high',

        details: errorInfo,
        ipAddress: 'system',
        userAgent: 'billing-scheduler',

      })

      // Log audit trail
      await auditLogger.logAdmin('billing_task_failed', {
        adminId: 'system',
        resource: 'billing_scheduler',
        details: {
          taskName,
          error: error.message,
          stack: error.stack,
          ...details
        },
        ipAddress: 'system',
        userAgent: 'billing-scheduler',
        success: false
      })

      // Send email alerts to administrators
      try {
        await emailService.sendBillingFailureAlert({
          error: error instanceof Error ? error.message : 'Unknown billing error',
          affectedClients: [taskName],
          failedOperations: 1,
          timestamp: new Date().toISOString()
        })
        console.log('📧 Billing failure alert sent to administrators')
      } catch (emailError) {
        console.error('Failed to send billing failure alert:', emailError)
      }

      // Create support ticket for critical failures if severity is high
      if (taskName.includes('monthly-billing') || taskName.includes('critical')) {
        try {
          await this.createCriticalFailureTicket(taskName, error)
          console.log('🎫 Critical failure support ticket created')
        } catch (ticketError) {
          console.error('Failed to create critical failure ticket:', ticketError)
        }
      }

    } catch (logError) {
      console.error('❌ Failed to log billing error:', logError)
    }
  }

  /**
   * Get comprehensive billing system health status
   */
  public async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical'
    tasks: { [key: string]: boolean }
    metrics: {
      activeSubscriptions: number
      pendingInvoices: number
      overdueInvoices: number
      failedPayments: number
      lastBillingRun: string | null
    }
    issues: string[]
  }> {
    try {
      const tasks = this.getStatus()
      const issues: string[] = []

      // Check if all tasks are running
      Object.entries(tasks).forEach(([name, isRunning]) => {
        if (!isRunning) {
          issues.push(`Task '${name}' is not running`)
        }
      })

      // Get system metrics
      const [activeSubscriptions] = await db.select({ count: count() })
        .from(subscriptions)
        .where(eq(subscriptions.status, 'active'))

      const [pendingInvoices] = await db.select({ count: count() })
        .from(billingInvoices)
        .where(eq(billingInvoices.status, 'sent'))

      const [overdueInvoices] = await db.select({ count: count() })
        .from(billingInvoices)
        .where(eq(billingInvoices.status, 'overdue'))

      const [failedPayments] = await db.select({ count: count() })
        .from(billingInvoices)
        .where(eq(billingInvoices.status, 'failed'))

      // Check for critical issues
      if (overdueInvoices.count > 10) {
        issues.push(`High number of overdue invoices: ${overdueInvoices.count}`)
      }

      if (failedPayments.count > 5) {
        issues.push(`High number of failed payments: ${failedPayments.count}`)
      }

      // Determine overall status
      let status: 'healthy' | 'warning' | 'critical' = 'healthy'
      if (issues.length > 0) {
        status = issues.some(issue => issue.includes('critical') || issue.includes('failed')) ? 'critical' : 'warning'
      }

      return {
        status,
        tasks,
        metrics: {
          activeSubscriptions: activeSubscriptions.count,
          pendingInvoices: pendingInvoices.count,
          overdueInvoices: overdueInvoices.count,
          failedPayments: failedPayments.count,
          lastBillingRun: null // TODO: Track last billing run
        },
        issues
      }

    } catch (error) {
      console.error('❌ Error getting system health:', error)
      return {
        status: 'critical',
        tasks: this.getStatus(),
        metrics: {
          activeSubscriptions: 0,
          pendingInvoices: 0,
          overdueInvoices: 0,
          failedPayments: 0,
          lastBillingRun: null
        },
        issues: ['Failed to retrieve system health metrics']
      }
    }
  }

  /**
   * Manual trigger for billing generation (for testing/emergency)
   */
  public async triggerManualBilling(adminId?: string): Promise<any> {
    console.log('🔄 Manual billing trigger initiated...')

    try {
      const result = await this.generateMonthlyBilling()

      console.log('✅ Manual billing completed:', result)

      // TODO: Log manual trigger
      // await db.insert(systemLogs).values({
      //   level: 'info',
      //   category: 'billing',
      //   message: 'Manual billing triggered',
      //   data: JSON.stringify({ adminId, result }),
      //   createdAt: new Date()
      // })

      return result
    } catch (error) {
      await this.handleBillingFailure('manual-billing', error as Error, { adminId })
      throw error
    }
  }

  /**
   * Schedule billing health monitoring
   * Runs every 6 hours
   */
  private scheduleBillingHealthMonitoring(): void {
    const task = cron.schedule('0 */6 * * *', async () => {
      console.log('🔍 Starting billing health monitoring...')

      try {
        const healthReport = await billingMonitor.monitorBillingHealth()
        console.log('✅ Billing health monitoring completed:', {
          status: healthReport.status,
          alertCount: healthReport.alerts.length,
          criticalAlerts: healthReport.alerts.filter(a => a.severity === 'critical').length
        })

        // Send alerts if critical issues found
        if (healthReport.status === 'critical') {
          await this.sendCriticalAlert(healthReport)
        }

        // Log monitoring activity
        await auditLogger.logAdmin('billing_health_monitoring', {
          adminId: 'system',
          resource: 'billing_monitor',
          details: {
            status: healthReport.status,
            alertCount: healthReport.alerts.length,
            metrics: healthReport.metrics
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Billing health monitoring failed:', error)
        await this.handleBillingFailure('billing-health-monitoring', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('billing-health-monitoring', task)
    task.start()
    console.log('📅 Billing health monitoring scheduled: Every 6 hours')
  }

  /**
   * Schedule dunning management
   * Runs daily at 11:00 AM IST
   */
  private scheduleDunningManagement(): void {
    const task = cron.schedule('0 11 * * *', async () => {
      console.log('⚖️ Starting dunning management...')

      try {
        const result = await dunningManager.processDunningActions()
        console.log('✅ Dunning management completed:', result)

        // Log dunning activity
        await auditLogger.logAdmin('dunning_management', {
          adminId: 'system',
          resource: 'dunning_manager',
          details: {
            processed: result.processed,
            actionsExecuted: result.actionsExecuted,
            errors: result.errors
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Dunning management failed:', error)
        await this.handleBillingFailure('dunning-management', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('dunning-management', task)
    task.start()
    console.log('📅 Dunning management scheduled: Daily at 11:00 AM IST')
  }

  /**
   * Schedule payment failure processing
   * Runs every 2 hours
   */
  private schedulePaymentFailureProcessing(): void {
    const task = cron.schedule('0 */2 * * *', async () => {
      console.log('💳 Starting payment failure processing...')

      try {
        const result = await paymentFailureHandler.processPaymentRetries()
        console.log('✅ Payment failure processing completed:', result)

        // Log payment retry activity
        await auditLogger.logAdmin('payment_failure_processing', {
          adminId: 'system',
          resource: 'payment_failure_handler',
          details: {
            processed: result.processed,
            retried: result.retried,
            failed: result.failed
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Payment failure processing failed:', error)
        await this.handleBillingFailure('payment-failure-processing', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('payment-failure-processing', task)
    task.start()
    console.log('📅 Payment failure processing scheduled: Every 2 hours')
  }

  /**
   * Send critical alert to administrators
   */
  private async sendCriticalAlert(healthReport: any): Promise<void> {
    try {
      // Log critical alert
      await securityMonitor.logSecurityEvent({
        type: 'billing_critical_alert',
        severity: 'critical',

        details: {
          status: healthReport.status,
          alerts: healthReport.alerts.filter((a: any) => a.severity === 'critical'),
          metrics: healthReport.metrics,
          timestamp: new Date().toISOString()
        },
        ipAddress: 'system',
        userAgent: 'billing-scheduler',

      })

      // Send critical system alert to administrators
      try {
        await emailService.sendCriticalSystemAlert({
          title: 'Critical Billing System Alert',
          description: `Critical billing system issue detected: ${healthReport.status || 'Unknown error'}`,
          systemMetrics: {
            failedBillings: healthReport.metrics?.failedBillings || 0,
            overdueInvoices: healthReport.metrics?.overdueInvoices || 0,
            systemLoad: 'High',
            errorRate: 'Critical'
          },
          timestamp: new Date().toISOString()
        })
        console.log('🚨 CRITICAL BILLING ALERT: Email sent to administrators')
      } catch (emailError) {
        console.error('Failed to send critical system alert:', emailError)
      }

      // Create emergency support ticket
      try {
        await this.createEmergencyTicket(healthReport)
        console.log('🆘 Emergency support ticket created for critical alert')
      } catch (ticketError) {
        console.error('Failed to create emergency ticket:', ticketError)
      }

    } catch (error) {
      console.error('❌ Failed to send critical alert:', error)
    }
  }

  /**
   * Create support ticket for critical billing failures
   */
  private async createCriticalFailureTicket(operation: string, error: any): Promise<void> {
    try {
      const ticketData = {
        title: `Critical Billing Failure: ${operation}`,
        description: `Automated ticket created for critical billing system failure.

**Operation:** ${operation}
**Error:** ${error instanceof Error ? error.message : 'Unknown error'}
**Timestamp:** ${new Date().toISOString()}
**Severity:** High
**Type:** System Failure

**Details:**
${error instanceof Error ? error.stack : JSON.stringify(error, null, 2)}

**Required Actions:**
1. Investigate billing system logs
2. Check affected client accounts
3. Verify payment processing status
4. Monitor system recovery

This ticket was automatically generated by the billing scheduler.`,
        priority: 'high' as const,
        category: 'technical' as const,
        status: 'open' as const,
        clientId: null, // System ticket
        createdBy: 'system',
        assignedTo: 'admin'
      }

      // Insert support ticket into database
      const [newTicket] = await db.insert(supportTickets).values({
        title: ticketData.title,
        description: ticketData.description,
        priority: ticketData.priority,
        category: ticketData.category,
        status: ticketData.status,
        clientId: null, // System-generated ticket
        createdBy: 'billing-scheduler'
      }).returning()

      console.log(`✅ Critical failure ticket created: ${newTicket.id}`)

    } catch (ticketError) {
      console.error('❌ Failed to create critical failure ticket:', ticketError)
      throw ticketError
    }
  }

  /**
   * Create emergency support ticket for critical system alerts
   */
  private async createEmergencyTicket(healthReport: any): Promise<void> {
    try {
      const criticalAlerts = healthReport.alerts?.filter((a: any) => a.severity === 'critical') || []

      const ticketData = {
        title: 'EMERGENCY: Critical Billing System Alert',
        description: `EMERGENCY TICKET - Critical billing system issue requiring immediate attention.

**Alert Type:** Critical System Failure
**System Status:** ${healthReport.status || 'Unknown'}
**Timestamp:** ${new Date().toISOString()}
**Severity:** Critical
**Priority:** Emergency

**Critical Alerts (${criticalAlerts.length}):**
${criticalAlerts.map((alert: any, index: number) => `${index + 1}. ${alert.message || alert.type || 'Unknown alert'}`).join('\n')}

**System Metrics:**
- Failed Billings: ${healthReport.metrics?.failedBillings || 'Unknown'}
- Overdue Invoices: ${healthReport.metrics?.overdueInvoices || 'Unknown'}
- System Load: Critical
- Error Rate: High
- Health Status: ${healthReport.status}

**Immediate Actions Required:**
1. IMMEDIATE system investigation
2. Check all billing operations
3. Verify payment gateway status
4. Contact technical team lead
5. Prepare system recovery plan

**Full Health Report:**
${JSON.stringify(healthReport, null, 2)}

⚠️ This is an EMERGENCY ticket generated automatically by the billing system monitoring.
🚨 IMMEDIATE ATTENTION REQUIRED - System stability at risk.`,
        priority: 'urgent' as const,
        category: 'technical' as const,
        status: 'open' as const
      }

      // Insert emergency support ticket
      const [emergencyTicket] = await db.insert(supportTickets).values({
        title: ticketData.title,
        description: ticketData.description,
        priority: ticketData.priority,
        category: ticketData.category,
        status: ticketData.status,
        clientId: null, // System-generated emergency ticket
        createdBy: 'billing-emergency-system'
      }).returning()

      console.log(`🆘 Emergency ticket created: ${emergencyTicket.id}`)

    } catch (ticketError) {
      console.error('❌ Failed to create emergency ticket:', ticketError)
      throw ticketError
    }
  }

  /**
   * Schedule daily discount expiration check
   */
  private scheduleDiscountExpirationCheck(): void {
    const task = cron.schedule('0 7 * * *', async () => {
      try {
        console.log('🎯 [Discount Expiration] Starting daily discount expiration check...')

        if (this.config.dryRun) {
          console.log('🔄 [Discount Expiration] DRY RUN MODE - No actual changes will be made')
        }

        const result = await discountExpirationService.checkAndExpireDiscounts()

        console.log(`✅ [Discount Expiration] Daily check completed:
          - Total Checked: ${result.totalChecked}
          - Successfully Expired: ${result.totalExpired}
          - Errors: ${result.totalErrors}
          - Expired Subscriptions: ${result.expiredSubscriptions.join(', ') || 'None'}
        `)

        // Log to audit system
        await auditLogger.log({
          action: 'DISCOUNT_EXPIRATION_CHECK',
          resource: 'discount_scheduler',
          resourceId: 'system',
          details: result,
          success: result.totalErrors === 0,
          severity: result.totalErrors === 0 ? 'low' : 'medium',
          category: 'system'
        })

        // Monitor for errors
        if (result.totalErrors > 0) {
          await securityMonitor.logSecurityEvent({
            type: 'system_error',
            severity: 'medium',
            ipAddress: '127.0.0.1',
            details: {
              message: `${result.totalErrors} discount expiration errors occurred`,
              errorCount: result.totalErrors,
              errors: result.errors
            }
          })

          // Send alert email
          await emailService.sendAlert({
            to: '<EMAIL>',
            subject: `Discount Expiration Errors - ${new Date().toDateString()}`,
            message: `${result.totalErrors} errors occurred during discount expiration check. Details: ${result.errors.join('; ')}`
          })
        }

      } catch (error) {
        console.error('❌ [Discount Expiration] Scheduler error:', error)

        await auditLogger.log({
          action: 'DISCOUNT_EXPIRATION_CHECK',
          resource: 'discount_scheduler',
          resourceId: 'system',
          details: { error: error instanceof Error ? error.message : 'Unknown error' },
          success: false,
          severity: 'high',
          category: 'system',
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        })

        await securityMonitor.logSecurityEvent({
          type: 'system_error',
          severity: 'high',
          ipAddress: '127.0.0.1',
          details: {
            message: `Discount expiration scheduler failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        })
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('discountExpiration', task)
    console.log('📅 [Discount Expiration] Scheduled daily check at 7:00 AM IST')
  }
}

// Export singleton instance
export const billingScheduler = new BillingScheduler()

// Export class for testing
export { BillingScheduler }
