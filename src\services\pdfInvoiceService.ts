import jsPDF from 'jspdf'
import J<PERSON>Z<PERSON> from 'jszip'
import { db } from '../db'
import { billingInvoices, clients, billingSubscriptions, subscriptions, billingPayments } from '../db/schema'
import { eq, and, desc } from 'drizzle-orm'

interface InvoiceData {
  // Invoice details
  id: string
  invoiceNumber: string
  amount: string
  status: string | null
  issuedDate: string
  dueDate: string
  paidDate: string | null
  
  // Client details
  client: {
    schoolName: string
    schoolCode: string
    email: string
    phone: string | null
    address: string | null
    contactPerson: string | null
  }
  
  // Billing cycle details
  billingCycle?: {
    currentPeriodStart: string
    currentPeriodEnd: string
    studentCount: number
    monthlyAmount: string}
  
  // Subscription details
  subscription?: {
    planName: string
    pricePerStudent: string
    billingCycle: string
    studentCount: number
  }
  
  // Payment details (if paid)
  payment?: {
    razorpayPaymentId: string | null
    paymentMethod: string | null
    createdAt: string | null
  }
}

interface PDFGenerationResult {
  success: boolean
  pdfBuffer?: Buffer
  fileName?: string
  error?: string
}

interface BulkPDFResult {
  success: boolean
  zipBuffer?: Buffer
  fileName?: string
  generatedCount?: number
  failedCount?: number
  errors?: string[]
  error?: string
}

interface InvoicePDFInfo {
  invoiceId: string
  invoiceNumber: string
  schoolName: string
  success: boolean
  pdfBuffer?: Buffer
  fileName?: string
  error?: string
}

class PDFInvoiceService {
  private readonly companyInfo = {
    name: 'Schopio',
    tagline: 'School Management System',
    address: 'Orionix Technologies Pvt. Ltd.',
    city: 'India',
    email: '<EMAIL>',
    website: 'https://schopio.com',
    phone: '+91-XXXXXXXXXX'
  }

  /**
   * Generate PDF invoice for a given invoice ID
   */
  async generateInvoicePDF(invoiceId: string): Promise<PDFGenerationResult> {
    return this.generateInvoicePDFWithRetry(invoiceId, 3)
  }

  /**
   * Generate PDF invoice with retry logic for better reliability
   */
  private async generateInvoicePDFWithRetry(invoiceId: string, maxRetries: number): Promise<PDFGenerationResult> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📄 Generating PDF for invoice ${invoiceId} (attempt ${attempt}/${maxRetries})`)

        // Fetch comprehensive invoice data
        const invoiceData = await this.fetchInvoiceData(invoiceId)
        if (!invoiceData) {
          return { success: false, error: 'Invoice not found' }
        }

        // Generate PDF with timeout protection
        const pdfBuffer = await this.createPDFDocumentWithTimeout(invoiceData, 30000) // 30 second timeout
        const fileName = `invoice-${invoiceData.invoiceNumber}.pdf`

        console.log(`✅ PDF generated successfully for invoice ${invoiceData.invoiceNumber} (${(pdfBuffer.length / 1024).toFixed(1)} KB)`)

        return {
          success: true,
          pdfBuffer,
          fileName
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        console.error(`❌ PDF generation attempt ${attempt} failed for invoice ${invoiceId}:`, lastError.message)

        // If this is not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000) // Exponential backoff, max 5 seconds
          console.log(`⏳ Retrying in ${delay}ms...`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    return {
      success: false,
      error: `Failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`
    }
  }

  /**
   * Create PDF document with timeout protection
   */
  private async createPDFDocumentWithTimeout(data: InvoiceData, timeoutMs: number): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`PDF generation timeout after ${timeoutMs}ms`))
      }, timeoutMs)

      try {
        const pdfBuffer = this.createPDFDocument(data)
        clearTimeout(timeout)
        resolve(pdfBuffer)
      } catch (error) {
        clearTimeout(timeout)
        reject(error)
      }
    })
  }

  /**
   * Fetch comprehensive invoice data from database
   */
  private async fetchInvoiceData(invoiceId: string): Promise<InvoiceData | null> {
    try {
      const [invoiceResult] = await db.select({
        // Invoice fields
        id: billingInvoices.id,
        invoiceNumber: billingInvoices.invoiceNumber,
        amount: billingInvoices.totalAmount,
        status: billingInvoices.status,
        issuedDate: billingInvoices.issuedDate,
        dueDate: billingInvoices.dueDate,
        paidDate: billingInvoices.paidDate,
        subscriptionId: billingInvoices.subscriptionId,
        clientId: billingInvoices.clientId,
        
        // Client fields
        schoolName: clients.schoolName,
        schoolCode: clients.schoolCode,
        email: clients.email,
        phone: clients.phone,
        address: clients.address,
        contactPerson: clients.contactPerson
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .where(eq(billingInvoices.id, invoiceId))
      .limit(1)

      if (!invoiceResult) {
        return null
      }

      // Fetch billing cycle details if available
      let billingCycleData: any = null
      let subscriptionData: any = null

      if (invoiceResult.subscriptionId) {
        const [cycleResult] = await db.select({
          currentPeriodStart: billingSubscriptions.currentPeriodStart,
          currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
          studentCount: billingSubscriptions.studentCount,
          monthlyAmount: billingSubscriptions.monthlyAmount,
          id: billingSubscriptions.id
        })
        .from(billingSubscriptions)
        .where(eq(billingSubscriptions.id, invoiceResult.subscriptionId))
        .limit(1)

        if (cycleResult) {
          billingCycleData = cycleResult

          // Fetch subscription details
          if (cycleResult.id) {
            const [subResult] = await db.select({
              planName: subscriptions.planName,
              pricePerStudent: subscriptions.pricePerStudent,
              billingCycle: subscriptions.billingCycle,
              studentCount: subscriptions.studentCount
            })
            .from(subscriptions)
            .where(eq(subscriptions.id, cycleResult.id))
            .limit(1)

            if (subResult) {
              subscriptionData = subResult
            }
          }
        }
      }

      // Fetch payment details if invoice is paid
      let paymentData = null
      if (invoiceResult.status === 'paid') {
        const [paymentResult] = await db.select({
          razorpayPaymentId: billingPayments.razorpayPaymentId,
          paymentMethod: billingPayments.paymentMethod,
          createdAt: billingPayments.createdAt
        })
        .from(billingPayments)
        .where(eq(billingPayments.invoiceId, invoiceId))
        .orderBy(desc(billingPayments.createdAt))
        .limit(1)

        if (paymentResult) {
          paymentData = paymentResult
        }
      }

      // Structure the data
      const invoiceData: InvoiceData = {
        id: invoiceResult.id || '',
        invoiceNumber: invoiceResult.invoiceNumber,
        amount: invoiceResult.amount,
        status: invoiceResult.status || 'draft',
        issuedDate: invoiceResult.issuedDate,
        dueDate: invoiceResult.dueDate,
        paidDate: invoiceResult.paidDate,

        client: {
          schoolName: invoiceResult.schoolName || 'Unknown School',
          schoolCode: invoiceResult.schoolCode || 'N/A',
          email: invoiceResult.email || 'N/A',
          phone: invoiceResult.phone,
          address: invoiceResult.address,
          contactPerson: invoiceResult.contactPerson
        },

        billingCycle: (() => {
          if (billingCycleData && billingCycleData.currentPeriodStart && billingCycleData.currentPeriodEnd) {
            return {
              currentPeriodStart: billingCycleData.currentPeriodStart,
              currentPeriodEnd: billingCycleData.currentPeriodEnd,
              studentCount: billingCycleData.studentCount,
              monthlyAmount: billingCycleData.monthlyAmount,
            }
          } else {
            // Generate default billing period if not available
            const defaultPeriod = this.generateDefaultBillingPeriod(invoiceResult.issuedDate)
            return {
              currentPeriodStart: defaultPeriod.start,
              currentPeriodEnd: defaultPeriod.end,
              studentCount: billingCycleData?.studentCount || 1,
              monthlyAmount: billingCycleData?.monthlyAmount || invoiceResult.amount,
            }
          }
        })(),

        subscription: subscriptionData ? {
          planName: subscriptionData.planName || 'Basic Plan',
          pricePerStudent: subscriptionData.pricePerStudent,
          billingCycle: subscriptionData.billingCycle,
          studentCount: subscriptionData.studentCount
        } : undefined,

        payment: paymentData ? {
          razorpayPaymentId: paymentData.razorpayPaymentId,
          paymentMethod: paymentData.paymentMethod,
          createdAt: paymentData.createdAt?.toISOString() || null
        } : undefined
      }

      return invoiceData
    } catch (error) {
      console.error('Error fetching invoice data:', error)
      return null
    }
  }

  /**
   * Create PDF document from invoice data
   */
  private createPDFDocument(data: InvoiceData): Buffer {
    const doc = new jsPDF()
    
    // Set up fonts and colors
    const primaryColor = '#2563eb' // Blue
    const secondaryColor = '#64748b' // Gray
    const accentColor = '#f59e0b' // Orange
    
    let yPosition = 20

    // Header with company branding
    yPosition = this.addHeader(doc, yPosition, primaryColor)
    
    // Invoice title and number
    yPosition = this.addInvoiceTitle(doc, data, yPosition, primaryColor)
    
    // Client and company information
    yPosition = this.addClientInfo(doc, data, yPosition, secondaryColor)
    
    // Invoice details table
    yPosition = this.addInvoiceDetails(doc, data, yPosition, primaryColor, secondaryColor)
    
    // Payment information (if paid)
    if (data.payment) {
      yPosition = this.addPaymentInfo(doc, data, yPosition, secondaryColor)
    }
    
    // Footer
    this.addFooter(doc, secondaryColor)
    
    return Buffer.from(doc.output('arraybuffer'))
  }

  /**
   * Add header with company branding
   */
  private addHeader(doc: jsPDF, yPos: number, primaryColor: string): number {
    const pageWidth = doc.internal.pageSize.width

    // Header background for professional look
    doc.setFillColor('#f8fafc')
    doc.rect(0, 0, pageWidth, 50, 'F')

    // Company name with enhanced styling
    doc.setFontSize(28)
    doc.setTextColor(primaryColor)
    doc.setFont('helvetica', 'bold')
    doc.text(this.companyInfo.name, 20, yPos + 5)

    // Tagline with better positioning
    doc.setFontSize(14)
    doc.setTextColor('#475569')
    doc.setFont('helvetica', 'normal')
    doc.text(this.companyInfo.tagline, 20, yPos + 15)

    // Professional company details (right aligned)
    doc.setFontSize(10)
    doc.setTextColor('#64748b')
    doc.text(this.companyInfo.address, pageWidth - 20, yPos + 5, { align: 'right' })
    doc.text(`${this.companyInfo.city}`, pageWidth - 20, yPos + 11, { align: 'right' })
    doc.text(`Email: ${this.companyInfo.email}`, pageWidth - 20, yPos + 17, { align: 'right' })
    doc.text(`Web: ${this.companyInfo.website}`, pageWidth - 20, yPos + 23, { align: 'right' })
    doc.text(`Phone: ${this.companyInfo.phone}`, pageWidth - 20, yPos + 29, { align: 'right' })

    // Professional separator line
    doc.setDrawColor('#e2e8f0')
    doc.setLineWidth(0.5)
    doc.line(20, yPos + 40, pageWidth - 20, yPos + 40)

    return yPos + 50
  }

  /**
   * Add invoice title and number
   */
  private addInvoiceTitle(doc: jsPDF, data: InvoiceData, yPos: number, primaryColor: string): number {
    const pageWidth = doc.internal.pageSize.width

    // Invoice title
    doc.setFontSize(20)
    doc.setTextColor(primaryColor)
    doc.setFont('helvetica', 'bold')
    doc.text('INVOICE', 20, yPos)

    // Invoice number and status
    doc.setFontSize(12)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'normal')
    doc.text(`Invoice #: ${data.invoiceNumber}`, pageWidth - 20, yPos, { align: 'right' })

    // Status badge
    const status = data.status || 'draft'
    const statusColor = this.getStatusColor(status)
    doc.setFontSize(10)
    doc.setTextColor(statusColor)
    doc.setFont('helvetica', 'bold')
    doc.text(`Status: ${status.toUpperCase()}`, pageWidth - 20, yPos + 8, { align: 'right' })

    return yPos + 25
  }

  /**
   * Add client and billing information
   */
  private addClientInfo(doc: jsPDF, data: InvoiceData, yPos: number, secondaryColor: string): number {
    const pageWidth = doc.internal.pageSize.width
    const midPoint = pageWidth / 2

    // Bill To section
    doc.setFontSize(12)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'bold')
    doc.text('Bill To:', 20, yPos)

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(data.client.schoolName, 20, yPos + 8)
    doc.text(`School Code: ${data.client.schoolCode}`, 20, yPos + 16)
    doc.text(`Email: ${data.client.email}`, 20, yPos + 24)

    if (data.client.contactPerson) {
      doc.text(`Contact: ${data.client.contactPerson}`, 20, yPos + 32)
    }
    if (data.client.phone) {
      doc.text(`Phone: ${data.client.phone}`, 20, yPos + 40)
    }
    if (data.client.address) {
      const addressLines = this.wrapText(doc, data.client.address, 80)
      addressLines.forEach((line, index) => {
        doc.text(line, 20, yPos + 48 + (index * 8))
      })
    }

    // Invoice Details section (right side)
    doc.setFontSize(12)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'bold')
    doc.text('Invoice Details:', midPoint + 20, yPos)

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(`Issue Date: ${this.formatDate(data.issuedDate)}`, midPoint + 20, yPos + 8)
    doc.text(`Due Date: ${this.formatDate(data.dueDate)}`, midPoint + 20, yPos + 16)

    if (data.paidDate) {
      doc.text(`Paid Date: ${this.formatDate(data.paidDate)}`, midPoint + 20, yPos + 24)
    }

    if (data.billingCycle) {
      doc.text(`Billing Period:`, midPoint + 20, yPos + 32)
      doc.text(`${this.formatDate(data.billingCycle.currentPeriodStart)} - ${this.formatDate(data.billingCycle.currentPeriodEnd)}`, midPoint + 20, yPos + 40)
    }

    return yPos + 70
  }

  /**
   * Add invoice details table
   */
  private addInvoiceDetails(doc: jsPDF, data: InvoiceData, yPos: number, primaryColor: string, secondaryColor: string): number {
    const pageWidth = doc.internal.pageSize.width
    const tableWidth = pageWidth - 40
    const startX = 20

    // Table header
    doc.setFillColor(primaryColor)
    doc.rect(startX, yPos, tableWidth, 12, 'F')

    doc.setFontSize(10)
    doc.setTextColor('#ffffff')
    doc.setFont('helvetica', 'bold')
    doc.text('Description', startX + 5, yPos + 8)
    doc.text('Quantity', startX + 100, yPos + 8)
    doc.text('Rate (₹)', startX + 130, yPos + 8)
    doc.text('Amount (₹)', startX + 160, yPos + 8, { align: 'right' })

    yPos += 12

    // Service description
    let description = 'School Management System Subscription'
    if (data.subscription) {
      description = `${data.subscription.planName} - ${data.subscription.billingCycle} subscription`
    }

    // Table row
    doc.setFillColor('#f9fafb')
    doc.rect(startX, yPos, tableWidth, 10, 'F')

    doc.setFontSize(9)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'normal')
    doc.text(description, startX + 5, yPos + 7)

    if (data.billingCycle) {
      doc.text(data.billingCycle.studentCount.toString(), startX + 100, yPos + 7)
      doc.text(`₹${parseFloat(data.subscription?.pricePerStudent || '0').toFixed(2)}`, startX + 130, yPos + 7)
    } else {
      doc.text('1', startX + 100, yPos + 7)
      doc.text(`₹${parseFloat(data.amount).toFixed(2)}`, startX + 130, yPos + 7)
    }

    doc.text(`₹${parseFloat(data.amount).toFixed(2)}`, startX + tableWidth - 5, yPos + 7, { align: 'right' })

    yPos += 10

    // Setup fee is not part of the current billing schema

    // Discount amount is not part of the current billing schema

    // Subtotal and total (no GST as company is not GST registered)
    yPos += 5

    // Subtotal
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'normal')
    doc.text('Subtotal:', startX + 120, yPos)
    doc.text(`₹${parseFloat(data.amount).toFixed(2)}`, startX + tableWidth - 5, yPos, { align: 'right' })

    yPos += 8

    // Total
    doc.setFont('helvetica', 'bold')
    doc.setFontSize(12)
    doc.text('Total Amount:', startX + 120, yPos)
    doc.text(`₹${parseFloat(data.amount).toFixed(2)}`, startX + tableWidth - 5, yPos, { align: 'right' })

    // Total box
    doc.setDrawColor(primaryColor)
    doc.setLineWidth(1)
    doc.rect(startX + 115, yPos - 5, tableWidth - 115, 12)

    return yPos + 20
  }

  /**
   * Add payment information (if paid)
   */
  private addPaymentInfo(doc: jsPDF, data: InvoiceData, yPos: number, secondaryColor: string): number {
    if (!data.payment) return yPos

    doc.setFontSize(12)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'bold')
    doc.text('Payment Information:', 20, yPos)

    yPos += 10

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')

    if (data.payment.razorpayPaymentId) {
      doc.text(`Payment ID: ${data.payment.razorpayPaymentId}`, 20, yPos)
      yPos += 8
    }

    if (data.payment.paymentMethod) {
      doc.text(`Payment Method: ${data.payment.paymentMethod.toUpperCase()}`, 20, yPos)
      yPos += 8
    }

    if (data.payment.createdAt) {
      doc.text(`Processed At: ${this.formatDateTime(data.payment.createdAt)}`, 20, yPos)
      yPos += 8
    }

    return yPos + 10
  }

  /**
   * Add footer with terms and company info
   */
  private addFooter(doc: jsPDF, secondaryColor: string): void {
    const pageHeight = doc.internal.pageSize.height
    const pageWidth = doc.internal.pageSize.width
    const footerY = pageHeight - 40

    // Horizontal line
    doc.setDrawColor('#e5e7eb')
    doc.line(20, footerY - 10, pageWidth - 20, footerY - 10)

    // Terms and conditions
    doc.setFontSize(8)
    doc.setTextColor(secondaryColor)
    doc.setFont('helvetica', 'normal')

    const terms = [
      'Terms & Conditions:',
      '• Payment is due within 15 days of invoice date',
      '• Late payment penalty: 2% per day after grace period',
      '• Service suspension after 15 days of non-payment',
      '• All amounts are in Indian Rupees (INR)'
    ]

    terms.forEach((term, index) => {
      if (index === 0) {
        doc.setFont('helvetica', 'bold')
      } else {
        doc.setFont('helvetica', 'normal')
      }
      doc.text(term, 20, footerY + (index * 6))
    })

    // Company footer info
    doc.setFont('helvetica', 'normal')
    doc.text(`Generated by ${this.companyInfo.name} | ${this.companyInfo.email}`, pageWidth - 20, pageHeight - 10, { align: 'right' })
    doc.text(`${this.companyInfo.website} | ${this.companyInfo.phone}`, pageWidth - 20, pageHeight - 4, { align: 'right' })
  }

  /**
   * Utility methods
   */
  private getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'paid': return '#059669'
      case 'overdue': return '#dc2626'
      case 'sent': return '#2563eb'
      case 'draft': return '#64748b'
      default: return '#64748b'
    }
  }

  private formatDate(dateString: string | null | undefined): string {
    if (!dateString) {
      return 'Not Available'
    }

    const date = new Date(dateString)
    if (isNaN(date.getTime())) {
      return 'Invalid Date'
    }

    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  private formatDateTime(dateString: string | null | undefined): string {
    if (!dateString) {
      return 'Not Available'
    }

    const date = new Date(dateString)
    if (isNaN(date.getTime())) {
      return 'Invalid Date'
    }

    return date.toLocaleString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  /**
   * Calculate next billing date with proper 30-day cycle handling
   * Handles month-end edge cases properly
   */
  private calculateNextBillingDate(currentDate: Date): Date {
    const nextDate = new Date(currentDate)
    nextDate.setDate(nextDate.getDate() + 30)
    return nextDate
  }

  /**
   * Calculate billing period end date from start date
   */
  private calculatePeriodEndDate(startDate: Date): Date {
    const endDate = new Date(startDate)
    endDate.setDate(endDate.getDate() + 29) // 30-day period (inclusive)
    return endDate
  }

  /**
   * Generate default billing period if not available in database
   */
  private generateDefaultBillingPeriod(invoiceDate: string): { start: string, end: string } {
    const invoiceDateTime = new Date(invoiceDate)
    if (isNaN(invoiceDateTime.getTime())) {
      // Fallback to current date if invoice date is invalid
      invoiceDateTime.setTime(Date.now())
    }

    // Calculate period start (beginning of current month)
    const periodStart = new Date(invoiceDateTime.getFullYear(), invoiceDateTime.getMonth(), 1)

    // Calculate period end (30 days from start)
    const periodEnd = this.calculatePeriodEndDate(periodStart)

    return {
      start: periodStart.toISOString().split('T')[0],
      end: periodEnd.toISOString().split('T')[0]
    }
  }

  private wrapText(doc: jsPDF, text: string, maxWidth: number): string[] {
    const words = text.split(' ')
    const lines: string[] = []
    let currentLine = ''

    words.forEach(word => {
      const testLine = currentLine + (currentLine ? ' ' : '') + word
      const textWidth = doc.getTextWidth(testLine)

      if (textWidth > maxWidth && currentLine) {
        lines.push(currentLine)
        currentLine = word
      } else {
        currentLine = testLine
      }
    })

    if (currentLine) {
      lines.push(currentLine)
    }

    return lines
  }

  /**
   * Generate ZIP file containing multiple invoice PDFs
   */
  async generateBulkInvoicePDFs(invoiceIds: string[]): Promise<BulkPDFResult> {
    try {
      if (invoiceIds.length === 0) {
        return { success: false, error: 'No invoice IDs provided' }
      }

      if (invoiceIds.length > 50) {
        return { success: false, error: 'Maximum 50 invoices allowed per bulk operation' }
      }

      console.log(`🔄 Starting bulk PDF generation for ${invoiceIds.length} billingInvoices...`)

      // Process invoices in parallel batches for better performance
      const batchSize = 5 // Process 5 invoices at a time to avoid overwhelming the system
      const pdfResults: InvoicePDFInfo[] = []
      const errors: string[] = []
      let successCount = 0
      let failedCount = 0

      for (let i = 0; i < invoiceIds.length; i += batchSize) {
        const batch = invoiceIds.slice(i, i + batchSize)
        console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(invoiceIds.length / batchSize)} (${batch.length} invoices)`)

        // Process batch in parallel
        const batchPromises = batch.map(async (invoiceId) => {
          try {
            // Get invoice basic info first
            const [invoiceInfo] = await db.select({
              id: billingInvoices.id,
              invoiceNumber: billingInvoices.invoiceNumber,
              schoolName: clients.schoolName
            })
            .from(billingInvoices)
            .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
            .where(eq(billingInvoices.id, invoiceId))
            .limit(1)

            if (!invoiceInfo) {
              return {
                invoiceId,
                invoiceNumber: 'Unknown',
                schoolName: 'Unknown',
                success: false,
                error: 'Invoice not found'
              }
            }

            // Generate PDF with timeout
            const pdfResult = await this.generateInvoicePDFWithRetry(invoiceId, 2) // Reduced retries for bulk operations

            if (pdfResult.success && pdfResult.pdfBuffer) {
              return {
                invoiceId: invoiceInfo.id,
                invoiceNumber: invoiceInfo.invoiceNumber,
                schoolName: invoiceInfo.schoolName || 'Unknown School',
                success: true,
                pdfBuffer: pdfResult.pdfBuffer,
                fileName: pdfResult.fileName || `invoice-${invoiceInfo.invoiceNumber}.pdf`
              }
            } else {
              return {
                invoiceId: invoiceInfo.id,
                invoiceNumber: invoiceInfo.invoiceNumber,
                schoolName: invoiceInfo.schoolName || 'Unknown School',
                success: false,
                error: pdfResult.error || 'Unknown error'
              }
            }
          } catch (error) {
            return {
              invoiceId,
              invoiceNumber: 'Unknown',
              schoolName: 'Unknown',
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        })

        // Wait for batch to complete
        const batchResults = await Promise.all(batchPromises)

        // Process results
        batchResults.forEach(result => {
          pdfResults.push(result)
          if (result.success) {
            successCount++
          } else {
            failedCount++
            errors.push(`Invoice ${result.invoiceNumber}: ${result.error}`)
          }
        })

        // Small delay between batches to prevent overwhelming the system
        if (i + batchSize < invoiceIds.length) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      console.log(`📊 Bulk PDF generation completed: ${successCount} success, ${failedCount} failed`)

      // If no PDFs were generated successfully, return error
      if (successCount === 0) {
        return {
          success: false,
          error: 'No PDFs could be generated',
          generatedCount: 0,
          failedCount,
          errors
        }
      }

      // Create ZIP file with successful PDFs
      const zipResult = await this.createZipFromPDFs(pdfResults.filter(r => r.success))

      if (!zipResult.success) {
        return {
          success: false,
          error: zipResult.error || 'Failed to create ZIP file',
          generatedCount: successCount,
          failedCount,
          errors
        }
      }

      return {
        success: true,
        zipBuffer: zipResult.zipBuffer,
        fileName: zipResult.fileName,
        generatedCount: successCount,
        failedCount,
        errors: errors.length > 0 ? errors : undefined
      }

    } catch (error) {
      console.error('Error in bulk PDF generation:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in bulk PDF generation'
      }
    }
  }

  /**
   * Create ZIP file from PDF buffers
   */
  private async createZipFromPDFs(pdfInfos: InvoicePDFInfo[]): Promise<{ success: boolean; zipBuffer?: Buffer; fileName?: string; error?: string }> {
    try {
      const zip = new JSZip()
      const timestamp = new Date().toISOString().slice(0, 10) // YYYY-MM-DD format

      // Group PDFs by school to organize in folders
      const schoolGroups: { [schoolName: string]: InvoicePDFInfo[] } = {}

      pdfInfos.forEach(pdfInfo => {
        const schoolName = this.sanitizeFileName(pdfInfo.schoolName)
        if (!schoolGroups[schoolName]) {
          schoolGroups[schoolName] = []
        }
        schoolGroups[schoolName].push(pdfInfo)
      })

      // Add PDFs to ZIP, organized by school folders
      for (const [schoolName, schoolPdfs] of Object.entries(schoolGroups)) {
        const schoolFolder = zip.folder(schoolName)

        if (schoolFolder) {
          for (const pdfInfo of schoolPdfs) {
            if (pdfInfo.pdfBuffer && pdfInfo.fileName) {
              const sanitizedFileName = this.sanitizeFileName(pdfInfo.fileName)
              schoolFolder.file(sanitizedFileName, pdfInfo.pdfBuffer)
            }
          }
        }
      }

      // Add summary file
      const summary = this.generateBulkSummary(pdfInfos, timestamp)
      zip.file('INVOICE_SUMMARY.txt', summary)

      // Generate ZIP buffer
      const zipBuffer = await zip.generateAsync({
        type: 'nodebuffer',
        compression: 'DEFLATE',
        compressionOptions: { level: 6 }
      })

      const fileName = `invoices-bulk-${timestamp}.zip`

      console.log(`📦 ZIP file created: ${fileName} (${(zipBuffer.length / 1024 / 1024).toFixed(2)} MB)`)

      return {
        success: true,
        zipBuffer,
        fileName
      }

    } catch (error) {
      console.error('Error creating ZIP file:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error creating ZIP file'
      }
    }
  }

  /**
   * Generate summary text for bulk download
   */
  private generateBulkSummary(pdfInfos: InvoicePDFInfo[], timestamp: string): string {
    const successful = pdfInfos.filter(p => p.success)
    const failed = pdfInfos.filter(p => !p.success)

    let summary = `SCHOPIO BULK INVOICE DOWNLOAD SUMMARY\n`
    summary += `Generated on: ${new Date().toLocaleString('en-IN')}\n`
    summary += `Timestamp: ${timestamp}\n`
    summary += `\n`
    summary += `STATISTICS:\n`
    summary += `Total Invoices Requested: ${pdfInfos.length}\n`
    summary += `Successfully Generated: ${successful.length}\n`
    summary += `Failed: ${failed.length}\n`
    summary += `\n`

    if (successful.length > 0) {
      summary += `SUCCESSFUL INVOICES:\n`
      summary += `${'School Name'.padEnd(30)} | ${'Invoice Number'.padEnd(15)} | File Name\n`
      summary += `${'-'.repeat(80)}\n`

      successful.forEach(pdf => {
        summary += `${pdf.schoolName.padEnd(30)} | ${pdf.invoiceNumber.padEnd(15)} | ${pdf.fileName}\n`
      })
      summary += `\n`
    }

    if (failed.length > 0) {
      summary += `FAILED INVOICES:\n`
      summary += `${'Invoice ID'.padEnd(36)} | ${'Invoice Number'.padEnd(15)} | Error\n`
      summary += `${'-'.repeat(80)}\n`

      failed.forEach(pdf => {
        summary += `${pdf.invoiceId.padEnd(36)} | ${pdf.invoiceNumber.padEnd(15)} | ${pdf.error || 'Unknown error'}\n`
      })
      summary += `\n`
    }

    summary += `\nGenerated by Schopio School Management System\n`
    summary += `For support, contact: <EMAIL>\n`

    return summary
  }

  /**
   * Sanitize filename for safe file system usage
   */
  private sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[<>:"/\\|?*]/g, '_') // Replace invalid characters
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .replace(/_+/g, '_') // Replace multiple underscores with single
      .trim()
  }

  /**
   * Generate merged PDF containing multiple invoices in a single document
   */
  async generateMergedInvoicePDF(invoiceIds: string[]): Promise<PDFGenerationResult> {
    try {
      if (invoiceIds.length === 0) {
        return { success: false, error: 'No invoice IDs provided' }
      }

      if (invoiceIds.length > 20) {
        return { success: false, error: 'Maximum 20 invoices allowed for merged PDF' }
      }

      console.log(`📑 Generating merged PDF for ${invoiceIds.length} billingInvoices...`)

      // Fetch all invoice data
      const invoiceDataList: InvoiceData[] = []
      const errors: string[] = []

      for (const invoiceId of invoiceIds) {
        try {
          const invoiceData = await this.fetchInvoiceData(invoiceId)
          if (invoiceData) {
            invoiceDataList.push(invoiceData)
          } else {
            errors.push(`Invoice ${invoiceId} not found`)
          }
        } catch (error) {
          errors.push(`Error fetching invoice ${invoiceId}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }

      if (invoiceDataList.length === 0) {
        return {
          success: false,
          error: `No valid invoices found. Errors: ${errors.join(', ')}`
        }
      }

      // Create merged PDF document
      const mergedPdfBuffer = this.createMergedPDFDocument(invoiceDataList)
      const timestamp = new Date().toISOString().slice(0, 10)
      const fileName = `invoices-merged-${timestamp}.pdf`

      console.log(`✅ Merged PDF generated: ${fileName} (${(mergedPdfBuffer.length / 1024).toFixed(1)} KB)`)

      return {
        success: true,
        pdfBuffer: mergedPdfBuffer,
        fileName
      }

    } catch (error) {
      console.error('Error generating merged PDF:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in merged PDF generation'
      }
    }
  }

  /**
   * Create merged PDF document from multiple invoice data
   */
  private createMergedPDFDocument(invoiceDataList: InvoiceData[]): Buffer {
    const doc = new jsPDF()

    // Set up fonts and colors
    const primaryColor = '#2563eb' // Blue
    const secondaryColor = '#64748b' // Gray

    // Add cover page
    this.addMergedCoverPage(doc, invoiceDataList, primaryColor, secondaryColor)

    // Add each invoice on separate pages
    invoiceDataList.forEach((invoiceData, index) => {
      if (index > 0) {
        doc.addPage() // Add new page for each invoice after the first
      } else {
        doc.addPage() // Add page after cover page
      }

      let yPosition = 20

      // Header with company branding
      yPosition = this.addHeader(doc, yPosition, primaryColor)

      // Invoice title and number
      yPosition = this.addInvoiceTitle(doc, invoiceData, yPosition, primaryColor)

      // Client and company information
      yPosition = this.addClientInfo(doc, invoiceData, yPosition, secondaryColor)

      // Invoice details table
      yPosition = this.addInvoiceDetails(doc, invoiceData, yPosition, primaryColor, secondaryColor)

      // Payment information (if paid)
      if (invoiceData.payment) {
        yPosition = this.addPaymentInfo(doc, invoiceData, yPosition, secondaryColor)
      }

      // Footer
      this.addFooter(doc, secondaryColor)
    })

    return Buffer.from(doc.output('arraybuffer'))
  }

  /**
   * Add cover page for merged PDF
   */
  private addMergedCoverPage(doc: jsPDF, invoiceDataList: InvoiceData[], primaryColor: string, secondaryColor: string): void {
    const pageWidth = doc.internal.pageSize.width
    const pageHeight = doc.internal.pageSize.height

    // Title
    doc.setFontSize(28)
    doc.setTextColor(primaryColor)
    doc.setFont('helvetica', 'bold')
    doc.text('INVOICE COLLECTION', pageWidth / 2, 60, { align: 'center' })

    // Subtitle
    doc.setFontSize(16)
    doc.setTextColor(secondaryColor)
    doc.setFont('helvetica', 'normal')
    doc.text(`${invoiceDataList.length} Invoices`, pageWidth / 2, 80, { align: 'center' })

    // Generation info
    doc.setFontSize(12)
    doc.text(`Generated on: ${new Date().toLocaleDateString('en-IN')}`, pageWidth / 2, 100, { align: 'center' })

    // Invoice summary table
    let yPos = 130
    doc.setFontSize(14)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'bold')
    doc.text('Invoice Summary:', 20, yPos)

    yPos += 20

    // Table headers
    doc.setFontSize(10)
    doc.setFont('helvetica', 'bold')
    doc.text('Invoice Number', 20, yPos)
    doc.text('School Name', 80, yPos)
    doc.text('Amount', 140, yPos)
    doc.text('Status', 170, yPos)

    // Horizontal line
    doc.setDrawColor('#e5e7eb')
    doc.line(20, yPos + 5, pageWidth - 20, yPos + 5)

    yPos += 15

    // Invoice list
    doc.setFont('helvetica', 'normal')
    let totalAmount = 0

    invoiceDataList.forEach((invoice, index) => {
      if (yPos > pageHeight - 50) {
        doc.addPage()
        yPos = 30
      }

      doc.text(invoice.invoiceNumber, 20, yPos)
      doc.text(invoice.client.schoolName.substring(0, 25), 80, yPos)
      doc.text(`₹${parseFloat(invoice.amount).toLocaleString('en-IN')}`, 140, yPos)
      doc.text((invoice.status || 'draft').toUpperCase(), 170, yPos)

      totalAmount += parseFloat(invoice.amount)
      yPos += 12
    })

    // Total
    yPos += 10
    doc.setDrawColor('#e5e7eb')
    doc.line(20, yPos - 5, pageWidth - 20, yPos - 5)

    doc.setFont('helvetica', 'bold')
    doc.text('TOTAL AMOUNT:', 80, yPos)
    doc.text(`₹${totalAmount.toLocaleString('en-IN')}`, 140, yPos)

    // Footer
    doc.setFontSize(8)
    doc.setTextColor(secondaryColor)
    doc.setFont('helvetica', 'normal')
    doc.text('Generated by Schopio School Management System', pageWidth / 2, pageHeight - 20, { align: 'center' })
  }
}

// Export singleton instance
export const pdfInvoiceService = new PDFInvoiceService()
