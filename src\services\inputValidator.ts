import { z } from 'zod'
import DOMPurify from 'isomorphic-dompurify'
import validator from 'validator'

export interface ValidationResult {
  isValid: boolean
  sanitizedData?: any
  errors?: string[]
  securityIssues?: string[]
}

export interface SecurityValidationOptions {
  allowHtml?: boolean
  maxLength?: number
  allowedTags?: string[]
  allowedAttributes?: string[]
  checkSqlInjection?: boolean
  checkXss?: boolean
  checkPathTraversal?: boolean
  checkCommandInjection?: boolean
}

class InputValidator {
  private static instance: InputValidator
  private suspiciousPatterns: RegExp[] = []
  private sqlInjectionPatterns: RegExp[] = []
  private xssPatterns: RegExp[] = []
  private pathTraversalPatterns: RegExp[] = []
  private commandInjectionPatterns: RegExp[] = []

  private constructor() {
    this.setupSecurityPatterns()
  }

  static getInstance(): InputValidator {
    if (!InputValidator.instance) {
      InputValidator.instance = new InputValidator()
    }
    return InputValidator.instance
  }

  private setupSecurityPatterns(): void {
    // SQL Injection patterns
    this.sqlInjectionPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
      /((\%27)|(\')|((\%3D)|(=))[^\n]*((\%27)|(\')|((\%3D)|(=))))/gi,
      /((\%27)|(\'))union/gi,
      /(((\%27)|(\'))|((\%3D)|(=)))[^\n]*((\%27)|(\')|((\%3D)|(=)))/gi,
      /((\%27)|(\'))|((\%3D)|(=))[^\n]*((\%27)|(\')|((\%3D)|(=)))/gi,
      /exec(\s|\+)+(s|x)p\w+/gi,
      /UNION(?:\s+ALL)?\s+SELECT/gi
    ]

    // XSS patterns
    this.xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<img[^>]+src[^>]*>/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
      /<link[^>]*>/gi,
      /<meta[^>]*>/gi
    ]

    // Path traversal patterns
    this.pathTraversalPatterns = [
      /\.\.\//g,
      /\.\.\\/g,
      /%2e%2e%2f/gi,
      /%2e%2e%5c/gi,
      /\.\.%2f/gi,
      /\.\.%5c/gi,
      /%2e%2e\//gi,
      /%2e%2e\\/gi
    ]

    // Command injection patterns
    this.commandInjectionPatterns = [
      /[;&|`$(){}[\]]/g,
      /\b(cat|ls|pwd|id|whoami|uname|ps|netstat|ifconfig|ping|wget|curl|nc|telnet|ssh|ftp|chmod|chown|rm|mv|cp|mkdir|rmdir)\b/gi,
      /(\||;|&|`|\$\(|\$\{)/g
    ]

    // General suspicious patterns
    this.suspiciousPatterns = [
      ...this.sqlInjectionPatterns,
      ...this.xssPatterns,
      ...this.pathTraversalPatterns,
      ...this.commandInjectionPatterns
    ]
  }

  /**
   * Comprehensive input validation and sanitization
   */
  validateAndSanitize(
    input: any,
    schema: z.ZodSchema,
    options: SecurityValidationOptions = {}
  ): ValidationResult {
    const errors: string[] = []
    const securityIssues: string[] = []

    try {
      // First, validate with Zod schema
      const zodResult = schema.safeParse(input)
      if (!zodResult.success) {
        zodResult.error.errors.forEach(err => {
          errors.push(`${err.path.join('.')}: ${err.message}`)
        })
        return { isValid: false, errors }
      }

      let sanitizedData = zodResult.data

      // Apply security validation and sanitization
      sanitizedData = this.applySanitization(sanitizedData, options, securityIssues)

      return {
        isValid: securityIssues.length === 0,
        sanitizedData,
        errors: errors.length > 0 ? errors : undefined,
        securityIssues: securityIssues.length > 0 ? securityIssues : undefined
      }

    } catch (error) {
      errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return { isValid: false, errors }
    }
  }

  /**
   * Apply sanitization to data
   */
  private applySanitization(
    data: any,
    options: SecurityValidationOptions,
    securityIssues: string[]
  ): any {
    if (typeof data === 'string') {
      return this.sanitizeString(data, options, securityIssues)
    }

    if (Array.isArray(data)) {
      return data.map(item => this.applySanitization(item, options, securityIssues))
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(data)) {
        // Sanitize the key as well
        const sanitizedKey = this.sanitizeString(key, { ...options, allowHtml: false }, securityIssues)
        sanitized[sanitizedKey] = this.applySanitization(value, options, securityIssues)
      }
      return sanitized
    }

    return data
  }

  /**
   * Sanitize string input
   */
  private sanitizeString(
    input: string,
    options: SecurityValidationOptions,
    securityIssues: string[]
  ): string {
    let sanitized = input

    // Check length
    if (options.maxLength && sanitized.length > options.maxLength) {
      securityIssues.push(`Input exceeds maximum length of ${options.maxLength}`)
      sanitized = sanitized.substring(0, options.maxLength)
    }

    // Check for SQL injection
    if (options.checkSqlInjection !== false) {
      for (const pattern of this.sqlInjectionPatterns) {
        if (pattern.test(sanitized)) {
          securityIssues.push('Potential SQL injection detected')
          break
        }
      }
    }

    // Check for XSS
    if (options.checkXss !== false) {
      for (const pattern of this.xssPatterns) {
        if (pattern.test(sanitized)) {
          securityIssues.push('Potential XSS attack detected')
          break
        }
      }
    }

    // Check for path traversal
    if (options.checkPathTraversal !== false) {
      for (const pattern of this.pathTraversalPatterns) {
        if (pattern.test(sanitized)) {
          securityIssues.push('Potential path traversal attack detected')
          break
        }
      }
    }

    // Check for command injection
    if (options.checkCommandInjection !== false) {
      for (const pattern of this.commandInjectionPatterns) {
        if (pattern.test(sanitized)) {
          securityIssues.push('Potential command injection detected')
          break
        }
      }
    }

    // Sanitize HTML if needed
    if (options.allowHtml) {
      const config = {
        ALLOWED_TAGS: options.allowedTags || ['b', 'i', 'em', 'strong', 'p', 'br'],
        ALLOWED_ATTR: options.allowedAttributes || ['class'],
        KEEP_CONTENT: true,
        RETURN_DOM: false,
        RETURN_DOM_FRAGMENT: false,
        RETURN_DOM_IMPORT: false
      }
      sanitized = DOMPurify.sanitize(sanitized, config)
    } else {
      // Strip all HTML tags
      sanitized = sanitized.replace(/<[^>]*>/g, '')
    }

    // Additional sanitization
    sanitized = this.additionalSanitization(sanitized)

    return sanitized
  }

  /**
   * Additional sanitization steps
   */
  private additionalSanitization(input: string): string {
    let sanitized = input

    // Normalize whitespace
    sanitized = sanitized.replace(/\s+/g, ' ').trim()

    // Remove null bytes
    sanitized = sanitized.replace(/\0/g, '')

    // Escape special characters for safety
    sanitized = validator.escape(sanitized)

    return sanitized
  }

  /**
   * Validate email with enhanced security
   */
  validateEmail(email: string): ValidationResult {
    const errors: string[] = []
    const securityIssues: string[] = []

    // Basic email validation
    if (!validator.isEmail(email)) {
      errors.push('Invalid email format')
    }

    // Check for suspicious patterns
    if (this.containsSuspiciousPatterns(email)) {
      securityIssues.push('Email contains suspicious patterns')
    }

    // Check email length
    if (email.length > 254) {
      securityIssues.push('Email exceeds maximum length')
    }

    // Normalize email
    const sanitizedEmail = validator.normalizeEmail(email, {
      gmail_lowercase: true,
      gmail_remove_dots: false,
      gmail_remove_subaddress: false,
      outlookdotcom_lowercase: true,
      outlookdotcom_remove_subaddress: false,
      yahoo_lowercase: true,
      yahoo_remove_subaddress: false,
      icloud_lowercase: true,
      icloud_remove_subaddress: false
    })

    return {
      isValid: errors.length === 0 && securityIssues.length === 0,
      sanitizedData: sanitizedEmail || email,
      errors: errors.length > 0 ? errors : undefined,
      securityIssues: securityIssues.length > 0 ? securityIssues : undefined
    }
  }

  /**
   * Validate phone number
   */
  validatePhone(phone: string, locale: string = 'IN'): ValidationResult {
    const errors: string[] = []
    const securityIssues: string[] = []

    // Remove all non-digit characters for validation
    const cleanPhone = phone.replace(/\D/g, '')

    // Basic phone validation
    if (!validator.isMobilePhone(cleanPhone, locale as any)) {
      errors.push('Invalid phone number format')
    }

    // Check for suspicious patterns
    if (this.containsSuspiciousPatterns(phone)) {
      securityIssues.push('Phone number contains suspicious patterns')
    }

    return {
      isValid: errors.length === 0 && securityIssues.length === 0,
      sanitizedData: cleanPhone,
      errors: errors.length > 0 ? errors : undefined,
      securityIssues: securityIssues.length > 0 ? securityIssues : undefined
    }
  }

  /**
   * Validate URL
   */
  validateUrl(url: string): ValidationResult {
    const errors: string[] = []
    const securityIssues: string[] = []

    // Basic URL validation
    if (!validator.isURL(url, {
      protocols: ['http', 'https'],
      require_protocol: true,
      require_host: true,
      require_valid_protocol: true,
      allow_underscores: false,
      allow_trailing_dot: false,
      allow_protocol_relative_urls: false
    })) {
      errors.push('Invalid URL format')
    }

    // Check for suspicious patterns
    if (this.containsSuspiciousPatterns(url)) {
      securityIssues.push('URL contains suspicious patterns')
    }

    // Check for localhost/private IPs in production
    if (process.env.NODE_ENV === 'production') {
      const suspiciousHosts = ['localhost', '127.0.0.1', '0.0.0.0', '::1']
      const urlObj = new URL(url)
      if (suspiciousHosts.some(host => urlObj.hostname.includes(host))) {
        securityIssues.push('URL points to localhost or private IP')
      }
    }

    return {
      isValid: errors.length === 0 && securityIssues.length === 0,
      sanitizedData: url,
      errors: errors.length > 0 ? errors : undefined,
      securityIssues: securityIssues.length > 0 ? securityIssues : undefined
    }
  }

  /**
   * Check if input contains suspicious patterns
   */
  private containsSuspiciousPatterns(input: string): boolean {
    return this.suspiciousPatterns.some(pattern => pattern.test(input))
  }

  /**
   * Create enhanced Zod schema with security validation
   */
  createSecureSchema<T>(baseSchema: z.ZodSchema<T>, options: SecurityValidationOptions = {}) {
    return baseSchema.refine(
      (data) => {
        const result = this.validateAndSanitize(data, baseSchema, options)
        return result.isValid
      },
      {
        message: 'Input contains security violations or invalid data'
      }
    )
  }

  /**
   * Middleware for request validation
   */
  createValidationMiddleware(schema: z.ZodSchema, options: SecurityValidationOptions = {}) {
    return async (c: any, next: () => Promise<void>) => {
      try {
        const body = await c.req.json()
        const result = this.validateAndSanitize(body, schema, options)

        if (!result.isValid) {
          return c.json({
            error: 'Validation failed',
            details: result.errors,
            securityIssues: result.securityIssues
          }, 400)
        }

        // Replace request body with sanitized data
        c.req.body = result.sanitizedData
        await next()

      } catch (error) {
        return c.json({
          error: 'Invalid request format',
          message: error instanceof Error ? error.message : 'Unknown error'
        }, 400)
      }
    }
  }
}

export const inputValidator = InputValidator.getInstance()
