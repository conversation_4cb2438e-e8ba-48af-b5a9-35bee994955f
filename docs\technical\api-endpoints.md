# 🚀 Schopio API Documentation - Complete Reference

## 📋 API Architecture Overview

**Framework**: Hono.js with method chaining
**Authentication**: Multi-role JWT system
**Database**: Neon PostgreSQL with Drizzle ORM
**Validation**: Zod schemas with @hono/zod-validator
**Base URL**: `/api`

## 🔐 Multi-Role Authentication System

### **Authentication Middleware Types**
1. **School Authentication**: `schoolAuthMiddleware`
2. **Admin Authentication**: `adminAuthMiddleware`
3. **Partner Authentication**: `partnerAuthMiddleware`

### **JWT Token Structure**
```typescript
// School User Token
{
  userId: string,
  email: string,
  clientId: string,
  role: 'admin' | 'billing' | 'viewer' | 'support',
  type: 'school'
}

// Admin User Token
{
  userId: string,
  email: string,
  role: 'super_admin' | 'sales' | 'support' | 'billing',
  permissions: string[],
  type: 'admin'
}

// Partner User Token
{
  userId: string,
  email: string,
  partnerCode: string,
  type: 'partner'
}
```

### **Authorization Header Format**
```
Authorization: Bearer <jwt_token>
```

## 📊 Standardized Response Format

### **Success Response**
```typescript
{
  success: true,
  data: any,
  message?: string,
  pagination?: {
    page: number,
    limit: number,
    total: number,
    totalPages: number
  }
}
```

### **Error Response**
```typescript
{
  success: false,
  error: {
    code: string,
    message: string,
    details?: any
  },
  timestamp: string,
  path: string
}
```

### **HTTP Status Codes**
- `200` - Success
- `201` - Created
- `400` - Bad Request / Validation Error
- `401` - Unauthorized / Invalid Token
- `403` - Forbidden / Insufficient Permissions
- `404` - Not Found
- `409` - Conflict / Duplicate Resource
- `500` - Internal Server Error

---

## 🌐 Public API Endpoints (No Authentication)

### **Lead Management**

#### `POST /api/leads`
Create new lead from contact form.

**Request Body:**
```typescript
{
  email: string,
  schoolName: string,
  contactPerson: string,
  phone?: string,
  estimatedStudents: number,
  source: 'landing_page' | 'referral' | 'demo_request',
  message?: string
}
```

**Response:** `201 Created`
```typescript
{
  success: true,
  data: {
    id: string,
    email: string,
    schoolName: string,
    contactPerson: string,
    status: 'new'
  },
  message: "Lead created successfully"
}
```

#### `GET /api/leads`
Get all leads with filtering (public access for admin tools).

**Query Parameters:**
- `page?: number` (default: 1)
- `limit?: number` (default: 20, max: 100)
- `status?: string`
- `source?: string`
- `search?: string`
- `dateFrom?: string` (ISO date)
- `dateTo?: string` (ISO date)
- `sortBy?: 'createdAt' | 'updatedAt' | 'contactPerson' | 'estimatedStudents'`
- `sortOrder?: 'asc' | 'desc'`

#### `PUT /api/leads/:id`
Update lead information.

**Request Body:**
```typescript
{
  status?: 'new' | 'contacted' | 'qualified' | 'demo_scheduled' | 'converted' | 'lost',
  notes?: string,
  estimatedStudents?: number,
  followUpDate?: string
}
```

### **Demo Booking**

#### `POST /api/demo`
Schedule product demo.

**Request Body:**
```typescript
{
  leadId?: string,
  email: string,
  schoolName: string,
  contactPerson: string,
  phone?: string,
  estimatedStudents: number,
  scheduledDate: string, // ISO datetime
  demoType: 'online' | 'onsite' | 'hybrid',
  preferredTime?: string,
  requirements?: string
}
```

#### `GET /api/demo`
Get demo bookings with filtering.

#### `PUT /api/demo/:id`
Update demo booking status.

### **Pricing Calculator**

#### `GET /api/pricing/:studentCount`
Calculate pricing for student count.

**Response:**
```typescript
{
  success: true,
  data: {
    studentCount: number,
    basePrice: number,
    volumeDiscount: number,
    discountedPrice: number,
    monthlyTotal: number,
    yearlyTotal: number,
    yearlyDiscount: number,
    currency: "INR"
  }
}
```

#### `GET /api/pricing/tiers`
Get pricing tiers and volume discounts.

### **AI Chat**

#### `POST /api/ai-chat`
Process AI chatbot conversation.

**Request Body:**
```typescript
{
  message: string,
  conversationId?: string,
  context?: {
    page: string,
    userType?: string
  }
}
```

---

## 🔐 Authentication API

### **School Registration & Login**

#### `POST /api/auth/school/register`
Register new school user.

**Request Body:**
```typescript
{
  email: string,
  name: string,
  schoolName: string,
  phone?: string,
  role: 'admin' | 'billing' | 'viewer' | 'support',
  clientId: string // Must be existing client
}
```

#### `POST /api/auth/school/verify-email`
Verify email with OTP code.

**Request Body:**
```typescript
{
  email: string,
  otpCode: string
}
```

**Response:**
```typescript
{
  message: "Email verified successfully",
  token: string,
  user: {
    id: string,
    email: string,
    name: string,
    role: string
  }
}
```

#### `POST /api/auth/school/login`
School user login with OTP.

**Request Body:**
```typescript
{
  email: string
}
```

#### `GET /api/auth/profile`
Get current user profile (requires JWT).

#### `POST /api/auth/profile/resend-verification`
Resend email verification OTP.

### **Partner Authentication**

#### `POST /api/auth/partner/login`
Partner login with credentials.

**Request Body:**
```typescript
{
  email: string,
  password: string
}
```

#### `GET /api/auth/partner/profile`
Get partner profile (requires Partner JWT).

---

## 🛡️ Admin API (Requires Admin JWT)

### **Admin Authentication**

#### `POST /api/admin/auth/login`
Admin user login.

**Request Body:**
```typescript
{
  email: string,
  password: string
}
```

### **Lead Management**

#### `GET /api/admin/leads`
Get all leads with comprehensive filtering.

**Query Parameters:**
- `page?: number`, `limit?: number`
- `status?: string`, `source?: string`, `search?: string`
- `dateFrom?: string`, `dateTo?: string`
- `sortBy?: string`, `sortOrder?: 'asc' | 'desc'`

#### `POST /api/admin/leads/:id/convert`
Convert lead to client.

**Request Body:**
```typescript
{
  schoolCode: string,
  actualStudentCount: number,
  averageMonthlyFee: number,
  contactDetails: {
    email: string,
    phone: string,
    address: string
  },
  subscriptionData?: {
    planId: string,
    startDate: string
  }
}
```

### **Client Management**

#### `GET /api/admin/clients`
Get all clients with comprehensive data.

**Query Parameters:**
- `status?: string`, `page?: number`, `limit?: number`
- `search?: string`, `include_subscription?: boolean`
- `include_requests?: boolean`, `request_status?: string`
- `revenue_min?: number`, `revenue_max?: number`
- `partner_id?: string`, `has_production_request?: boolean`
- `requires_attention?: boolean`
- `sort_by?: string`, `sort_order?: 'asc' | 'desc'`

#### `GET /api/admin/clients/:id/comprehensive`
Get comprehensive client data including analytics.

**Response includes:**
- Client profile and contact details
- All software requests and status history
- Subscription and billing information
- Support tickets and communication history
- Revenue analytics and metrics
- Audit logs and system events

#### `PUT /api/admin/clients/:id`
Update client information.

### **Subscription Management**

#### `GET /api/admin/subscriptions`
Get all subscriptions with filtering.

#### `POST /api/admin/subscriptions`
Create new subscription for client.

**Request Body:**
```typescript
{
  clientId: string,
  planId: string,
  studentCount: number,
  pricePerStudent: number,
  billingCycle: 'monthly' | 'yearly',
  startDate: string,
  operationalExpenses?: {
    serverCosts: number,
    supportCosts: number,
    maintenanceCosts: number,
    otherExpenses: number
  }
}
```

#### `PUT /api/admin/subscriptions/:id`
Update subscription details.

### **Partner Management**

#### `GET /api/admin/partners`
Get all partners with filtering.

#### `POST /api/admin/partners`
Create new partner.

**Request Body:**
```typescript
{
  name: string,
  email: string,
  phone?: string,
  profitSharingPercent: number, // 35-50%
  referralCode?: string // Auto-generated if not provided
}
```

### **Support Ticket Management**

#### `GET /api/admin/support/tickets`
Get all support tickets across all clients.

#### `POST /api/admin/support/tickets/:id/assign`
Assign ticket to admin user.

#### `PUT /api/admin/support/tickets/:id`
Update ticket status and add internal notes.

### **Analytics & Reporting**

#### `GET /api/admin/analytics/dashboard`
Get comprehensive business analytics.

**Response:**
```typescript
{
  success: true,
  data: {
    overview: {
      totalClients: number,
      activeSubscriptions: number,
      monthlyRevenue: number,
      churnRate: number
    },
    leads: {
      total: number,
      conversionRate: number,
      bySource: object,
      byStatus: object
    },
    revenue: {
      monthly: number,
      yearly: number,
      growth: number,
      byClient: array
    },
    partners: {
      totalPartners: number,
      totalEarnings: number,
      topPerformers: array
    }
  }
}
```

---

## 🏫 School Portal API (Requires School JWT)

### **Dashboard & Overview**

#### `GET /api/school/health`
Health check endpoint.

#### `GET /api/school/dashboard`
Get school dashboard with subscription status and key metrics.

**Response:**
```typescript
{
  success: true,
  data: {
    school: {
      id: string,
      schoolName: string,
      actualStudentCount: number,
      averageMonthlyFee: number
    },
    subscription: {
      id: string,
      status: 'active' | 'suspended' | 'cancelled',
      studentCount: number,
      monthlyAmount: number,
      nextBillingDate: string
    },
    billing: {
      pendingInvoices: number,
      overdueAmount: number,
      lastPaymentDate: string
    },
    support: {
      openTickets: number,
      lastTicketDate: string
    }
  }
}
```

### **Student Management**

#### `GET /api/school/students`
Get student count and management information.

#### `PUT /api/school/students/count`
Update actual student count (admin role only).

**Request Body:**
```typescript
{
  actualStudentCount: number
}
```

### **Subscription Management**

#### `GET /api/school/subscription`
Get current subscription details with billing information.

### **Billing & Payments**

#### `GET /api/school/billing/invoices`
Get all invoices for the school.

**Query Parameters:**
- `page?: number`, `limit?: number`
- `status?: 'pending' | 'paid' | 'overdue' | 'cancelled'`
- `dateFrom?: string`, `dateTo?: string`

#### `GET /api/school/billing/payments`
Get payment history.

#### `POST /api/school/billing/create-order`
Create Razorpay payment order for invoice.

**Request Body:**
```typescript
{
  invoiceId: string
}
```

**Response:**
```typescript
{
  success: true,
  data: {
    orderId: string,
    amount: number,
    currency: "INR",
    key: string // Razorpay key
  }
}
```

#### `POST /api/subscriptions/create-manual-payment-order`
Create manual payment order for subscription billing.

**Request Body:**
```typescript
{
  subscriptionId: string,
  amount: number
}
```

**Response:**
```typescript
{
  success: true,
  data: {
    orderId: string,
    amount: number,
    currency: "INR",
    keyId: string,
    schoolName: string,
    description: string
  }
}
```

#### `POST /api/subscriptions/verify-manual-payment`
Verify manual payment and update subscription status.

**Request Body:**
```typescript
{
  razorpay_payment_id: string,
  razorpay_order_id: string,
  razorpay_signature: string,
  subscriptionId: string
}
```

**Response:**
```typescript
{
  success: true,
  message: "Payment verified successfully",
  data: {
    paymentId: string,
    amount: number,
    currency: "INR",
    status: "completed"
  }
}
```

### **Support System**

#### `GET /api/school/support/tickets`
Get school's support tickets.

**Query Parameters:**
- `page?: number`, `limit?: number`
- `status?: 'open' | 'in_progress' | 'resolved' | 'closed'`
- `priority?: 'low' | 'medium' | 'high' | 'urgent'`
- `category?: 'billing' | 'technical' | 'feature_request' | 'bug'`
- `search?: string`

#### `POST /api/school/support/tickets`
Create new support ticket.

**Request Body:**
```typescript
{
  subject: string,
  description: string,
  priority: 'low' | 'medium' | 'high' | 'urgent',
  category: 'billing' | 'technical' | 'feature_request' | 'bug'
}
```

#### `POST /api/school/support/tickets/:id/messages`
Add message to existing ticket.

**Request Body:**
```typescript
{
  message: string,
  attachments?: string[] // File URLs
}
```

---

## 🤝 Partner API (Requires Partner JWT)

### **Partner Dashboard**

#### `GET /api/partner/dashboard`
Get partner dashboard with performance metrics.

**Response:**
```typescript
{
  success: true,
  data: {
    partner: {
      id: string,
      name: string,
      email: string,
      referralCode: string,
      profitSharingPercent: number
    },
    performance: {
      totalClients: number,
      activeClients: number,
      totalEarnings: number,
      monthlyEarnings: number,
      conversionRate: number
    },
    earnings: {
      currentMonth: number,
      lastMonth: number,
      growth: number,
      pendingWithdrawal: number
    },
    clients: {
      recent: array, // Recent client conversions
      topPerforming: array // Highest revenue clients
    }
  }
}
```

### **Partner Analytics**

#### `GET /api/partner/analytics`
Get detailed partner analytics and trends.

**Query Parameters:**
- `period?: 'week' | 'month' | 'quarter' | 'year'`
- `startDate?: string`, `endDate?: string`

### **Support & Client Management**

#### `GET /api/partner/support/tickets`
Get support tickets for partner's clients.

#### `GET /api/partner/support/tickets/:id`
Get specific ticket details.

#### `POST /api/partner/support/tickets/:id/escalate`
Escalate client ticket to admin.

**Request Body:**
```typescript
{
  reason: string,
  priority: 'high' | 'urgent',
  internalNotes?: string
}
```

#### `POST /api/partner/support/tickets/:id/messages`
Add message to client support ticket.

### **Earnings Management**

#### `GET /api/partner/earnings`
Get detailed earnings breakdown.

**Query Parameters:**
- `page?: number`, `limit?: number`
- `period?: string`, `clientId?: string`

---

## 💳 Billing & Payment API

### **Billing Management**

#### `GET /api/billing/health`
Billing service health check.

#### `POST /api/billing/generate-invoice`
Generate invoice for billing cycle (Admin only).

**Request Body:**
```typescript
{
  billingCycleId: string,
  dueDate?: string,
  notes?: string
}
```

#### `GET /api/billing/pdf/health`
PDF generation service health check.

### **Payment Processing**

#### `POST /api/payments/create-order`
Create Razorpay payment order.

**Request Body:**
```typescript
{
  invoiceId: string,
  amount?: number // Override amount if needed
}
```

#### `POST /api/payments/verify`
Verify Razorpay payment signature.

**Request Body:**
```typescript
{
  paymentId: string,
  orderId: string,
  signature: string,
  invoiceId: string
}
```

---

## 🔗 Webhook API

### **Payment Webhooks**

#### `POST /api/webhooks/razorpay`
Handle Razorpay payment webhooks.

**Headers Required:**
- `x-razorpay-signature`: Webhook signature for verification

**Supported Events:**
- `payment.captured`
- `payment.failed`
- `subscription.charged`
- `subscription.cancelled`

#### `POST /api/webhooks/test`
Test webhook endpoint for development.

---

## ⏰ Scheduler API (Admin Only)

### **Billing Automation**

#### `GET /api/scheduler/status`
Get scheduler service status.

#### `POST /api/scheduler/test`
Test billing automation manually.

---

## 🔄 Hono.js Implementation Patterns

### **Route Registration with Method Chaining**
```typescript
// Main API router
const app = new Hono()
  .basePath('/api')
  .use('*', cors())
  .use('*', logger())
  .use('*', securityHeaders())

  // Route registration
  .route("/leads", leadsRoutes)
  .route("/demo", demoRoutes)
  .route("/pricing", pricingRoutes)
  .route("/admin", adminRoutes)
  .route("/school", schoolRoutes)
  .route("/partner", partnerRoutes)
  .route("/ai-chat", aiChatRoutes)
  .route("/auth", authRoutes)
  .route("/scheduler", schedulerRoutes)
  .route("/payments", paymentsRoutes)
  .route("/webhooks", webhooksRoutes)
  .route("/billing", billingRoutes)

  // Health check
  .get('/health', (c) => c.json({
    status: 'ok',
    timestamp: new Date(),
    version: '1.0.0'
  }))

  // Global error handling
  .onError((err, c) => {
    console.error('API Error:', err);
    return c.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal Server Error',
        timestamp: new Date().toISOString(),
        path: c.req.path
      }
    }, 500);
  });
```

### **Authentication Middleware Pattern**
```typescript
// School authentication middleware
const schoolAuthMiddleware = async (c: Context, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return c.json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Authorization token required' }
      }, 401);
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, JWT_SECRET) as SchoolJWTPayload;

    if (decoded.type !== 'school') {
      return c.json({
        success: false,
        error: { code: 'FORBIDDEN', message: 'Invalid token type' }
      }, 403);
    }

    c.set('schoolUser', decoded);
    await next();
  } catch (error) {
    return c.json({
      success: false,
      error: { code: 'INVALID_TOKEN', message: 'Invalid or expired token' }
    }, 401);
  }
};

// Role-based access control
const requireSchoolRole = (allowedRoles: string[]) =>
  async (c: Context, next: Next) => {
    const user = c.get('schoolUser');
    if (!allowedRoles.includes(user.role)) {
      return c.json({
        success: false,
        error: { code: 'INSUFFICIENT_PERMISSIONS', message: 'Access denied' }
      }, 403);
    }
    await next();
  };
```

---

## 🛡️ Security & Validation

### **Zod Validation Schemas**
```typescript
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';

// Lead creation schema
const createLeadSchema = z.object({
  email: z.string().email('Invalid email format'),
  schoolName: z.string().min(2, 'School name too short').max(255, 'School name too long'),
  contactPerson: z.string().min(2, 'Contact person name too short').max(255, 'Name too long'),
  phone: z.string().optional(),
  estimatedStudents: z.number().min(1, 'Must have at least 1 student').max(10000, 'Maximum 10,000 students'),
  source: z.enum(['landing_page', 'referral', 'demo_request']),
  message: z.string().max(1000, 'Message too long').optional()
});

// Subscription creation schema
const createSubscriptionSchema = z.object({
  clientId: z.string().uuid('Invalid client ID'),
  planId: z.string().uuid('Invalid plan ID'),
  studentCount: z.number().min(1).max(10000),
  pricePerStudent: z.number().min(0),
  billingCycle: z.enum(['monthly', 'yearly']),
  startDate: z.string().datetime('Invalid start date'),
  operationalExpenses: z.object({
    serverCosts: z.number().min(0).default(0),
    supportCosts: z.number().min(0).default(0),
    maintenanceCosts: z.number().min(0).default(0),
    otherExpenses: z.number().min(0).default(0)
  }).optional()
});

// Usage with Hono
app.post('/leads',
  zValidator('json', createLeadSchema),
  async (c) => {
    const validatedData = c.req.valid('json');
    // Process validated data
  }
);
```

### **Security Headers & Rate Limiting**
```typescript
// Security headers middleware
const securityHeaders = async (c: Context, next: Next) => {
  c.header('X-Content-Type-Options', 'nosniff');
  c.header('X-Frame-Options', 'DENY');
  c.header('X-XSS-Protection', '1; mode=block');
  c.header('Referrer-Policy', 'strict-origin-when-cross-origin');
  await next();
};

// Rate limiting implementation
const rateLimiter = (maxRequests: number, windowMs: number) =>
  async (c: Context, next: Next) => {
    const clientIP = c.req.header('x-forwarded-for') ||
                     c.req.header('x-real-ip') ||
                     'unknown';

    // Check rate limit from database/cache
    const isAllowed = await checkRateLimit(clientIP, maxRequests, windowMs);

    if (!isAllowed) {
      return c.json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests. Please try again later.',
          retryAfter: windowMs / 1000
        }
      }, 429);
    }

    await next();
  };
```

### **Data Sanitization**
```typescript
// Input sanitization
const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential XSS characters
    .substring(0, 1000); // Limit length
};

// SQL injection prevention (using Drizzle ORM)
// Drizzle automatically prevents SQL injection through parameterized queries
const getClientById = async (clientId: string) => {
  return await db
    .select()
    .from(clients)
    .where(eq(clients.id, clientId)) // Safe parameterized query
    .limit(1);
};
```

---

## 📈 Business Logic Examples

### **Pricing Calculation**
```typescript
// Volume discount calculation
const calculatePricing = (studentCount: number) => {
  const basePrice = 50; // ₹50 per student per month
  let volumeDiscount = 0;

  if (studentCount >= 1000) volumeDiscount = 0.20; // 20% discount
  else if (studentCount >= 500) volumeDiscount = 0.15; // 15% discount
  else if (studentCount >= 200) volumeDiscount = 0.10; // 10% discount
  else if (studentCount >= 100) volumeDiscount = 0.05; // 5% discount

  const discountedPrice = basePrice * (1 - volumeDiscount);
  const monthlyTotal = discountedPrice * studentCount;
  const yearlyTotal = monthlyTotal * 10; // 2 months free for yearly

  return {
    studentCount,
    basePrice,
    volumeDiscount: volumeDiscount * 100,
    discountedPrice: Math.round(discountedPrice * 100) / 100,
    monthlyTotal: Math.round(monthlyTotal * 100) / 100,
    yearlyTotal: Math.round(yearlyTotal * 100) / 100,
    yearlyDiscount: Math.round((monthlyTotal * 2) * 100) / 100,
    currency: "INR"
  };
};
```

### **Pro-rated Billing Implementation**
```typescript
const calculateProRatedBilling = (
  startDate: Date,
  studentCount: number,
  pricePerStudent: number
) => {
  const now = new Date();
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  const daysInMonth = endOfMonth.getDate();
  const remainingDays = daysInMonth - startDate.getDate() + 1;

  const dailyRate = pricePerStudent / daysInMonth;
  const proratedAmount = dailyRate * remainingDays * studentCount;

  return {
    baseAmount: pricePerStudent * studentCount,
    proratedAmount: Math.round(proratedAmount * 100) / 100,
    remainingDays,
    daysInMonth,
    nextBillingDate: new Date(now.getFullYear(), now.getMonth() + 1, 1)
  };
};
```

### **Partner Earnings Calculation**
```typescript
const calculatePartnerEarnings = (
  subscriptionAmount: number,
  operationalExpenses: number,
  profitSharingPercent: number
) => {
  const netRevenue = subscriptionAmount - operationalExpenses;
  const partnerEarnings = netRevenue * (profitSharingPercent / 100);

  return {
    grossRevenue: subscriptionAmount,
    operationalExpenses,
    netRevenue: Math.max(0, netRevenue),
    partnerEarnings: Math.max(0, Math.round(partnerEarnings * 100) / 100),
    profitMargin: netRevenue > 0 ? (netRevenue / subscriptionAmount) * 100 : 0
  };
};
```

---

## 🏥 System Management & Health Monitoring

### **System Initialization**

#### **GET /api/admin/system/init**
Get system initialization status and service health.

**Authentication**: None required (auto-initializes services)

**Response**:
```json
{
  "success": true,
  "initialized": true,
  "healthy": true,
  "status": {
    "initialized": true,
    "environment": "production",
    "config": {
      "enableBillingScheduler": true,
      "enableHealthMonitoring": true,
      "enableMissedBillDetection": true
    },
    "services": {
      "billing-scheduler": {
        "name": "Billing Scheduler",
        "status": "running",
        "healthy": true
      },
      "health-monitor": {
        "name": "Health Monitor",
        "status": "running",
        "healthy": true
      },
      "missed-bill-detector": {
        "name": "Missed Bill Detector",
        "status": "running",
        "healthy": true
      }
    }
  },
  "timestamp": "2025-07-10T12:00:00.000Z"
}
```

#### **POST /api/admin/system/init**
Force re-initialization of all services (admin only).

**Authentication**: Admin JWT required
**Authorization**: `super_admin` role required

**Response**:
```json
{
  "success": true,
  "message": "Services re-initialized successfully",
  "initialized": true,
  "healthy": true,
  "status": { /* same as GET response */ },
  "timestamp": "2025-07-10T12:00:00.000Z"
}
```

### **Health Monitoring**

#### **GET /api/admin/health**
Comprehensive system health check with detailed metrics.

**Authentication**: None required

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-10T12:00:00.000Z",
  "uptime": 86400,
  "services": {
    "billing": {
      "status": "healthy",
      "scheduler": true
    },
    "database": {
      "status": "healthy",
      "connectionTime": 45,
      "activeSubscriptions": 1250
    },
    "external": {
      "razorpay": "configured",
      "email": "configured"
    }
  },
  "metrics": {
    "activeSubscriptions": 1250,
    "systemLoad": 35,
    "memoryUsage": {
      "used": 512,
      "total": 1024
    }
  },
  "responseTime": "123ms"
}
```

**Status Codes**:
- `200`: System healthy
- `206`: System degraded (some non-critical issues)
- `503`: System unhealthy (critical issues)

#### **GET /api/admin/health/simple**
Simple health check for load balancers.

**Authentication**: None required

**Response**:
- `200 OK`: System healthy
- `503 UNHEALTHY`: System has issues

**Response Body**: `"OK"` or `"UNHEALTHY"`

### **Automated Billing System**

#### **Billing Scheduler Status**
The automated billing system runs the following operations:

**Monthly Billing Generation**:
- **Schedule**: 1st of every month at 6:00 AM IST
- **Operation**: Generates invoices for all active subscriptions
- **Batch Size**: 100 schools per batch
- **Concurrency**: Maximum 10 concurrent operations
- **Error Handling**: Automatic retry with exponential backoff

**Daily Overdue Processing**:
- **Schedule**: Daily at 9:00 AM IST
- **Operation**: Processes overdue invoices and applies penalties
- **Grace Period**: 3 days default
- **Penalty Rate**: 2% daily default

**Health Monitoring**:
- **Schedule**: Every 15 minutes
- **Operation**: Checks system health and sends alerts
- **Alert Thresholds**: >10% overdue rate, >5% error rate, >80% memory usage

**Missed Bill Detection**:
- **Schedule**: Every 6 hours
- **Operation**: Scans for missed billing periods and recovers them
- **Recovery Window**: Up to 3 months
- **Alert Threshold**: >5 missed bills triggers admin notification

---

## 🚀 Production Deployment Notes

### **Environment Variables Required**
```bash
# Application
NODE_ENV=production
AUTO_START_SERVICES=true

# Database
DATABASE_URL=postgresql://...
DIRECT_URL=postgresql://...

# Authentication
JWT_SECRET=your-secret-key
ADMIN_JWT_SECRET=your-admin-secret
PARTNER_JWT_SECRET=your-partner-secret

# Email Service
RESEND_API_KEY=your-resend-key
FROM_EMAIL=<EMAIL>
FROM_NAME=Schopio

# Payment Gateway
RAZORPAY_KEY_ID=your-razorpay-key
RAZORPAY_KEY_SECRET=your-razorpay-secret
RAZORPAY_WEBHOOK_SECRET=your-webhook-secret

# External Services
GEMINI_API_KEY=your-gemini-key

# Automated Billing System
BILLING_SCHEDULER_ENABLED=true
BILLING_SCHEDULER_TIMEZONE=Asia/Kolkata
BILLING_DRY_RUN=false

# Health Monitoring
HEALTH_MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL_MINUTES=15
OVERDUE_ALERT_THRESHOLD=10
ERROR_RATE_ALERT_THRESHOLD=5
MEMORY_ALERT_THRESHOLD=80

# Missed Bill Detection
MISSED_BILL_DETECTION_ENABLED=true
MISSED_BILL_CHECK_INTERVAL_HOURS=6
MAX_RECOVERY_MONTHS=3
MISSED_BILL_ALERT_THRESHOLD=5

# Error Handling
PRODUCTION_ERROR_HANDLING=true
MAX_RETRY_ATTEMPTS=3
RETRY_BASE_DELAY_MS=1000
RETRY_MAX_DELAY_MS=30000
```

### **API Rate Limits (Production)**
- **Public endpoints**: 100 requests/minute per IP
- **Authenticated endpoints**: 1000 requests/minute per user
- **Admin endpoints**: 5000 requests/minute per admin
- **Webhook endpoints**: No limit (verified signatures)

### **Monitoring & Logging**
- All API requests logged with request ID
- Error tracking with stack traces
- Performance monitoring for slow queries
- Security event logging for failed auth attempts
- **Automated billing operations** logged with detailed metrics
- **Health monitoring** with real-time alerts
- **Error recovery** tracking and reporting

### **Automated System Features**
- ✅ **Zero-Admin Billing**: Fully automated monthly invoice generation
- ✅ **Production Error Handling**: Automatic retry and recovery mechanisms
- ✅ **Real-time Health Monitoring**: 15-minute health checks with alerts
- ✅ **Missed Bill Prevention**: 6-hour scanning with automatic recovery
- ✅ **Enterprise Scalability**: Handles 5,000+ schools with batch processing
- ✅ **Auto-Start Services**: Services initialize automatically on server boot

---

**Last Updated**: 2025-07-10
**API Version**: 1.0.0
**Automated Billing**: ✅ Production Ready
**Documentation Status**: ✅ Complete and Accurate
