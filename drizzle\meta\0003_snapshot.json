{"id": "9ca8bf98-2f70-421a-8e58-c3ebfd83d69b", "prevId": "617e3676-aaa0-4f52-89df-43a73be3275e", "version": "7", "dialect": "postgresql", "tables": {"public.admin_users": {"name": "admin_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"admin_users_email_unique": {"name": "admin_users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.advance_payments": {"name": "advance_payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "payment_id": {"name": "payment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "months_paid": {"name": "months_paid", "type": "integer", "primaryKey": false, "notNull": true}, "amount_per_month": {"name": "amount_per_month", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "start_month": {"name": "start_month", "type": "date", "primaryKey": false, "notNull": true}, "end_month": {"name": "end_month", "type": "date", "primaryKey": false, "notNull": true}, "remaining_months": {"name": "remaining_months", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"advance_payments_subscription_id_billing_subscriptions_id_fk": {"name": "advance_payments_subscription_id_billing_subscriptions_id_fk", "tableFrom": "advance_payments", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "advance_payments_payment_id_billing_transactions_id_fk": {"name": "advance_payments_payment_id_billing_transactions_id_fk", "tableFrom": "advance_payments", "tableTo": "billing_transactions", "columnsFrom": ["payment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "admin_id": {"name": "admin_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource": {"name": "resource", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource_id": {"name": "resource_id", "type": "uuid", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "severity": {"name": "severity", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"audit_logs_user_id_client_users_id_fk": {"name": "audit_logs_user_id_client_users_id_fk", "tableFrom": "audit_logs", "tableTo": "client_users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "audit_logs_admin_id_admin_users_id_fk": {"name": "audit_logs_admin_id_admin_users_id_fk", "tableFrom": "audit_logs", "tableTo": "admin_users", "columnsFrom": ["admin_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "audit_logs_client_id_clients_id_fk": {"name": "audit_logs_client_id_clients_id_fk", "tableFrom": "audit_logs", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_invoice_items": {"name": "billing_invoice_items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "invoice_id": {"name": "invoice_id", "type": "uuid", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}, "unit_price": {"name": "unit_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "period_start": {"name": "period_start", "type": "date", "primaryKey": false, "notNull": false}, "period_end": {"name": "period_end", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_invoice_items_invoice_id_billing_invoices_id_fk": {"name": "billing_invoice_items_invoice_id_billing_invoices_id_fk", "tableFrom": "billing_invoice_items", "tableTo": "billing_invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_invoices": {"name": "billing_invoices", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "invoice_number": {"name": "invoice_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "subtotal": {"name": "subtotal", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "tax_amount": {"name": "tax_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "penalty_amount": {"name": "penalty_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'draft'"}, "issued_date": {"name": "issued_date", "type": "date", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": true}, "paid_date": {"name": "paid_date", "type": "date", "primaryKey": false, "notNull": false}, "voided_date": {"name": "voided_date", "type": "date", "primaryKey": false, "notNull": false}, "period_start": {"name": "period_start", "type": "date", "primaryKey": false, "notNull": true}, "period_end": {"name": "period_end", "type": "date", "primaryKey": false, "notNull": true}, "razorpay_order_id": {"name": "razorpay_order_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "pdf_url": {"name": "pdf_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_invoices_subscription_id_billing_subscriptions_id_fk": {"name": "billing_invoices_subscription_id_billing_subscriptions_id_fk", "tableFrom": "billing_invoices", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "billing_invoices_client_id_clients_id_fk": {"name": "billing_invoices_client_id_clients_id_fk", "tableFrom": "billing_invoices", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"billing_invoices_invoice_number_unique": {"name": "billing_invoices_invoice_number_unique", "nullsNotDistinct": false, "columns": ["invoice_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_payment_events": {"name": "billing_payment_events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "payment_id": {"name": "payment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "event_data": {"name": "event_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'system'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_payment_events_payment_id_billing_payments_id_fk": {"name": "billing_payment_events_payment_id_billing_payments_id_fk", "tableFrom": "billing_payment_events", "tableTo": "billing_payments", "columnsFrom": ["payment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_payment_methods": {"name": "billing_payment_methods", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "razorpay_payment_method_id": {"name": "razorpay_payment_method_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "card_last4": {"name": "card_last4", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": false}, "card_brand": {"name": "card_brand", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "card_exp_month": {"name": "card_exp_month", "type": "integer", "primaryKey": false, "notNull": false}, "card_exp_year": {"name": "card_exp_year", "type": "integer", "primaryKey": false, "notNull": false}, "bank_name": {"name": "bank_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "account_last4": {"name": "account_last4", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_payment_methods_client_id_clients_id_fk": {"name": "billing_payment_methods_client_id_clients_id_fk", "tableFrom": "billing_payment_methods", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_payment_reminders": {"name": "billing_payment_reminders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "invoice_id": {"name": "invoice_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "reminder_type": {"name": "reminder_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "days_before": {"name": "days_before", "type": "integer", "primaryKey": false, "notNull": false}, "days_after": {"name": "days_after", "type": "integer", "primaryKey": false, "notNull": false}, "sent_date": {"name": "sent_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "email_sent": {"name": "email_sent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "sms_sent": {"name": "sms_sent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "email_opened": {"name": "email_opened", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "email_clicked": {"name": "email_clicked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_payment_reminders_invoice_id_billing_invoices_id_fk": {"name": "billing_payment_reminders_invoice_id_billing_invoices_id_fk", "tableFrom": "billing_payment_reminders", "tableTo": "billing_invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "billing_payment_reminders_client_id_clients_id_fk": {"name": "billing_payment_reminders_client_id_clients_id_fk", "tableFrom": "billing_payment_reminders", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_payments": {"name": "billing_payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "invoice_id": {"name": "invoice_id", "type": "uuid", "primaryKey": false, "notNull": false}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "payment_method_id": {"name": "payment_method_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'INR'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "razorpay_payment_id": {"name": "razorpay_payment_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "razorpay_order_id": {"name": "razorpay_order_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "razorpay_signature": {"name": "razorpay_signature", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "failure_reason": {"name": "failure_reason", "type": "text", "primaryKey": false, "notNull": false}, "failure_code": {"name": "failure_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "authorized_at": {"name": "authorized_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "captured_at": {"name": "captured_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "failed_at": {"name": "failed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "attempt_count": {"name": "attempt_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "next_retry_at": {"name": "next_retry_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "partner_commission_processed": {"name": "partner_commission_processed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "commission_processing_date": {"name": "commission_processing_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "commission_escrow_id": {"name": "commission_escrow_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_payments_invoice_id_billing_invoices_id_fk": {"name": "billing_payments_invoice_id_billing_invoices_id_fk", "tableFrom": "billing_payments", "tableTo": "billing_invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "billing_payments_subscription_id_billing_subscriptions_id_fk": {"name": "billing_payments_subscription_id_billing_subscriptions_id_fk", "tableFrom": "billing_payments", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "billing_payments_client_id_clients_id_fk": {"name": "billing_payments_client_id_clients_id_fk", "tableFrom": "billing_payments", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "billing_payments_payment_method_id_billing_payment_methods_id_fk": {"name": "billing_payments_payment_method_id_billing_payment_methods_id_fk", "tableFrom": "billing_payments", "tableTo": "billing_payment_methods", "columnsFrom": ["payment_method_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_plans": {"name": "billing_plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "billing_cycle": {"name": "billing_cycle", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "price_per_student": {"name": "price_per_student", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "setup_fee": {"name": "setup_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "trial_period_days": {"name": "trial_period_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "razorpay_plan_id": {"name": "razorpay_plan_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_subscriptions": {"name": "billing_subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": false}, "student_count": {"name": "student_count", "type": "integer", "primaryKey": false, "notNull": true}, "price_per_student": {"name": "price_per_student", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "monthly_amount": {"name": "monthly_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'incomplete'"}, "current_period_start": {"name": "current_period_start", "type": "date", "primaryKey": false, "notNull": true}, "current_period_end": {"name": "current_period_end", "type": "date", "primaryKey": false, "notNull": true}, "next_billing_date": {"name": "next_billing_date", "type": "date", "primaryKey": false, "notNull": true}, "trial_start": {"name": "trial_start", "type": "date", "primaryKey": false, "notNull": false}, "trial_end": {"name": "trial_end", "type": "date", "primaryKey": false, "notNull": false}, "billing_cycle": {"name": "billing_cycle", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'monthly'"}, "grace_period_days": {"name": "grace_period_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 3}, "penalty_rate": {"name": "penalty_rate", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false, "default": "'2.00'"}, "auto_renew": {"name": "auto_renew", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": false}, "current_penalty_amount": {"name": "current_penalty_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "last_payment_date": {"name": "last_payment_date", "type": "date", "primaryKey": false, "notNull": false}, "payment_status": {"name": "payment_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "auto_penalty_enabled": {"name": "auto_penalty_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "has_active_discount": {"name": "has_active_discount", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "current_discount_percentage": {"name": "current_discount_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "discount_end_date": {"name": "discount_end_date", "type": "date", "primaryKey": false, "notNull": false}, "discount_start_date": {"name": "discount_start_date", "type": "date", "primaryKey": false, "notNull": false}, "original_monthly_amount": {"name": "original_monthly_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "discount_reason": {"name": "discount_reason", "type": "text", "primaryKey": false, "notNull": false}, "advance_payment_balance": {"name": "advance_payment_balance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "advance_months_remaining": {"name": "advance_months_remaining", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "operational_expenses": {"name": "operational_expenses", "type": "jsonb", "primaryKey": false, "notNull": false}, "database_costs": {"name": "database_costs", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "website_maintenance": {"name": "website_maintenance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "support_costs": {"name": "support_costs", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "infrastructure_costs": {"name": "infrastructure_costs", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_operational_expenses": {"name": "total_operational_expenses", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "setup_fee": {"name": "setup_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "razorpay_subscription_id": {"name": "razorpay_subscription_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "razorpay_customer_id": {"name": "razorpay_customer_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "activated_at": {"name": "activated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "canceled_at": {"name": "canceled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancel_reason": {"name": "cancel_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_subscriptions_client_id_clients_id_fk": {"name": "billing_subscriptions_client_id_clients_id_fk", "tableFrom": "billing_subscriptions", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "billing_subscriptions_plan_id_billing_plans_id_fk": {"name": "billing_subscriptions_plan_id_billing_plans_id_fk", "tableFrom": "billing_subscriptions", "tableTo": "billing_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "billing_subscriptions_created_by_admin_users_id_fk": {"name": "billing_subscriptions_created_by_admin_users_id_fk", "tableFrom": "billing_subscriptions", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_transactions": {"name": "billing_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "original_amount": {"name": "original_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "penalty_amount": {"name": "penalty_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": true}, "payment_date": {"name": "payment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_advance_payment": {"name": "is_advance_payment", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "advance_months_covered": {"name": "advance_months_covered", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "invoice_number": {"name": "invoice_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "receipt_number": {"name": "receipt_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "invoice_pdf_path": {"name": "invoice_pdf_path", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "receipt_pdf_path": {"name": "receipt_pdf_path", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "email_sent_at": {"name": "email_sent_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "razorpay_payment_id": {"name": "razorpay_payment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "razorpay_order_id": {"name": "razorpay_order_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "failure_reason": {"name": "failure_reason", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_transactions_subscription_id_billing_subscriptions_id_fk": {"name": "billing_transactions_subscription_id_billing_subscriptions_id_fk", "tableFrom": "billing_transactions", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "billing_transactions_client_id_clients_id_fk": {"name": "billing_transactions_client_id_clients_id_fk", "tableFrom": "billing_transactions", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.client_users": {"name": "client_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'admin'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "otp_code": {"name": "otp_code", "type": "<PERSON><PERSON><PERSON>(6)", "primaryKey": false, "notNull": false}, "otp_expires_at": {"name": "otp_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"client_users_client_id_clients_id_fk": {"name": "client_users_client_id_clients_id_fk", "tableFrom": "client_users", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"client_users_email_unique": {"name": "client_users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clients": {"name": "clients", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "lead_id": {"name": "lead_id", "type": "uuid", "primaryKey": false, "notNull": false}, "school_name": {"name": "school_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "school_code": {"name": "school_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "contact_person": {"name": "contact_person", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "actual_student_count": {"name": "actual_student_count", "type": "integer", "primaryKey": false, "notNull": true}, "estimated_student_count": {"name": "estimated_student_count", "type": "integer", "primaryKey": false, "notNull": false}, "average_monthly_fee": {"name": "average_monthly_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_fee": {"name": "class_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "onboarding_status": {"name": "onboarding_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"clients_lead_id_leads_id_fk": {"name": "clients_lead_id_leads_id_fk", "tableFrom": "clients", "tableTo": "leads", "columnsFrom": ["lead_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"clients_school_code_unique": {"name": "clients_school_code_unique", "nullsNotDistinct": false, "columns": ["school_code"]}, "clients_email_unique": {"name": "clients_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.commission_release_audit": {"name": "commission_release_audit", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "escrow_id": {"name": "escrow_id", "type": "uuid", "primaryKey": false, "notNull": true}, "action_type": {"name": "action_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "action_reason": {"name": "action_reason", "type": "text", "primaryKey": false, "notNull": false}, "previous_status": {"name": "previous_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "new_status": {"name": "new_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "amount_affected": {"name": "amount_affected", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "triggered_by": {"name": "triggered_by", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "triggered_by_user": {"name": "triggered_by_user", "type": "uuid", "primaryKey": false, "notNull": false}, "system_reference": {"name": "system_reference", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "risk_factors": {"name": "risk_factors", "type": "jsonb", "primaryKey": false, "notNull": false}, "conditions_evaluated": {"name": "conditions_evaluated", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"commission_release_audit_escrow_id_partner_commission_escrow_id_fk": {"name": "commission_release_audit_escrow_id_partner_commission_escrow_id_fk", "tableFrom": "commission_release_audit", "tableTo": "partner_commission_escrow", "columnsFrom": ["escrow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "commission_release_audit_triggered_by_user_admin_users_id_fk": {"name": "commission_release_audit_triggered_by_user_admin_users_id_fk", "tableFrom": "commission_release_audit", "tableTo": "admin_users", "columnsFrom": ["triggered_by_user"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.demo_bookings": {"name": "demo_bookings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "lead_id": {"name": "lead_id", "type": "uuid", "primaryKey": false, "notNull": false}, "scheduled_date": {"name": "scheduled_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "demo_type": {"name": "demo_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'scheduled'"}, "meeting_link": {"name": "meeting_link", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"demo_bookings_lead_id_leads_id_fk": {"name": "demo_bookings_lead_id_leads_id_fk", "tableFrom": "demo_bookings", "tableTo": "leads", "columnsFrom": ["lead_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.leads": {"name": "leads", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "school_name": {"name": "school_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "contact_person": {"name": "contact_person", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "estimated_students": {"name": "estimated_students", "type": "integer", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'new'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"leads_email_unique": {"name": "leads_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operational_expenses": {"name": "operational_expenses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "category_name": {"name": "category_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "amount_per_school": {"name": "amount_per_school", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "is_percentage": {"name": "is_percentage", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "percentage_value": {"name": "percentage_value", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "applies_to": {"name": "applies_to", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'all'"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"operational_expenses_created_by_admin_users_id_fk": {"name": "operational_expenses_created_by_admin_users_id_fk", "tableFrom": "operational_expenses", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_commission_config": {"name": "partner_commission_config", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": false}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "commission_percentage": {"name": "commission_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "holding_period_days": {"name": "holding_period_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 30}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"partner_commission_config_partner_id_partners_id_fk": {"name": "partner_commission_config_partner_id_partners_id_fk", "tableFrom": "partner_commission_config", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_commission_config_subscription_id_billing_subscriptions_id_fk": {"name": "partner_commission_config_subscription_id_billing_subscriptions_id_fk", "tableFrom": "partner_commission_config", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_commission_config_created_by_admin_users_id_fk": {"name": "partner_commission_config_created_by_admin_users_id_fk", "tableFrom": "partner_commission_config", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_commission_escrow": {"name": "partner_commission_escrow", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "month_year": {"name": "month_year", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": true}, "base_amount": {"name": "base_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "commission_percentage": {"name": "commission_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "commission_amount": {"name": "commission_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "operational_expenses": {"name": "operational_expenses", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "net_commission": {"name": "net_commission", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "escrow_status": {"name": "escrow_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "school_payment_status": {"name": "school_payment_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "school_payment_id": {"name": "school_payment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "school_payment_date": {"name": "school_payment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "hold_until_date": {"name": "hold_until_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "release_conditions": {"name": "release_conditions", "type": "jsonb", "primaryKey": false, "notNull": false}, "auto_release_enabled": {"name": "auto_release_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "risk_score": {"name": "risk_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "razorpay_transfer_id": {"name": "razorpay_transfer_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "razorpay_account_id": {"name": "razorpay_account_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "transfer_status": {"name": "transfer_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "released_at": {"name": "released_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "released_by": {"name": "released_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"partner_commission_escrow_partner_id_partners_id_fk": {"name": "partner_commission_escrow_partner_id_partners_id_fk", "tableFrom": "partner_commission_escrow", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_commission_escrow_school_id_clients_id_fk": {"name": "partner_commission_escrow_school_id_clients_id_fk", "tableFrom": "partner_commission_escrow", "tableTo": "clients", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_commission_escrow_subscription_id_billing_subscriptions_id_fk": {"name": "partner_commission_escrow_subscription_id_billing_subscriptions_id_fk", "tableFrom": "partner_commission_escrow", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_commission_escrow_created_by_admin_users_id_fk": {"name": "partner_commission_escrow_created_by_admin_users_id_fk", "tableFrom": "partner_commission_escrow", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "partner_commission_escrow_released_by_admin_users_id_fk": {"name": "partner_commission_escrow_released_by_admin_users_id_fk", "tableFrom": "partner_commission_escrow", "tableTo": "admin_users", "columnsFrom": ["released_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_commission_transactions": {"name": "partner_commission_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": false}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "payment_id": {"name": "payment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "school_payment_amount": {"name": "school_payment_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "operational_expenses": {"name": "operational_expenses", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "profit_amount": {"name": "profit_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "commission_percentage": {"name": "commission_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "commission_amount": {"name": "commission_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "penalty_amount": {"name": "penalty_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "hold_until_date": {"name": "hold_until_date", "type": "date", "primaryKey": false, "notNull": false}, "eligible_date": {"name": "eligible_date", "type": "date", "primaryKey": false, "notNull": false}, "paid_date": {"name": "paid_date", "type": "date", "primaryKey": false, "notNull": false}, "payout_method": {"name": "payout_method", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "transaction_reference": {"name": "transaction_reference", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "payout_amount": {"name": "payout_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "payout_notes": {"name": "payout_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"partner_commission_transactions_partner_id_partners_id_fk": {"name": "partner_commission_transactions_partner_id_partners_id_fk", "tableFrom": "partner_commission_transactions", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_commission_transactions_subscription_id_billing_subscriptions_id_fk": {"name": "partner_commission_transactions_subscription_id_billing_subscriptions_id_fk", "tableFrom": "partner_commission_transactions", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_commission_transactions_payment_id_billing_transactions_id_fk": {"name": "partner_commission_transactions_payment_id_billing_transactions_id_fk", "tableFrom": "partner_commission_transactions", "tableTo": "billing_transactions", "columnsFrom": ["payment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_earnings": {"name": "partner_earnings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "invoice_id": {"name": "invoice_id", "type": "uuid", "primaryKey": false, "notNull": true}, "payment_id": {"name": "payment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "gross_amount": {"name": "gross_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "total_expenses": {"name": "total_expenses", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "net_profit": {"name": "net_profit", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "partner_share_percentage": {"name": "partner_share_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "partner_earning": {"name": "partner_earning", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "calculated_at": {"name": "calculated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "available_at": {"name": "available_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "withdrawn_at": {"name": "withdrawn_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expense_breakdown": {"name": "expense_breakdown", "type": "jsonb", "primaryKey": false, "notNull": false}, "escrow_id": {"name": "escrow_id", "type": "uuid", "primaryKey": false, "notNull": false}, "escrow_status": {"name": "escrow_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'manual'"}, "calculated_by": {"name": "calculated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"partner_earnings_partner_id_partners_id_fk": {"name": "partner_earnings_partner_id_partners_id_fk", "tableFrom": "partner_earnings", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_earnings_client_id_clients_id_fk": {"name": "partner_earnings_client_id_clients_id_fk", "tableFrom": "partner_earnings", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_earnings_invoice_id_billing_invoices_id_fk": {"name": "partner_earnings_invoice_id_billing_invoices_id_fk", "tableFrom": "partner_earnings", "tableTo": "billing_invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_earnings_payment_id_billing_payments_id_fk": {"name": "partner_earnings_payment_id_billing_payments_id_fk", "tableFrom": "partner_earnings", "tableTo": "billing_payments", "columnsFrom": ["payment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_earnings_calculated_by_admin_users_id_fk": {"name": "partner_earnings_calculated_by_admin_users_id_fk", "tableFrom": "partner_earnings", "tableTo": "admin_users", "columnsFrom": ["calculated_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_fund_accounts": {"name": "partner_fund_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "account_number": {"name": "account_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "ifsc_code": {"name": "ifsc_code", "type": "<PERSON><PERSON><PERSON>(11)", "primaryKey": false, "notNull": true}, "account_holder_name": {"name": "account_holder_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "bank_name": {"name": "bank_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "branch_name": {"name": "branch_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "validation_status": {"name": "validation_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "validation_date": {"name": "validation_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "validation_reference": {"name": "validation_reference", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "validation_notes": {"name": "validation_notes", "type": "text", "primaryKey": false, "notNull": false}, "razorpay_fund_account_id": {"name": "razorpay_fund_account_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "razorpay_contact_id": {"name": "razorpay_contact_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "verified_by": {"name": "verified_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"partner_fund_accounts_verified_by_admin_users_id_fk": {"name": "partner_fund_accounts_verified_by_admin_users_id_fk", "tableFrom": "partner_fund_accounts", "tableTo": "admin_users", "columnsFrom": ["verified_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_system_config": {"name": "partner_system_config", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "config_key": {"name": "config_key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "config_value": {"name": "config_value", "type": "text", "primaryKey": false, "notNull": true}, "data_type": {"name": "data_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"partner_system_config_updated_by_admin_users_id_fk": {"name": "partner_system_config_updated_by_admin_users_id_fk", "tableFrom": "partner_system_config", "tableTo": "admin_users", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"partner_system_config_config_key_unique": {"name": "partner_system_config_config_key_unique", "nullsNotDistinct": false, "columns": ["config_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_transactions": {"name": "partner_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "transaction_type": {"name": "transaction_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "reference_id": {"name": "reference_id", "type": "uuid", "primaryKey": false, "notNull": false}, "reference_type": {"name": "reference_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "balance_before": {"name": "balance_before", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "balance_after": {"name": "balance_after", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_reversible": {"name": "is_reversible", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "reversed_at": {"name": "reversed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "reversed_by": {"name": "reversed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "reversal_reason": {"name": "reversal_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"partner_transactions_partner_id_partners_id_fk": {"name": "partner_transactions_partner_id_partners_id_fk", "tableFrom": "partner_transactions", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "partner_transactions_reversed_by_admin_users_id_fk": {"name": "partner_transactions_reversed_by_admin_users_id_fk", "tableFrom": "partner_transactions", "tableTo": "admin_users", "columnsFrom": ["reversed_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partners": {"name": "partners", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_code": {"name": "partner_code", "type": "<PERSON><PERSON><PERSON>(8)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "bank_account_number": {"name": "bank_account_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "bank_ifsc_code": {"name": "bank_ifsc_code", "type": "<PERSON><PERSON><PERSON>(11)", "primaryKey": false, "notNull": false}, "bank_account_holder_name": {"name": "bank_account_holder_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "profit_share_percentage": {"name": "profit_share_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "primary_fund_account_id": {"name": "primary_fund_account_id", "type": "uuid", "primaryKey": false, "notNull": false}, "fund_account_verified": {"name": "fund_account_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "razorpay_contact_id": {"name": "razorpay_contact_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "auto_payout_enabled": {"name": "auto_payout_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "minimum_payout_amount": {"name": "minimum_payout_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'1000'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"partners_created_by_admin_users_id_fk": {"name": "partners_created_by_admin_users_id_fk", "tableFrom": "partners", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"partners_partner_code_unique": {"name": "partners_partner_code_unique", "nullsNotDistinct": false, "columns": ["partner_code"]}, "partners_email_unique": {"name": "partners_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rate_limits": {"name": "rate_limits", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "endpoint": {"name": "endpoint", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "request_count": {"name": "request_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "window_start": {"name": "window_start", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_request": {"name": "last_request", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "blocked": {"name": "blocked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "blocked_until": {"name": "blocked_until", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.razorpay_customers": {"name": "razorpay_customers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "razorpay_customer_id": {"name": "razorpay_customer_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "customer_email": {"name": "customer_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_name": {"name": "customer_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_contact": {"name": "customer_contact", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"razorpay_customers_client_id_clients_id_fk": {"name": "razorpay_customers_client_id_clients_id_fk", "tableFrom": "razorpay_customers", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"razorpay_customers_razorpay_customer_id_unique": {"name": "razorpay_customers_razorpay_customer_id_unique", "nullsNotDistinct": false, "columns": ["razorpay_customer_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.referral_codes": {"name": "referral_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(8)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "max_usage": {"name": "max_usage", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deactivated_at": {"name": "deactivated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deactivated_by": {"name": "deactivated_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"referral_codes_partner_id_partners_id_fk": {"name": "referral_codes_partner_id_partners_id_fk", "tableFrom": "referral_codes", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "referral_codes_deactivated_by_admin_users_id_fk": {"name": "referral_codes_deactivated_by_admin_users_id_fk", "tableFrom": "referral_codes", "tableTo": "admin_users", "columnsFrom": ["deactivated_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"referral_codes_code_unique": {"name": "referral_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.request_status_history": {"name": "request_status_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "request_id": {"name": "request_id", "type": "uuid", "primaryKey": false, "notNull": true}, "from_status": {"name": "from_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "to_status": {"name": "to_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "changed_by": {"name": "changed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "change_reason": {"name": "change_reason", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"request_status_history_request_id_software_requests_id_fk": {"name": "request_status_history_request_id_software_requests_id_fk", "tableFrom": "request_status_history", "tableTo": "software_requests", "columnsFrom": ["request_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.school_referrals": {"name": "school_referrals", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "referral_code_id": {"name": "referral_code_id", "type": "uuid", "primaryKey": false, "notNull": true}, "referred_at": {"name": "referred_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "referral_source": {"name": "referral_source", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "applied_by": {"name": "applied_by", "type": "uuid", "primaryKey": false, "notNull": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "verified_by": {"name": "verified_by", "type": "uuid", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"school_referrals_client_id_clients_id_fk": {"name": "school_referrals_client_id_clients_id_fk", "tableFrom": "school_referrals", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "school_referrals_partner_id_partners_id_fk": {"name": "school_referrals_partner_id_partners_id_fk", "tableFrom": "school_referrals", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "school_referrals_referral_code_id_referral_codes_id_fk": {"name": "school_referrals_referral_code_id_referral_codes_id_fk", "tableFrom": "school_referrals", "tableTo": "referral_codes", "columnsFrom": ["referral_code_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "school_referrals_applied_by_client_users_id_fk": {"name": "school_referrals_applied_by_client_users_id_fk", "tableFrom": "school_referrals", "tableTo": "client_users", "columnsFrom": ["applied_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "school_referrals_verified_by_admin_users_id_fk": {"name": "school_referrals_verified_by_admin_users_id_fk", "tableFrom": "school_referrals", "tableTo": "admin_users", "columnsFrom": ["verified_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.security_events": {"name": "security_events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "admin_id": {"name": "admin_id", "type": "uuid", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": true}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "severity": {"name": "severity", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "resolved": {"name": "resolved", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "resolved_by": {"name": "resolved_by", "type": "uuid", "primaryKey": false, "notNull": false}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"security_events_user_id_client_users_id_fk": {"name": "security_events_user_id_client_users_id_fk", "tableFrom": "security_events", "tableTo": "client_users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "security_events_admin_id_admin_users_id_fk": {"name": "security_events_admin_id_admin_users_id_fk", "tableFrom": "security_events", "tableTo": "admin_users", "columnsFrom": ["admin_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "security_events_resolved_by_admin_users_id_fk": {"name": "security_events_resolved_by_admin_users_id_fk", "tableFrom": "security_events", "tableTo": "admin_users", "columnsFrom": ["resolved_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.software_requests": {"name": "software_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "request_type": {"name": "request_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "student_count": {"name": "student_count", "type": "integer", "primaryKey": false, "notNull": true}, "faculty_count": {"name": "faculty_count", "type": "integer", "primaryKey": false, "notNull": true}, "complete_address": {"name": "complete_address", "type": "text", "primaryKey": false, "notNull": true}, "contact_number": {"name": "contact_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "primary_email": {"name": "primary_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "average_monthly_fee": {"name": "average_monthly_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_1_fee": {"name": "class_1_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_4_fee": {"name": "class_4_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_6_fee": {"name": "class_6_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_10_fee": {"name": "class_10_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_11_fee": {"name": "class_11_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_12_fee": {"name": "class_12_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_11_12_fee": {"name": "class_11_12_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "calculated_average_fee": {"name": "calculated_average_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "terms_accepted": {"name": "terms_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "terms_accepted_at": {"name": "terms_accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "terms_version": {"name": "terms_version", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "review_notes": {"name": "review_notes", "type": "text", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "approved_at": {"name": "approved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "activated_at": {"name": "activated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"software_requests_client_id_clients_id_fk": {"name": "software_requests_client_id_clients_id_fk", "tableFrom": "software_requests", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "software_requests_reviewed_by_admin_users_id_fk": {"name": "software_requests_reviewed_by_admin_users_id_fk", "tableFrom": "software_requests", "tableTo": "admin_users", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_auth": {"name": "subscription_auth", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "razorpay_order_id": {"name": "razorpay_order_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "razorpay_payment_id": {"name": "razorpay_payment_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "razorpay_customer_id": {"name": "razorpay_customer_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'INR'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "auth_type": {"name": "auth_type", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "default": "'initial_authentication'"}, "failure_reason": {"name": "failure_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "failed_at": {"name": "failed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"subscription_auth_subscription_id_subscriptions_id_fk": {"name": "subscription_auth_subscription_id_subscriptions_id_fk", "tableFrom": "subscription_auth", "tableTo": "subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "subscription_auth_client_id_clients_id_fk": {"name": "subscription_auth_client_id_clients_id_fk", "tableFrom": "subscription_auth", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_change_log": {"name": "subscription_change_log", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "admin_id": {"name": "admin_id", "type": "uuid", "primaryKey": false, "notNull": false}, "change_type": {"name": "change_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "old_values": {"name": "old_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "new_values": {"name": "new_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "effective_date": {"name": "effective_date", "type": "date", "primaryKey": false, "notNull": true}, "billing_adjustment": {"name": "billing_adjustment", "type": "jsonb", "primaryKey": false, "notNull": false}, "commission_adjustment": {"name": "commission_adjustment", "type": "jsonb", "primaryKey": false, "notNull": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscription_change_log_subscription_id_billing_subscriptions_id_fk": {"name": "subscription_change_log_subscription_id_billing_subscriptions_id_fk", "tableFrom": "subscription_change_log", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "subscription_change_log_admin_id_admin_users_id_fk": {"name": "subscription_change_log_admin_id_admin_users_id_fk", "tableFrom": "subscription_change_log", "tableTo": "admin_users", "columnsFrom": ["admin_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_discounts": {"name": "subscription_discounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "discount_percentage": {"name": "discount_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "discount_duration_months": {"name": "discount_duration_months", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "remaining_months": {"name": "remaining_months", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscription_discounts_subscription_id_billing_subscriptions_id_fk": {"name": "subscription_discounts_subscription_id_billing_subscriptions_id_fk", "tableFrom": "subscription_discounts", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "subscription_discounts_created_by_admin_users_id_fk": {"name": "subscription_discounts_created_by_admin_users_id_fk", "tableFrom": "subscription_discounts", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_expenses": {"name": "subscription_expenses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "monthly_operational_cost": {"name": "monthly_operational_cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "expense_breakdown": {"name": "expense_breakdown", "type": "jsonb", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'operational'"}, "effective_from": {"name": "effective_from", "type": "date", "primaryKey": false, "notNull": true}, "effective_until": {"name": "effective_until", "type": "date", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscription_expenses_subscription_id_billing_subscriptions_id_fk": {"name": "subscription_expenses_subscription_id_billing_subscriptions_id_fk", "tableFrom": "subscription_expenses", "tableTo": "billing_subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "subscription_expenses_created_by_admin_users_id_fk": {"name": "subscription_expenses_created_by_admin_users_id_fk", "tableFrom": "subscription_expenses", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_plans": {"name": "subscription_plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "billing_cycle": {"name": "billing_cycle", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "price_per_student": {"name": "price_per_student", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "discount_percentage": {"name": "discount_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscriptions": {"name": "subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": false}, "plan_name": {"name": "plan_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'Basic Plan'"}, "student_count": {"name": "student_count", "type": "integer", "primaryKey": false, "notNull": true}, "price_per_student": {"name": "price_per_student", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "monthly_amount": {"name": "monthly_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "yearly_discount_percentage": {"name": "yearly_discount_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false, "default": "'16.67'"}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "next_billing_date": {"name": "next_billing_date", "type": "date", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "auto_renew": {"name": "auto_renew", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "razorpay_subscription_id": {"name": "razorpay_subscription_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "razorpay_plan_id": {"name": "razorpay_plan_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "razorpay_customer_id": {"name": "razorpay_customer_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "activated_at": {"name": "activated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "billing_cycle": {"name": "billing_cycle", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'monthly'"}, "due_date": {"name": "due_date", "type": "integer", "primaryKey": false, "notNull": false, "default": 15}, "grace_period_days": {"name": "grace_period_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 3}, "setup_fee": {"name": "setup_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "discount_percentage": {"name": "discount_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "operational_expenses": {"name": "operational_expenses", "type": "jsonb", "primaryKey": false, "notNull": false}, "database_costs": {"name": "database_costs", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "website_maintenance": {"name": "website_maintenance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "support_costs": {"name": "support_costs", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "infrastructure_costs": {"name": "infrastructure_costs", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_operational_expenses": {"name": "total_operational_expenses", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscriptions_client_id_clients_id_fk": {"name": "subscriptions_client_id_clients_id_fk", "tableFrom": "subscriptions", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "subscriptions_plan_id_subscription_plans_id_fk": {"name": "subscriptions_plan_id_subscription_plans_id_fk", "tableFrom": "subscriptions", "tableTo": "subscription_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "subscriptions_created_by_admin_users_id_fk": {"name": "subscriptions_created_by_admin_users_id_fk", "tableFrom": "subscriptions", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.support_tickets": {"name": "support_tickets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'medium'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'open'"}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"support_tickets_client_id_clients_id_fk": {"name": "support_tickets_client_id_clients_id_fk", "tableFrom": "support_tickets", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "support_tickets_created_by_client_users_id_fk": {"name": "support_tickets_created_by_client_users_id_fk", "tableFrom": "support_tickets", "tableTo": "client_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.terms_conditions": {"name": "terms_conditions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "effective_date": {"name": "effective_date", "type": "date", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"terms_conditions_created_by_admin_users_id_fk": {"name": "terms_conditions_created_by_admin_users_id_fk", "tableFrom": "terms_conditions", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"terms_conditions_version_unique": {"name": "terms_conditions_version_unique", "nullsNotDistinct": false, "columns": ["version"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ticket_messages": {"name": "ticket_messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "ticket_id": {"name": "ticket_id", "type": "uuid", "primaryKey": false, "notNull": false}, "sender_type": {"name": "sender_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "sender_id": {"name": "sender_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "attachments": {"name": "attachments", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ticket_messages_ticket_id_support_tickets_id_fk": {"name": "ticket_messages_ticket_id_support_tickets_id_fk", "tableFrom": "ticket_messages", "tableTo": "support_tickets", "columnsFrom": ["ticket_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_idempotency": {"name": "webhook_idempotency", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "idempotency_key": {"name": "idempotency_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "webhook_source": {"name": "webhook_source", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "webhook_data": {"name": "webhook_data", "type": "jsonb", "primaryKey": false, "notNull": true}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "processing_result": {"name": "processing_result", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"webhook_idempotency_idempotency_key_unique": {"name": "webhook_idempotency_idempotency_key_unique", "nullsNotDistinct": false, "columns": ["idempotency_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.withdrawal_requests": {"name": "withdrawal_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "requested_amount": {"name": "requested_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "available_balance": {"name": "available_balance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "request_month": {"name": "request_month", "type": "date", "primaryKey": false, "notNull": true}, "requested_at": {"name": "requested_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "processed_by": {"name": "processed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "transaction_reference": {"name": "transaction_reference", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "bank_details_snapshot": {"name": "bank_details_snapshot", "type": "jsonb", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "text", "primaryKey": false, "notNull": false}, "processing_fee": {"name": "processing_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "net_amount": {"name": "net_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"withdrawal_requests_partner_id_partners_id_fk": {"name": "withdrawal_requests_partner_id_partners_id_fk", "tableFrom": "withdrawal_requests", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "withdrawal_requests_reviewed_by_admin_users_id_fk": {"name": "withdrawal_requests_reviewed_by_admin_users_id_fk", "tableFrom": "withdrawal_requests", "tableTo": "admin_users", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "withdrawal_requests_processed_by_admin_users_id_fk": {"name": "withdrawal_requests_processed_by_admin_users_id_fk", "tableFrom": "withdrawal_requests", "tableTo": "admin_users", "columnsFrom": ["processed_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}