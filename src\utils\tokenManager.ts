import jwt from 'jsonwebtoken'
import { UserType } from './authUtils'

// Token refresh configuration
const REFRESH_THRESHOLDS: Record<UserType, number> = {
  school: 2 * 60 * 60, // 2 hours before expiry
  admin: 1 * 60 * 60,  // 1 hour before expiry
  partner: 2 * 60 * 60 // 2 hours before expiry
}

// Token generation utilities
export class TokenManager {
  
  // Generate school user token
  static generateSchoolToken(
    userId: string,
    email: string,
    role: string,
    clientId: string,
    schoolName: string,
    permissions: string[] = []
  ): string {
    const payload = {
      userId,
      email,
      role,
      clientId,
      schoolName,
      permissions,
      type: 'school',
      jti: crypto.randomUUID(),
      iat: Math.floor(Date.now() / 1000),
      iss: 'schopio-school',
      aud: 'schopio-school-portal'
    }

    return jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', {
      expiresIn: '12h',
      algorithm: 'HS256'
    })
  }

  // Generate admin user token
  static generateAdminToken(
    adminId: string,
    email: string,
    role: string,
    permissions: string[]
  ): string {
    const payload = {
      userId: adminId,
      email,
      role,
      permissions,
      type: 'admin',
      jti: crypto.randomUUID(),
      iat: Math.floor(Date.now() / 1000),
      iss: 'schopio-admin',
      aud: 'schopio-admin-panel'
    }

    return jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', {
      expiresIn: '8h',
      algorithm: 'HS256'
    })
  }

  // Generate partner user token
  static generatePartnerToken(
    partnerId: string,
    email: string,
    companyName: string,
    partnerCode: string
  ): string {
    const payload = {
      userId: partnerId,
      email,
      companyName,
      partnerCode,
      type: 'partner',
      jti: crypto.randomUUID(),
      iat: Math.floor(Date.now() / 1000),
      iss: 'schopio-partner',
      aud: 'schopio-partner-portal'
    }

    return jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', {
      expiresIn: '7d',
      algorithm: 'HS256'
    })
  }

  // Verify and decode token
  static verifyToken(token: string, expectedType: UserType): any {
    try {
      const issuerMap: Record<UserType, string> = {
        school: 'schopio-school',
        admin: 'schopio-admin',
        partner: 'schopio-partner'
      }

      const audienceMap: Record<UserType, string> = {
        school: 'schopio-school-portal',
        admin: 'schopio-admin-panel',
        partner: 'schopio-partner-portal'
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key', {
        algorithms: ['HS256'],
        issuer: issuerMap[expectedType],
        audience: audienceMap[expectedType]
      }) as any

      // Verify token type matches expected type
      if (decoded.type !== expectedType) {
        throw new Error(`Token type mismatch. Expected: ${expectedType}, Got: ${decoded.type}`)
      }

      return decoded
    } catch (error) {
      throw error
    }
  }

  // Check if token needs refresh
  static needsRefresh(token: string, userType: UserType): boolean {
    try {
      const decoded = jwt.decode(token) as any
      if (!decoded || !decoded.exp) {
        return true
      }

      const now = Math.floor(Date.now() / 1000)
      const threshold = REFRESH_THRESHOLDS[userType]
      
      return (decoded.exp - now) < threshold
    } catch (error) {
      return true
    }
  }

  // Get token expiration time
  static getTokenExpiration(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any
      if (!decoded || !decoded.exp) {
        return null
      }

      return new Date(decoded.exp * 1000)
    } catch (error) {
      return null
    }
  }

  // Get time until token expires
  static getTimeUntilExpiry(token: string): number {
    try {
      const decoded = jwt.decode(token) as any
      if (!decoded || !decoded.exp) {
        return 0
      }

      const now = Math.floor(Date.now() / 1000)
      return Math.max(0, decoded.exp - now)
    } catch (error) {
      return 0
    }
  }

  // Refresh token (placeholder for future implementation)
  static async refreshToken(currentToken: string, userType: UserType): Promise<string | null> {
    try {
      // TODO: Implement token refresh logic
      // This would typically involve:
      // 1. Validating the current token
      // 2. Checking if user is still active in database
      // 3. Generating a new token with updated expiration
      // 4. Optionally blacklisting the old token

      const decoded = this.verifyToken(currentToken, userType)
      
      // For now, just generate a new token with the same payload
      switch (userType) {
        case 'school':
          return this.generateSchoolToken(
            decoded.userId,
            decoded.email,
            decoded.role,
            decoded.clientId,
            decoded.schoolName,
            decoded.permissions || []
          )
        
        case 'admin':
          return this.generateAdminToken(
            decoded.userId,
            decoded.email,
            decoded.role,
            decoded.permissions || []
          )
        
        case 'partner':
          return this.generatePartnerToken(
            decoded.userId,
            decoded.email,
            decoded.companyName,
            decoded.partnerCode
          )
        
        default:
          return null
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
      return null
    }
  }

  // Validate token structure without verification (for client-side checks)
  static isValidTokenStructure(token: string): boolean {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) {
        return false
      }

      // Try to decode the payload
      const payload = JSON.parse(atob(parts[1]))
      
      // Check required fields
      return !!(payload.userId && payload.email && payload.type && payload.exp)
    } catch (error) {
      return false
    }
  }

  // Get token payload without verification (for client-side use)
  static getTokenPayload(token: string): any {
    try {
      return jwt.decode(token)
    } catch (error) {
      return null
    }
  }

  // Check if token is expired (client-side check)
  static isTokenExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as any
      if (!decoded || !decoded.exp) {
        return true
      }

      const now = Math.floor(Date.now() / 1000)
      return decoded.exp < now
    } catch (error) {
      return true
    }
  }

  // Blacklist token (placeholder for future implementation)
  static async blacklistToken(token: string): Promise<void> {
    try {
      // TODO: Implement token blacklisting
      // This would typically involve storing the token JTI in a blacklist
      // that gets checked during token verification
      
      const decoded = jwt.decode(token) as any
      if (decoded && decoded.jti) {
        console.log(`Token ${decoded.jti} should be blacklisted`)
        // Store in Redis or database blacklist
      }
    } catch (error) {
      console.error('Token blacklisting failed:', error)
    }
  }
}

// Token validation middleware for API routes
export function createTokenValidationMiddleware(userType: UserType) {
  return (req: any, res: any, next: any) => {
    try {
      const authHeader = req.headers.authorization
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Authorization token required' })
      }

      const token = authHeader.substring(7)
      const decoded = TokenManager.verifyToken(token, userType)
      
      // Add user data to request
      req.user = decoded
      req.userType = userType
      
      next()
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        return res.status(401).json({ error: 'Invalid token' })
      }
      if (error instanceof jwt.TokenExpiredError) {
        return res.status(401).json({ error: 'Token expired' })
      }
      
      console.error('Token validation error:', error)
      return res.status(500).json({ error: 'Authentication failed' })
    }
  }
}

// Export token validation middlewares for each user type
export const schoolTokenValidation = createTokenValidationMiddleware('school')
export const adminTokenValidation = createTokenValidationMiddleware('admin')
export const partnerTokenValidation = createTokenValidationMiddleware('partner')
