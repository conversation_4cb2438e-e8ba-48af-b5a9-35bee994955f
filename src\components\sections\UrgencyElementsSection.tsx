'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Clock, 
  Users, 
  Calendar, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  ArrowRight,
  Zap,
  Target,
  Star
} from 'lucide-react'

const UrgencyElementsSection = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 7,
    hours: 23,
    minutes: 45,
    seconds: 30
  })

  const [availableSlots, setAvailableSlots] = useState(3)

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 }
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 }
        } else if (prev.days > 0) {
          return { ...prev, days: prev.days - 1, hours: 23, minutes: 59, seconds: 59 }
        }
        return prev
      })
    }, 1000)

    // Simulate slot booking
    const slotTimer = setInterval(() => {
      if (Math.random() < 0.3 && availableSlots > 1) {
        setAvailableSlots(prev => prev - 1)
      }
    }, 30000)

    return () => {
      clearInterval(timer)
      clearInterval(slotTimer)
    }
  }, [availableSlots])

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const urgencyItems = [
    {
      icon: Calendar,
      title: "Limited Implementation Slots",
      description: "Only 3 slots remaining for Q1 2024 implementation",
      action: "Reserve Your Slot",
      urgency: "high",
      color: "from-red-500 to-red-600"
    },
    {
      icon: Users,
      title: "Join 500+ Schools",
      description: "Be part of the fastest-growing school management community",
      action: "Join Now",
      urgency: "medium",
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: TrendingUp,
      title: "Early Adopter Benefits",
      description: "Get 6 months free support + priority feature requests",
      action: "Claim Benefits",
      urgency: "high",
      color: "from-emerald-500 to-emerald-600"
    }
  ]

  const recentActivity = [
    { school: "Delhi Public School", action: "booked demo", time: "2 minutes ago" },
    { school: "Kendriya Vidyalaya", action: "started implementation", time: "15 minutes ago" },
    { school: "Ryan International", action: "completed setup", time: "1 hour ago" },
    { school: "DAV Public School", action: "booked demo&quot;, time: &quot;2 hours ago" }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-red-50 to-orange-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-red-100 text-red-700 border border-red-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <AlertCircle className="w-4 h-4" />
            Limited Time Opportunity
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Don&apos;t Miss Out - 
            <span className="bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent"> Act Now</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Implementation slots are filling fast. Schools that implement now get priority support and exclusive benefits.
          </p>
        </motion.div>

        {/* Countdown Timer */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="mb-12"
        >
          <Card className="bg-gradient-to-r from-red-600 to-orange-600 border-0 shadow-xl max-w-4xl mx-auto">
            <CardContent padding="xl">
              <div className="text-center text-white">
                <div className="flex items-center justify-center gap-2 mb-4">
                  <Clock className="w-6 h-6" />
                  <h3 className="text-2xl font-bold">Q1 2024 Implementation Deadline</h3>
                </div>
                
                <div className="grid grid-cols-4 gap-4 mb-6">
                  {[
                    { label: 'Days', value: timeLeft.days },
                    { label: 'Hours', value: timeLeft.hours },
                    { label: 'Minutes', value: timeLeft.minutes },
                    { label: 'Seconds', value: timeLeft.seconds }
                  ].map((item, index) => (
                    <div key={index} className="bg-white/20 rounded-lg p-4">
                      <div className="text-3xl font-bold">{item.value.toString().padStart(2, '0')}</div>
                      <div className="text-sm text-red-100">{item.label}</div>
                    </div>
                  ))}
                </div>

                <p className="text-red-100 mb-6">
                  Reserve your implementation slot before the deadline to ensure Q1 2024 go-live
                </p>

                <Button
                  size="lg"
                  icon={ArrowRight}
                  iconPosition="right"
                  className="bg-white text-red-600 hover:bg-red-50 font-bold px-8 py-4 text-lg"
                >
                  Reserve Implementation Slot
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Urgency Items */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="grid lg:grid-cols-3 gap-8 mb-12"
        >
          {urgencyItems.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <CardContent padding="lg">
                  <div className="text-center">
                    <div className={`w-16 h-16 bg-gradient-to-r ${item.color} rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <item.icon className="w-8 h-8 text-white" />
                    </div>
                    
                    <h3 className="text-xl font-bold text-slate-900 mb-3">{item.title}</h3>
                    <p className="text-slate-600 mb-6">{item.description}</p>
                    
                    {item.urgency === 'high' && (
                      <div className="inline-flex items-center gap-1 bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm font-bold mb-4">
                        <AlertCircle className="w-4 h-4" />
                        High Demand
                      </div>
                    )}

                    <Button
                      size="lg"
                      icon={ArrowRight}
                      iconPosition="right"
                      className={`w-full bg-gradient-to-r ${item.color} hover:opacity-90 text-white font-bold`}
                    >
                      {item.action}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Available Slots Indicator */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
          className="mb-12"
        >
          <Card className="bg-white border border-orange-200 shadow-lg max-w-2xl mx-auto">
            <CardContent padding="lg">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-4">
                  <Target className="w-6 h-6 text-orange-600" />
                  <h3 className="text-xl font-bold text-slate-900">Implementation Slots Available</h3>
                </div>
                
                <div className="flex justify-center gap-2 mb-4">
                  {[...Array(5)].map((_, index) => (
                    <div
                      key={index}
                      className={`w-4 h-4 rounded-full ${
                        index < availableSlots ? 'bg-emerald-500' : 'bg-slate-300'
                      }`}
                    />
                  ))}
                </div>
                
                <p className="text-slate-600 mb-4">
                  <span className="font-bold text-orange-600">{availableSlots} slots remaining</span> for Q1 2024 implementation
                </p>
                
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <p className="text-sm text-orange-700">
                    <strong>Note:</strong> Once all slots are filled, next available implementation will be Q2 2024
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="mb-12"
        >
          <Card className="bg-white border-0 shadow-lg max-w-3xl mx-auto">
            <CardContent padding="lg">
              <div className="text-center mb-6">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Zap className="w-5 h-5 text-blue-600" />
                  <h3 className="text-xl font-bold text-slate-900">Live Activity</h3>
                </div>
                <p className="text-slate-600">See what other schools are doing right now</p>
              </div>

              <div className="space-y-3">
                {recentActivity.map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-slate-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse" />
                      <span className="font-medium text-slate-900">{activity.school}</span>
                      <span className="text-slate-600">{activity.action}</span>
                    </div>
                    <span className="text-sm text-slate-500">{activity.time}</span>
                  </motion.div>
                ))}
              </div>

              <div className="text-center mt-6">
                <Button
                  size="lg"
                  icon={Users}
                  iconPosition="left"
                  className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:opacity-90 text-white font-bold"
                >
                  Join These Schools
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Final CTA with Social Proof */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
        >
          <Card className="bg-gradient-to-r from-slate-900 to-blue-900 border-0 shadow-xl">
            <CardContent padding="xl">
              <div className="text-center text-white">
                <div className="flex items-center justify-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  ))}
                  <span className="ml-2 text-blue-200">4.9/5 from 500+ schools</span>
                </div>

                <h3 className="text-3xl font-bold mb-4">
                  Don&apos;t Let Your School Fall Behind
                </h3>
                
                <p className="text-blue-200 mb-8 max-w-2xl mx-auto">
                  While you&apos;re deciding, other schools are already implementing and gaining competitive advantages. 
                  Join them before it&apos;s too late.
                </p>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-300">500+</div>
                    <div className="text-sm text-blue-200">Schools Already Using</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-300">₹50Cr+</div>
                    <div className="text-sm text-blue-200">Total Savings Generated</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-300">14 Days</div>
                    <div className="text-sm text-blue-200">Average Implementation</div>
                  </div>
                </div>

                <Button
                  size="lg"
                  icon={ArrowRight}
                  iconPosition="right"
                  className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:opacity-90 text-white font-bold px-12 py-4 text-xl"
                >
                  Start Implementation Today
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default UrgencyElementsSection
