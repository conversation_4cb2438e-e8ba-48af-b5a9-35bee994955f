// Exact Feature Comparison Table Data from Screenshot
// "Feature Comparison Overview" table structure

export const featureComparisonTable = {
  title: "Feature Comparison Overview",
  subtitle: "See how we compare to typical school management competitors",
  columns: [
    {
      id: "feature",
      title: "Feature",
      width: "40%"
    },
    {
      id: "competitors",
      title: "Competitors",
      width: "30%"
    },
    {
      id: "you",
      title: "You",
      width: "30%"
    }
  ],
  rows: [
    {
      id: 1,
      feature: "Finance + Payroll + Audit",
      competitors: {
        status: "partial",
        text: "Partial",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      },
      you: {
        status: "full",
        text: "Full Lifecycle",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      }
    },
    {
      id: 2,
      feature: "Fee Management + Razorpay",
      competitors: {
        status: "basic",
        text: "Basic UPI",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      },
      you: {
        status: "advanced",
        text: "Full Digital Reconciliation",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      }
    },
    {
      id: 3,
      feature: "AI-Powered Analytics & Predictive",
      competitors: {
        status: "rare",
        text: "Rare",
        icon: "🚫",
        bgColor: "bg-red-50",
        textColor: "text-red-700"
      },
      you: {
        status: "advanced",
        text: "Gemma-3.27B Insights",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      }
    },
    {
      id: 4,
      feature: "Transport Management System",
      competitors: {
        status: "minimal",
        text: "Minimal",
        icon: "🚫",
        bgColor: "bg-red-50",
        textColor: "text-red-700"
      },
      you: {
        status: "comprehensive",
        text: "Comprehensive Management",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      }
    },
    {
      id: 5,
      feature: "Hostel + Admission + Library",
      competitors: {
        status: "offered",
        text: "Offered",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      },
      you: {
        status: "unified",
        text: "Unified, Role-based UX",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      }
    },
    {
      id: 6,
      feature: "Multilingual Web Portals",
      competitors: {
        status: "limited",
        text: "Limited",
        icon: "⚠️",
        bgColor: "bg-yellow-50",
        textColor: "text-yellow-700"
      },
      you: {
        status: "modern",
        text: "Modern, Responsive UI",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      }
    },
    {
      id: 7,
      feature: "Real-time Chat Across Roles",
      competitors: {
        status: "basic",
        text: "Basic",
        icon: "⚠️",
        bgColor: "bg-yellow-50",
        textColor: "text-yellow-700"
      },
      you: {
        status: "crossrole",
        text: "Cross-role Chat & Feedback",
        icon: "✅",
        bgColor: "bg-green-50",
        textColor: "text-green-700"
      }
    }
  ]
};

// Summary statistics for visual impact
export const comparisonStats = {
  totalFeatures: 7,
  ourAdvantages: 7, // All features where we're better
  competitorLimitations: 4, // Features where competitors are limited/poor
  criticalDifferences: 2 // AI and Transport - major differentiators
};

// Key messaging for the comparison
export const comparisonMessages = {
  headline: "Why Schools Choose Us Over the Competition",
  subheadline: "Complete solutions vs. partial offerings",
  keyPoints: [
    "Advanced AI insights while competitors offer basic reporting",
    "Comprehensive transport management vs. minimal transport features",
    "Modern responsive UI vs. limited web responsiveness",
    "Cross-role communication vs. basic messaging",
    "Full digital reconciliation vs. basic UPI payments",
    "Unified role-based experience vs. fragmented systems"
  ],
  cta: "See Complete Feature List"
};

// Color scheme matching the screenshot
export const tableTheme = {
  header: {
    background: "bg-gray-800",
    text: "text-white",
    font: "font-semibold"
  },
  feature: {
    background: "bg-gray-50",
    text: "text-gray-900",
    font: "font-medium"
  },
  competitors: {
    background: "bg-white",
    text: "text-gray-700"
  },
  you: {
    background: "bg-white", 
    text: "text-gray-700"
  },
  borders: "border-gray-200"
};

export default featureComparisonTable;
