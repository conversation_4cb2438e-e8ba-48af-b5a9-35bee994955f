'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { AuthUtils } from '@/src/utils/authUtils'

interface AdminAuthWrapperProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
}

export default function AdminAuthWrapper({ 
  children, 
  requireAuth = true, 
  redirectTo 
}: AdminAuthWrapperProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authenticated = AuthUtils.isAuthenticated('admin')
        setIsAuthenticated(authenticated)

        // Handle authentication logic
        if (requireAuth && !authenticated) {
          // Admin needs to be authenticated but isn't
          const loginUrl = `/admin/login?redirect=${encodeURIComponent(pathname)}`
          router.replace(loginUrl)
          return
        }

        if (!requireAuth && authenticated) {
          // Admin is authenticated but on login page
          const dashboardUrl = redirectTo || '/admin/dashboard'
          router.replace(dashboardUrl)
          return
        }

        // Check if token is expiring soon (admin tokens expire in 8h)
        if (authenticated && AuthUtils.isTokenExpiringSoon('admin')) {
          console.warn('Admin token expiring soon')
          // TODO: Implement token refresh or show warning
        }

      } catch (error) {
        console.error('Admin auth check failed:', error)
        AuthUtils.handleAuthError('admin', error)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()

    // Listen for storage changes (logout from another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'adminToken') {
        checkAuth()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [requireAuth, redirectTo, router, pathname])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto"></div>
          <p className="mt-4 text-gray-300">Verifying admin authentication...</p>
        </div>
      </div>
    )
  }

  // Don't render children if auth state doesn't match requirements
  if (requireAuth && !isAuthenticated) {
    return null // Will redirect
  }

  if (!requireAuth && isAuthenticated) {
    return null // Will redirect
  }

  return <>{children}</>
}

// Higher-order component for protected admin routes
export function withAdminAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: { requireAuth?: boolean; redirectTo?: string } = {}
) {
  return function AuthenticatedAdminComponent(props: P) {
    return (
      <AdminAuthWrapper 
        requireAuth={options.requireAuth ?? true}
        redirectTo={options.redirectTo}
      >
        <Component {...props} />
      </AdminAuthWrapper>
    )
  }
}

// Hook for admin authentication state
export function useAdminAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)
  const [admin, setAdmin] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkAuth = () => {
      const authenticated = AuthUtils.isAuthenticated('admin')
      const adminData = AuthUtils.getCurrentUser('admin')
      
      setIsAuthenticated(authenticated)
      setAdmin(adminData)
      setLoading(false)
    }

    checkAuth()

    // Listen for auth changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'adminToken') {
        checkAuth()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  const login = (token: string) => {
    AuthUtils.setToken('admin', token)
    setIsAuthenticated(true)
    setAdmin(AuthUtils.getCurrentUser('admin'))
  }

  const logout = () => {
    AuthUtils.logout('admin')
    setIsAuthenticated(false)
    setAdmin(null)
  }

  return {
    isAuthenticated,
    admin,
    loading,
    login,
    logout
  }
}

// Admin role checking hook
export function useAdminRole(requiredRoles: string[] = []) {
  const { admin, isAuthenticated, loading } = useAdminAuth()
  
  const hasRequiredRole = () => {
    if (!admin || !isAuthenticated) return false
    
    // Super admin has access to everything
    if (admin.role === 'super_admin') return true
    
    // Check if admin has any of the required roles
    return requiredRoles.length === 0 || requiredRoles.includes(admin.role)
  }

  return {
    hasAccess: hasRequiredRole(),
    admin,
    isAuthenticated,
    loading
  }
}

// Admin permission checking hook
export function useAdminPermission(requiredPermission: string) {
  const { admin, isAuthenticated, loading } = useAdminAuth()
  
  const hasPermission = () => {
    if (!admin || !isAuthenticated) return false
    
    // Super admin has all permissions
    if (admin.role === 'super_admin') return true
    
    // Check if admin has the specific permission
    return admin.permissions?.includes('*') || admin.permissions?.includes(requiredPermission)
  }

  return {
    hasPermission: hasPermission(),
    admin,
    isAuthenticated,
    loading
  }
}
