import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { billingScheduler } from '@/src/services/billingScheduler'
import { adminAuthMiddleware, requireAdminRole } from '@/src/middleware/admin-auth'

const app = new Hono()

// Initialize scheduler (admin only)
app.post('/init', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    billingScheduler.init()
    return c.json({
      success: true,
      message: 'Billing scheduler initialized successfully'
    })
  } catch (error) {
    console.error('Scheduler init error:', error)
    return c.json({
      success: false,
      error: 'Failed to initialize scheduler'
    }, 500)
  }
})

// Get scheduler status
app.get('/status', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const status = billingScheduler.getStatus()
    return c.json({
      success: true,
      status,
      enabled: Object.values(status).some(running => running)
    })
  } catch (error) {
    console.error('Scheduler status error:', error)
    return c.json({
      success: false,
      error: 'Failed to get scheduler status'
    }, 500)
  }
})

// Stop scheduler (admin only)
app.post('/stop', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    billingScheduler.stop()
    return c.json({
      success: true,
      message: 'Billing scheduler stopped successfully'
    })
  } catch (error) {
    console.error('Scheduler stop error:', error)
    return c.json({
      success: false,
      error: 'Failed to stop scheduler'
    }, 500)
  }
})

// Manual billing generation (admin only)
app.post('/generate-billing', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    console.log('🔄 Manual billing generation triggered by admin')
    const result = await billingScheduler.generateMonthlyBilling()
    
    return c.json({
      success: true,
      message: 'Manual billing generation completed',
      result
    })
  } catch (error) {
    console.error('Manual billing generation error:', error)
    return c.json({
      success: false,
      error: 'Failed to generate billing'
    }, 500)
  }
})

// Test scheduler functionality (admin only)
app.post('/test', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    // Create a test scheduler instance with dry run enabled
    const testScheduler = new (await import('@/src/services/billingScheduler')).BillingScheduler({
      enabled: true,
      timezone: 'Asia/Kolkata',
      dryRun: true
    })

    const result = await testScheduler.generateMonthlyBilling()
    
    return c.json({
      success: true,
      message: 'Test billing generation completed (dry run)',
      result
    })
  } catch (error) {
    console.error('Test billing generation error:', error)
    return c.json({
      success: false,
      error: 'Failed to test billing generation'
    }, 500)
  }
})

export default app
