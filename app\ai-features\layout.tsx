import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'AI-Powered School Management System & Educational Analytics | Schopio',
  description: 'Advanced AI-powered school management system with predictive analytics, automated insights, and intelligent educational management features. Transform your school with AI.',
  keywords: 'AI school management system, educational analytics, AI-powered ERP, smart school management, predictive analytics education, artificial intelligence education, AI educational software, machine learning school management',
  openGraph: {
    title: 'AI-Powered School Management System & Educational Analytics | Schopio',
    description: 'Advanced AI-powered school management system with predictive analytics, automated insights, and intelligent educational management features. Transform your school with AI.',
    type: 'website',
  },
}

export default function AIFeaturesLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}
