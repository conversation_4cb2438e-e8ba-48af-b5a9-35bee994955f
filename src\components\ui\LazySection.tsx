'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import LoadingSpinner from './LoadingSpinner'

interface LazySectionProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  threshold?: number
  rootMargin?: string
  className?: string
  loadingText?: string
  minLoadTime?: number
}

const LazySection: React.FC<LazySectionProps> = ({
  children,
  fallback,
  threshold = 0.1,
  rootMargin = '100px',
  className = '',
  loadingText = 'Loading...',
  minLoadTime = 500
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [showContent, setShowContent] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true)
          
          // Simulate loading time for smooth UX
          const startTime = Date.now()
          
          setTimeout(() => {
            // Ensure minimum loading time for smooth transition
            const elapsed = Date.now() - startTime
            const remainingTime = Math.max(0, minLoadTime - elapsed)

            setTimeout(() => {
              setShowContent(true)
            }, remainingTime)
          }, 100)
        }
      },
      {
        threshold,
        rootMargin
      }
    )

    const currentRef = ref.current
    if (currentRef) {
      observer.observe(currentRef)
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef)
      }
    }
  }, [isVisible, threshold, rootMargin, minLoadTime])

  const defaultFallback = (
    <div className="flex items-center justify-center py-20">
      <LoadingSpinner 
        size="lg" 
        variant="bounce" 
        text={loadingText}
      />
    </div>
  )

  return (
    <div ref={ref} className={className}>
      {!showContent ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isVisible ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        >
          {fallback || defaultFallback}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          {children}
        </motion.div>
      )}
    </div>
  )
}

export default LazySection
