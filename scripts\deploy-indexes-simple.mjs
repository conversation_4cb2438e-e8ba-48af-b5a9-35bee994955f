#!/usr/bin/env node

/**
 * Simple Performance Index Deployment Script
 * Deploys critical performance indexes for the discount system
 */

import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

async function deployIndexes() {
  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable is required');
  }

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });

  try {
    console.log('🔗 Connecting to database...');
    
    // Test connection
    await pool.query('SELECT 1');
    console.log('✅ Database connection successful');
    
    // Define critical indexes to create
    const indexes = [
      // Subscription Discounts Indexes
      `CREATE INDEX IF NOT EXISTS idx_subscription_discounts_active 
       ON subscription_discounts(subscription_id, is_active) 
       WHERE is_active = true`,
       
      `CREATE INDEX IF NOT EXISTS idx_subscription_discounts_dates 
       ON subscription_discounts(end_date, is_active) 
       WHERE is_active = true`,
       
      // Subscription Expenses Indexes
      `CREATE INDEX IF NOT EXISTS idx_subscription_expenses_active 
       ON subscription_expenses(subscription_id, is_active) 
       WHERE is_active = true`,
       
      // Partner Commission Config Indexes
      `CREATE INDEX IF NOT EXISTS idx_partner_commission_config_active 
       ON partner_commission_config(partner_id, subscription_id, is_active) 
       WHERE is_active = true`,
       
      // Partner Commission Transactions Indexes
      `CREATE INDEX IF NOT EXISTS idx_commission_transactions_status 
       ON partner_commission_transactions(status, eligible_date)`,
       
      `CREATE INDEX IF NOT EXISTS idx_commission_transactions_partner_status 
       ON partner_commission_transactions(partner_id, status, eligible_date)`,
       
      // Advance Payments Indexes
      `CREATE INDEX IF NOT EXISTS idx_advance_payments_remaining 
       ON advance_payments(subscription_id, remaining_months) 
       WHERE remaining_months > 0`,
       
      // Enhanced Billing Subscriptions Indexes
      `CREATE INDEX IF NOT EXISTS idx_billing_subscriptions_discount 
       ON billing_subscriptions(has_active_discount, discount_end_date) 
       WHERE has_active_discount = true`,
       
      // Enhanced Billing Transactions Indexes
      `CREATE INDEX IF NOT EXISTS idx_billing_transactions_invoice 
       ON billing_transactions(invoice_number) 
       WHERE invoice_number IS NOT NULL`,
       
      `CREATE INDEX IF NOT EXISTS idx_billing_transactions_discount 
       ON billing_transactions(subscription_id, discount_amount) 
       WHERE discount_amount > 0`
    ];
    
    console.log(`🔧 Deploying ${indexes.length} critical performance indexes...`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < indexes.length; i++) {
      const indexSQL = indexes[i];
      
      try {
        console.log(`⚡ Creating index ${i + 1}/${indexes.length}...`);
        
        await pool.query(indexSQL);
        successCount++;
        
        // Extract index name for logging
        const indexMatch = indexSQL.match(/CREATE INDEX IF NOT EXISTS (\w+)/i);
        if (indexMatch) {
          console.log(`✅ Created: ${indexMatch[1]}`);
        }
        
      } catch (error) {
        errorCount++;
        console.error(`❌ Error creating index ${i + 1}:`, error.message);
      }
    }
    
    // Run ANALYZE to update statistics
    console.log('\n📊 Updating table statistics...');
    const tables = [
      'subscription_discounts',
      'subscription_expenses', 
      'partner_commission_config',
      'partner_commission_transactions',
      'advance_payments',
      'billing_subscriptions',
      'billing_transactions'
    ];
    
    for (const table of tables) {
      try {
        await pool.query(`ANALYZE ${table}`);
        console.log(`📈 Analyzed: ${table}`);
      } catch (error) {
        console.error(`⚠️  Could not analyze ${table}:`, error.message);
      }
    }
    
    console.log('\n📊 DEPLOYMENT SUMMARY:');
    console.log(`✅ Successful indexes: ${successCount}`);
    console.log(`❌ Failed indexes: ${errorCount}`);
    console.log(`📈 Success rate: ${((successCount / (successCount + errorCount)) * 100).toFixed(1)}%`);
    
    if (errorCount === 0) {
      console.log('\n🎉 All performance indexes deployed successfully!');
      console.log('🚀 Database is now optimized for discount system queries');
    }
    
    // Verify indexes were created
    const indexCheck = await pool.query(`
      SELECT indexname, tablename 
      FROM pg_indexes 
      WHERE indexname LIKE 'idx_%discount%' 
         OR indexname LIKE 'idx_%commission%' 
         OR indexname LIKE 'idx_%advance%'
         OR indexname LIKE 'idx_billing%'
      ORDER BY tablename, indexname
    `);
    
    console.log(`\n🔍 Verified ${indexCheck.rows.length} discount system indexes:`);
    indexCheck.rows.forEach(row => {
      console.log(`   📌 ${row.tablename}.${row.indexname}`);
    });
    
  } catch (error) {
    console.error('💥 Fatal error during deployment:', error);
    throw error;
  } finally {
    await pool.end();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the deployment
console.log('🚀 Starting Performance Index Deployment...');
console.log('📅 Date:', new Date().toISOString());
console.log('🎯 Target: Discount System Performance Optimization\n');

deployIndexes()
  .then(() => {
    console.log('\n✨ Deployment completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Deployment failed:', error);
    process.exit(1);
  });
