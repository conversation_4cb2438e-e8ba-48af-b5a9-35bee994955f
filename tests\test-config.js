/**
 * Test Configuration for Schopio System Tests
 * 
 * This file contains all test configurations, environment settings,
 * and test data for the comprehensive test suite.
 */

require('dotenv').config({ path: '.env.local' });

// Environment-specific configurations
const environments = {
  development: {
    baseUrl: 'http://localhost:3000',
    timeout: 30000,
    retries: 2,
    parallel: false
  },
  staging: {
    baseUrl: process.env.STAGING_URL || 'https://staging.schopio.com',
    timeout: 45000,
    retries: 3,
    parallel: true
  },
  production: {
    baseUrl: process.env.PRODUCTION_URL || 'https://schopio.com',
    timeout: 60000,
    retries: 3,
    parallel: true,
    readOnly: true // Only run read-only tests in production
  }
};

// Current environment
const currentEnv = process.env.NODE_ENV || 'development';
const envConfig = environments[currentEnv] || environments.development;

// Test user credentials
const testCredentials = {
  admin: {
    email: process.env.TEST_ADMIN_EMAIL || '<EMAIL>',
    password: process.env.TEST_ADMIN_PASSWORD || 'Admin@123456'
  },
  school: {
    email: process.env.TEST_SCHOOL_EMAIL || '<EMAIL>',
    password: process.env.TEST_SCHOOL_PASSWORD || 'password123'
  },
  partner: {
    email: process.env.TEST_PARTNER_EMAIL || '<EMAIL>',
    password: process.env.TEST_PARTNER_PASSWORD || 'partner123'
  }
};

// Test data templates
const testDataTemplates = {
  subscription: {
    studentCount: 100,
    pricePerStudent: 50,
    billingCycle: 'monthly',
    gracePeriodDays: 3,
    operationalExpenses: {
      databaseCosts: 500,
      websiteMaintenance: 300,
      supportCosts: 200,
      infrastructureCosts: 400
    }
  },
  school: {
    schoolName: 'Test School',
    contactPerson: 'Test Admin',
    email: '<EMAIL>',
    phone: '+91-9876543210',
    address: '123 Test Street, Test City, Test State 123456',
    actualStudentCount: 150,
    averageMonthlyFee: 2500
  },
  partner: {
    name: 'Test Partner',
    email: '<EMAIL>',
    companyName: 'Test Partner Company',
    phone: '+91-9876543211',
    address: '456 Partner Street, Partner City, Partner State 654321',
    profitSharePercentage: 40.00
  },
  discount: {
    percentage: 15.0,
    durationMonths: 6,
    reason: 'Test discount for integration testing'
  }
};

// Test categories and their configurations
const testCategories = {
  authentication: {
    name: 'Authentication & Authorization',
    icon: '🔐',
    critical: true,
    timeout: 10000,
    retries: 2
  },
  billing: {
    name: 'Billing System',
    icon: '💳',
    critical: true,
    timeout: 30000,
    retries: 3
  },
  partner: {
    name: 'Partner Management',
    icon: '🤝',
    critical: false,
    timeout: 20000,
    retries: 2
  },
  school: {
    name: 'School Portal',
    icon: '🏫',
    critical: true,
    timeout: 20000,
    retries: 2
  },
  admin: {
    name: 'Admin Dashboard',
    icon: '👨‍💼',
    critical: true,
    timeout: 25000,
    retries: 2
  },
  integration: {
    name: 'System Integration',
    icon: '🔗',
    critical: true,
    timeout: 15000,
    retries: 1
  },
  performance: {
    name: 'Performance Tests',
    icon: '⚡',
    critical: false,
    timeout: 60000,
    retries: 1
  },
  security: {
    name: 'Security Tests',
    icon: '🔒',
    critical: true,
    timeout: 20000,
    retries: 2
  }
};

// API endpoints for testing
const apiEndpoints = {
  auth: {
    adminLogin: '/api/admin/login',
    schoolLogin: '/api/school/login',
    partnerLogin: '/api/partner/login',
    logout: '/api/auth/logout'
  },
  admin: {
    dashboard: '/api/admin/dashboard',
    subscriptions: '/api/admin/subscriptions',
    partners: '/api/admin/partners',
    earnings: '/api/admin/earnings',
    billing: '/api/admin/billing',
    invoices: '/api/admin/invoices'
  },
  school: {
    dashboard: '/api/school/dashboard',
    billing: '/api/school/billing',
    subscription: '/api/school/subscription',
    invoices: '/api/school/billing/invoices',
    support: '/api/school/support'
  },
  partner: {
    dashboard: '/api/partner/dashboard',
    earnings: '/api/partner/earnings',
    clients: '/api/partner/clients',
    analytics: '/api/partner/analytics',
    withdrawals: '/api/partner/withdrawals'
  },
  billing: {
    invoicePdf: '/api/billing/invoice/:invoiceId/pdf',
    bulkPdf: '/api/billing/invoices/bulk-pdf',
    paymentOrder: '/api/billing/create-order',
    verifyPayment: '/api/billing/verify-payment'
  },
  system: {
    health: '/api/health',
    status: '/api/status'
  }
};

// Test result thresholds
const thresholds = {
  successRate: {
    critical: 95, // Critical tests must have 95%+ success rate
    normal: 85,   // Normal tests must have 85%+ success rate
    warning: 70   // Below 70% triggers warnings
  },
  performance: {
    responseTime: {
      fast: 500,     // < 500ms is fast
      acceptable: 2000, // < 2s is acceptable
      slow: 5000     // > 5s is slow
    },
    throughput: {
      minimum: 10,   // Minimum requests per second
      target: 50     // Target requests per second
    }
  }
};

// Test reporting configuration
const reporting = {
  formats: ['console', 'json', 'html'],
  outputDir: './test-results',
  includeTimestamps: true,
  includeStackTraces: true,
  generateCoverage: false,
  sendNotifications: {
    email: process.env.TEST_NOTIFICATION_EMAIL || false,
    slack: process.env.TEST_SLACK_WEBHOOK || false,
    teams: process.env.TEST_TEAMS_WEBHOOK || false
  }
};

// Mock data for testing
const mockData = {
  razorpayOrder: {
    id: 'order_test123456789',
    amount: 500000, // ₹5000 in paise
    currency: 'INR',
    status: 'created'
  },
  razorpayPayment: {
    id: 'pay_test123456789',
    order_id: 'order_test123456789',
    amount: 500000,
    currency: 'INR',
    status: 'captured',
    method: 'card'
  },
  invoice: {
    invoiceNumber: 'INV-TEST-001',
    amount: 5000,
    taxAmount: 900,
    totalAmount: 5900,
    status: 'sent',
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
  }
};

// Export configuration
module.exports = {
  // Environment configuration
  environment: currentEnv,
  config: {
    ...envConfig,
    ...testCredentials
  },
  
  // Test data and templates
  testData: testDataTemplates,
  mockData,
  
  // Test configuration
  categories: testCategories,
  endpoints: apiEndpoints,
  thresholds,
  reporting,
  
  // Utility functions
  getEndpoint: (category, endpoint, params = {}) => {
    let url = apiEndpoints[category]?.[endpoint];
    if (!url) return null;
    
    // Replace URL parameters
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`:${key}`, value);
    });
    
    return `${envConfig.baseUrl}${url}`;
  },
  
  isProductionMode: () => currentEnv === 'production',
  isCriticalTest: (category) => testCategories[category]?.critical || false,
  
  // Test data generators
  generateTestEmail: (prefix = 'test') => `${prefix}+${Date.now()}@example.com`,
  generateTestPhone: () => `+91-${Math.floor(Math.random() * 9000000000) + 1000000000}`,
  generateTestId: () => `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  
  // Validation helpers
  validateResponse: (response, expectedStatus = 200) => {
    if (response.status !== expectedStatus) {
      throw new Error(`Expected status ${expectedStatus}, got ${response.status}`);
    }
    return true;
  },
  
  validateJsonResponse: (data, requiredFields = []) => {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid JSON response');
    }
    
    for (const field of requiredFields) {
      if (!(field in data)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    return true;
  }
};
