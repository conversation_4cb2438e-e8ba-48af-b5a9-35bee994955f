import { escrowReleaseJob } from './escrowReleaseJob'

/**
 * Escrow Release Scheduler
 * Manages automated execution of escrow release jobs
 * with configurable intervals and safety controls
 */

export interface SchedulerConfig {
  enabled: boolean
  intervalMinutes: number
  maxBatchSize: number
  maxDailyAmount: number
  riskThreshold: number
  emergencyStopEnabled: boolean
}

class EscrowSchedulerService {
  private intervalId: NodeJS.Timeout | null = null
  private isRunning = false
  private config: SchedulerConfig = {
    enabled: true,
    intervalMinutes: 60, // Run every hour
    maxBatchSize: 50,
    maxDailyAmount: 500000, // ₹5 lakh daily limit
    riskThreshold: 70,
    emergencyStopEnabled: false
  }

  /**
   * Start the scheduler
   */
  start(customConfig?: Partial<SchedulerConfig>) {
    if (this.intervalId) {
      console.log('⚠️ Scheduler already running')
      return
    }

    // Update config
    this.config = { ...this.config, ...customConfig }

    if (!this.config.enabled) {
      console.log('📅 Scheduler disabled in config')
      return
    }

    console.log(`🚀 Starting escrow release scheduler - interval: ${this.config.intervalMinutes} minutes`)

    // Run immediately on start
    this.executeJob()

    // Schedule recurring execution
    this.intervalId = setInterval(() => {
      this.executeJob()
    }, this.config.intervalMinutes * 60 * 1000)

    console.log('✅ Escrow release scheduler started')
  }

  /**
   * Stop the scheduler
   */
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
      console.log('🛑 Escrow release scheduler stopped')
    }
  }

  /**
   * Execute the release job
   */
  private async executeJob() {
    if (this.isRunning) {
      console.log('⏳ Previous job still running, skipping...')
      return
    }

    this.isRunning = true

    try {
      console.log('🔄 Executing scheduled escrow release job...')

      const result = await escrowReleaseJob.executeReleaseJob({
        maxBatchSize: this.config.maxBatchSize,
        maxDailyAmount: this.config.maxDailyAmount,
        emergencyStopEnabled: this.config.emergencyStopEnabled,
        dryRun: false,
        riskThreshold: this.config.riskThreshold
      })

      console.log(`✅ Scheduled job completed: ${result.successfulReleases}/${result.processedCount} releases`)

      // Log significant events
      if (result.failedReleases > 0) {
        console.warn(`⚠️ ${result.failedReleases} releases failed:`, result.errors)
      }

      if (result.totalAmountReleased > 100000) { // ₹1 lakh+
        console.log(`💰 Large release batch: ₹${result.totalAmountReleased} released`)
      }

    } catch (error) {
      console.error('❌ Scheduled job execution failed:', error)
    } finally {
      this.isRunning = false
    }
  }

  /**
   * Update scheduler configuration
   */
  updateConfig(newConfig: Partial<SchedulerConfig>) {
    const wasRunning = this.intervalId !== null
    
    if (wasRunning) {
      this.stop()
    }

    this.config = { ...this.config, ...newConfig }

    if (wasRunning && this.config.enabled) {
      this.start()
    }

    console.log('⚙️ Scheduler configuration updated:', this.config)
  }

  /**
   * Get current scheduler status
   */
  getStatus() {
    return {
      isRunning: this.intervalId !== null,
      jobInProgress: this.isRunning,
      config: this.config,
      nextRunTime: this.intervalId ? 
        new Date(Date.now() + this.config.intervalMinutes * 60 * 1000) : 
        null
    }
  }

  /**
   * Emergency stop all operations
   */
  emergencyStop(reason: string) {
    console.log(`🚨 EMERGENCY STOP: ${reason}`)
    
    this.stop()
    this.config.emergencyStopEnabled = true
    
    return {
      success: true,
      message: 'Scheduler emergency stopped',
      reason,
      timestamp: new Date()
    }
  }

  /**
   * Resume operations after emergency stop
   */
  resumeOperations() {
    console.log('🔄 Resuming scheduler operations')
    
    this.config.emergencyStopEnabled = false
    this.start()
    
    return {
      success: true,
      message: 'Scheduler operations resumed',
      timestamp: new Date()
    }
  }
}

// Export singleton instance
export const escrowScheduler = new EscrowSchedulerService()

// Auto-start in production (can be disabled via environment variable)
if (process.env.NODE_ENV === 'production' && process.env.DISABLE_ESCROW_SCHEDULER !== 'true') {
  // Start with production-safe defaults
  escrowScheduler.start({
    enabled: true,
    intervalMinutes: 60, // Every hour
    maxBatchSize: 25, // Conservative batch size
    maxDailyAmount: 250000, // ₹2.5 lakh daily limit
    riskThreshold: 60, // Lower risk threshold for production
    emergencyStopEnabled: false
  })
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('📴 Shutting down escrow scheduler...')
  escrowScheduler.stop()
})

process.on('SIGINT', () => {
  console.log('📴 Shutting down escrow scheduler...')
  escrowScheduler.stop()
})
