import { Hono } from "hono"
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { clientPaymentService } from '@/src/services/clientPaymentService'
import {
  schoolAuthMiddleware,
  requireSchoolRole,
  getCurrentSchoolUser
} from '@/src/middleware/school-auth'

const app = new Hono()

// Apply authentication middleware to all routes
app.use('*', schoolAuthMiddleware)

/**
 * Get invoice payment information
 * GET /api/client-payments/invoice/:invoiceId
 */
app.get('/invoice/:invoiceId', 
  requireSchoolRole(['admin', 'billing']),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const invoiceId = c.req.param('invoiceId')

      if (!invoiceId) {
        return c.json({
          success: false,
          error: 'Invoice ID is required'
        }, 400)
      }

      const result = await clientPaymentService.getInvoicePaymentInfo(invoiceId, clientId)

      if (!result.success) {
        return c.json({
          success: false,
          error: result.error
        }, result.error?.includes('not found') ? 404 : 400)
      }

      return c.json({
        success: true,
        data: result.data
      })

    } catch (error) {
      console.error('Get invoice payment info error:', error)
      return c.json({
        success: false,
        error: 'Failed to fetch invoice payment information'
      }, 500)
    }
  }
)

/**
 * Create payment order for invoice
 * POST /api/client-payments/create-order
 */
app.post('/create-order',
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    invoiceId: z.string().uuid('Invalid invoice ID format'),
    paymentMethod: z.enum(['card', 'netbanking', 'upi', 'wallet']).optional(),
    notes: z.string().optional()
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const { invoiceId, paymentMethod, notes } = c.req.valid('json')

      console.log(`🔄 Creating payment order for invoice ${invoiceId} by client ${clientId}`)

      const result = await clientPaymentService.createPaymentOrder(
        invoiceId, 
        clientId, 
        paymentMethod
      )

      if (!result.success) {
        console.error('Payment order creation failed:', result.error)
        return c.json({
          success: false,
          error: result.error
        }, 400)
      }

      console.log(`✅ Payment order created successfully for invoice ${invoiceId}`)

      return c.json({
        success: true,
        data: result.data
      })

    } catch (error) {
      console.error('Create payment order error:', error)
      return c.json({
        success: false,
        error: 'Failed to create payment order. Please try again.'
      }, 500)
    }
  }
)

/**
 * Verify payment completion
 * POST /api/client-payments/verify
 */
app.post('/verify',
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    razorpayOrderId: z.string().min(1, 'Razorpay order ID is required'),
    razorpayPaymentId: z.string().min(1, 'Razorpay payment ID is required'),
    razorpaySignature: z.string().min(1, 'Razorpay signature is required'),
    invoiceId: z.string().uuid('Invalid invoice ID format')
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const verificationData = c.req.valid('json')

      console.log(`🔄 Verifying payment for invoice ${verificationData.invoiceId}`)

      const result = await clientPaymentService.verifyPayment({
        ...verificationData,
        clientId
      })

      if (!result.success) {
        console.error('Payment verification failed:', result.error)
        return c.json({
          success: false,
          error: result.error
        }, 400)
      }

      console.log(`✅ Payment verified successfully for invoice ${verificationData.invoiceId}`)

      return c.json({
        success: true,
        data: result.data,
        message: 'Payment verified successfully'
      })

    } catch (error) {
      console.error('Payment verification error:', error)
      return c.json({
        success: false,
        error: 'Payment verification failed. Please contact support if amount was deducted.'
      }, 500)
    }
  }
)

/**
 * Get client payment history
 * GET /api/client-payments/history
 */
app.get('/history',
  requireSchoolRole(['admin', 'billing', 'viewer']),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      
      // Get pagination parameters
      const page = parseInt(c.req.query('page') || '1')
      const limit = parseInt(c.req.query('limit') || '10')

      // Validate pagination parameters
      if (page < 1 || limit < 1 || limit > 100) {
        return c.json({
          success: false,
          error: 'Invalid pagination parameters'
        }, 400)
      }

      const result = await clientPaymentService.getClientPaymentHistory(clientId, page, limit)

      if (!result.success) {
        return c.json({
          success: false,
          error: result.error
        }, 500)
      }

      return c.json({
        success: true,
        data: result.data
      })

    } catch (error) {
      console.error('Get payment history error:', error)
      return c.json({
        success: false,
        error: 'Failed to fetch payment history'
      }, 500)
    }
  }
)

/**
 * Get payment status for specific invoice
 * GET /api/client-payments/invoice/:invoiceId/status
 */
app.get('/invoice/:invoiceId/status',
  requireSchoolRole(['admin', 'billing', 'viewer']),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const invoiceId = c.req.param('invoiceId')

      if (!invoiceId) {
        return c.json({
          success: false,
          error: 'Invoice ID is required'
        }, 400)
      }

      const result = await clientPaymentService.getInvoicePaymentInfo(invoiceId, clientId)

      if (!result.success) {
        return c.json({
          success: false,
          error: result.error
        }, result.error?.includes('not found') ? 404 : 400)
      }

      // Return simplified status information
      const invoice = result.data
      return c.json({
        success: true,
        data: {
          invoiceId: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          status: invoice.status,
          amount: invoice.totalAmount,
          canPay: invoice.canPay,
          paymentCount: invoice.paymentHistory.length,
          lastPaymentDate: invoice.paymentHistory.length > 0 
            ? invoice.paymentHistory[0].processedAt 
            : null
        }
      })

    } catch (error) {
      console.error('Get payment status error:', error)
      return c.json({
        success: false,
        error: 'Failed to fetch payment status'
      }, 500)
    }
  }
)

/**
 * Health check for client payment service
 * GET /api/client-payments/health
 */
app.get('/health', async (c) => {
  try {
    return c.json({
      success: true,
      status: 'healthy',
      service: 'client-payment-service',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    })
  } catch (error) {
    return c.json({
      success: false,
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

export default app
