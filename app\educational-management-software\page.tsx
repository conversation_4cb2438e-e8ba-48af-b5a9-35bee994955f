import { Metadata } from 'next'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Breadcrumb, BreadcrumbStructuredData } from '@/components/ui/Breadcrumb'
import { 
  GraduationCap, 
  Users, 
  BookOpen, 
  Calendar, 
  CreditCard, 
  BarChart3, 
  Shield, 
  CheckCircle,
  ArrowRight,
  Star,
  Brain,
  Target
} from 'lucide-react'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Best Educational Management Software in India | Complete School Solution | Schopio',
  description: 'Schopio is the leading educational management software in India. Complete solution for schools with student management, academic planning, and administrative tools. Trusted by 500+ educational institutions.',
  keywords: 'educational management software, educational institution management, school management software, academic management system, educational administration software, education management platform, school administration system',
  openGraph: {
    title: 'Best Educational Management Software in India | Complete School Solution | Schopio',
    description: 'Schopio is the leading educational management software in India. Complete solution for schools with student management, academic planning, and administrative tools. Trusted by 500+ educational institutions.',
    type: 'website',
  },
}

export default function EducationalManagementSoftwarePage() {
  const breadcrumbItems = [
    { label: 'Educational Management Software' }
  ]

  const managementAreas = [
    {
      icon: Users,
      title: "Student Management",
      description: "Comprehensive student information system with academic records and progress tracking",
      features: ["Student profiles", "Academic history", "Progress tracking", "Parent communication"]
    },
    {
      icon: GraduationCap,
      title: "Academic Management",
      description: "Complete academic planning with curriculum management and assessment tools",
      features: ["Curriculum planning", "Lesson planning", "Assessment tools", "Grade management"]
    },
    {
      icon: Calendar,
      title: "Administrative Management",
      description: "Streamlined administrative processes with scheduling and resource management",
      features: ["Timetable management", "Resource allocation", "Event planning", "Communication tools"]
    },
    {
      icon: CreditCard,
      title: "Financial Management",
      description: "Complete financial management with fee collection and budget planning",
      features: ["Fee management", "Budget planning", "Financial reports", "Payment tracking"]
    },
    {
      icon: BarChart3,
      title: "Performance Management",
      description: "Advanced analytics and reporting for educational performance insights",
      features: ["Performance analytics", "Custom reports", "Data insights", "Trend analysis"]
    },
    {
      icon: Brain,
      title: "AI-Powered Insights",
      description: "Intelligent analytics for predictive insights and automated recommendations",
      features: ["Predictive analytics", "Smart recommendations", "Risk assessment", "Performance optimization"]
    }
  ]

  const benefits = [
    "Complete educational institution management",
    "Streamlined academic and administrative processes",
    "Enhanced student-teacher-parent communication",
    "Real-time performance tracking and analytics",
    "Automated administrative workflows",
    "Comprehensive reporting and insights",
    "Scalable solution for all institution sizes",
    "24/7 support and training programs"
  ]

  const institutions = [
    { type: "K-12 Schools", count: "300+" },
    { type: "Higher Education", count: "150+" },
    { type: "Coaching Centers", count: "50+" },
    { type: "International Schools", count: "25+" }
  ]

  return (
    <main className="min-h-screen bg-white">
      <BreadcrumbStructuredData items={breadcrumbItems} />
      
      {/* Breadcrumb Navigation */}
      <section className="py-4 bg-white border-b">
        <div className="container mx-auto px-4">
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-purple-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-purple-100 text-purple-700 border border-purple-200 px-4 py-2 rounded-full text-sm font-bold mb-6">
              <GraduationCap className="w-4 h-4" />
              Complete Educational Solution
            </div>
            <h1 className="text-5xl lg:text-7xl font-bold text-slate-900 mb-6">
              Best Educational Management 
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent"> Software</span>
            </h1>
            <p className="text-xl text-slate-600 leading-relaxed mb-8">
              Schopio is India's leading educational management software trusted by 500+ institutions. 
              Complete solution for schools and educational organizations with student management, 
              academic planning, administrative tools, and AI-powered insights.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo">
                <Button size="lg" className="bg-purple-600 hover:bg-purple-700">
                  Get Free Demo <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/packages">
                <Button variant="outline" size="lg">
                  View Pricing
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Management Areas Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-4">
              Complete Educational Management Areas
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Comprehensive management solution covering all aspects of educational institutions
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {managementAreas.map((area, index) => (
              <Card key={index} className="border-2 hover:border-purple-200 transition-colors">
                <CardHeader>
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <area.icon className="w-6 h-6 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900">{area.title}</h3>
                  <p className="text-slate-600">{area.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {area.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center gap-2 text-sm text-slate-600">
                        <CheckCircle className="w-4 h-4 text-emerald-500" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-slate-900 mb-4">
                Why Choose Our Educational Management Software?
              </h2>
              <p className="text-xl text-slate-600">
                Transform your educational institution with comprehensive management solution
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-6 h-6 text-emerald-500 flex-shrink-0" />
                  <span className="text-slate-700">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Institution Types Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-900 mb-4">
              Trusted by Educational Institutions Across India
            </h2>
            <p className="text-xl text-slate-600">
              Our educational management software serves diverse institution types
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {institutions.map((institution, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Target className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900 mb-2">{institution.count}</h3>
                <p className="text-slate-600">{institution.type}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Transform Your Educational Institution?
            </h2>
            <p className="text-xl text-purple-100 mb-8">
              Join 500+ educational institutions using Schopio's comprehensive management software. 
              Get started with a free demo today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo">
                <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                  Get Free Demo <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/packages">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-purple-600">
                  View Pricing Plans
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
