import Razorpay from 'razorpay'
import crypto from 'crypto'
import { validatePaymentVerification, validateWebhookSignature } from 'razorpay/dist/utils/razorpay-utils'

interface RazorpayConfig {
  keyId: string
  keySecret: string
  webhookSecret: string
}

interface CreateOrderOptions {
  amount: number // Amount in paise (₹1 = 100 paise)
  currency: string
  receipt: string
  notes?: Record<string, string>
}

interface CreateSubscriptionOptions {
  planId: string
  customerEmail: string
  customerName: string
  customerContact?: string
  totalCount?: number
  notes?: Record<string, string>
  addons?: Array<{
    item: {
      name: string
      amount: number
      currency: string
    }
  }>
  startAt?: number // Unix timestamp for subscription start
  expireBy?: number // Unix timestamp for subscription expiry
  customerNotify?: 0 | 1
}

interface CreateCustomerOptions {
  name: string
  email: string
  contact?: string
  failExisting?: 0 | 1
  notes?: Record<string, string>
}

interface SubscriptionAuthOptions {
  subscriptionId: string
  customerId: string
  amount: number // Amount in paise for authentication transaction
  currency: string
  description: string
  notes?: Record<string, string>
}

interface VerifyPaymentOptions {
  razorpayOrderId: string
  razorpayPaymentId: string
  razorpaySignature: string
}

class RazorpayService {
  private razorpay: Razorpay
  private webhookSecret: string
  private keySecret: string

  constructor(config: RazorpayConfig) {
    this.razorpay = new Razorpay({
      key_id: config.keyId,
      key_secret: config.keySecret
    })
    this.webhookSecret = config.webhookSecret
    this.keySecret = config.keySecret
  }

  /**
   * Create a Razorpay order for one-time payment with production features (2025 best practices)
   */
  async createOrder(options: CreateOrderOptions): Promise<any> {
    try {
      // Enhanced validation
      if (!options.amount || options.amount < 100) {
        throw new Error('Minimum payment amount is ₹1 (100 paise)')
      }

      if (!options.receipt || options.receipt.length > 40) {
        throw new Error('Receipt ID is required and must be 40 characters or less')
      }

      if (!options.currency || options.currency !== 'INR') {
        throw new Error('Currency must be INR for Indian payments')
      }

      // Create order with production-level configuration (2025 standards)
      const orderData = {
        amount: options.amount,
        currency: options.currency,
        receipt: options.receipt,
        notes: {
          ...options.notes,
          created_at: new Date().toISOString(),
          environment: process.env.NODE_ENV || 'development',
          payment_type: 'subscription_payment',
          api_version: '2025.1',
          integration: 'schopio_manual_billing'
        },
        // Production settings (2025 best practices)
        payment_capture: 1, // Auto-capture payments for immediate settlement
        partial_payment: false // Don't allow partial payments for clean transactions
      }

      console.log('🔄 [Production] Creating Razorpay order with 2025 standards:', {
        amount: orderData.amount,
        currency: orderData.currency,
        receipt: orderData.receipt,
        receiptLength: orderData.receipt.length,
        paymentCapture: orderData.payment_capture,
        partialPayment: orderData.partial_payment
      })

      const order = await this.razorpay.orders.create(orderData)

      // Enhanced success logging
      console.log('✅ [Production] Razorpay order created successfully:', {
        id: order.id,
        amount: order.amount,
        currency: order.currency,
        receipt: order.receipt,
        status: order.status,
        created_at: order.created_at,
        attempts: order.attempts,
        amount_due: order.amount_due
      })

      return {
        success: true,
        order
      }
    } catch (error) {
      // Enhanced error logging with detailed information
      console.error('❌ [Production] Razorpay create order error:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        fullError: error, // Log the full error object for debugging
        errorType: typeof error,
        errorConstructor: error?.constructor?.name,
        orderData: {
          amount: options.amount,
          currency: options.currency,
          receipt: options.receipt,
          receiptLength: options.receipt?.length
        }
      })

      // Extract detailed error message
      let errorMessage = 'Failed to create payment order'
      let errorCode = 'UNKNOWN_ERROR'

      if (error && typeof error === 'object') {
        // Handle Razorpay API errors
        if ('error' in error && error.error && typeof error.error === 'object') {
          const razorpayError = error.error as any
          errorMessage = razorpayError.description || razorpayError.code || 'Razorpay API error'
          errorCode = razorpayError.code || 'RAZORPAY_ERROR'
        } else if ('message' in error && typeof error.message === 'string') {
          errorMessage = error.message
        } else if ('statusCode' in error) {
          errorMessage = `HTTP ${error.statusCode} error`
          errorCode = `HTTP_${error.statusCode}`
        }
      }

      // Return structured error response
      return {
        success: false,
        error: errorMessage,
        errorCode: errorCode,
        retryable: this.isRetryableError(error)
      }
    }
  }

  /**
   * Create a Razorpay plan for subscription billing
   */
  async createPlan(planData: {
    period: 'monthly' | 'yearly'
    interval: number
    amount: number // Amount in paise
    currency: string
    description: string
  }): Promise<any> {
    try {
      const plan = await this.razorpay.plans.create({
        period: planData.period,
        interval: planData.interval,
        item: {
          name: planData.description,
          amount: planData.amount,
          currency: planData.currency,
          description: planData.description
        }
      })

      return {
        success: true,
        plan
      }
    } catch (error) {
      console.error('Razorpay create plan error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Find existing customer by email
   */
  async findCustomerByEmail(email: string): Promise<{ success: boolean; customer?: any; error?: string }> {
    try {
      // Use the public method to get customers
      const customers = await this.razorpay.customers.all({
        count: 100 // Get up to 100 customers to search through
      })

      // Find customer with matching email
      const matchingCustomer = customers.items.find((customer: any) => customer.email === email)

      if (matchingCustomer) {
        return { success: true, customer: matchingCustomer }
      } else {
        return { success: false, error: 'Customer not found' }
      }
    } catch (error: any) {
      console.error('Error finding customer by email:', error)
      return { success: false, error: error.message || 'Unknown error' }
    }
  }

  /**
   * Create a Razorpay customer for subscription billing
   */
  async createCustomer(options: CreateCustomerOptions): Promise<any> {
    try {
      const customer = await this.razorpay.customers.create({
        name: options.name,
        email: options.email,
        contact: options.contact,
        fail_existing: options.failExisting || 0,
        notes: options.notes || {}
      })

      return {
        success: true,
        customer
      }
    } catch (error) {
      console.error('Razorpay create customer error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create a Razorpay subscription with enhanced options
   */
  async createSubscription(options: CreateSubscriptionOptions): Promise<any> {
    try {
      const subscriptionData: any = {
        plan_id: options.planId,
        customer_notify: options.customerNotify ?? 1,
        total_count: options.totalCount || 12, // Default to 12 months
        notes: {
          customer_email: options.customerEmail,
          customer_name: options.customerName,
          ...options.notes
        }
      }

      // Add optional parameters
      if (options.addons && options.addons.length > 0) {
        subscriptionData.addons = options.addons
      }

      if (options.startAt) {
        subscriptionData.start_at = options.startAt
      }

      if (options.expireBy) {
        subscriptionData.expire_by = options.expireBy
      }

      const subscription = await this.razorpay.subscriptions.create(subscriptionData)

      return {
        success: true,
        subscription
      }
    } catch (error) {
      console.error('Razorpay create subscription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create authentication transaction for subscription
   * This is the initial payment that authorizes future automatic charges
   */
  async createSubscriptionAuth(options: SubscriptionAuthOptions): Promise<any> {
    try {
      // Create an order for authentication transaction
      const authOrder = await this.razorpay.orders.create({
        amount: options.amount,
        currency: options.currency,
        receipt: `AUTH-${options.subscriptionId}-${Date.now()}`,
        notes: {
          subscription_id: options.subscriptionId,
          customer_id: options.customerId,
          auth_transaction: 'true',
          ...options.notes
        }
      })

      return {
        success: true,
        authOrder,
        subscriptionId: options.subscriptionId
      }
    } catch (error) {
      console.error('Razorpay create subscription auth error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Activate subscription after successful authentication
   */
  async activateSubscription(subscriptionId: string): Promise<any> {
    try {
      // Fetch subscription details first
      const subscription = await this.razorpay.subscriptions.fetch(subscriptionId)

      if (subscription.status === 'created') {
        // Subscription is ready to be activated
        return {
          success: true,
          subscription,
          message: 'Subscription is ready for activation'
        }
      }

      return {
        success: true,
        subscription,
        message: `Subscription status: ${subscription.status}`
      }
    } catch (error) {
      console.error('Razorpay activate subscription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Cancel subscription with enhanced options
   */
  async cancelSubscription(subscriptionId: string, cancelAtCycleEnd: boolean = false): Promise<any> {
    try {
      const subscription = await this.razorpay.subscriptions.cancel(subscriptionId, cancelAtCycleEnd)

      return {
        success: true,
        subscription
      }
    } catch (error) {
      console.error('Razorpay cancel subscription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Pause subscription
   */
  async pauseSubscription(subscriptionId: string, pauseAt?: number): Promise<any> {
    try {
      const pauseData: any = {}
      if (pauseAt) {
        pauseData.pause_at = pauseAt
      }

      const subscription = await this.razorpay.subscriptions.pause(subscriptionId, pauseData)

      return {
        success: true,
        subscription
      }
    } catch (error) {
      console.error('Razorpay pause subscription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Resume subscription
   */
  async resumeSubscription(subscriptionId: string, resumeAt?: number): Promise<any> {
    try {
      const resumeData: any = {}
      if (resumeAt) {
        resumeData.resume_at = resumeAt
      }

      const subscription = await this.razorpay.subscriptions.resume(subscriptionId, resumeData)

      return {
        success: true,
        subscription
      }
    } catch (error) {
      console.error('Razorpay resume subscription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Fetch payment details for receipt generation
   */
  async fetchPaymentDetails(paymentId: string): Promise<any> {
    try {
      const payment = await this.razorpay.payments.fetch(paymentId)

      console.log('✅ Payment details fetched:', {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        method: payment.method,
        created_at: payment.created_at
      })

      return {
        success: true,
        payment
      }
    } catch (error) {
      console.error('❌ Failed to fetch payment details:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Fetch order details
   */
  async fetchOrderDetails(orderId: string): Promise<any> {
    try {
      const order = await this.razorpay.orders.fetch(orderId)

      console.log('✅ Order details fetched:', {
        id: order.id,
        amount: order.amount,
        status: order.status,
        receipt: order.receipt
      })

      return {
        success: true,
        order
      }
    } catch (error) {
      console.error('❌ Failed to fetch order details:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Verify payment signature using Razorpay's official utility (2025 best practice)
   */
  verifyPaymentSignature(options: VerifyPaymentOptions): boolean {
    try {
      console.log('🔐 [Production] Verifying payment signature using official Razorpay utility')

      // Use Razorpay's official validatePaymentVerification utility
      const isValid = validatePaymentVerification(
        {
          order_id: options.razorpayOrderId,
          payment_id: options.razorpayPaymentId
        },
        options.razorpaySignature,
        this.keySecret
      )

      console.log('✅ Payment signature verification result:', {
        orderId: options.razorpayOrderId,
        paymentId: options.razorpayPaymentId,
        isValid: isValid,
        method: 'official_razorpay_utility'
      })

      return isValid
    } catch (error) {
      console.error('❌ Payment signature verification error:', error)

      // Fallback to manual verification for compatibility
      try {
        console.log('🔄 Falling back to manual signature verification')
        const body = options.razorpayOrderId + '|' + options.razorpayPaymentId
        const expectedSignature = crypto
          .createHmac('sha256', this.keySecret)
          .update(body.toString())
          .digest('hex')

        const isValid = expectedSignature === options.razorpaySignature

        console.log('✅ Fallback verification result:', {
          orderId: options.razorpayOrderId,
          paymentId: options.razorpayPaymentId,
          isValid: isValid,
          method: 'manual_fallback'
        })

        return isValid
      } catch (fallbackError) {
        console.error('❌ Fallback verification also failed:', fallbackError)
        return false
      }
    }
  }

  /**
   * Verify webhook signature using Razorpay's official utility (2025 best practice)
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    try {
      console.log('🔐 [Production] Verifying webhook signature using official Razorpay utility')

      // Use Razorpay's official validateWebhookSignature utility
      const isValid = validateWebhookSignature(body, signature, this.webhookSecret)

      console.log('✅ Webhook signature verification result:', {
        isValid: isValid,
        method: 'official_razorpay_utility'
      })

      return isValid
    } catch (error) {
      console.error('❌ Webhook signature verification error:', error)

      // Fallback to manual verification for compatibility
      try {
        console.log('🔄 Falling back to manual webhook verification')
        const expectedSignature = crypto
          .createHmac('sha256', this.webhookSecret)
          .update(body)
          .digest('hex')

        // Use crypto.timingSafeEqual to prevent timing attacks
        const isValid = crypto.timingSafeEqual(
          Buffer.from(expectedSignature),
          Buffer.from(signature)
        )

        console.log('✅ Fallback webhook verification result:', {
          isValid: isValid,
          method: 'manual_fallback'
        })

        return isValid
      } catch (fallbackError) {
        console.error('❌ Fallback webhook verification also failed:', fallbackError)
        return false
      }
    }
  }

  /**
   * Get payment details
   */
  async getPayment(paymentId: string): Promise<any> {
    try {
      const payment = await this.razorpay.payments.fetch(paymentId)
      return {
        success: true,
        payment
      }
    } catch (error) {
      console.error('Get payment error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get subscription details
   */
  async getSubscription(subscriptionId: string): Promise<any> {
    try {
      const subscription = await this.razorpay.subscriptions.fetch(subscriptionId)
      return {
        success: true,
        subscription
      }
    } catch (error) {
      console.error('Get subscription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }



  /**
   * Create invoice for subscription
   */
  async createInvoice(invoiceData: {
    customerId?: string
    amount: number
    currency: string
    description: string
    dueDate?: number // Unix timestamp
    receipt?: string
  }): Promise<any> {
    try {
      const invoicePayload: any = {
        type: 'invoice',
        amount: invoiceData.amount,
        currency: invoiceData.currency,
        description: invoiceData.description,
        receipt: invoiceData.receipt
      }

      if (invoiceData.customerId) {
        invoicePayload.customer_id = invoiceData.customerId
      }

      if (invoiceData.dueDate) {
        invoicePayload.due_date = invoiceData.dueDate
      }

      const invoice = await this.razorpay.invoices.create(invoicePayload)

      return {
        success: true,
        invoice
      }
    } catch (error) {
      console.error('Create invoice error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Process webhook event
   */
  processWebhookEvent(event: any): {
    eventType: string
    entityType: string
    entityId: string
    data: any
  } {
    return {
      eventType: event.event,
      entityType: event.entity,
      entityId: event.payload?.[event.entity]?.entity?.id || '',
      data: event.payload?.[event.entity]?.entity || {}
    }
  }

  /**
   * Get error code from Razorpay error for better error handling (2025 best practice)
   */
  private getErrorCode(error: any): string {
    if (error?.error?.code) {
      return error.error.code
    }
    if (error?.statusCode) {
      return `HTTP_${error.statusCode}`
    }
    return 'UNKNOWN_ERROR'
  }

  /**
   * Determine if error is retryable (2025 production standard)
   */
  private isRetryableError(error: any): boolean {
    const retryableCodes = [
      'GATEWAY_ERROR',
      'SERVER_ERROR',
      'NETWORK_ERROR',
      'TIMEOUT_ERROR'
    ]

    const errorCode = this.getErrorCode(error)
    return retryableCodes.includes(errorCode) ||
           (error?.statusCode >= 500 && error?.statusCode < 600)
  }

  /**
   * Generate production-level receipt ID (max 40 chars)
   */
  static generateReceiptId(prefix: string, identifier: string): string {
    const timestamp = Date.now().toString().slice(-8) // Last 8 digits
    const shortId = identifier.slice(-6) // Last 6 chars of identifier
    const receipt = `${prefix}-${shortId}-${timestamp}`

    // Ensure it's within 40 character limit
    if (receipt.length > 40) {
      const maxPrefixLength = 40 - 15 // Reserve 15 chars for ID and timestamp
      const truncatedPrefix = prefix.slice(0, maxPrefixLength)
      return `${truncatedPrefix}-${shortId}-${timestamp}`
    }

    return receipt
  }
}

// Create singleton instance
const razorpayConfig: RazorpayConfig = {
  keyId: process.env.RAZORPAY_KEY_ID || '',
  keySecret: process.env.RAZORPAY_KEY_SECRET || '',
  webhookSecret: process.env.RAZORPAY_WEBHOOK_SECRET || ''
}

export const razorpayService = new RazorpayService(razorpayConfig)

// Export class for testing
export { RazorpayService }
