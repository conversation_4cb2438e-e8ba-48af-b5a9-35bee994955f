import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

// Define route patterns for each user type
const SCHOOL_ROUTES = {
  protected: ['/school'],
  public: ['/school/login', '/school/signup', '/school/forgot-password'],
  dashboard: '/school/dashboard'
}

const ADMIN_ROUTES = {
  protected: ['/admin'],
  public: ['/admin/login'],
  dashboard: '/admin/dashboard'
}

const PARTNER_ROUTES = {
  protected: ['/partner'],
  public: ['/partner/login', '/partner/signup', '/partner/forgot-password'],
  dashboard: '/partner/dashboard'
}

// JWT token validation helper
async function validateToken(token: string, expectedType: string): Promise<any> {
  try {
    // Basic token structure check
    if (!token || typeof token !== 'string' || token.split('.').length !== 3) {
      return null
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    // Check token type and basic structure
    if (!decoded || decoded.type !== expectedType) {
      return null
    }

    // Check token expiration
    const now = Math.floor(Date.now() / 1000)
    if (decoded.exp && decoded.exp < now) {
      return null
    }

    // Check required fields
    if (!decoded.userId || !decoded.email) {
      return null
    }

    return decoded
  } catch (error) {
    // Log error for debugging but don't throw
    console.error(`Token validation error for ${expectedType}:`, error instanceof Error ? error.message : 'Unknown error')
    return null
  }
}

// Check if user is authenticated for specific type
async function isAuthenticated(request: NextRequest, userType: string): Promise<boolean> {
  try {
    // Get token from different sources
    let token = null

    // 1. Check Authorization header
    const authHeader = request.headers.get('authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    }

    // 2. Check cookies based on user type
    if (!token) {
      const cookieName = `${userType}Token`
      token = request.cookies.get(cookieName)?.value
    }

    // 3. For client-side navigation, we'll be more permissive
    // The client-side components will handle the actual validation
    if (!token) {
      // Check if this is a client-side navigation by looking at headers
      const referer = request.headers.get('referer')
      const userAgent = request.headers.get('user-agent')

      // If it's a browser navigation and no token in cookies,
      // let client-side handle it to avoid redirect loops
      if (referer && userAgent && !userAgent.includes('bot')) {
        return false
      }
    }

    if (!token) {
      return false
    }

    // Validate token
    const decoded = await validateToken(token, userType)

    // If token is invalid, clear it from cookies to prevent loops
    if (decoded === null && token) {
      // Token exists but is invalid - this could cause redirect loops
      return false
    }

    return decoded !== null
  } catch (error) {
    console.error(`Authentication check failed for ${userType}:`, error)
    return false
  }
}

// Check if route matches pattern
function matchesRoute(pathname: string, patterns: string[]): boolean {
  return patterns.some(pattern => {
    if (pattern.endsWith('*')) {
      return pathname.startsWith(pattern.slice(0, -1))
    }
    return pathname === pattern || pathname.startsWith(pattern + '/')
  })
}

// Main middleware function
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const url = request.nextUrl.clone()
  
  // Skip middleware for static files, API routes, and other non-page routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico' ||
    pathname.startsWith('/auth-test') || // Skip auth test page
    pathname.startsWith('/clear-admin-auth') || // Skip clear auth page
    pathname.startsWith('/cookie-test') // Skip cookie test page
  ) {
    return NextResponse.next()
  }

  // Handle School Routes (skip for now since school auth is under /profile)
  if (pathname.startsWith('/school')) {
    // School routes don't exist yet, skip middleware
    return NextResponse.next()
  }
  
  // Handle Admin Routes (temporarily disabled to prevent loops)
  if (pathname.startsWith('/admin')) {
    // Skip all admin middleware for now
    return NextResponse.next()
  }
  
  // Handle Partner Routes
  if (pathname.startsWith('/partner')) {
    const isPartnerAuthenticated = await isAuthenticated(request, 'partner')
    
    // If partner is authenticated and trying to access login/signup pages
    if (isPartnerAuthenticated && matchesRoute(pathname, PARTNER_ROUTES.public)) {
      url.pathname = PARTNER_ROUTES.dashboard
      return NextResponse.redirect(url)
    }
    
    // If partner is not authenticated and trying to access protected routes
    if (!isPartnerAuthenticated && !matchesRoute(pathname, PARTNER_ROUTES.public)) {
      url.pathname = '/partner/login'
      url.searchParams.set('redirect', pathname)
      return NextResponse.redirect(url)
    }
  }
  
  return NextResponse.next()
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
