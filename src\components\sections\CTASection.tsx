'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { ArrowRight, Check, Zap, Users, Shield, TrendingUp, Clock, Award } from 'lucide-react'

const CTASection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const benefits = [
    {
      icon: Zap,
      title: 'Complete Solution',
      description: 'All 8+ modules included from day one',
      color: 'text-yellow-600 bg-yellow-100'
    },
    {
      icon: Users,
      title: 'Unlimited Users',
      description: 'No per-user charges or hidden fees',
      color: 'text-blue-600 bg-blue-100'
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Bank-grade security & data protection',
      color: 'text-emerald-600 bg-emerald-100'
    },
    {
      icon: TrendingUp,
      title: 'AI-Powered Insights',
      description: 'Advanced analytics & predictive reports',
      color: 'text-purple-600 bg-purple-100'
    },
    {
      icon: Clock,
      title: '24/7 Support',
      description: 'Premium support & regular updates',
      color: 'text-red-600 bg-red-100'
    },
    {
      icon: Award,
      title: 'Proven Results',
      description: 'Trusted by leading educational institutions',
      color: 'text-indigo-600 bg-indigo-100'
    }
  ]

  return (
    <section className="py-16 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Zap className="w-4 h-4 fill-current" />
            Ready to Transform Your School?
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Join the Future of
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> School Management</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Experience the complete school management solution that grows with your institution.
          </p>
        </motion.div>

        {/* Benefits Grid */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-12"
        >
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.05 }}
            >
              <Card className="h-full bg-white/80 backdrop-blur-sm border-0 shadow-md hover:shadow-lg transition-all duration-300">
                <CardContent padding="md">
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${benefit.color}`}>
                      <benefit.icon className="w-5 h-5" />
                    </div>
                    <div>
                      <h3 className="text-base font-bold text-slate-900 mb-1">{benefit.title}</h3>
                      <p className="text-sm text-slate-600">{benefit.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Main CTA Card */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="max-w-3xl mx-auto"
        >
          <Card className="bg-gradient-to-br from-blue-900 via-slate-900 to-emerald-900 border-0 shadow-xl overflow-hidden">
            <CardContent padding="lg">
              <div className="text-center">
                <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs font-medium mb-4">
                  <Award className="w-3 h-3" />
                  Free Setup & Migration
                </div>

                <h3 className="text-2xl lg:text-3xl font-bold text-white mb-3">
                  Start Your Digital Transformation
                </h3>

                <p className="text-base text-blue-100 mb-6 max-w-xl mx-auto">
                  Book a personalized demo and see how Schopio can revolutionize your school&apos;s operations.
                </p>

                <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-6">
                  <Button
                    size="lg"
                    icon={ArrowRight}
                    iconPosition="right"
                    className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white font-bold px-6 py-3 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                    onClick={() => window.location.href = '/demo'}
                  >
                    Book Free Demo
                  </Button>
                  <Button
                    variant="outline"
                    size="md"
                    className="border border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-6 py-3"
                    onClick={() => window.location.href = '/solutions'}
                  >
                    Learn More
                  </Button>
                </div>

                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="text-white">
                    <div className="text-lg font-bold text-emerald-300">14 Days</div>
                    <div className="text-xs text-blue-200">Free Trial</div>
                  </div>
                  <div className="text-white">
                    <div className="text-lg font-bold text-emerald-300">30 Min</div>
                    <div className="text-xs text-blue-200">Demo</div>
                  </div>
                  <div className="text-white">
                    <div className="text-lg font-bold text-emerald-300">24/7</div>
                    <div className="text-xs text-blue-200">Support</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-8"
        >
          <p className="text-slate-500 text-sm mb-3">Trusted by educational institutions across India</p>
          <div className="flex flex-wrap justify-center items-center gap-6 opacity-60">
            <div className="text-slate-400 font-medium text-sm">500+ Schools</div>
            <div className="w-1 h-1 bg-slate-400 rounded-full"></div>
            <div className="text-slate-400 font-medium text-sm">100,000+ Students</div>
            <div className="w-1 h-1 bg-slate-400 rounded-full"></div>
            <div className="text-slate-400 font-medium text-sm">99.9% Uptime</div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default CTASection
