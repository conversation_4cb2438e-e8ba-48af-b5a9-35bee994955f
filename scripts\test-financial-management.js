/**
 * Financial Management System Test
 * Tests the financial analytics and management functionality
 */

const { neon } = require('@neondatabase/serverless')

const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const sql = neon(connectionString)

async function testFinancialManagement() {
  try {
    console.log('💰 Testing Financial Management System...')
    console.log('=' .repeat(50))

    // 1. Test Financial Analytics Data
    console.log('\n📊 Step 1: Testing Financial Analytics Data...')
    
    // Check if we have the necessary data for analytics
    const [subscriptionCount] = await sql`SELECT COUNT(*) as count FROM billing_subscriptions`
    const [invoiceCount] = await sql`SELECT COUNT(*) as count FROM billing_invoices`
    const [paymentCount] = await sql`SELECT COUNT(*) as count FROM billing_payments`
    const [expenseCount] = await sql`SELECT COUNT(*) as count FROM operational_expenses`

    console.log(`   Subscriptions: ${subscriptionCount.count}`)
    console.log(`   Invoices: ${invoiceCount.count}`)
    console.log(`   Payments: ${paymentCount.count}`)
    console.log(`   Operational Expenses: ${expenseCount.count}`)

    // 2. Test Revenue Calculations
    console.log('\n💵 Step 2: Testing Revenue Calculations...')
    
    // Calculate MRR (Monthly Recurring Revenue)
    const [mrrData] = await sql`
      SELECT 
        COALESCE(SUM(CAST(monthly_amount AS DECIMAL)), 0) as mrr,
        COUNT(*) as active_subscriptions
      FROM billing_subscriptions 
      WHERE status = 'active'
    `

    console.log(`   Monthly Recurring Revenue: ₹${mrrData.mrr}`)
    console.log(`   Active Subscriptions: ${mrrData.active_subscriptions}`)
    console.log(`   Annual Recurring Revenue: ₹${parseFloat(mrrData.mrr) * 12}`)

    // 3. Test Payment Analytics
    console.log('\n💳 Step 3: Testing Payment Analytics...')
    
    // Calculate payment collection rate
    const [paymentAnalytics] = await sql`
      SELECT 
        COUNT(CASE WHEN bi.status = 'paid' THEN 1 END) as paid_invoices,
        COUNT(*) as total_invoices,
        COALESCE(SUM(CASE WHEN bi.status = 'paid' THEN CAST(bi.total_amount AS DECIMAL) ELSE 0 END), 0) as collected_amount,
        COALESCE(SUM(CAST(bi.total_amount AS DECIMAL)), 0) as total_invoiced
      FROM billing_invoices bi
    `

    const collectionRate = paymentAnalytics.total_invoiced > 0 ? 
      (paymentAnalytics.collected_amount / paymentAnalytics.total_invoiced) * 100 : 0

    console.log(`   Total Invoices: ${paymentAnalytics.total_invoices}`)
    console.log(`   Paid Invoices: ${paymentAnalytics.paid_invoices}`)
    console.log(`   Collection Rate: ${collectionRate.toFixed(1)}%`)
    console.log(`   Total Invoiced: ₹${paymentAnalytics.total_invoiced}`)
    console.log(`   Total Collected: ₹${paymentAnalytics.collected_amount}`)

    // 4. Test Partner Commission Analytics
    console.log('\n🤝 Step 4: Testing Partner Commission Analytics...')
    
    const [partnerAnalytics] = await sql`
      SELECT 
        COUNT(DISTINCT p.id) as total_partners,
        COUNT(DISTINCT CASE WHEN p.is_active = true THEN p.id END) as active_partners,
        COALESCE(AVG(p.profit_share_percentage), 0) as avg_commission_rate,
        COALESCE(SUM(CAST(pe.partner_earning AS DECIMAL)), 0) as total_partner_earnings
      FROM partners p
      LEFT JOIN partner_earnings pe ON p.id = pe.partner_id
    `

    console.log(`   Total Partners: ${partnerAnalytics.total_partners}`)
    console.log(`   Active Partners: ${partnerAnalytics.active_partners}`)
    console.log(`   Average Commission Rate: ${parseFloat(partnerAnalytics.avg_commission_rate).toFixed(1)}%`)
    console.log(`   Total Partner Earnings: ₹${partnerAnalytics.total_partner_earnings}`)

    // 5. Test Operational Expenses
    console.log('\n🏢 Step 5: Testing Operational Expenses...')
    
    const [expenseAnalytics] = await sql`
      SELECT
        COALESCE(SUM(CAST(amount_per_school AS DECIMAL)), 0) as total_expenses,
        COUNT(*) as expense_records,
        COUNT(CASE WHEN category_name LIKE '%database%' THEN 1 END) as database_categories,
        COUNT(CASE WHEN category_name LIKE '%support%' THEN 1 END) as support_categories,
        COUNT(CASE WHEN category_name LIKE '%infrastructure%' THEN 1 END) as infrastructure_categories
      FROM operational_expenses
      WHERE is_active = true
    `

    const totalExpenses = parseFloat(expenseAnalytics.total_expenses)

    console.log(`   Total Expense Categories: ${expenseAnalytics.expense_records}`)
    console.log(`   Database-related: ${expenseAnalytics.database_categories}`)
    console.log(`   Support-related: ${expenseAnalytics.support_categories}`)
    console.log(`   Infrastructure-related: ${expenseAnalytics.infrastructure_categories}`)
    console.log(`   Total Monthly Expenses: ₹${totalExpenses}`)

    // 6. Calculate Key Financial Metrics
    console.log('\n📈 Step 6: Calculating Key Financial Metrics...')
    
    const monthlyRevenue = parseFloat(mrrData.mrr)
    const monthlyExpenses = totalExpenses
    const partnerCommissions = parseFloat(partnerAnalytics.total_partner_earnings)
    
    const grossProfit = monthlyRevenue - monthlyExpenses
    const netProfit = grossProfit - partnerCommissions
    const grossMargin = monthlyRevenue > 0 ? (grossProfit / monthlyRevenue) * 100 : 0
    const netMargin = monthlyRevenue > 0 ? (netProfit / monthlyRevenue) * 100 : 0

    console.log(`   Monthly Revenue: ₹${monthlyRevenue}`)
    console.log(`   Monthly Expenses: ₹${monthlyExpenses}`)
    console.log(`   Partner Commissions: ₹${partnerCommissions}`)
    console.log(`   Gross Profit: ₹${grossProfit}`)
    console.log(`   Net Profit: ₹${netProfit}`)
    console.log(`   Gross Margin: ${grossMargin.toFixed(1)}%`)
    console.log(`   Net Margin: ${netMargin.toFixed(1)}%`)

    // 7. Test Risk Metrics
    console.log('\n⚠️  Step 7: Testing Risk Metrics...')
    
    // Calculate overdue invoices
    const [overdueAnalytics] = await sql`
      SELECT 
        COUNT(*) as overdue_count,
        COALESCE(SUM(CAST(total_amount AS DECIMAL)), 0) as overdue_amount
      FROM billing_invoices
      WHERE status IN ('open', 'draft') AND due_date < CURRENT_DATE
    `

    const overdueRisk = paymentAnalytics.total_invoiced > 0 ? 
      (overdueAnalytics.overdue_amount / paymentAnalytics.total_invoiced) * 100 : 0

    console.log(`   Overdue Invoices: ${overdueAnalytics.overdue_count}`)
    console.log(`   Overdue Amount: ₹${overdueAnalytics.overdue_amount}`)
    console.log(`   Payment Default Risk: ${overdueRisk.toFixed(1)}%`)

    // 8. Summary and Health Check
    console.log('\n🎯 Step 8: Financial Health Summary...')
    
    const healthMetrics = {
      revenue: monthlyRevenue > 0 ? '✅' : '⚠️',
      profitability: netProfit > 0 ? '✅' : '⚠️',
      collections: collectionRate > 80 ? '✅' : '⚠️',
      expenses: monthlyExpenses < monthlyRevenue ? '✅' : '⚠️',
      partners: partnerAnalytics.active_partners > 0 ? '✅' : '⚠️'
    }

    console.log(`   Revenue Generation: ${healthMetrics.revenue} (₹${monthlyRevenue}/month)`)
    console.log(`   Profitability: ${healthMetrics.profitability} (${netMargin.toFixed(1)}% margin)`)
    console.log(`   Payment Collections: ${healthMetrics.collections} (${collectionRate.toFixed(1)}% rate)`)
    console.log(`   Expense Management: ${healthMetrics.expenses} (${(monthlyExpenses/monthlyRevenue*100).toFixed(1)}% of revenue)`)
    console.log(`   Partner Network: ${healthMetrics.partners} (${partnerAnalytics.active_partners} active partners)`)

    // 9. Test Data Availability for Analytics
    console.log('\n🔍 Step 9: Analytics Data Availability Check...')
    
    const dataAvailability = {
      subscriptions: subscriptionCount.count > 0,
      invoices: invoiceCount.count > 0,
      payments: paymentCount.count > 0,
      expenses: expenseCount.count > 0,
      partners: partnerAnalytics.total_partners > 0
    }

    console.log(`   Subscription Data: ${dataAvailability.subscriptions ? '✅' : '❌'} (${subscriptionCount.count} records)`)
    console.log(`   Invoice Data: ${dataAvailability.invoices ? '✅' : '❌'} (${invoiceCount.count} records)`)
    console.log(`   Payment Data: ${dataAvailability.payments ? '✅' : '❌'} (${paymentCount.count} records)`)
    console.log(`   Expense Data: ${dataAvailability.expenses ? '✅' : '❌'} (${expenseCount.count} records)`)
    console.log(`   Partner Data: ${dataAvailability.partners ? '✅' : '❌'} (${partnerAnalytics.total_partners} records)`)

    // 10. Final Assessment
    console.log('\n🎉 FINANCIAL MANAGEMENT TEST RESULTS:')
    console.log('=' .repeat(50))
    
    const allSystemsOperational = Object.values(dataAvailability).every(Boolean)
    const financialHealthGood = Object.values(healthMetrics).filter(m => m === '✅').length >= 3

    console.log(`📊 Data Availability: ${allSystemsOperational ? '✅ All systems have data' : '⚠️ Some systems missing data'}`)
    console.log(`💰 Financial Health: ${financialHealthGood ? '✅ Good financial health' : '⚠️ Needs attention'}`)
    console.log(`🔧 Analytics Ready: ${allSystemsOperational ? '✅ Ready for analytics' : '⚠️ Limited analytics capability'}`)

    if (allSystemsOperational && financialHealthGood) {
      console.log('\n✅ Financial Management System: FULLY OPERATIONAL')
      console.log('   - All data sources available')
      console.log('   - Financial metrics calculable')
      console.log('   - Analytics dashboard ready')
      console.log('   - Risk monitoring active')
    } else {
      console.log('\n⚠️ Financial Management System: PARTIALLY OPERATIONAL')
      console.log('   - Some data sources may be limited')
      console.log('   - Basic analytics available')
      console.log('   - Consider adding more test data for full functionality')
    }

    console.log('\n🎯 RECOMMENDATIONS:')
    if (!dataAvailability.payments) {
      console.log('   - Add payment data to test payment analytics')
    }
    if (netProfit <= 0) {
      console.log('   - Review expense allocation and pricing strategy')
    }
    if (collectionRate < 80) {
      console.log('   - Improve payment collection processes')
    }
    
    console.log('\n✅ Financial management system test completed!')

  } catch (error) {
    console.error('❌ Financial management test failed:', error)
    process.exit(1)
  }
}

testFinancialManagement()
