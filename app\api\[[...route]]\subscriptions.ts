import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { db } from '@/src/db'
import { subscriptions, clients, billingPayments, billingSubscriptions, billingTransactions, billingInvoices } from '@/src/db/schema'
import { eq, and } from 'drizzle-orm'
import {
  schoolAuthMiddleware,
  requireSchoolRole,
  getCurrentSchoolUser,
  ensureSchoolDataAccess,
  schoolSecurityHeaders
} from '@/src/middleware/school-auth'

import { getPaymentService } from '@/src/services/paymentGatewayFactory'
import { auditLogger } from '@/src/services/auditLogger'

// Initialize payment service
const paymentService = getPaymentService()

const app = new Hono()

// Apply security headers to all subscription routes
app.use('*', schoolSecurityHeaders)

// Apply authentication middleware to all subscription routes
app.use('*', schoolAuthMiddleware)

// Apply data access control to all subscription routes
app.use('*', ensureSchoolDataAccess)

/**
 * Get subscription details for Razorpay checkout
 * This endpoint returns subscription details for direct Razorpay subscription checkout
 * Following Razorpay's official subscription integration guide
 */
app.post('/get-subscription-details',
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    id: z.string().min(1, 'Subscription ID is required')
  })),
  async (c) => {
    try {
      console.log('🔍 [Subscription Details] Getting subscription for checkout...')

      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      console.log(`🔍 [Subscription Details] School user: ${schoolUser.email}, Client ID: ${clientId}`)

      const { id: subscriptionId } = c.req.valid('json')
      console.log(`🔄 Getting subscription details for ${subscriptionId} by client ${clientId}`)
      console.log(`🔍 [Debug] Subscription ID type: ${typeof subscriptionId}, length: ${subscriptionId.length}`)

      // Get subscription details with Razorpay subscription ID from billingSubscriptions table
      const [subscriptionData] = await db.select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        status: billingSubscriptions.status,
        studentCount: billingSubscriptions.studentCount,
        pricePerStudent: billingSubscriptions.pricePerStudent,
        razorpaySubscriptionId: billingSubscriptions.razorpaySubscriptionId,
        razorpayCustomerId: billingSubscriptions.razorpayCustomerId,
        billingCycle: billingSubscriptions.billingCycle,
        nextBillingDate: billingSubscriptions.nextBillingDate,
        schoolName: clients.schoolName,
        email: clients.email,
        phone: clients.phone
      })
      .from(billingSubscriptions)
      .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(and(
        eq(billingSubscriptions.id, subscriptionId),
        eq(billingSubscriptions.clientId, clientId)
      ))
      .limit(1)

      if (!subscriptionData) {
        console.log(`❌ [Debug] No subscription found for ID: ${subscriptionId}, Client: ${clientId}`)
        return c.json({
          success: false,
          error: 'Subscription not found or unauthorized access'
        }, 404)
      }

      console.log(`✅ [Debug] Found subscription data:`, {
        id: subscriptionData.id,
        clientId: subscriptionData.clientId,
        razorpaySubscriptionId: subscriptionData.razorpaySubscriptionId,
        razorpayCustomerId: subscriptionData.razorpayCustomerId,
        monthlyAmount: subscriptionData.monthlyAmount
      })

      if (!subscriptionData.razorpaySubscriptionId) {
        return c.json({
          success: false,
          error: 'Razorpay subscription not found. Please contact support.'
        }, 400)
      }

      console.log(`✅ Found Razorpay subscription: ${subscriptionData.razorpaySubscriptionId}`)

      // Log subscription details for authentication transaction
      console.log('✅ Subscription ready for authentication:', {
        subscriptionId: subscriptionData.razorpaySubscriptionId,
        monthlyAmount: subscriptionData.monthlyAmount,
        studentCount: subscriptionData.studentCount,
        schoolName: subscriptionData.schoolName
      })

      // Return subscription details for Razorpay checkout
      return c.json({
        success: true,
        data: {
          subscription: {
            id: subscriptionData.id,
            planName: 'Basic Plan', // Default plan name since billingSubscriptions doesn't store plan name
            monthlyAmount: subscriptionData.monthlyAmount,
            schoolName: subscriptionData.schoolName,
            studentCount: subscriptionData.studentCount,
            pricePerStudent: subscriptionData.pricePerStudent,
            razorpaySubscriptionId: subscriptionData.razorpaySubscriptionId,
            billingCycle: subscriptionData.billingCycle,
            nextBillingDate: subscriptionData.nextBillingDate
          },
          razorpayConfig: {
            keyId: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
            subscriptionId: subscriptionData.razorpaySubscriptionId, // Use subscription_id for subscription authentication
            name: 'Schopio',
            description: `${subscriptionData.schoolName} - Monthly Subscription (₹${parseFloat(subscriptionData.monthlyAmount).toLocaleString('en-IN')})`,
            image: '/logo.png',
            prefill: {
              email: subscriptionData.email,
              name: subscriptionData.schoolName,
              contact: subscriptionData.phone || ''
            },
            theme: {
              color: '#2563eb'
            },
            notes: {
              subscription_type: 'authentication',
              razorpay_subscription_id: subscriptionData.razorpaySubscriptionId,
              client_id: clientId,
              school_name: subscriptionData.schoolName,
              monthly_amount: `₹${parseFloat(subscriptionData.monthlyAmount).toLocaleString('en-IN')}`,
              student_count: subscriptionData.studentCount
            }
          }
        }
      })

    } catch (error) {
      console.error('❌ [Payment Order] Create subscription payment order error:', error)
      console.error('❌ [Payment Order] Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        type: typeof error
      })
      return c.json({
        success: false,
        error: 'Failed to create subscription payment order. Please try again.'
      }, 500)
    }
  }
)

/**
 * Verify subscription payment completion
 * POST /api/subscriptions/verify-payment
 */
app.post('/verify-payment',
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    razorpayOrderId: z.string().min(1, 'Razorpay order ID is required'),
    razorpayPaymentId: z.string().min(1, 'Razorpay payment ID is required'),
    razorpaySignature: z.string().min(1, 'Razorpay signature is required'),
    id: z.string().uuid('Invalid subscription ID format')
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const {
        razorpayOrderId,
        razorpayPaymentId,
        razorpaySignature,
        id: subscriptionId
      } = c.req.valid('json')

      console.log(`🔄 Verifying subscription payment for ${subscriptionId}`)

      // Verify payment signature
      const isValidSignature = paymentService.verifyPaymentSignature({
        razorpayOrderId,
        razorpayPaymentId,
        razorpaySignature
      })

      if (!isValidSignature) {
        console.error('Invalid payment signature for subscription payment')
        return c.json({
          success: false,
          error: 'Invalid payment signature'
        }, 400)
      }

      // Verify subscription belongs to client
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        status: billingSubscriptions.status,
        nextBillingDate: billingSubscriptions.nextBillingDate,
        schoolName: clients.schoolName,
        email: clients.email
      })
      .from(billingSubscriptions)
      .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(and(
        eq(billingSubscriptions.id, subscriptionId),
        eq(billingSubscriptions.clientId, clientId)
      ))
      .limit(1)

      if (!subscription) {
        return c.json({
          success: false,
          error: 'Subscription not found or unauthorized access'
        }, 404)
      }

      // Get payment details from Razorpay
      const paymentDetails = await paymentService.getPayment(razorpayPaymentId)

      if (!paymentDetails.success) {
        console.error('Failed to get payment details from Razorpay:', paymentDetails.error)
        return c.json({
          success: false,
          error: 'Failed to verify payment details'
        }, 500)
      }

      const payment = paymentDetails.payment
      const amountPaid = payment.amount / 100 // Convert paise to rupees

      // Record the payment (note: billingPayments table doesn't have subscriptionId field)
      const [paymentRecord] = await db.insert(billingPayments).values({
        clientId: clientId,
        subscriptionId: subscriptionId,
        // Note: invoiceId is null for subscription payments
        amount: amountPaid.toString(),
        razorpayPaymentId: razorpayPaymentId,
        status: 'succeeded',
        paymentMethod: payment.method || 'card'
      }).returning()

      // Update subscription next billing date (advance by one month)
      const currentNextBilling = new Date(subscription.nextBillingDate)
      const newNextBilling = new Date(currentNextBilling)
      newNextBilling.setMonth(newNextBilling.getMonth() + 1)

      await db.update(subscriptions)
        .set({
          nextBillingDate: newNextBilling.toISOString().split('T')[0],
          updatedAt: new Date()
        })
        .where(eq(subscriptions.id, subscriptionId))

      // Log successful payment
      await auditLogger.logPayment('subscription_manual_payment', {
        clientId: clientId,
        amount: amountPaid,
        currency: payment.currency,
        paymentId: razorpayPaymentId,
        success: true
      })

      console.log(`✅ Subscription payment verified successfully for ${subscriptionId}`)

      return c.json({
        success: true,
        data: {
          paymentId: paymentRecord.id,
          monthlyAmount: amountPaid,
          currency: payment.currency,
          status: 'completed',
          nextBillingDate: newNextBilling.toISOString().split('T')[0],
          message: 'Subscription payment completed successfully'
        }
      })

    } catch (error) {
      console.error('Verify subscription payment error:', error)
      return c.json({
        success: false,
        error: 'Failed to verify subscription payment. Please contact support.'
      }, 500)
    }
  }
)

/**
 * Create authentication transaction for automatic billing
 * POST /api/subscriptions/setup-auto-billing
 */
app.post('/setup-auto-billing',
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    id: z.string().uuid('Invalid subscription ID format')
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const { id: subscriptionId } = c.req.valid('json')

      console.log(`🔄 Setting up automatic billing for subscription ${subscriptionId} by client ${clientId}`)

      // Verify subscription belongs to the school and has Razorpay integration
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        status: billingSubscriptions.status,
        razorpaySubscriptionId: billingSubscriptions.razorpaySubscriptionId,
        razorpayCustomerId: billingSubscriptions.razorpayCustomerId,
        schoolName: clients.schoolName,
        email: clients.email
      })
      .from(billingSubscriptions)
      .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(and(
        eq(billingSubscriptions.id, subscriptionId),
        eq(billingSubscriptions.clientId, clientId)
      ))
      .limit(1)

      if (!subscription) {
        console.error(`❌ Subscription not found: ${subscriptionId} for client ${clientId}`)
        return c.json({
          success: false,
          error: 'Subscription not found or unauthorized access'
        }, 404)
      }

      // Check if subscription has Razorpay integration
      if (!subscription.razorpaySubscriptionId || !subscription.razorpayCustomerId) {
        console.error(`❌ Subscription ${subscriptionId} missing Razorpay integration`)
        return c.json({
          success: false,
          error: 'This subscription does not have automatic billing configured. Please contact support.'
        }, 400)
      }

      // Check if already activated
      if (subscription.status === 'active') {
        return c.json({
          success: false,
          error: 'Automatic billing is already activated for this subscription'
        }, 400)
      }

      // For subscription authentication, we use the subscription_id directly (not order_id)
      // This is the correct way for Razorpay subscriptions according to their docs
      console.log('✅ Using subscription-based authentication (not order-based)')

      // Get Razorpay configuration for subscription authentication
      const razorpayConfig = {
        keyId: process.env.RAZORPAY_KEY_ID!,
        subscriptionId: subscription.razorpaySubscriptionId, // Use subscription_id for auth
        name: 'Schopio',
        description: `Authorize automatic billing for ₹${subscription.monthlyAmount} monthly`,
        image: `${process.env.NEXT_PUBLIC_APP_URL}/logo.png`,
        prefill: {
          name: subscription.schoolName || '',
          email: subscription.email || ''
        },
        theme: {
          color: '#2563eb'
        },
        notes: {
          subscription_id: subscriptionId,
          client_id: clientId,
          school_name: subscription.schoolName || '',
          auth_purpose: 'automatic_billing_setup',
          monthly_amount: subscription.monthlyAmount.toString()
        }
      }

      console.log(`✅ Subscription authentication ready for subscription ${subscriptionId}`)

      return c.json({
        success: true,
        message: 'Subscription authentication ready',
        data: {
          subscription: {
            id: subscription.id,
            planName: 'Basic Plan',
            monthlyAmount: subscription.monthlyAmount,
            razorpaySubscriptionId: subscription.razorpaySubscriptionId,
            status: subscription.status
          },
          razorpayConfig,
          instructions: [
            'Complete the subscription authentication to activate automatic billing',
            `Monthly charges of ₹${subscription.monthlyAmount} will be automatic`,
            'You will receive email confirmation after activation'
          ]
        }
      })

    } catch (error) {
      console.error('Setup auto-billing error:', error)
      return c.json({
        success: false,
        error: 'Failed to setup automatic billing. Please try again.'
      }, 500)
    }
  }
)

/**
 * Verify authentication transaction and activate automatic billing
 * POST /api/subscriptions/verify-auto-billing
 */
app.post('/verify-auto-billing',
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    id: z.string().uuid(),
    razorpaySubscriptionId: z.string(),
    razorpayPaymentId: z.string(),
    razorpaySignature: z.string()
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const { id: subscriptionId, razorpaySubscriptionId, razorpayPaymentId, razorpaySignature } = c.req.valid('json')

      console.log(`🔄 Verifying subscription authentication for subscription ${subscriptionId}`)

      // Verify subscription payment signature (different from order-based verification)
      const crypto = require('crypto')
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
        .update(`${razorpayPaymentId}|${razorpaySubscriptionId}`)
        .digest('hex')

      const isValidSignature = expectedSignature === razorpaySignature

      if (!isValidSignature) {
        console.error('❌ Invalid payment signature for authentication transaction')
        return c.json({
          success: false,
          error: 'Payment verification failed. Invalid signature.'
        }, 400)
      }

      // Get subscription details
      const [subscription] = await db.select()
        .from(billingSubscriptions)
        .where(and(
          eq(billingSubscriptions.id, subscriptionId),
          eq(billingSubscriptions.clientId, clientId)
        ))
        .limit(1)

      if (!subscription) {
        return c.json({
          success: false,
          error: 'Subscription not found'
        }, 404)
      }

      // Activate subscription and update status
      const [updatedSubscription] = await db.update(billingSubscriptions)
        .set({
          status: 'active',
          activatedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(billingSubscriptions.id, subscriptionId))
        .returning()

      // Log successful activation
      await auditLogger.logPayment('auto_billing_activated', {
        clientId: clientId,
        paymentId: razorpayPaymentId,
        amount: 1, // ₹1 authentication
        currency: 'INR',
        success: true
      })

      console.log(`✅ Automatic billing activated for subscription ${subscriptionId}`)

      return c.json({
        success: true,
        message: 'Automatic billing activated successfully',
        subscription: {
          id: updatedSubscription.id,
          status: updatedSubscription.status,
          activatedAt: updatedSubscription.activatedAt,
          nextBillingDate: updatedSubscription.nextBillingDate
        },
        automaticBilling: {
          enabled: true,
          status: 'active',
          nextCharge: updatedSubscription.nextBillingDate,
          authenticationCompleted: true
        }
      })

    } catch (error) {
      console.error('Verify auto-billing error:', error)
      return c.json({
        success: false,
        error: 'Failed to verify authentication. Please try again.'
      }, 500)
    }
  }
)

/**
 * Verify subscription authentication
 * This endpoint verifies the subscription authentication transaction
 * Following Razorpay's official subscription verification process
 */
app.post('/verify-subscription-auth',
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    razorpay_payment_id: z.string().min(1, 'Payment ID is required'),
    razorpay_subscription_id: z.string().min(1, 'Subscription ID is required'),
    razorpay_signature: z.string().min(1, 'Signature is required'),
    subscription_id: z.string().min(1, 'Subscription ID is required')
  })),
  async (c) => {
    try {
      console.log('🔍 [Subscription Verification] Starting subscription authentication verification...')

      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId

      const {
        razorpay_payment_id,
        razorpay_subscription_id,
        razorpay_signature,
        subscription_id
      } = c.req.valid('json')

      console.log(`🔄 Verifying authentication payment for subscription ${subscription_id}`)

      // Get subscription details
      const [subscriptionData] = await db.select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        razorpaySubscriptionId: billingSubscriptions.razorpaySubscriptionId,
        status: billingSubscriptions.status
      })
      .from(billingSubscriptions)
      .where(and(
        eq(billingSubscriptions.id, subscription_id),
        eq(billingSubscriptions.clientId, clientId)
      ))
      .limit(1)

      if (!subscriptionData) {
        return c.json({
          success: false,
          error: 'Subscription not found or unauthorized access'
        }, 404)
      }

      // Verify the signature using Razorpay's verification method for subscription-based payments
      const crypto = require('crypto')
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
        .update(`${razorpay_payment_id}|${razorpay_subscription_id}`)
        .digest('hex')

      if (expectedSignature !== razorpay_signature) {
        console.error('❌ Signature verification failed')
        return c.json({
          success: false,
          error: 'Payment verification failed - invalid signature'
        }, 400)
      }

      console.log('✅ Subscription authentication verified successfully')

      // Update subscription status to authenticated
      await db.update(billingSubscriptions)
        .set({
          status: 'authenticated',
          updatedAt: new Date()
        })
        .where(eq(billingSubscriptions.id, subscription_id))

      console.log(`✅ Subscription ${subscription_id} marked as authenticated`)

      return c.json({
        success: true,
        message: 'Authentication payment verified successfully - automatic billing activated!',
        data: {
          subscriptionId: subscription_id,
          razorpaySubscriptionId: razorpay_subscription_id,
          razorpayPaymentId: razorpay_payment_id,
          status: 'authenticated'
        }
      })

    } catch (error) {
      console.error('❌ Subscription verification error:', error)
      return c.json({
        success: false,
        error: 'Internal server error during subscription verification'
      }, 500)
    }
  }
)

/**
 * Create manual payment order for subscription
 * This endpoint creates a Razorpay order for one-time manual payment
 */
app.post('/create-manual-payment-order',
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    subscriptionId: z.string().min(1, 'Subscription ID is required'),
    amount: z.number().positive('Amount must be positive')
  })),
  async (c) => {
    try {
      console.log('🔄 [Manual Payment] Creating payment order...')

      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const requestData = c.req.valid('json')
      const { subscriptionId, amount } = requestData

      console.log('📋 [Manual Payment] Request data:', {
        subscriptionId,
        amount,
        clientId,
        amountType: typeof amount,
        amountValue: amount,
        requestData
      })

      // Validate amount
      if (!amount || amount <= 0) {
        console.error('❌ [Manual Payment] Invalid amount:', amount)
        return c.json({
          success: false,
          error: 'Invalid payment amount. Amount must be greater than 0.'
        }, 400)
      }

      // Verify subscription belongs to this client
      console.log('🔍 [Manual Payment] Looking for subscription:', { subscriptionId, clientId })

      const [subscription] = await db
        .select({
          id: billingSubscriptions.id,
          clientId: billingSubscriptions.clientId,
          monthlyAmount: billingSubscriptions.monthlyAmount,
          currentPenaltyAmount: billingSubscriptions.currentPenaltyAmount,
          paymentStatus: billingSubscriptions.paymentStatus
        })
        .from(billingSubscriptions)
        .where(and(
          eq(billingSubscriptions.id, subscriptionId),
          eq(billingSubscriptions.clientId, clientId)
        ))
        .limit(1)

      if (!subscription) {
        console.error('❌ [Manual Payment] Subscription not found:', { subscriptionId, clientId })
        return c.json({
          success: false,
          error: 'Subscription not found or unauthorized access'
        }, 404)
      }

      console.log('✅ [Manual Payment] Subscription found:', {
        id: subscription.id,
        monthlyAmount: subscription.monthlyAmount,
        paymentStatus: subscription.paymentStatus
      })

      // Get school details for payment
      const [client] = await db
        .select({
          schoolName: clients.schoolName,
          email: clients.email,
          contactPerson: clients.contactPerson
        })
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1)

      if (!client) {
        return c.json({
          success: false,
          error: 'School details not found'
        }, 404)
      }

      // Create production-level Razorpay order with proper receipt format
      const timestamp = Date.now().toString().slice(-8) // Last 8 digits for uniqueness
      const clientShort = clientId.slice(-6) // Last 6 chars of client ID
      const receipt = `SUB-${clientShort}-${timestamp}` // Format: SUB-ABC123-12345678 (max 40 chars)

      console.log('📋 [Manual Payment] Creating receipt:', {
        receipt,
        length: receipt.length,
        maxAllowed: 40,
        isValid: receipt.length <= 40
      })

      const orderData = {
        amount: Math.round(amount * 100), // Convert to paise (production standard)
        currency: 'INR',
        receipt: receipt,
        notes: {
          subscription_id: subscriptionId,
          client_id: clientId,
          school_name: client.schoolName,
          payment_type: 'manual_subscription',
          monthly_amount: subscription.monthlyAmount,
          penalty_amount: subscription.currentPenaltyAmount || '0',
          created_by: 'schopio_billing_system',
          version: '2025.1'
        }
      }

      console.log('🔄 [Manual Payment] Creating Razorpay order with data:', orderData)

      const razorpayResponse = await paymentService.createOrder(orderData)

      if (!razorpayResponse.success || !razorpayResponse.order) {
        console.error('❌ [Manual Payment] Failed to create Razorpay order:', razorpayResponse.error)
        return c.json({
          success: false,
          error: razorpayResponse.error || 'Failed to create payment order'
        }, 500)
      }

      const razorpayOrder = razorpayResponse.order

      console.log('✅ [Manual Payment] Order created successfully:', {
        orderId: razorpayOrder.id,
        amount: amount,
        subscriptionId: subscriptionId,
        schoolName: client.schoolName
      })

      return c.json({
        success: true,
        data: {
          orderId: razorpayOrder.id,
          amount: amount,
          currency: 'INR',
          keyId: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
          schoolName: client.schoolName,
          description: `Monthly Subscription Payment - ${client.schoolName}`
        }
      })

    } catch (error) {
      console.error('❌ Manual payment order creation error:', error)

      // Log detailed error information
      if (error instanceof Error) {
        console.error('Error message:', error.message)
        console.error('Error stack:', error.stack)
      }

      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create payment order'
      }, 500)
    }
  }
)

/**
 * Verify manual payment and update subscription status
 */
app.post('/verify-manual-payment',
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    razorpay_payment_id: z.string().min(1, 'Payment ID is required'),
    razorpay_order_id: z.string().min(1, 'Order ID is required'),
    razorpay_signature: z.string().min(1, 'Signature is required'),
    subscriptionId: z.string().min(1, 'Subscription ID is required')
  })),
  async (c) => {
    try {
      console.log('🔄 [Manual Payment] Verifying payment...')

      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const { razorpay_payment_id, razorpay_order_id, razorpay_signature, subscriptionId } = c.req.valid('json')

      // Verify payment signature using 2025 production standards
      console.log('🔐 [Payment Verification] Verifying payment signature with production standards')

      const isValidSignature = paymentService.verifyPaymentSignature({
        razorpayOrderId: razorpay_order_id,
        razorpayPaymentId: razorpay_payment_id,
        razorpaySignature: razorpay_signature
      })

      if (!isValidSignature) {
        console.error('❌ [Payment Verification] Invalid payment signature - potential security threat')

        // Log security incident for monitoring
        console.error('🚨 [Security Alert] Payment signature verification failed:', {
          orderId: razorpay_order_id,
          paymentId: razorpay_payment_id,
          timestamp: new Date().toISOString(),
          clientId: clientId,
          subscriptionId: subscriptionId
        })

        return c.json({
          success: false,
          error: 'Payment verification failed. Invalid signature.',
          errorCode: 'SIGNATURE_VERIFICATION_FAILED'
        }, 400)
      }

      console.log('✅ [Payment Verification] Signature verified successfully')

      // Get payment details from Razorpay for receipt generation
      const paymentResult = await paymentService.fetchPaymentDetails(razorpay_payment_id)
      if (!paymentResult.success) {
        console.error('❌ Failed to fetch payment details:', paymentResult.error)
        return c.json({
          success: false,
          error: 'Failed to fetch payment details from payment gateway'
        }, 500)
      }

      const payment = paymentResult.payment
      const amountPaid = payment.amount / 100 // Convert paise to rupees

      console.log('💰 Payment details retrieved:', {
        paymentId: payment.id,
        amount: amountPaid,
        method: payment.method,
        status: payment.status,
        created_at: payment.created_at
      })

      // Update subscription payment status
      await db
        .update(billingSubscriptions)
        .set({
          paymentStatus: 'paid',
          lastPaymentDate: new Date().toISOString().split('T')[0],
          currentPenaltyAmount: '0.00', // Reset penalty after payment
          updatedAt: new Date()
        })
        .where(and(
          eq(billingSubscriptions.id, subscriptionId),
          eq(billingSubscriptions.clientId, clientId)
        ))

      // Record payment in billing_payments table
      await db.insert(billingPayments).values({
        subscriptionId: subscriptionId,
        clientId: clientId,
        amount: amountPaid.toString(),
        razorpayPaymentId: razorpay_payment_id,
        status: 'succeeded',
        paymentMethod: paymentResult.payment.method || 'card',
        createdAt: new Date(),
        updatedAt: new Date()
      })

      // Log successful payment
      await auditLogger.logPayment('manual_subscription_payment', {
        clientId: clientId,
        amount: amountPaid,
        currency: paymentResult.payment.currency,
        paymentId: razorpay_payment_id,
        success: true
      })

      console.log('✅ [Manual Payment] Payment verified and recorded:', {
        paymentId: razorpay_payment_id,
        amount: amountPaid,
        subscriptionId: subscriptionId
      })

      // Get client details for receipt
      const [client] = await db.select({
        schoolName: clients.schoolName,
        email: clients.email,
        address: clients.address,
        contactPerson: clients.contactPerson
      })
      .from(clients)
      .where(eq(clients.id, clientId))
      .limit(1)

      // Update subscription payment status and next billing date
      const nextBillingDate = new Date()
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1)

      await db.update(billingSubscriptions)
        .set({
          paymentStatus: 'paid',
          lastPaymentDate: new Date().toISOString().split('T')[0],
          nextBillingDate: nextBillingDate.toISOString().split('T')[0],
          currentPenaltyAmount: '0.00' // Reset penalties after payment
        })
        .where(eq(billingSubscriptions.id, subscriptionId))

      // Generate invoice for this payment
      const invoiceNumber = `INV-${new Date().getFullYear()}${(new Date().getMonth() + 1).toString().padStart(2, '0')}-${clientId.slice(-6).toUpperCase()}`

      const [newInvoice] = await db.insert(billingInvoices).values({
        subscriptionId: subscriptionId,
        clientId: clientId,
        invoiceNumber,
        subtotal: amountPaid.toString(),
        taxAmount: '0.00',
        totalAmount: amountPaid.toString(),
        status: 'paid',
        issuedDate: new Date().toISOString().split('T')[0],
        dueDate: new Date().toISOString().split('T')[0],
        paidDate: new Date().toISOString().split('T')[0],
        periodStart: new Date().toISOString().split('T')[0],
        periodEnd: nextBillingDate.toISOString().split('T')[0]
      }).returning()

      // Send payment confirmation email with receipt
      if (client?.email) {
        const emailService = await import('@/src/services/emailService')
        await emailService.emailService.sendPaymentConfirmationWithPDF({
          schoolName: client.schoolName,
          contactPerson: client.contactPerson || 'Admin',
          email: client.email,
          invoiceNumber,
          amount: amountPaid.toString(),
          dueDate: new Date().toISOString().split('T')[0],
          invoiceId: newInvoice.id
        })
      }

      return c.json({
        success: true,
        message: 'Payment verified successfully',
        data: {
          paymentId: razorpay_payment_id,
          amount: amountPaid,
          currency: payment.currency,
          status: 'completed',
          invoiceId: newInvoice.id,
          invoiceNumber,
          nextBillingDate: nextBillingDate.toISOString().split('T')[0],
          receipt: {
            paymentId: payment.id,
            orderId: razorpay_order_id,
            amount: amountPaid,
            currency: payment.currency,
            method: payment.method,
            status: payment.status,
            createdAt: payment.created_at,
            schoolName: client?.schoolName,
            contactEmail: client?.email,
            description: `Monthly Subscription Payment - ${client?.schoolName}`,
            transactionId: payment.acquirer_data?.bank_transaction_id || payment.id,
            receiptNumber: `RCP-${Date.now()}-${payment.id.slice(-6)}`,
            paymentPeriod: `${new Date().toLocaleDateString('en-IN', { day: 'numeric', month: 'short' })} - ${nextBillingDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}`
          }
        }
      })

    } catch (error) {
      console.error('❌ Manual payment verification error:', error)
      return c.json({
        success: false,
        error: 'Failed to verify payment'
      }, 500)
    }
  }
)

export default app
