import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { SEO } from "@/lib/constants";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import LiveChat from "@/components/ui/LiveChat";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: SEO.pages.home.title,
    template: `%s | Schopio`,
  },
  description: SEO.pages.home.description,
  keywords: SEO.keywords.join(', '),
  authors: [{ name: "Schopio Team", url: "https://schopio.orionixtech.com" }],
  creator: "Schop<PERSON>",
  publisher: "Schopio",
  category: "Education Technology",
  classification: "School Management Software",
  metadataBase: new URL("https://schopio.orionixtech.com"),
  alternates: {
    canonical: "https://schopio.orionixtech.com",
  },
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://schopio.orionixtech.com",
    title: SEO.pages.home.title,
    description: SEO.pages.home.description,
    siteName: "Schopio",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Schopio - Best School Management System & ERP Software in India",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: SEO.pages.home.title,
    description: SEO.pages.home.description,
    images: ["/og-image.jpg"],
    creator: "@schopio",
    site: "@schopio",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "google-site-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Structured Data for Organization
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Schopio",
    "alternateName": "Schopio School Management System",
    "url": "https://schopio.orionixtech.com",
    "logo": "https://schopio.orionixtech.com/logo.png",
    "description": "Leading school management system and ERP software in India. Complete educational management solution with AI-powered insights.",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "IN",
      "addressRegion": "Karnataka",
      "addressLocality": "Bangalore"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+91-9304928363",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://linkedin.com/company/schopio",
      "https://twitter.com/schopio",
      "https://facebook.com/schopio"
    ]
  };

  // Structured Data for Software Application
  const softwareSchema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "Schopio School Management System",
    "applicationCategory": "EducationalApplication",
    "operatingSystem": "Web Browser",
    "description": "Complete school management system and ERP software with 8+ modules, AI-powered insights, and comprehensive educational management tools.",
    "url": "https://schopio.orionixtech.com",
    "author": {
      "@type": "Organization",
      "name": "Schopio"
    },
    "offers": {
      "@type": "Offer",
      "price": "20",
      "priceCurrency": "INR",
      "priceSpecification": {
        "@type": "UnitPriceSpecification",
        "price": "20",
        "priceCurrency": "INR",
        "unitText": "per student per month"
      }
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "500",
      "bestRating": "5"
    }
  };

  return (
    <html lang="en" className={inter.variable}>
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(softwareSchema),
          }}
        />
      </head>
      <body className="font-sans antialiased bg-white text-neutral-900">
        <Header />
        <div className="pt-16 lg:pt-20">
          {children}
        </div>
        <Footer />
        <LiveChat />
      </body>
    </html>
  );
}
