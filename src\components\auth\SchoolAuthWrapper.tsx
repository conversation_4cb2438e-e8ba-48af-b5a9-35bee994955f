'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { AuthUtils } from '@/src/utils/authUtils'

interface SchoolAuthWrapperProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
}

export default function SchoolAuthWrapper({ 
  children, 
  requireAuth = true, 
  redirectTo 
}: SchoolAuthWrapperProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authenticated = AuthUtils.isAuthenticated('school')
        setIsAuthenticated(authenticated)

        // Handle authentication logic
        if (requireAuth && !authenticated) {
          // User needs to be authenticated but isn't
          const loginUrl = `/school/login?redirect=${encodeURIComponent(pathname)}`
          router.replace(loginUrl)
          return
        }

        if (!requireAuth && authenticated) {
          // User is authenticated but on a public page (like login)
          const dashboardUrl = redirectTo || '/school/dashboard'
          router.replace(dashboardUrl)
          return
        }

        // Check if token is expiring soon and show warning
        if (authenticated && AuthUtils.isTokenExpiringSoon('school')) {
          console.warn('School token expiring soon')
          // TODO: Implement token refresh or show warning
        }

      } catch (error) {
        console.error('School auth check failed:', error)
        AuthUtils.handleAuthError('school', error)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()

    // Listen for storage changes (logout from another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'schoolToken') {
        checkAuth()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [requireAuth, redirectTo, router, pathname])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying authentication...</p>
        </div>
      </div>
    )
  }

  // Don't render children if auth state doesn't match requirements
  if (requireAuth && !isAuthenticated) {
    return null // Will redirect
  }

  if (!requireAuth && isAuthenticated) {
    return null // Will redirect
  }

  return <>{children}</>
}

// Higher-order component for protected school routes
export function withSchoolAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: { requireAuth?: boolean; redirectTo?: string } = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <SchoolAuthWrapper 
        requireAuth={options.requireAuth ?? true}
        redirectTo={options.redirectTo}
      >
        <Component {...props} />
      </SchoolAuthWrapper>
    )
  }
}

// Hook for school authentication state
export function useSchoolAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkAuth = () => {
      const authenticated = AuthUtils.isAuthenticated('school')
      const userData = AuthUtils.getCurrentUser('school')
      
      setIsAuthenticated(authenticated)
      setUser(userData)
      setLoading(false)
    }

    checkAuth()

    // Listen for auth changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'schoolToken') {
        checkAuth()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  const login = (token: string) => {
    AuthUtils.setToken('school', token)
    setIsAuthenticated(true)
    setUser(AuthUtils.getCurrentUser('school'))
  }

  const logout = () => {
    AuthUtils.logout('school')
    setIsAuthenticated(false)
    setUser(null)
  }

  return {
    isAuthenticated,
    user,
    loading,
    login,
    logout
  }
}
