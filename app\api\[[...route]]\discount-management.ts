import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { db } from '@/src/db'
import {
  subscriptionDiscounts,
  subscriptionExpenses,
  partnerCommissionConfig,
  partnerCommissionTransactions,
  billingSubscriptions,
  clients,
  partners,
  adminUsers
} from '@/src/db/schema'
import { eq, and, desc, sql, isNotNull } from 'drizzle-orm'
import {
  adminAuthMiddleware,
  requireAdminRole,
  requirePermission,
  getCurrentAdmin
} from '@/src/middleware/admin-auth'


const app = new Hono()

// ===== DISCOUNT MANAGEMENT =====

// Create Subscription Discount with enhanced validation
const createDiscountSchema = z.object({
  discountPercentage: z.number()
    .min(1, 'Discount percentage must be at least 1%')
    .max(100, 'Discount percentage cannot exceed 100%'),
  durationMonths: z.number()
    .min(1, 'Duration must be at least 1 month')
    .max(24, 'Duration cannot exceed 24 months'),
  startDate: z.string()
    .datetime('Invalid start date format')
    .refine((date) => {
      const startDate = new Date(date)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      return startDate >= today
    }, 'Start date cannot be in the past'),
  reason: z.string()
    .min(10, 'Discount reason must be at least 10 characters')
    .max(500, 'Discount reason cannot exceed 500 characters')
})

app.post('/admin/subscriptions/:id/discounts',
  adminAuthMiddleware,
  requireAdminRole(['super_admin', 'billing']),
  zValidator('json', createDiscountSchema),
  async (c) => {
    try {
      const subscriptionId = c.req.param('id')
      const { discountPercentage, durationMonths, startDate, reason } = c.req.valid('json')
      const currentAdmin = getCurrentAdmin(c)

      // ✅ ENHANCED: Validate subscription exists and is active with security checks
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        status: billingSubscriptions.status,
        hasActiveDiscount: billingSubscriptions.hasActiveDiscount,
        currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage,
        discountEndDate: billingSubscriptions.discountEndDate,
        schoolName: clients.schoolName
      })
      .from(billingSubscriptions)
      .innerJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        return c.json({ success: false, error: 'Subscription not found' }, 404)
      }

      if (subscription.status !== 'active') {
        return c.json({ success: false, error: 'Can only add discounts to active subscriptions' }, 400)
      }

      // ✅ SECURITY: Additional validation for high-value discounts
      if (discountPercentage > 75 && !currentAdmin?.role?.includes('super_admin')) {
        return c.json({
          success: false,
          error: 'Discounts above 75% require super admin authorization'
        }, 403)
      }

      // ✅ SECURITY: Validate minimum amount after discount
      const originalAmountForValidation = parseFloat(subscription.monthlyAmount)
      const discountedAmountForValidation = originalAmountForValidation * (1 - discountPercentage / 100)
      const minimumAmountRequired = 1000

      if (discountedAmountForValidation < minimumAmountRequired) {
        return c.json({
          success: false,
          error: `Discount would reduce monthly amount to ₹${discountedAmountForValidation.toFixed(2)}, which is below the minimum of ₹${minimumAmountRequired}`
        }, 400)
      }

      // ✅ FIXED: Use unified system - only check billingSubscriptions
      if (subscription.hasActiveDiscount) {
        return c.json({ success: false, error: 'Active discount already exists for this subscription' }, 409)
      }

      // Additional business rule validations
      if (discountPercentage >= 50 && durationMonths > 12) {
        return c.json({
          success: false,
          error: 'Discounts of 50% or more cannot be applied for more than 12 months'
        }, 400)
      }

      // Validate minimum subscription amount after discount
      const originalAmount = parseFloat(subscription.monthlyAmount)
      const discountedAmount = originalAmount * (1 - discountPercentage / 100)
      const minimumAmount = 1000 // Minimum ₹1000 per month

      if (discountedAmount < minimumAmount) {
        return c.json({
          success: false,
          error: `Discount would reduce monthly amount to ₹${discountedAmount.toFixed(2)}, which is below the minimum of ₹${minimumAmount}`
        }, 400)
      }

      // Validate admin permissions for high-value discounts
      if (discountPercentage > 75 && !currentAdmin?.role?.includes('super_admin')) {
        return c.json({
          success: false,
          error: 'Discounts above 75% require super admin approval'
        }, 403)
      }

      // Calculate end date
      const start = new Date(startDate)
      const end = new Date(start)
      end.setMonth(end.getMonth() + durationMonths)

      // ✅ FIXED: Use unified system - only update billingSubscriptions
      await db.transaction(async (tx) => {
        // Calculate discount amounts
        const originalAmountValue = parseFloat(subscription.monthlyAmount)
        const discountAmountValue = (originalAmountValue * discountPercentage) / 100
        const discountedAmountValue = originalAmountValue - discountAmountValue

        // Update subscription with proper amount changes
        await tx.update(billingSubscriptions)
          .set({
            originalMonthlyAmount: originalAmountValue.toString(),
            monthlyAmount: discountedAmountValue.toString(),
            hasActiveDiscount: true,
            currentDiscountPercentage: discountPercentage.toString(),
            discountStartDate: start.toISOString().split('T')[0],
            discountEndDate: end.toISOString().split('T')[0],
            discountReason: reason,
            updatedAt: new Date()
          })
          .where(eq(billingSubscriptions.id, subscriptionId))
      })

      // Calculate savings using original amount
      const originalAmountForSavings = parseFloat(subscription.monthlyAmount)
      const monthlySavings = (originalAmountForSavings * discountPercentage) / 100
      const totalSavings = monthlySavings * durationMonths

      // ✅ ENHANCED: Comprehensive audit logging
      try {
        const { auditLogger } = await import('@/src/services/auditLogger')

        await auditLogger.log({
          action: 'DISCOUNT_APPLIED',
          resource: 'subscription',
          resourceId: subscriptionId,
          details: {
            discountPercentage,
            durationMonths,
            startDate: start.toISOString(),
            reason,
            originalAmount: originalAmountForSavings,
            discountedAmount: originalAmountForSavings * (1 - discountPercentage / 100),
            monthlySavings,
            totalSavings,
            schoolName: subscription.schoolName
          },
          success: true,
          severity: 'medium',
          category: 'payment',
          // Admin info included in details
        })
      } catch (auditError) {
        console.error('Failed to log discount application audit:', auditError)
      }

      // Legacy console log for immediate debugging
      console.log('Discount created:', {
        subscriptionId,
        discountPercentage,
        durationMonths,
        monthlySavings,
        totalSavings,
        schoolName: subscription.schoolName,
        adminId: currentAdmin?.id
      })

      return c.json({
        success: true,
        data: {
          monthlySavings,
          totalSavings,
          schoolName: subscription.schoolName,
          discountPercentage,
          durationMonths,
          startDate: start.toISOString(),
          endDate: end.toISOString(),
          reason
        }
      })

    } catch (error) {
      console.error('Error creating discount:', error)

      // ✅ ENHANCED: Add audit logging for failures
      try {
        const { auditLogger } = await import('@/src/services/auditLogger')

        await auditLogger.log({
          action: 'DISCOUNT_APPLICATION_FAILED',
          resource: 'subscription',
          resourceId: c.req.param('id'),
          details: {
            error: error instanceof Error ? error.message : 'Unknown error',
            discountPercentage: c.req.valid('json').discountPercentage,
            durationMonths: c.req.valid('json').durationMonths,
            reason: c.req.valid('json').reason
          },
          success: false,
          severity: 'high',
          category: 'payment',
          // Admin info included in details
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        })
      } catch (auditError) {
        console.error('Failed to log discount application failure audit:', auditError)
      }

      return c.json({ success: false, error: 'Failed to create discount' }, 500)
    }
  }
)

// Get Subscription Discounts
app.get('/admin/subscriptions/:id/discounts',
  adminAuthMiddleware,
  requireAdminRole(['super_admin', 'billing', 'support']),
  async (c) => {
    try {
      const subscriptionId = c.req.param('id')

      const discounts = await db.select({
        id: subscriptionDiscounts.id,
        discountPercentage: subscriptionDiscounts.discountPercentage,
        durationMonths: subscriptionDiscounts.discountDurationMonths,
        startDate: subscriptionDiscounts.startDate,
        endDate: subscriptionDiscounts.endDate,
        remainingMonths: subscriptionDiscounts.remainingMonths,
        isActive: subscriptionDiscounts.isActive,
        reason: subscriptionDiscounts.reason,
        createdAt: subscriptionDiscounts.createdAt,
        createdByName: adminUsers.name
      })
      .from(subscriptionDiscounts)
      .leftJoin(adminUsers, eq(subscriptionDiscounts.createdBy, adminUsers.id))
      .where(eq(subscriptionDiscounts.subscriptionId, subscriptionId))
      .orderBy(desc(subscriptionDiscounts.createdAt))

      return c.json({ success: true, data: discounts })

    } catch (error) {
      console.error('Error fetching discounts:', error)
      return c.json({ success: false, error: 'Failed to fetch discounts' }, 500)
    }
  }
)

// Update Discount (Deactivate)
app.put('/admin/discounts/:id/deactivate',
  adminAuthMiddleware,
  requireAdminRole(['super_admin', 'billing']),
  async (c) => {
    try {
      const discountId = c.req.param('id')
      const currentAdmin = getCurrentAdmin(c)

      // Get discount details
      const [discount] = await db.select({
        id: subscriptionDiscounts.id,
        subscriptionId: subscriptionDiscounts.subscriptionId,
        discountPercentage: subscriptionDiscounts.discountPercentage,
        isActive: subscriptionDiscounts.isActive,
        schoolName: clients.schoolName
      })
      .from(subscriptionDiscounts)
      .innerJoin(billingSubscriptions, eq(subscriptionDiscounts.subscriptionId, billingSubscriptions.id))
      .innerJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(eq(subscriptionDiscounts.id, discountId))
      .limit(1)

      if (!discount) {
        return c.json({ success: false, error: 'Discount not found' }, 404)
      }

      if (!discount.isActive) {
        return c.json({ success: false, error: 'Discount is already inactive' }, 400)
      }

      // Deactivate discount
      await db.update(subscriptionDiscounts)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(subscriptionDiscounts.id, discountId))

      // Update subscription discount status
      await db.update(billingSubscriptions)
        .set({
          hasActiveDiscount: false,
          currentDiscountPercentage: '0',
          discountEndDate: null,
          updatedAt: new Date()
        })
        .where(eq(billingSubscriptions.id, discount.subscriptionId!))

      // Log audit event
      console.log('Discount deactivated:', {
        discountId,
        subscriptionId: discount.subscriptionId,
        discountPercentage: discount.discountPercentage,
        adminId: currentAdmin?.id
      })

      return c.json({ success: true, message: 'Discount deactivated successfully' })

    } catch (error) {
      console.error('Error deactivating discount:', error)
      return c.json({ success: false, error: 'Failed to deactivate discount' }, 500)
    }
  }
)

// ===== EXPENSE MANAGEMENT =====

// Set Operational Expenses
const setExpensesSchema = z.object({
  monthlyOperationalCost: z.number().min(0),
  description: z.string().optional(),
  category: z.enum(['operational', 'infrastructure', 'support']).default('operational'),
  effectiveFrom: z.string().datetime()
})

app.post('/admin/subscriptions/:id/expenses',
  adminAuthMiddleware,
  requireAdminRole(['super_admin', 'billing']),
  zValidator('json', setExpensesSchema),
  async (c) => {
    try {
      const subscriptionId = c.req.param('id')
      const { monthlyOperationalCost, description, category, effectiveFrom } = c.req.valid('json')
      const currentAdmin = getCurrentAdmin(c)

      // Validate subscription exists
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        schoolName: clients.schoolName
      })
      .from(billingSubscriptions)
      .innerJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        return c.json({ success: false, error: 'Subscription not found' }, 404)
      }

      // Deactivate existing active expenses
      await db.update(subscriptionExpenses)
        .set({
          isActive: false,
          effectiveUntil: new Date(effectiveFrom).toISOString().split('T')[0],
          updatedAt: new Date()
        })
        .where(and(
          eq(subscriptionExpenses.subscriptionId, subscriptionId),
          eq(subscriptionExpenses.isActive, true)
        ))

      // Create new expense record
      const [expense] = await db.insert(subscriptionExpenses)
        .values({
          subscriptionId,
          monthlyOperationalCost: monthlyOperationalCost.toString(),
          description,
          category,
          effectiveFrom: new Date(effectiveFrom).toISOString().split('T')[0],
          isActive: true,
          createdBy: currentAdmin?.id
        })
        .returning()

      // Log audit event
      console.log('Expenses updated:', {
        subscriptionId,
        monthlyOperationalCost,
        category,
        description,
        schoolName: subscription.schoolName,
        adminId: currentAdmin?.id
      })

      return c.json({ 
        success: true, 
        data: expense,
        message: 'Operational expenses updated successfully'
      })

    } catch (error) {
      console.error('Error setting expenses:', error)
      return c.json({ success: false, error: 'Failed to set expenses' }, 500)
    }
  }
)

// Get Subscription Expenses
app.get('/admin/subscriptions/:id/expenses',
  adminAuthMiddleware,
  requireAdminRole(['super_admin', 'billing', 'support']),
  async (c) => {
    try {
      const subscriptionId = c.req.param('id')

      const expenses = await db.select({
        id: subscriptionExpenses.id,
        monthlyOperationalCost: subscriptionExpenses.monthlyOperationalCost,
        description: subscriptionExpenses.description,
        category: subscriptionExpenses.category,
        effectiveFrom: subscriptionExpenses.effectiveFrom,
        effectiveUntil: subscriptionExpenses.effectiveUntil,
        isActive: subscriptionExpenses.isActive,
        createdAt: subscriptionExpenses.createdAt,
        createdByName: adminUsers.name
      })
      .from(subscriptionExpenses)
      .leftJoin(adminUsers, eq(subscriptionExpenses.createdBy, adminUsers.id))
      .where(eq(subscriptionExpenses.subscriptionId, subscriptionId))
      .orderBy(desc(subscriptionExpenses.createdAt))

      return c.json({ success: true, data: expenses })

    } catch (error) {
      console.error('Error fetching expenses:', error)
      return c.json({ success: false, error: 'Failed to fetch expenses' }, 500)
    }
  }
)

// ===== COMMISSION CONFIGURATION =====

// Set Partner Commission Configuration
const setCommissionConfigSchema = z.object({
  partnerId: z.string().uuid(),
  commissionPercentage: z.number().min(0).max(100),
  holdingPeriodDays: z.number().min(0).default(30)
})

app.post('/admin/subscriptions/:id/commission-config',
  adminAuthMiddleware,
  requireAdminRole(['super_admin', 'billing']),
  zValidator('json', setCommissionConfigSchema),
  async (c) => {
    try {
      const subscriptionId = c.req.param('id')
      const { partnerId, commissionPercentage, holdingPeriodDays } = c.req.valid('json')
      const currentAdmin = getCurrentAdmin(c)

      // Validate subscription and partner exist
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        schoolName: clients.schoolName
      })
      .from(billingSubscriptions)
      .innerJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        return c.json({ success: false, error: 'Subscription not found' }, 404)
      }

      const [partner] = await db.select({
        id: partners.id,
        name: partners.name
      })
      .from(partners)
      .where(eq(partners.id, partnerId))
      .limit(1)

      if (!partner) {
        return c.json({ success: false, error: 'Partner not found' }, 404)
      }

      // Deactivate existing active commission config
      await db.update(partnerCommissionConfig)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(and(
          eq(partnerCommissionConfig.subscriptionId, subscriptionId),
          eq(partnerCommissionConfig.isActive, true)
        ))

      // Create new commission configuration
      const [commissionConfig] = await db.insert(partnerCommissionConfig)
        .values({
          partnerId,
          subscriptionId,
          commissionPercentage: commissionPercentage.toString(),
          holdingPeriodDays,
          isActive: true,
          createdBy: currentAdmin?.id
        })
        .returning()

      // Log audit event
      console.log('Commission config updated:', {
        subscriptionId,
        partnerId,
        partnerName: partner.name,
        commissionPercentage,
        holdingPeriodDays,
        schoolName: subscription.schoolName,
        adminId: currentAdmin?.id
      })

      return c.json({
        success: true,
        data: {
          ...commissionConfig,
          partnerName: partner.name,
          schoolName: subscription.schoolName
        },
        message: 'Commission configuration updated successfully'
      })

    } catch (error) {
      console.error('Error setting commission config:', error)
      return c.json({ success: false, error: 'Failed to set commission configuration' }, 500)
    }
  }
)

// Get Commission Overview Dashboard
app.get('/admin/commissions/overview',
  adminAuthMiddleware,
  requireAdminRole(['super_admin', 'billing', 'support']),
  async (c) => {
    try {
      // Get total eligible amount
      const [eligibleStats] = await db.select({
        totalEligibleAmount: sql<number>`COALESCE(SUM(${partnerCommissionTransactions.commissionAmount}), 0)`,
        eligibleCount: sql<number>`COUNT(*)`
      })
      .from(partnerCommissionTransactions)
      .where(eq(partnerCommissionTransactions.status, 'eligible'))

      // Get total held amount
      const [heldStats] = await db.select({
        totalHeldAmount: sql<number>`COALESCE(SUM(${partnerCommissionTransactions.commissionAmount}), 0)`,
        heldCount: sql<number>`COUNT(*)`
      })
      .from(partnerCommissionTransactions)
      .where(eq(partnerCommissionTransactions.status, 'held'))

      // Get total paid this month
      const currentMonth = new Date()
      currentMonth.setDate(1)
      const [paidStats] = await db.select({
        totalPaidThisMonth: sql<number>`COALESCE(SUM(${partnerCommissionTransactions.payoutAmount}), 0)`,
        paidCount: sql<number>`COUNT(*)`
      })
      .from(partnerCommissionTransactions)
      .where(and(
        eq(partnerCommissionTransactions.status, 'paid'),
        sql`${partnerCommissionTransactions.paidDate} >= ${currentMonth}`
      ))

      // Get partners with eligible payouts
      const partnersWithEligible = await db.select({
        partnerId: partnerCommissionTransactions.partnerId,
        partnerName: partners.name,
        totalEligibleAmount: sql<number>`SUM(${partnerCommissionTransactions.commissionAmount})`,
        transactionCount: sql<number>`COUNT(*)`
      })
      .from(partnerCommissionTransactions)
      .innerJoin(partners, eq(partnerCommissionTransactions.partnerId, partners.id))
      .where(eq(partnerCommissionTransactions.status, 'eligible'))
      .groupBy(partnerCommissionTransactions.partnerId, partners.name)
      .orderBy(sql`SUM(${partnerCommissionTransactions.commissionAmount}) DESC`)

      return c.json({
        success: true,
        data: {
          overview: {
            totalEligibleAmount: eligibleStats.totalEligibleAmount || 0,
            totalHeldAmount: heldStats.totalHeldAmount || 0,
            totalPaidThisMonth: paidStats.totalPaidThisMonth || 0,
            partnersWithEligiblePayouts: partnersWithEligible.length,
            eligibleTransactions: eligibleStats.eligibleCount || 0,
            heldTransactions: heldStats.heldCount || 0,
            paidTransactionsThisMonth: paidStats.paidCount || 0
          },
          partnersWithEligible
        }
      })

    } catch (error) {
      console.error('Error fetching commission overview:', error)
      return c.json({ success: false, error: 'Failed to fetch commission overview' }, 500)
    }
  }
)

export default app
