# Troubleshoot: "Pay Now" Button Missing

## 🎯 Quick Diagnosis

The "Pay Now" button appears when `billingSummary.outstandingAmount > 0`. If it's missing, the outstanding amount is 0.

### ⚡ Quick Fix (Try This First)

Run this SQL command to force payment due:
```sql
UPDATE subscriptions 
SET next_billing_date = CURRENT_DATE - INTERVAL '1 day'
WHERE status = 'active';
```

Then restart your app and check if the button appears.

## 🔍 Step-by-Step Diagnosis

### Step 1: Check Server Logs

1. **Restart your application** to see fresh logs
2. **Login to school dashboard** and go to `/profile/billing`
3. **Check server console** for billing calculator logs:

**Expected logs:**
```
🔍 [Billing Calculator] Calculating billing status for client: abc-123
✅ [Billing Calculator] Found subscription: { id: "sub-123", monthlyAmount: "5000", ... }
📅 [Billing Calculator] Date comparison: { currentDate: "2025-07-06", nextBillingDate: "2025-07-05", ... }
💳 [Billing Calculator] Payment status: { isPaid: false, isOverdue: true }
💰 [Billing Calculator] Outstanding calculation: { outstandingAmount: 5000, ... }
🎯 [Billing Calculator] Final result: { paymentStatus: "due", outstandingAmount: 5000 }
📊 [Billing Summary] Returning billing summary: { outstandingAmount: 5000 }
```

**Problem indicators:**
- `❌ No active subscription found` → No subscription exists
- `outstandingAmount: 0` → Billing date is in future or payment exists
- `isPaid: true` → Recent payment found
- `currentDate < nextBillingDate` → Billing date not reached yet

### Step 2: Database Investigation

Run the debug script to get SQL queries:
```bash
node scripts/debug-billing-data.js
```

Execute the provided SQL queries to check:

1. **Subscription exists and is active**
2. **Monthly amount is set correctly**
3. **Billing date is not in future**
4. **No recent payments exist**
5. **Client ID matches between login and subscription**

### Step 3: Frontend API Response

1. **Open browser dev tools** (F12)
2. **Go to Network tab**
3. **Visit `/profile/billing`**
4. **Find the API call** to `/api/school/billing`
5. **Check the response**:

**Expected response for "Pay Now" to show:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "outstandingAmount": 5000,        // Must be > 0
      "paymentStatus": "due",           // Not "current"
      "currentMonthAmount": 5000,
      "nextBillingDate": "2025-07-05"
    }
  }
}
```

**Problem indicators:**
```json
{
  "summary": {
    "outstandingAmount": 0,             // ❌ This causes missing button
    "paymentStatus": "current",         // ❌ No payment due
    "paymentStatus": "no_subscription"  // ❌ No subscription found
  }
}
```

## 🔧 Common Issues & Fixes

### Issue 1: Billing Date in Future

**Symptom:** `outstandingAmount: 0`, `paymentStatus: "current"`

**Cause:** Mock payments advanced `next_billing_date` to future months

**Fix:**
```sql
-- Reset to current date
UPDATE subscriptions 
SET next_billing_date = CURRENT_DATE
WHERE status = 'active' AND next_billing_date > CURRENT_DATE;

-- Or force to yesterday for immediate payment due
UPDATE subscriptions 
SET next_billing_date = CURRENT_DATE - INTERVAL '1 day'
WHERE status = 'active';
```

### Issue 2: No Subscription Found

**Symptom:** `❌ No active subscription found for client`

**Cause:** No subscription exists or client ID mismatch

**Fix:**
```sql
-- Check if subscription exists
SELECT s.*, c.school_name 
FROM subscriptions s 
LEFT JOIN clients c ON s.client_id = c.id 
WHERE s.status = 'active';

-- Check client_users table for login client_id
SELECT cu.*, c.school_name 
FROM client_users cu 
LEFT JOIN clients c ON cu.client_id = c.id;

-- If client_id mismatch, update subscription:
UPDATE subscriptions 
SET client_id = 'correct-client-id'
WHERE id = 'subscription-id';
```

### Issue 3: Monthly Amount is Zero

**Symptom:** `currentMonthAmount: 0`, `outstandingAmount: 0`

**Fix:**
```sql
-- Check monthly amounts
SELECT id, monthly_amount, price_per_student, student_count 
FROM subscriptions 
WHERE status = 'active';

-- Fix zero amounts
UPDATE subscriptions 
SET monthly_amount = (price_per_student * student_count)
WHERE monthly_amount IS NULL OR monthly_amount = 0;
```

### Issue 4: Recent Payment Exists

**Symptom:** `isPaid: true`, `outstandingAmount: 0`

**Cause:** Payment record exists for current month

**Fix:**
```sql
-- Check recent payments
SELECT * FROM payments 
WHERE client_id = 'your-client-id' 
  AND status = 'success'
  AND processed_at >= DATE_TRUNC('month', CURRENT_DATE)
ORDER BY processed_at DESC;

-- If these are mock payments, delete them:
DELETE FROM payments 
WHERE razorpay_payment_id LIKE 'pay_%' 
  AND razorpay_order_id LIKE 'order_%';
```

### Issue 5: Subscription Status Not Active

**Fix:**
```sql
-- Check subscription status
SELECT id, status, client_id FROM subscriptions;

-- Activate subscription
UPDATE subscriptions 
SET status = 'active' 
WHERE id = 'subscription-id';
```

## 🧪 Testing Steps

After applying fixes:

1. **Restart application**
2. **Clear browser cache**
3. **Login to school dashboard**
4. **Go to `/profile/billing`**
5. **Check server logs** for billing calculator output
6. **Verify API response** in browser dev tools
7. **Confirm "Pay Now" button** appears

## 🎯 Success Criteria

The "Pay Now" button should appear when:

- ✅ `subscription.status = 'active'`
- ✅ `subscription.monthly_amount > 0`
- ✅ `subscription.next_billing_date <= current_date`
- ✅ No recent payments in current month
- ✅ API returns `outstandingAmount > 0`
- ✅ Frontend condition `billingSummary.outstandingAmount > 0` is true

## 🚨 Emergency Fix

If nothing else works, use this emergency fix to force payment due:

```sql
-- Force immediate payment due
UPDATE subscriptions 
SET next_billing_date = '2025-01-01',  -- Far in past
    monthly_amount = 5000               -- Ensure amount is set
WHERE status = 'active';

-- Remove any recent payments
DELETE FROM payments 
WHERE client_id IN (SELECT client_id FROM subscriptions WHERE status = 'active')
  AND processed_at >= '2025-07-01';
```

## 📞 Still Not Working?

If the button still doesn't appear:

1. **Check client authentication** - Ensure you're logged in as the correct school
2. **Verify client_id matching** - Login client_id must match subscription client_id
3. **Check subscription ownership** - Subscription must belong to logged-in school
4. **Review server errors** - Look for any errors in billing calculation
5. **Test with different user** - Try logging in as different school user

## 🎉 Success!

Once the "Pay Now" button appears:

1. **Test payment flow** with mock payments
2. **Set up real Razorpay credentials** for production
3. **Monitor billing automation** going forward

---

**The "Pay Now" button should now be visible in your school dashboard!**
