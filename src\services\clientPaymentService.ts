import { db } from '../db'
import { billingInvoices, clients, billingPayments, billingSubscriptions, subscriptions, schoolReferrals } from '../db/schema'
import { eq, and, desc, count } from 'drizzle-orm'
import { razorpayService } from './razorpayService'
import { emailService } from './emailService'

interface PaymentOrderData {
  invoiceId: string
  clientId: string
  amount: number
  currency: string
  receipt: string
  notes?: Record<string, string>
}

interface PaymentVerificationData {
  razorpayOrderId: string
  razorpayPaymentId: string
  razorpaySignature: string
  invoiceId: string
  clientId: string
}

interface ClientPaymentResult {
  success: boolean
  data?: any
  error?: string
}

interface InvoicePaymentInfo {
  id: string
  invoiceNumber: string
  monthlyAmount: string
  status: string
  nextBillingDate: string
  schoolName: string
  email: string
  canPay: boolean
  paymentHistory: any[]
}

/**
 * Dedicated service for client payment operations
 * Separates client payment UX from admin subscription management
 */
export class ClientPaymentService {
  
  /**
   * Get invoice payment information for client
   */
  async getInvoicePaymentInfo(invoiceId: string, clientId: string): Promise<ClientPaymentResult> {
    try {
      // Fetch invoice with client details
      const [invoiceData] = await db
        .select({
          invoice: billingInvoices,
          client: {
            schoolName: clients.schoolName,
            email: clients.email,
            phone: clients.phone,
            contactPerson: clients.contactPerson
          }
        })
        .from(billingInvoices)
        .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
        .where(and(
          eq(billingInvoices.id, invoiceId),
          eq(billingInvoices.clientId, clientId)
        ))
        .limit(1)

      if (!invoiceData) {
        return {
          success: false,
          error: 'Invoice not found or access denied'
        }
      }

      // Get payment history for this invoice
      const paymentHistory = await db
        .select()
        .from(billingPayments)
        .where(eq(billingPayments.invoiceId, invoiceId))
        .orderBy(desc(billingPayments.createdAt))

      const invoice = invoiceData.invoice
      const client = invoiceData.client

      const paymentInfo: InvoicePaymentInfo = {
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        monthlyAmount: invoice.totalAmount,
        status: invoice.status || 'draft',
        nextBillingDate: invoice.dueDate || '',
        schoolName: client?.schoolName || 'Unknown School',
        email: client?.email || '',
        canPay: ['sent', 'overdue'].includes(invoice.status || ''),
        paymentHistory: paymentHistory.map(payment => ({
          id: payment.id,
          monthlyAmount: payment.amount,
          status: payment.status,
          paymentMethod: payment.paymentMethod,
          createdAt: payment.createdAt,
          razorpayPaymentId: payment.razorpayPaymentId
        }))
      }

      return {
        success: true,
        data: paymentInfo
      }

    } catch (error) {
      console.error('Error fetching invoice payment info:', error)
      return {
        success: false,
        error: 'Failed to fetch invoice information'
      }
    }
  }

  /**
   * Create payment order for invoice with enhanced UX
   */
  async createPaymentOrder(invoiceId: string, clientId: string, paymentMethod?: string): Promise<ClientPaymentResult> {
    try {
      // Validate invoice and client
      const invoiceInfo = await this.getInvoicePaymentInfo(invoiceId, clientId)
      
      if (!invoiceInfo.success || !invoiceInfo.data) {
        return invoiceInfo
      }

      const invoice = invoiceInfo.data

      // Check if invoice can be paid
      if (!invoice.canPay) {
        return {
          success: false,
          error: `Invoice cannot be paid. Current status: ${invoice.status}`
        }
      }

      // Check if invoice is already paid
      if (invoice.status === 'paid') {
        return {
          success: false,
          error: 'Invoice is already paid'
        }
      }

      // Create Razorpay order
      const amount = Math.round(parseFloat(invoice.totalAmount) * 100) // Convert to paise
      const orderData: PaymentOrderData = {
        invoiceId,
        clientId,
        amount,
        currency: 'INR',
        receipt: `PAY-${invoice.invoiceNumber}`,
        notes: {
          invoice_id: invoiceId,
          client_id: clientId,
          school_name: invoice.schoolName,
          invoice_number: invoice.invoiceNumber,
          payment_method: paymentMethod || 'not_specified',
          service: 'schopio_school_management'
        }
      }

      const orderResult = await razorpayService.createOrder(orderData)

      if (!orderResult.success) {
        console.error('Razorpay order creation failed:', orderResult.error)
        return {
          success: false,
          error: 'Failed to create payment order. Please try again.'
        }
      }

      // Return payment configuration for client
      return {
        success: true,
        data: {
          order: orderResult.order,
          invoice: {
            id: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            monthlyAmount: invoice.totalAmount,
            schoolName: invoice.schoolName
          },
          razorpayConfig: {
            keyId: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
            currency: 'INR',
            name: 'Schopio',
            description: `Payment for Invoice ${invoice.invoiceNumber}`,
            image: '/logo.png', // Add company logo
            prefill: {
              email: invoice.email,
              name: invoice.schoolName,
              contact: '' // Will be filled from client data if available
            },
            theme: {
              color: '#2563eb'
            },
            modal: {
              ondismiss: () => {
                console.log('Payment modal dismissed')
              }
            }
          }
        }
      }

    } catch (error) {
      console.error('Error creating payment order:', error)
      return {
        success: false,
        error: 'Failed to initiate payment. Please try again.'
      }
    }
  }

  /**
   * Verify payment and update records with enhanced error handling
   */
  async verifyPayment(verificationData: PaymentVerificationData): Promise<ClientPaymentResult> {
    try {
      const { razorpayOrderId, razorpayPaymentId, razorpaySignature, invoiceId, clientId } = verificationData

      // Verify payment signature
      const isValidSignature = razorpayService.verifyPaymentSignature({
        razorpayOrderId,
        razorpayPaymentId,
        razorpaySignature
      })

      if (!isValidSignature) {
        console.error('Invalid payment signature:', { razorpayOrderId, razorpayPaymentId })
        return {
          success: false,
          error: 'Payment verification failed. Invalid signature.'
        }
      }

      // Fetch payment details from Razorpay
      const paymentDetails = await razorpayService.getPayment(razorpayPaymentId)
      
      if (!paymentDetails.success) {
        return {
          success: false,
          error: 'Failed to fetch payment details from gateway'
        }
      }

      const payment = paymentDetails.payment

      // Verify invoice belongs to client
      const [invoiceData] = await db
        .select({
          id: billingInvoices.id,
          invoiceNumber: billingInvoices.invoiceNumber,
          monthlyAmount: billingInvoices.totalAmount,
          status: billingInvoices.status,
          clientId: billingInvoices.clientId,
          subscriptionId: billingInvoices.subscriptionId,
          client: {
            schoolName: clients.schoolName,
            email: clients.email
          }
        })
        .from(billingInvoices)
        .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
        .where(and(
          eq(billingInvoices.id, invoiceId),
          eq(billingInvoices.clientId, clientId)
        ))
        .limit(1)

      if (!invoiceData) {
        return {
          success: false,
          error: 'Invoice not found or access denied'
        }
      }

      // Check if payment amount matches invoice amount
      const expectedAmount = Math.round(parseFloat(invoiceData.monthlyAmount) * 100)
      if (payment.amount !== expectedAmount) {
        console.error('Payment amount mismatch:', { 
          expected: expectedAmount, 
          received: payment.amount,
          invoiceId 
        })
        return {
          success: false,
          error: 'Payment amount does not match invoice amount'
        }
      }

      // Process payment in database transaction
      const result = await db.transaction(async (tx) => {
        // Create payment record
        const [newPayment] = await tx.insert(billingPayments).values({
          invoiceId: invoiceId,
          clientId,
          razorpayPaymentId,
          razorpayOrderId,
          amount: invoiceData.monthlyAmount,
          currency: 'INR',
          status: 'succeeded',
          paymentMethod: payment.method || 'razorpay',
          createdAt: new Date()
        }).returning()

        // Update invoice status
        await tx.update(billingInvoices)
          .set({
            status: 'paid',
            paidDate: new Date().toISOString().split('T')[0]
          })
          .where(eq(billingInvoices.id, invoiceId))

        // Update billing cycle status if applicable
        if (invoiceData.subscriptionId) {
          await tx.update(billingSubscriptions)
            .set({ status: 'paid' })
            .where(eq(billingSubscriptions.id, invoiceData.subscriptionId))
        }

        return newPayment
      })

      console.log(`✅ Payment verified successfully: ${razorpayPaymentId} for invoice ${invoiceData.invoiceNumber}`)

      // Process partner commission after successful payment
      try {
        // Check if this school has a partner referral
        const [partnerReferral] = await db
          .select({
            partnerId: schoolReferrals.partnerId,
            isActive: schoolReferrals.isActive
          })
          .from(schoolReferrals)
          .where(and(
            eq(schoolReferrals.clientId, clientId),
            eq(schoolReferrals.isActive, true)
          ))
          .limit(1)

        if (partnerReferral && partnerReferral.partnerId) {
          console.log(`Processing commission for partner ${partnerReferral.partnerId} on payment ${result.id}`)

          const grossAmount = parseFloat(result.amount)

          // Import commission processor
          const { commissionProcessor } = await import('@/src/services/commissionProcessor')

          await commissionProcessor.processCommissionForPayment(
            result.id,
            partnerReferral.partnerId,
            clientId,
            grossAmount
          )

          console.log(`Commission processing completed for payment ${result.id}`)
        } else {
          console.log(`No active partner referral found for client ${clientId}`)
        }
      } catch (commissionError) {
        console.error('Commission processing error:', commissionError)
        // Don't fail the payment if commission processing fails
      }

      // Send payment confirmation email (async, don't wait)
      this.sendPaymentConfirmationEmail(invoiceData, result).catch(error => {
        console.error('Failed to send payment confirmation email:', error)
      })

      return {
        success: true,
        data: {
          payment: {
            id: result.id,
            razorpayPaymentId,
            monthlyAmount: result.amount,
            status: result.status,
            createdAt: result.createdAt
          },
          invoice: {
            id: invoiceData.id,
            invoiceNumber: invoiceData.invoiceNumber,
            status: 'paid',
            paidDate: new Date().toISOString().split('T')[0]
          }
        }
      }

    } catch (error) {
      console.error('Error verifying payment:', error)
      return {
        success: false,
        error: 'Payment verification failed. Please contact support if amount was deducted.'
      }
    }
  }

  /**
   * Send payment confirmation email
   */
  private async sendPaymentConfirmationEmail(invoiceData: any, paymentData: any): Promise<void> {
    try {
      if (!invoiceData.client?.email) {
        console.warn('No email address found for payment confirmation')
        return
      }

      await emailService.sendPaymentConfirmationWithPDF({
        schoolName: invoiceData.client.schoolName,
        contactPerson: invoiceData.client.contactPerson || null,
        email: invoiceData.client.email,
        invoiceNumber: invoiceData.invoiceNumber,
        amount: invoiceData.monthlyAmount,
        dueDate: new Date().toLocaleDateString('en-IN'),
        invoiceId: invoiceData.id
      })

      console.log(`📧 Payment confirmation email sent to ${invoiceData.client.email}`)
    } catch (error) {
      console.error('Error sending payment confirmation email:', error)
    }
  }

  /**
   * Get client payment history with pagination
   */
  async getClientPaymentHistory(clientId: string, page: number = 1, limit: number = 10): Promise<ClientPaymentResult> {
    try {
      const offset = (page - 1) * limit

      const paymentHistory = await db
        .select({
          payment: billingPayments,
          invoice: {
            invoiceNumber: billingInvoices.invoiceNumber,
            issuedDate: billingInvoices.issuedDate,
            monthlyAmount: billingInvoices.totalAmount
          }
        })
        .from(billingPayments)
        .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))
        .where(eq(billingPayments.clientId, clientId))
        .orderBy(desc(billingPayments.createdAt))
        .limit(limit)
        .offset(offset)

      // Get total count for pagination
      const [{ count: totalPayments }] = await db
        .select({ count: count() })
        .from(billingPayments)
        .where(eq(billingPayments.clientId, clientId))

      return {
        success: true,
        data: {
          payments: paymentHistory.map(({ payment, invoice }) => ({
            id: payment.id,
            monthlyAmount: payment.amount,
            currency: payment.currency,
            status: payment.status,
            paymentMethod: payment.paymentMethod,
            razorpayPaymentId: payment.razorpayPaymentId,
            createdAt: payment.createdAt,
            invoice: invoice ? {
              invoiceNumber: invoice.invoiceNumber,
              issuedDate: invoice.issuedDate,
              monthlyAmount: invoice.monthlyAmount
            } : null
          })),
          pagination: {
            page,
            limit,
            total: totalPayments,
            totalPages: Math.ceil(totalPayments / limit)
          }
        }
      }

    } catch (error) {
      console.error('Error fetching payment history:', error)
      return {
        success: false,
        error: 'Failed to fetch payment history'
      }
    }
  }
}

// Export singleton instance
export const clientPaymentService = new ClientPaymentService()
