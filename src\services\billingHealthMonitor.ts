/**
 * Billing Health Monitor
 * Comprehensive monitoring system for billing operations with real-time alerts
 */

import * as cron from 'node-cron'
import { db } from '@/db'
import { billingSubscriptions, billingInvoices, billingPayments, auditLogs } from '@/db/schema'
import { eq, and, gte, lte, count, sql, desc } from 'drizzle-orm'
import { auditLogger } from './auditLogger'
import { emailService } from './emailService'

interface HealthMetrics {
  timestamp: string
  billing: {
    activeSubscriptions: number
    pendingInvoices: number
    overdueInvoices: number
    successfulPayments24h: number
    failedPayments24h: number
    averageProcessingTime: number
  }
  system: {
    memoryUsage: number
    cpuUsage: number
    uptime: number
    errorRate24h: number
  }
  alerts: Array<{
    level: 'info' | 'warning' | 'critical'
    message: string
    timestamp: string
  }>
}

interface HealthConfig {
  enabled: boolean
  checkIntervalMinutes: number
  alertThresholds: {
    overdueInvoicesPercent: number
    errorRatePercent: number
    memoryUsagePercent: number
    processingTimeMs: number
  }
  alertChannels: {
    email: boolean
    webhook: boolean
  }
}

class BillingHealthMonitor {
  private config: HealthConfig
  private task: cron.ScheduledTask | null = null
  private isRunning = false
  private lastMetrics: HealthMetrics | null = null

  constructor() {
    this.config = {
      enabled: process.env.HEALTH_MONITORING_ENABLED !== 'false',
      checkIntervalMinutes: parseInt(process.env.HEALTH_CHECK_INTERVAL_MINUTES || '15'),
      alertThresholds: {
        overdueInvoicesPercent: parseFloat(process.env.OVERDUE_ALERT_THRESHOLD || '10'),
        errorRatePercent: parseFloat(process.env.ERROR_RATE_ALERT_THRESHOLD || '5'),
        memoryUsagePercent: parseFloat(process.env.MEMORY_ALERT_THRESHOLD || '80'),
        processingTimeMs: parseInt(process.env.PROCESSING_TIME_ALERT_THRESHOLD || '5000')
      },
      alertChannels: {
        email: process.env.HEALTH_ALERT_EMAIL !== 'false',
        webhook: process.env.HEALTH_ALERT_WEBHOOK === 'true'
      }
    }
  }

  async init(): Promise<void> {
    if (!this.config.enabled) {
      console.log('⏭️ Health monitoring disabled')
      return
    }

    console.log('💓 Initializing billing health monitor...')
    console.log(`⚙️ Configuration:`, this.config)

    // Schedule regular health checks
    this.scheduleHealthChecks()

    // Run initial health check
    await this.runHealthCheck()

    console.log('✅ Billing health monitor initialized')
  }

  private scheduleHealthChecks(): void {
    // Run every N minutes
    const cronExpression = `*/${this.config.checkIntervalMinutes} * * * *`
    
    this.task = cron.schedule(cronExpression, async () => {
      if (this.isRunning) {
        console.log('⏭️ Health check already running, skipping...')
        return
      }

      try {
        await this.runHealthCheck()
      } catch (error) {
        console.error('❌ Health check failed:', error)
      }
    }, {
      timezone: 'Asia/Kolkata'
    })

    this.task.start()
    console.log(`📅 Health monitoring scheduled: Every ${this.config.checkIntervalMinutes} minutes`)
  }

  async runHealthCheck(): Promise<HealthMetrics> {
    if (this.isRunning) {
      throw new Error('Health check already running')
    }

    this.isRunning = true

    try {
      const metrics = await this.collectMetrics()
      const alerts = await this.analyzeMetrics(metrics)
      
      metrics.alerts = alerts

      // Send alerts if any critical issues
      const criticalAlerts = alerts.filter(a => a.level === 'critical')
      if (criticalAlerts.length > 0) {
        await this.sendAlerts(criticalAlerts, metrics)
      }

      // Store metrics for trend analysis
      this.lastMetrics = metrics

      // Log health check
      await auditLogger.logAdmin('health_check', {
        adminId: 'system',
        resource: 'health_monitor',
        details: {
          metrics: {
            activeSubscriptions: metrics.billing.activeSubscriptions,
            pendingInvoices: metrics.billing.pendingInvoices,
            overdueInvoices: metrics.billing.overdueInvoices,
            errorRate: metrics.system.errorRate24h
          },
          alerts: alerts.length,
          criticalAlerts: criticalAlerts.length
        },
        ipAddress: 'system',
        userAgent: 'health-monitor',
        success: criticalAlerts.length === 0
      })

      return metrics

    } finally {
      this.isRunning = false
    }
  }

  private async collectMetrics(): Promise<HealthMetrics> {
    const timestamp = new Date().toISOString()
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    // Billing metrics
    const [
      activeSubsResult,
      pendingInvoicesResult,
      overdueInvoicesResult,
      successfulPaymentsResult,
      failedPaymentsResult
    ] = await Promise.all([
      // Active subscriptions
      db.select({ count: count() })
        .from(billingSubscriptions)
        .where(eq(billingSubscriptions.status, 'active')),

      // Pending invoices
      db.select({ count: count() })
        .from(billingInvoices)
        .where(eq(billingInvoices.status, 'pending')),

      // Overdue invoices
      db.select({ count: count() })
        .from(billingInvoices)
        .where(
          and(
            eq(billingInvoices.status, 'pending'),
            lte(billingInvoices.dueDate, now.toISOString().split('T')[0])
          )
        ),

      // Successful payments in last 24h
      db.select({ count: count() })
        .from(billingPayments)
        .where(
          and(
            eq(billingPayments.status, 'succeeded'),
            gte(billingPayments.createdAt, yesterday)
          )
        ),

      // Failed payments in last 24h (would need to track these)
      Promise.resolve([{ count: 0 }]) // TODO: Implement failed payment tracking
    ])

    // System metrics
    const memUsage = process.memoryUsage()
    const memoryUsage = Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)

    // Error rate from audit logs
    const errorLogsResult = await db.select({ count: count() })
      .from(auditLogs)
      .where(
        and(
          eq(auditLogs.success, false),
          gte(auditLogs.timestamp, yesterday)
        )
      )

    const totalLogsResult = await db.select({ count: count() })
      .from(auditLogs)
      .where(gte(auditLogs.timestamp, yesterday))

    const errorRate24h = totalLogsResult[0]?.count > 0 
      ? Math.round((errorLogsResult[0]?.count / totalLogsResult[0]?.count) * 100)
      : 0

    return {
      timestamp,
      billing: {
        activeSubscriptions: activeSubsResult[0]?.count || 0,
        pendingInvoices: pendingInvoicesResult[0]?.count || 0,
        overdueInvoices: overdueInvoicesResult[0]?.count || 0,
        successfulPayments24h: successfulPaymentsResult[0]?.count || 0,
        failedPayments24h: failedPaymentsResult[0]?.count || 0,
        averageProcessingTime: 0 // TODO: Calculate from payment processing times
      },
      system: {
        memoryUsage,
        cpuUsage: 0, // TODO: Implement CPU usage tracking
        uptime: process.uptime(),
        errorRate24h
      },
      alerts: []
    }
  }

  private async analyzeMetrics(metrics: HealthMetrics): Promise<Array<{ level: 'info' | 'warning' | 'critical'; message: string; timestamp: string }>> {
    const alerts: Array<{ level: 'info' | 'warning' | 'critical'; message: string; timestamp: string }> = []
    const timestamp = new Date().toISOString()

    // Check overdue invoices percentage
    if (metrics.billing.pendingInvoices > 0) {
      const overduePercent = (metrics.billing.overdueInvoices / metrics.billing.pendingInvoices) * 100
      if (overduePercent >= this.config.alertThresholds.overdueInvoicesPercent) {
        alerts.push({
          level: 'critical',
          message: `High overdue invoice rate: ${overduePercent.toFixed(1)}% (${metrics.billing.overdueInvoices}/${metrics.billing.pendingInvoices})`,
          timestamp
        })
      }
    }

    // Check error rate
    if (metrics.system.errorRate24h >= this.config.alertThresholds.errorRatePercent) {
      alerts.push({
        level: 'critical',
        message: `High error rate: ${metrics.system.errorRate24h}% in last 24 hours`,
        timestamp
      })
    }

    // Check memory usage
    if (metrics.system.memoryUsage >= this.config.alertThresholds.memoryUsagePercent) {
      alerts.push({
        level: 'warning',
        message: `High memory usage: ${metrics.system.memoryUsage}%`,
        timestamp
      })
    }

    // Check for no recent payments (potential issue)
    if (metrics.billing.activeSubscriptions > 10 && metrics.billing.successfulPayments24h === 0) {
      alerts.push({
        level: 'warning',
        message: `No successful payments in last 24 hours despite ${metrics.billing.activeSubscriptions} active subscriptions`,
        timestamp
      })
    }

    // Check for system health trends
    if (this.lastMetrics) {
      const overdueIncrease = metrics.billing.overdueInvoices - this.lastMetrics.billing.overdueInvoices
      if (overdueIncrease > 5) {
        alerts.push({
          level: 'warning',
          message: `Rapid increase in overdue invoices: +${overdueIncrease} since last check`,
          timestamp
        })
      }
    }

    return alerts
  }

  private async sendAlerts(alerts: Array<{ level: string; message: string; timestamp: string }>, metrics: HealthMetrics): Promise<void> {
    if (!this.config.alertChannels.email) {
      return
    }

    try {
      const alertMessage = `
🚨 SCHOPIO BILLING SYSTEM ALERT

${alerts.length} critical issue(s) detected:

${alerts.map(alert => `🔴 ${alert.message}`).join('\n')}

Current System Status:
📊 Active Subscriptions: ${metrics.billing.activeSubscriptions}
📋 Pending Invoices: ${metrics.billing.pendingInvoices}
⚠️ Overdue Invoices: ${metrics.billing.overdueInvoices}
💳 Successful Payments (24h): ${metrics.billing.successfulPayments24h}
💾 Memory Usage: ${metrics.system.memoryUsage}%
📈 Error Rate (24h): ${metrics.system.errorRate24h}%
⏱️ System Uptime: ${Math.round(metrics.system.uptime / 3600)} hours

Please investigate immediately.

Timestamp: ${metrics.timestamp}
      `

      await emailService.sendAlert({
        to: '<EMAIL>',
        subject: `🚨 Schopio: ${alerts.length} Critical Billing Alert(s)`,
        message: alertMessage
      })

      console.log('📧 Critical health alerts sent to admin')

    } catch (error) {
      console.error('Failed to send health alerts:', error)
    }
  }

  async getMetrics(): Promise<HealthMetrics | null> {
    return this.lastMetrics
  }

  async shutdown(): Promise<void> {
    if (this.task) {
      this.task.stop()
      this.task = null
    }
    console.log('🛑 Billing health monitor stopped')
  }

  getStatus(): { running: boolean; config: HealthConfig; lastCheck?: string } {
    return {
      running: this.isRunning,
      config: this.config,
      lastCheck: this.lastMetrics?.timestamp
    }
  }
}

// Export singleton instance
export const billingHealthMonitor = new BillingHealthMonitor()
