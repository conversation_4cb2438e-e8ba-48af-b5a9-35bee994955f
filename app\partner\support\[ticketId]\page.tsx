'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/Button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ArrowLeft,
  MessageSquare,
  Clock,
  AlertTriangle,
  Send,
  ArrowUp,
  User,
  Headphones,
  Building,
  UserCheck,
  CheckCircle
} from 'lucide-react'
import Link from 'next/link'

interface TicketMessage {
  id: string
  content: string
  senderType: 'school' | 'partner' | 'admin'
  senderName: string
  createdAt: string
  isInternal: boolean
}

interface TicketDetail {
  id: string
  title: string
  description: string
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  category: string
  createdAt: string
  updatedAt: string
  school: {
    name: string
    email: string
    contactPerson: string
  }
  messages: TicketMessage[]
  statusHistory: Array<{
    status: string
    timestamp: string
    note: string
  }>
}

export default function PartnerTicketDetailPage() {
  const [ticket, setTicket] = useState<TicketDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [newMessage, setNewMessage] = useState('')
  const [isInternal, setIsInternal] = useState(false)
  const [sendingMessage, setSendingMessage] = useState(false)
  const [escalationReason, setEscalationReason] = useState('')
  const [newPriority, setNewPriority] = useState('')
  const [escalating, setEscalating] = useState(false)
  const [showEscalation, setShowEscalation] = useState(false)
  
  const router = useRouter()
  const params = useParams()
  const ticketId = params.ticketId as string

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem('partnerToken')
    if (!token) {
      router.push('/partner/login')
      return
    }

    if (ticketId) {
      fetchTicketDetail()
    }
  }, [router, ticketId])

  const fetchTicketDetail = async () => {
    try {
      const token = localStorage.getItem('partnerToken')
      const response = await fetch(`/api/partner/support/tickets/${ticketId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('partnerToken')
          localStorage.removeItem('partner')
          router.push('/partner/login')
          return
        }
        throw new Error('Failed to fetch ticket details')
      }

      const data = await response.json()
      setTicket(data.data)
    } catch (error) {
      console.error('Ticket detail error:', error)
      setError('Failed to load ticket details')
    } finally {
      setLoading(false)
    }
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return

    setSendingMessage(true)
    try {
      const token = localStorage.getItem('partnerToken')
      const response = await fetch(`/api/partner/support/tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: newMessage,
          isInternal
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send message')
      }

      setNewMessage('')
      setIsInternal(false)
      await fetchTicketDetail() // Refresh ticket data
    } catch (error) {
      console.error('Send message error:', error)
      setError('Failed to send message')
    } finally {
      setSendingMessage(false)
    }
  }

  const handleEscalate = async () => {
    if (!escalationReason.trim()) return

    setEscalating(true)
    try {
      const token = localStorage.getItem('partnerToken')
      const response = await fetch(`/api/partner/support/tickets/${ticketId}/escalate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          escalationReason,
          ...(newPriority && { newPriority }),
          internalNotes: `Escalated by partner due to: ${escalationReason}`
        })
      })

      if (!response.ok) {
        throw new Error('Failed to escalate ticket')
      }

      setEscalationReason('')
      setNewPriority('')
      setShowEscalation(false)
      await fetchTicketDetail() // Refresh ticket data
    } catch (error) {
      console.error('Escalation error:', error)
      setError('Failed to escalate ticket')
    } finally {
      setEscalating(false)
    }
  }

  const handleStatusUpdate = async (newStatus: string) => {
    try {
      const token = localStorage.getItem('partnerToken')
      const response = await fetch(`/api/partner/support/tickets/${ticketId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus,
          note: `Status updated to ${newStatus} by partner`
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update ticket status')
      }

      await fetchTicketDetail() // Refresh ticket data
    } catch (error) {
      console.error('Status update error:', error)
      setError('Failed to update ticket status')
    }
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      open: 'destructive',
      in_progress: 'default',
      resolved: 'secondary',
      closed: 'outline'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status.replace('_', ' ')}</Badge>
  }

  const getPriorityBadge = (priority: string) => {
    const colors = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    }
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[priority as keyof typeof colors]}`}>
        {priority}
      </span>
    )
  }

  const getSenderIcon = (senderType: string) => {
    switch (senderType) {
      case 'school':
        return <Building className="w-4 h-4 text-blue-500" />
      case 'partner':
        return <Headphones className="w-4 h-4 text-emerald-500" />
      case 'admin':
        return <User className="w-4 h-4 text-purple-500" />
      default:
        return <User className="w-4 h-4 text-gray-500" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading ticket details...</p>
        </div>
      </div>
    )
  }

  if (error && !ticket) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchTicketDetail}>Try Again</Button>
        </div>
      </div>
    )
  }

  if (!ticket) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/partner/support">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Tickets
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">{ticket.title}</h1>
          <p className="text-gray-600 mt-1">
            Ticket #{ticket.id.slice(-8)} • {ticket.school.name}
          </p>
        </div>
        <div className="flex items-center gap-3">
          {getPriorityBadge(ticket.priority)}
          {getStatusBadge(ticket.status)}

          {/* Quick Actions */}
          <div className="flex gap-2">
            {ticket.status === 'open' && (
              <Button
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => handleStatusUpdate('in_progress')}
              >
                <UserCheck className="w-4 h-4 mr-2" />
                Take Ticket
              </Button>
            )}
            {ticket.status === 'in_progress' && (
              <Button
                size="sm"
                className="bg-green-600 hover:bg-green-700"
                onClick={() => handleStatusUpdate('resolved')}
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Mark Resolved
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={() => setShowEscalation(true)}>
              <AlertTriangle className="w-4 h-4 mr-2" />
              Escalate
            </Button>
          </div>
        </div>
      </div>

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-700">
            {error}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Ticket Description */}
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 whitespace-pre-wrap">{ticket.description}</p>
            </CardContent>
          </Card>

          {/* Messages */}
          <Card>
            <CardHeader>
              <CardTitle>Conversation</CardTitle>
              <CardDescription>
                {ticket.messages.length} messages
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {ticket.messages.map((message) => (
                  <div key={message.id} className={`flex gap-3 ${message.isInternal ? 'bg-yellow-50 p-3 rounded-lg' : ''}`}>
                    <div className="flex-shrink-0 mt-1">
                      {getSenderIcon(message.senderType)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-gray-900">{message.senderName}</span>
                        <span className="text-xs text-gray-500">
                          {new Date(message.createdAt).toLocaleString()}
                        </span>
                        {message.isInternal && (
                          <Badge variant="outline" className="text-xs">Internal</Badge>
                        )}
                      </div>
                      <p className="text-gray-700 whitespace-pre-wrap">{message.content}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Reply Form */}
          {ticket.status !== 'closed' && (
            <Card>
              <CardHeader>
                <CardTitle>Add Response</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Textarea
                    placeholder="Type your response..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    rows={4}
                  />
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="internal"
                        checked={isInternal}
                        onChange={(e) => setIsInternal(e.target.checked)}
                        className="rounded"
                      />
                      <label htmlFor="internal" className="text-sm text-gray-600">
                        Internal note (not visible to school)
                      </label>
                    </div>
                    <Button onClick={handleSendMessage} disabled={sendingMessage || !newMessage.trim()}>
                      {sendingMessage ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Sending...
                        </div>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2" />
                          Send Response
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Ticket Info */}
          <Card>
            <CardHeader>
              <CardTitle>Ticket Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Status</label>
                <p className="mt-1">{getStatusBadge(ticket.status)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Priority</label>
                <p className="mt-1">{getPriorityBadge(ticket.priority)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Category</label>
                <p className="mt-1 text-gray-900">{ticket.category}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Created</label>
                <p className="mt-1 text-gray-900">{new Date(ticket.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Last Updated</label>
                <p className="mt-1 text-gray-900">{new Date(ticket.updatedAt).toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>

          {/* School Info */}
          <Card>
            <CardHeader>
              <CardTitle>School Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">School Name</label>
                <p className="mt-1 text-gray-900">{ticket.school.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Contact Person</label>
                <p className="mt-1 text-gray-900">{ticket.school.contactPerson}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Email</label>
                <p className="mt-1 text-gray-900">{ticket.school.email}</p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          {ticket.status !== 'closed' && (
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setShowEscalation(!showEscalation)}
                  >
                    <ArrowUp className="w-4 h-4 mr-2" />
                    Escalate to Admin
                  </Button>
                  
                  {showEscalation && (
                    <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
                      <Textarea
                        placeholder="Reason for escalation..."
                        value={escalationReason}
                        onChange={(e) => setEscalationReason(e.target.value)}
                        rows={3}
                      />
                      <Select value={newPriority} onValueChange={setNewPriority}>
                        <SelectTrigger>
                          <SelectValue placeholder="New Priority (optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="urgent">Urgent</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="low">Low</SelectItem>
                        </SelectContent>
                      </Select>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={handleEscalate}
                          disabled={escalating || !escalationReason.trim()}
                        >
                          {escalating ? 'Escalating...' : 'Escalate'}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setShowEscalation(false)}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
