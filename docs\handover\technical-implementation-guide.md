# 🔧 Schopio Technical Implementation Guide

## 🏗️ **System Architecture**

### **Technology Stack**
```
Frontend: Next.js 14 + TypeScript + Tailwind CSS + shadcn/ui
Backend: Hono.js API with method chaining
Database: Neon PostgreSQL + Drizzle ORM
Payment: Razorpay (Test Mode)
Email: Resend Service
AI: Google Gemma-3.27B
Hosting: Vercel (Frontend) + Neon (Database)
```

### **Project Structure**
```
landing-page/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin Dashboard
│   ├── partner/           # Partner Portal
│   ├── profile/           # School Portal
│   └── api/[[...route]]/  # Hono.js API Routes
├── src/
│   ├── db/                # Database Schema & Connection
│   ├── services/          # Business Logic Services
│   ├── middleware/        # Authentication & Authorization
│   └── components/        # Reusable UI Components
├── docs/                  # Documentation
└── public/               # Static Assets
```

## 🗄️ **Database Schema Overview**

### **Core Tables**
- **clients:** School information and subscription details
- **subscriptions:** Subscription plans and billing configuration
- **billingInvoices:** Generated invoices for schools
- **billingPayments:** Payment records and transaction history
- **partners:** Referral partner information
- **schoolReferrals:** Partner-school referral relationships
- **partnerEarnings:** Commission calculations and tracking

### **Financial Tables**
- **subscriptionExpenses:** Operational expenses per subscription
- **discountApplications:** Applied discounts and duration
- **withdrawalRequests:** Partner withdrawal requests
- **commissionEscrow:** Commission holding and release management

### **Performance Optimization**
- **25 Database Indexes:** Deployed for optimal query performance
- **Composite Indexes:** Multi-column indexes for complex queries
- **Foreign Key Constraints:** Data integrity and referential consistency

## 🔐 **Authentication & Authorization**

### **Authentication Methods**
- **EMAIL OTP Only:** No SMS verification (using Resend service)
- **JWT Tokens:** Secure token-based authentication
- **Role-Based Access:** Admin, Partner, School user roles

### **Authorization Levels**
```typescript
Admin Roles:
- super_admin: Full system access
- billing: Financial management access
- support: Customer support access

Partner Roles:
- partner: Standard partner access
- premium_partner: Enhanced features access

School Roles:
- admin: School administrator
- user: Standard school user
```

### **Security Implementation**
- **Middleware Protection:** Route-level authentication
- **Token Validation:** JWT signature verification
- **Role Verification:** Permission-based access control
- **Rate Limiting:** API request throttling

## 💳 **Payment System Integration**

### **Razorpay Configuration**
```typescript
// Current: Test Mode
RAZORPAY_KEY_ID=rzp_test_xxxxx
RAZORPAY_KEY_SECRET=xxxxx

// Production: (To be configured)
RAZORPAY_KEY_ID=rzp_live_xxxxx
RAZORPAY_KEY_SECRET=xxxxx
```

### **Payment Flow**
1. **Invoice Generation:** PDF creation with school details
2. **Payment Link:** Razorpay payment page integration
3. **Webhook Verification:** Signature validation for security
4. **Payment Recording:** Database transaction logging
5. **Commission Trigger:** Automatic partner commission calculation

### **Webhook Handling**
- **Endpoint:** `/api/webhooks/razorpay`
- **Verification:** HMAC SHA256 signature validation
- **Events:** payment.captured, payment.failed
- **Error Handling:** Retry mechanism for failed webhooks

## 🤖 **Commission Calculation System**

### **Automatic Triggers**
```typescript
// Payment Success → Commission Calculation
Payment Endpoints:
- /api/payments (Razorpay payments)
- /api/client-payments (Manual payments)
- /src/services/clientPaymentService.ts

Commission Flow:
1. Payment verification
2. Partner referral check
3. Commission calculation
4. Escrow record creation
5. Partner notification
```

### **Manual Recalculation**
```typescript
// Admin endpoint for missing commissions
POST /api/admin/recalculate-commissions

Process:
1. Find payments without commission records
2. Check partner referrals
3. Calculate missing commissions
4. Create commission records
5. Update partner balances
```

### **Commission Formula**
```typescript
const grossAmount = schoolPayment
const operationalExpenses = getSubscriptionExpenses()
const discounts = getAppliedDiscounts()
const netProfit = grossAmount - operationalExpenses - discounts
const partnerCommission = netProfit * (partnerCommissionRate / 100)
const adminEarnings = netProfit - partnerCommission
```

## 📧 **Email System Integration**

### **Resend Configuration**
```typescript
RESEND_API_KEY=re_xxxxx
FROM_EMAIL=<EMAIL>
```

### **Email Templates**
- **OTP Verification:** Authentication codes
- **Invoice Delivery:** PDF invoice attachments
- **Payment Confirmation:** Receipt and transaction details
- **Commission Notifications:** Partner earning alerts
- **Overdue Reminders:** Payment due notifications

### **Email Service Implementation**
```typescript
// src/services/emailService.ts
- sendOTP()
- sendInvoice()
- sendPaymentConfirmation()
- sendCommissionNotification()
- sendOverdueReminder()
```

## 🔄 **API Architecture**

### **Hono.js Route Structure**
```typescript
app/api/[[...route]]/
├── route.ts           # Main router configuration
├── admin.ts           # Admin management APIs
├── partner.ts         # Partner portal APIs
├── school.ts          # School portal APIs
├── payments.ts        # Payment processing APIs
├── client-payments.ts # Manual payment APIs
└── webhooks.ts        # External webhook handlers
```

### **API Response Format**
```typescript
// Success Response
{
  success: true,
  data: {...},
  message?: string
}

// Error Response
{
  success: false,
  error: string,
  details?: {...}
}
```

### **Error Handling**
- **Global Error Handler:** Centralized error processing
- **Validation Errors:** Zod schema validation
- **Database Errors:** Drizzle ORM error handling
- **External API Errors:** Razorpay/Resend error handling

## 🎨 **Frontend Implementation**

### **UI Component Library**
- **shadcn/ui:** Base component library
- **Tailwind CSS:** Utility-first styling
- **Lucide Icons:** Consistent icon system
- **Framer Motion:** Smooth animations

### **State Management**
- **React Hooks:** useState, useEffect for local state
- **Context API:** Global state for authentication
- **Server State:** API calls with error handling
- **Form State:** React Hook Form for complex forms

### **Responsive Design**
- **Mobile-First:** Progressive enhancement approach
- **Breakpoints:** sm, md, lg, xl responsive design
- **Touch-Friendly:** Mobile-optimized interactions
- **Performance:** Optimized images and lazy loading

## 🧪 **Testing Strategy**

### **Current Testing Status**
- **API Testing:** Comprehensive service layer testing
- **Unit Tests:** Core business logic functions
- **Integration Tests:** Database and external API integration
- **Manual Testing:** UI and workflow validation

### **Testing Tools**
```typescript
// Testing Framework
- Jest: Unit and integration testing
- Supertest: API endpoint testing
- React Testing Library: Component testing
- Playwright: End-to-end testing (planned)
```

### **Test Coverage Goals**
- **Services:** 90%+ coverage for business logic
- **APIs:** 85%+ coverage for endpoints
- **Components:** 80%+ coverage for UI components
- **E2E:** Critical user journeys

## 🚀 **Deployment & DevOps**

### **Current Deployment**
- **Frontend:** Vercel automatic deployment
- **Database:** Neon PostgreSQL cloud hosting
- **Environment:** Development/staging on Vercel
- **Production:** Ready for deployment

### **Environment Variables**
```typescript
// Database
DATABASE_URL=postgresql://...

// Authentication
JWT_SECRET=xxxxx
ADMIN_JWT_SECRET=xxxxx

// Payment Gateway
RAZORPAY_KEY_ID=rzp_test_xxxxx
RAZORPAY_KEY_SECRET=xxxxx

// Email Service
RESEND_API_KEY=re_xxxxx

// AI Integration
GEMINI_API_KEY=xxxxx
```

### **Monitoring & Logging**
- **Console Logging:** Structured logging for debugging
- **Error Tracking:** Console error monitoring
- **Performance:** Database query optimization
- **Uptime:** Service availability monitoring

## 🔧 **Development Workflow**

### **Code Quality**
- **TypeScript:** Strict type checking
- **ESLint:** Code linting and formatting
- **Prettier:** Code formatting consistency
- **Git Hooks:** Pre-commit validation

### **Development Commands**
```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server

# Database
npm run db:generate  # Generate migrations
npm run db:migrate   # Run migrations
npm run db:studio    # Open Drizzle Studio

# Testing
npm run test         # Run test suite
npm run test:watch   # Watch mode testing
npm run type-check   # TypeScript validation
```

### **Git Workflow**
- **Main Branch:** Production-ready code
- **Development:** Feature development branch
- **Feature Branches:** Individual feature development
- **Pull Requests:** Code review process

## 📊 **Performance Optimization**

### **Database Optimization**
- **Indexes:** 25 performance indexes deployed
- **Query Optimization:** Efficient Drizzle ORM queries
- **Connection Pooling:** Neon connection management
- **Caching Strategy:** Query result caching (planned)

### **Frontend Optimization**
- **Code Splitting:** Next.js automatic splitting
- **Image Optimization:** Next.js image optimization
- **Bundle Analysis:** Webpack bundle analyzer
- **Lazy Loading:** Component and route lazy loading

### **API Optimization**
- **Response Compression:** Gzip compression
- **Request Validation:** Early validation and rejection
- **Rate Limiting:** API request throttling
- **Caching Headers:** HTTP caching strategies

---

**Document Version:** 1.0  
**Last Updated:** July 8, 2025  
**Next Review:** August 8, 2025
