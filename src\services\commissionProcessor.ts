import { db } from '@/src/db'
import { 
  partnerCommissionEscrow, 
  commissionReleaseAudit, 
  partnerTransactions,
  partnerFundAccounts,
  billingPayments,
  partners
} from '@/src/db/schema'
import { eq, and, sql, lte, desc } from 'drizzle-orm'
import { commissionCalculationEngine, type CommissionCalculation, type ReleaseConditions } from './commissionCalculationEngine'
import { razorpayRouteService } from './razorpayRouteService'

/**
 * Commission Processor Service
 * Handles automated commission processing, escrow management, and release automation
 */

export interface ProcessingResult {
  success: boolean
  escrowId?: string
  commissionAmount?: number
  riskScore?: number
  holdUntilDate?: Date
  error?: string
}

export interface ReleaseResult {
  success: boolean
  escrowId: string
  releasedAmount: number
  releaseType: 'automatic' | 'manual'
  newPartnerBalance: number
  error?: string
}

export interface EscrowStatus {
  id: string
  status: 'school_paid' | 'conditions_met' | 'released' | 'held' | 'cancelled'
  commissionAmount: number
  riskScore: number
  holdUntilDate: Date
  canRelease: boolean
  releaseConditions: ReleaseConditions
}

export class CommissionProcessor {

  /**
   * Process commission for a school payment
   */
  async processCommissionForPayment(
    paymentId: string,
    partnerId: string,
    schoolId: string,
    grossAmount: number
  ): Promise<ProcessingResult> {
    try {
      const currentDate = new Date()
      const monthYear = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`

      // Calculate commission using the calculation engine
      const calculation = await commissionCalculationEngine.calculateMonthlyCommission(
        schoolId,
        partnerId,
        grossAmount,
        monthYear,
        paymentId
      )

      // Calculate hold until date
      const holdUntilDate = new Date()
      holdUntilDate.setDate(holdUntilDate.getDate() + calculation.holdPeriod)

      // Create escrow record
      const [escrowRecord] = await db.insert(partnerCommissionEscrow).values({
        partnerId,
        schoolId,
        monthYear,
        baseAmount: grossAmount.toString(),
        commissionPercentage: calculation.partnerSharePercentage.toString(),
        commissionAmount: calculation.finalCommissionAmount.toString(),
        operationalExpenses: calculation.operationalExpenses.totalExpenses.toString(),
        netCommission: calculation.finalCommissionAmount.toString(),
        escrowStatus: 'school_paid',
        riskScore: calculation.riskScore,
        holdUntilDate,
        autoReleaseEnabled: calculation.riskScore <= 70,
        schoolPaymentId: paymentId,
        releaseConditions: {
          grossAmount: calculation.grossAmount,
          operationalExpenses: calculation.operationalExpenses.totalExpenses,
          netAmount: calculation.netAmount,
          partnerSharePercentage: calculation.partnerSharePercentage,
          riskFactors: calculation.metadata.riskFactors,
          calculationMethod: calculation.metadata.calculationMethod
        }
      }).returning()

      // Create audit record
      await db.insert(commissionReleaseAudit).values({
        escrowId: escrowRecord.id,
        actionType: 'escrow_created',
        triggeredBy: 'system',
        actionReason: 'School payment processed',
        previousStatus: null,
        newStatus: 'school_paid',
        amountAffected: calculation.finalCommissionAmount.toString(),
        metadata: {
          paymentId,
          calculationEngine: 'v1.0',
          riskFactors: calculation.metadata.riskFactors
        }
      })

      console.log(`✅ Commission escrow created: ${escrowRecord.id} for ₹${calculation.finalCommissionAmount}`)

      return {
        success: true,
        escrowId: escrowRecord.id,
        commissionAmount: calculation.finalCommissionAmount,
        riskScore: calculation.riskScore,
        holdUntilDate
      }

    } catch (error) {
      console.error('Commission processing error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Check and process eligible escrow releases
   */
  async processEligibleReleases(adminUserId?: string): Promise<ReleaseResult[]> {
    try {
      const results: ReleaseResult[] = []

      // Get escrows eligible for release
      const eligibleEscrows = await db
        .select({
          id: partnerCommissionEscrow.id,
          partnerId: partnerCommissionEscrow.partnerId,
          schoolId: partnerCommissionEscrow.schoolId,
          commissionAmount: partnerCommissionEscrow.commissionAmount,
          escrowStatus: partnerCommissionEscrow.escrowStatus,
          riskScore: partnerCommissionEscrow.riskScore,
          holdUntilDate: partnerCommissionEscrow.holdUntilDate,
          autoReleaseEnabled: partnerCommissionEscrow.autoReleaseEnabled,
          monthYear: partnerCommissionEscrow.monthYear
        })
        .from(partnerCommissionEscrow)
        .where(and(
          eq(partnerCommissionEscrow.escrowStatus, 'school_paid'),
          eq(partnerCommissionEscrow.autoReleaseEnabled, true),
          lte(partnerCommissionEscrow.holdUntilDate, new Date())
        ))
        .orderBy(desc(partnerCommissionEscrow.createdAt))

      console.log(`Found ${eligibleEscrows.length} escrows eligible for release`)

      for (const escrow of eligibleEscrows) {
        try {
          // Evaluate release conditions
          const releaseConditions = await commissionCalculationEngine.determineReleaseConditions(
            escrow.id,
            'success', // Assuming school payment is successful if in escrow
            escrow.riskScore || 0
          )

          if (releaseConditions.canRelease) {
            const releaseResult = await this.releaseCommissionFromEscrow(
              escrow.id,
              adminUserId || 'system',
              'automatic'
            )
            results.push(releaseResult)
          } else {
            console.log(`Escrow ${escrow.id} not ready for release: ${releaseConditions.holdReason}`)
          }

        } catch (error) {
          console.error(`Error processing escrow ${escrow.id}:`, error)
          results.push({
            success: false,
            escrowId: escrow.id,
            releasedAmount: 0,
            releaseType: 'automatic',
            newPartnerBalance: 0,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      return results

    } catch (error) {
      console.error('Batch release processing error:', error)
      throw new Error(`Failed to process eligible releases: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Release commission from escrow to partner
   */
  async releaseCommissionFromEscrow(
    escrowId: string,
    adminUserId: string,
    releaseType: 'automatic' | 'manual' = 'manual'
  ): Promise<ReleaseResult> {
    return await db.transaction(async (tx) => {
      try {
        // Get escrow record
        const [escrowRecord] = await tx
          .select()
          .from(partnerCommissionEscrow)
          .where(eq(partnerCommissionEscrow.id, escrowId))
          .limit(1)

        if (!escrowRecord) {
          throw new Error(`Escrow record not found: ${escrowId}`)
        }

        if (escrowRecord.escrowStatus !== 'school_paid') {
          throw new Error(`Invalid escrow status: ${escrowRecord.escrowStatus}`)
        }

        const commissionAmount = parseFloat(escrowRecord.commissionAmount)

        // Update escrow status
        await tx
          .update(partnerCommissionEscrow)
          .set({
            escrowStatus: 'released',
            releasedAt: new Date(),
            releasedBy: adminUserId
          })
          .where(eq(partnerCommissionEscrow.id, escrowId))

        // Get current partner balance
        const [balanceResult] = await tx
          .select({
            balance: sql<number>`COALESCE(SUM(CASE WHEN transaction_type = 'EARNING' THEN CAST(amount AS DECIMAL) WHEN transaction_type = 'WITHDRAWAL' THEN -CAST(amount AS DECIMAL) ELSE 0 END), 0)`
          })
          .from(partnerTransactions)
          .where(eq(partnerTransactions.partnerId, escrowRecord.partnerId))

        const currentBalance = balanceResult?.balance || 0
        const newBalance = currentBalance + commissionAmount

        // Create partner transaction
        await tx.insert(partnerTransactions).values({
          partnerId: escrowRecord.partnerId,
          transactionType: 'EARNING',
          amount: commissionAmount.toString(),
          description: `Commission released from escrow - ${escrowRecord.monthYear}`,
          referenceId: escrowRecord.id,
          referenceType: 'commission_escrow',
          balanceBefore: currentBalance.toString(),
          balanceAfter: newBalance.toString(),
          createdBy: adminUserId
        })

        // Create audit record
        await tx.insert(commissionReleaseAudit).values({
          escrowId: escrowRecord.id,
          actionType: 'commission_released',
          triggeredBy: releaseType === 'automatic' ? 'system' : 'admin',
          triggeredByUser: releaseType === 'manual' ? adminUserId : null,
          actionReason: releaseType === 'automatic' ? 'Automatic release conditions met' : 'Manual release approved',
          previousStatus: 'school_paid',
          newStatus: 'released',
          amountAffected: commissionAmount.toString(),
          metadata: {
            releaseType,
            newPartnerBalance: newBalance,
            holdPeriodCompleted: true
          }
        })

        // Initiate fund transfer to partner account (if configured)
        let transferResult = null
        try {
          transferResult = await razorpayRouteService.transferFunds({
            escrowId: escrowRecord.id,
            partnerId: escrowRecord.partnerId,
            amount: commissionAmount,
            currency: 'INR',
            notes: {
              escrow_id: escrowRecord.id,
              month_year: escrowRecord.monthYear,
              release_type: releaseType
            }
          })

          if (transferResult.success) {
            console.log(`💸 Fund transfer initiated: ${transferResult.transferId} for ₹${commissionAmount}`)
          } else {
            console.warn(`⚠️ Fund transfer failed: ${transferResult.error}`)
            // Note: We don't fail the escrow release if transfer fails
            // The commission is still released to partner balance
          }
        } catch (transferError) {
          console.error('Fund transfer error:', transferError)
          // Continue with escrow release even if transfer fails
        }

        console.log(`✅ Commission released from escrow: ${escrowId} - ₹${commissionAmount}`)

        return {
          success: true,
          escrowId: escrowRecord.id,
          releasedAmount: commissionAmount,
          releaseType,
          newPartnerBalance: newBalance,
          transferResult
        }

      } catch (error) {
        console.error('Commission release error:', error)
        throw error
      }
    })
  }

  /**
   * Get escrow status with release conditions
   */
  async getEscrowStatus(escrowId: string): Promise<EscrowStatus | null> {
    try {
      const [escrow] = await db
        .select()
        .from(partnerCommissionEscrow)
        .where(eq(partnerCommissionEscrow.id, escrowId))
        .limit(1)

      if (!escrow) {
        return null
      }

      // Evaluate current release conditions
      const releaseConditions = await commissionCalculationEngine.determineReleaseConditions(
        escrow.id,
        'success', // Assuming payment is successful if in escrow
        escrow.riskScore || 0
      )

      return {
        id: escrow.id,
        status: escrow.escrowStatus as any,
        commissionAmount: parseFloat(escrow.commissionAmount),
        riskScore: escrow.riskScore || 0,
        holdUntilDate: escrow.holdUntilDate ? new Date(escrow.holdUntilDate) : new Date(),
        canRelease: releaseConditions.canRelease,
        releaseConditions
      }

    } catch (error) {
      console.error('Get escrow status error:', error)
      return null
    }
  }

  /**
   * Get escrow details by ID
   */
  async getEscrowDetails(escrowId: string) {
    try {
      const [escrow] = await db
        .select()
        .from(partnerCommissionEscrow)
        .where(eq(partnerCommissionEscrow.id, escrowId))
        .limit(1)

      if (!escrow) {
        throw new Error('Escrow not found')
      }

      // Evaluate current release conditions
      const releaseConditions = await commissionCalculationEngine.determineReleaseConditions(
        escrow.id,
        'success', // Assuming payment is successful if in escrow
        escrow.riskScore || 0
      )

      return {
        id: escrow.id,
        partnerId: escrow.partnerId,
        schoolId: escrow.schoolId,
        monthYear: escrow.monthYear,
        baseAmount: parseFloat(escrow.baseAmount),
        commissionAmount: parseFloat(escrow.commissionAmount),
        operationalExpenses: parseFloat(escrow.operationalExpenses || '0'),
        netCommission: parseFloat(escrow.netCommission),
        status: escrow.escrowStatus as any,
        riskScore: escrow.riskScore || 0,
        holdUntilDate: escrow.holdUntilDate ? new Date(escrow.holdUntilDate) : new Date(),
        autoReleaseEnabled: escrow.autoReleaseEnabled,
        canRelease: releaseConditions.canRelease,
        releaseConditions,
        createdAt: escrow.createdAt,
        releasedAt: escrow.releasedAt,
        releasedBy: escrow.releasedBy
      }

    } catch (error) {
      console.error('Get escrow details error:', error)
      throw new Error(`Failed to get escrow details: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get partner commission summary
   */
  async getPartnerCommissionSummary(partnerId: string) {
    try {
      const [summary] = await db
        .select({
          totalEscrowAmount: sql<number>`COALESCE(SUM(CASE WHEN escrow_status = 'school_paid' THEN CAST(commission_amount AS DECIMAL) ELSE 0 END), 0)`,
          totalReleasedAmount: sql<number>`COALESCE(SUM(CASE WHEN escrow_status = 'released' THEN CAST(commission_amount AS DECIMAL) ELSE 0 END), 0)`,
          pendingReleaseCount: sql<number>`COUNT(CASE WHEN escrow_status = 'school_paid' THEN 1 END)`,
          totalEscrowCount: sql<number>`COUNT(*)`
        })
        .from(partnerCommissionEscrow)
        .where(eq(partnerCommissionEscrow.partnerId, partnerId))

      return summary || {
        totalEscrowAmount: 0,
        totalReleasedAmount: 0,
        pendingReleaseCount: 0,
        totalEscrowCount: 0
      }

    } catch (error) {
      console.error('Partner commission summary error:', error)
      throw new Error(`Failed to get commission summary: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}

// Export singleton instance
export const commissionProcessor = new CommissionProcessor()
