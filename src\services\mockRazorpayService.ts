/**
 * Mock Razorpay Service for Testing
 * This service simulates Razorpay API responses for development/testing
 * when real Razorpay credentials are not available
 */

import crypto from 'crypto'

interface CreateOrderOptions {
  amount: number
  currency: string
  receipt: string
  notes?: Record<string, string>
}

interface MockOrder {
  id: string
  entity: string
  amount: number
  amount_paid: number
  amount_due: number
  currency: string
  receipt: string
  offer_id: string | null
  status: string
  attempts: number
  notes: Record<string, string>
  created_at: number
}

interface MockPayment {
  id: string
  entity: string
  amount: number
  currency: string
  status: string
  order_id: string
  invoice_id: string | null
  international: boolean
  method: string
  amount_refunded: number
  refund_status: string | null
  captured: boolean
  description: string
  card_id: string | null
  bank: string | null
  wallet: string | null
  vpa: string | null
  email: string
  contact: string
  notes: Record<string, string>
  fee: number
  tax: number
  error_code: string | null
  error_description: string | null
  error_source: string | null
  error_step: string | null
  error_reason: string | null
  acquirer_data: Record<string, any>
  created_at: number
}

class MockRazorpayService {
  private keySecret: string

  constructor(keySecret: string = 'mock_key_secret') {
    this.keySecret = keySecret
  }

  /**
   * Create a mock payment order
   */
  async createOrder(options: CreateOrderOptions): Promise<{
    success: boolean
    order?: MockOrder
    error?: string
  }> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 100))

      const orderId = `order_${crypto.randomBytes(8).toString('hex')}`
      const currentTime = Math.floor(Date.now() / 1000)

      const mockOrder: MockOrder = {
        id: orderId,
        entity: 'order',
        amount: options.amount,
        amount_paid: 0,
        amount_due: options.amount,
        currency: options.currency,
        receipt: options.receipt,
        offer_id: null,
        status: 'created',
        attempts: 0,
        notes: options.notes || {},
        created_at: currentTime
      }

      console.log(`🎭 Mock Razorpay: Created order ${orderId} for ₹${options.amount / 100}`)

      return {
        success: true,
        order: mockOrder
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Verify payment signature (mock implementation)
   */
  verifyPaymentSignature(options: {
    razorpayOrderId: string
    razorpayPaymentId: string
    razorpaySignature: string
  }): boolean {
    try {
      // For mock service, we'll generate the expected signature
      const body = options.razorpayOrderId + '|' + options.razorpayPaymentId
      const expectedSignature = crypto
        .createHmac('sha256', this.keySecret)
        .update(body.toString())
        .digest('hex')

      console.log(`🎭 Mock Razorpay: Verifying signature for payment ${options.razorpayPaymentId}`)
      
      // For testing, we'll accept any signature that follows the pattern
      return options.razorpaySignature.length > 10
    } catch (error) {
      console.error('Mock payment signature verification error:', error)
      return false
    }
  }

  /**
   * Get mock payment details
   */
  async getPayment(paymentId: string): Promise<{
    success: boolean
    payment?: MockPayment
    error?: string
  }> {
    try {
      await new Promise(resolve => setTimeout(resolve, 50))

      const mockPayment: MockPayment = {
        id: paymentId,
        entity: 'payment',
        amount: 100000, // ₹1000 in paise
        currency: 'INR',
        status: 'captured',
        order_id: `order_${crypto.randomBytes(8).toString('hex')}`,
        invoice_id: null,
        international: false,
        method: 'card',
        amount_refunded: 0,
        refund_status: null,
        captured: true,
        description: 'Mock payment for testing',
        card_id: null,
        bank: null,
        wallet: null,
        vpa: null,
        email: '<EMAIL>',
        contact: '+************',
        notes: {},
        fee: 2360, // 2.36% fee
        tax: 424, // 18% GST on fee
        error_code: null,
        error_description: null,
        error_source: null,
        error_step: null,
        error_reason: null,
        acquirer_data: {},
        created_at: Math.floor(Date.now() / 1000)
      }

      console.log(`🎭 Mock Razorpay: Retrieved payment ${paymentId}`)

      return {
        success: true,
        payment: mockPayment
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create mock customer
   */
  async createCustomer(options: {
    name: string
    email: string
    contact?: string
    notes?: Record<string, string>
  }): Promise<{
    success: boolean
    customer?: any
    error?: string
  }> {
    try {
      await new Promise(resolve => setTimeout(resolve, 100))

      const customerId = `cust_${crypto.randomBytes(8).toString('hex')}`

      const mockCustomer = {
        id: customerId,
        entity: 'customer',
        name: options.name,
        email: options.email,
        contact: options.contact || '',
        gstin: null,
        notes: options.notes || {},
        created_at: Math.floor(Date.now() / 1000)
      }

      console.log(`🎭 Mock Razorpay: Created customer ${customerId}`)

      return {
        success: true,
        customer: mockCustomer
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create mock subscription auth (for automatic billing setup)
   */
  async createSubscriptionAuth(options: {
    subscriptionId: string
    customerId: string
    amount: number
    currency: string
    description: string
    notes?: Record<string, string>
  }): Promise<{
    success: boolean
    authTransaction?: any
    error?: string
  }> {
    try {
      await new Promise(resolve => setTimeout(resolve, 150))

      const authId = `auth_${crypto.randomBytes(8).toString('hex')}`

      const mockAuthTransaction = {
        id: authId,
        entity: 'payment',
        amount: options.amount,
        currency: options.currency,
        status: 'authorized',
        method: 'card',
        description: options.description,
        customer_id: options.customerId,
        subscription_id: options.subscriptionId,
        notes: options.notes || {},
        created_at: Math.floor(Date.now() / 1000)
      }

      console.log(`🎭 Mock Razorpay: Created subscription auth ${authId} for ₹${options.amount / 100}`)

      return {
        success: true,
        authTransaction: mockAuthTransaction
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Mock fetch payment details
   */
  async fetchPaymentDetails(paymentId: string): Promise<any> {
    try {
      console.log('🎭 [Mock] Fetching payment details for:', paymentId)

      const mockPayment = {
        id: paymentId,
        entity: 'payment',
        amount: 500000, // ₹5000 in paise
        currency: 'INR',
        status: 'captured',
        order_id: `order_mock_${Date.now()}`,
        method: 'card',
        amount_refunded: 0,
        refund_status: null,
        captured: true,
        description: 'Mock subscription payment',
        card_id: null,
        bank: null,
        wallet: null,
        vpa: null,
        email: '<EMAIL>',
        contact: '+************',
        created_at: Math.floor(Date.now() / 1000),
        acquirer_data: {
          bank_transaction_id: `mock_txn_${Date.now()}`
        }
      }

      return {
        success: true,
        payment: mockPayment
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Mock fetch order details
   */
  async fetchOrderDetails(orderId: string): Promise<any> {
    try {
      console.log('🎭 [Mock] Fetching order details for:', orderId)

      const mockOrder = {
        id: orderId,
        entity: 'order',
        amount: 500000, // ₹5000 in paise
        amount_paid: 500000,
        amount_due: 0,
        currency: 'INR',
        receipt: `receipt_${Date.now()}`,
        status: 'paid',
        attempts: 1,
        created_at: Math.floor(Date.now() / 1000)
      }

      return {
        success: true,
        order: mockOrder
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Mock webhook signature verification (always returns true for testing)
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    console.log('🎭 [Mock] Webhook signature verification (always returns true for testing)')
    return true
  }
}

export { MockRazorpayService }
export type { CreateOrderOptions, MockOrder, MockPayment }
