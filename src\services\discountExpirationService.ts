import { db } from '@/src/db'
import { billingSubscriptions, clients } from '@/src/db/schema'
import { eq, and, lte, sql } from 'drizzle-orm'
import { discountManagementService } from './discountManagementService'

export interface ExpiringDiscount {
  subscriptionId: string
  clientId: string
  schoolName: string
  discountPercentage: number
  discountEndDate: Date
  originalAmount: number
  discountedAmount: number
  monthlySavings: number
}

export interface ExpirationResult {
  totalChecked: number
  totalExpired: number
  totalErrors: number
  expiredSubscriptions: string[]
  errors: string[]
}

class DiscountExpirationService {
  /**
   * Main method to check and expire discounts - called by billing scheduler
   */
  async checkAndExpireDiscounts(): Promise<ExpirationResult> {
    try {
      console.log('🔄 [Discount Expiration] Starting daily discount expiration check...')

      const today = new Date().toISOString().split('T')[0]
      
      // Find all subscriptions with expired discounts
      const expiredDiscounts = await this.getExpiredDiscounts(today)
      
      console.log(`📊 [Discount Expiration] Found ${expiredDiscounts.length} expired discounts to process`)

      const result: ExpirationResult = {
        totalChecked: expiredDiscounts.length,
        totalExpired: 0,
        totalErrors: 0,
        expiredSubscriptions: [],
        errors: []
      }

      // Process each expired discount
      for (const discount of expiredDiscounts) {
        try {
          await this.expireSubscriptionDiscount(discount)
          result.totalExpired++
          result.expiredSubscriptions.push(discount.subscriptionId)
          
          console.log(`✅ [Discount Expired] ${discount.schoolName}: ${discount.discountPercentage}% discount expired, reverted to ₹${discount.originalAmount}`)
          
        } catch (error) {
          const errorMsg = `Failed to expire discount for ${discount.schoolName} (${discount.subscriptionId}): ${error instanceof Error ? error.message : 'Unknown error'}`
          console.error(`❌ [Discount Expiration Error] ${errorMsg}`)
          result.errors.push(errorMsg)
          result.totalErrors++
        }
      }

      // Send notifications if there were expirations or errors
      if (result.totalExpired > 0 || result.totalErrors > 0) {
        await this.sendExpirationNotifications(result)
      }

      console.log(`🎯 [Discount Expiration Complete] Processed: ${result.totalChecked}, Expired: ${result.totalExpired}, Errors: ${result.totalErrors}`)

      return result

    } catch (error) {
      console.error('❌ [Discount Expiration Service Error]', error)
      throw error
    }
  }

  /**
   * Get all subscriptions with expired discounts
   */
  private async getExpiredDiscounts(today: string): Promise<ExpiringDiscount[]> {
    try {
      const expiredDiscounts = await db.select({
        subscriptionId: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        schoolName: clients.schoolName,
        discountPercentage: billingSubscriptions.currentDiscountPercentage,
        discountEndDate: billingSubscriptions.discountEndDate,
        originalAmount: billingSubscriptions.originalMonthlyAmount,
        discountedAmount: billingSubscriptions.monthlyAmount
      })
      .from(billingSubscriptions)
      .innerJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(and(
        eq(billingSubscriptions.hasActiveDiscount, true),
        lte(billingSubscriptions.discountEndDate, today)
      ))

      return expiredDiscounts
        .filter(discount =>
          discount.clientId !== null &&
          discount.originalAmount !== null &&
          parseFloat(discount.originalAmount) > 0
        ) // Filter out invalid discounts
        .map(discount => ({
          subscriptionId: discount.subscriptionId,
          clientId: discount.clientId!,
          schoolName: discount.schoolName,
          discountPercentage: parseFloat(discount.discountPercentage || '0'),
          discountEndDate: new Date(discount.discountEndDate!),
          originalAmount: parseFloat(discount.originalAmount!),
          discountedAmount: parseFloat(discount.discountedAmount),
          monthlySavings: parseFloat(discount.originalAmount!) - parseFloat(discount.discountedAmount)
        }))

    } catch (error) {
      console.error('Error fetching expired discounts:', error)
      throw error
    }
  }

  /**
   * Expire a specific subscription discount with comprehensive error handling
   */
  private async expireSubscriptionDiscount(discount: ExpiringDiscount): Promise<void> {
    try {
      // Validate discount data before processing
      if (!discount.subscriptionId) {
        throw new Error('Invalid discount data: missing subscription ID')
      }

      if (!discount.originalAmount || discount.originalAmount <= 0) {
        throw new Error('Invalid discount data: missing or invalid original amount')
      }

      // Use the enhanced discount management service to expire the discount
      await discountManagementService.expireDiscount(discount.subscriptionId)

      // Log the expiration for audit trail
      await this.logDiscountExpiration(discount)

      console.log(`✅ [Discount Expiration] Successfully expired discount for ${discount.schoolName}`)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error(`❌ [Discount Expiration Error] Failed to expire discount for subscription ${discount.subscriptionId}: ${errorMessage}`)

      // Log the error for audit purposes
      await this.logDiscountExpirationError(discount, errorMessage)

      throw new Error(`Failed to expire discount for ${discount.schoolName}: ${errorMessage}`)
    }
  }

  /**
   * Log discount expiration errors for audit trail
   */
  private async logDiscountExpirationError(discount: ExpiringDiscount, errorMessage: string): Promise<void> {
    try {
      console.log(`📝 [Audit Log] Discount Expiration Error: {
        subscriptionId: "${discount.subscriptionId}",
        schoolName: "${discount.schoolName}",
        discountPercentage: ${discount.discountPercentage}%,
        originalAmount: ₹${discount.originalAmount},
        discountedAmount: ₹${discount.discountedAmount},
        errorMessage: "${errorMessage}",
        failedAt: "${new Date().toISOString()}",
        action: "AUTOMATIC_EXPIRATION_FAILED"
      }`)

      // TODO: Implement proper error audit logging to database table
      // await db.insert(auditLogs).values({
      //   action: 'DISCOUNT_EXPIRATION_FAILED',
      //   entityType: 'subscription',
      //   entityId: discount.subscriptionId,
      //   details: JSON.stringify({ discount, errorMessage }),
      //   performedBy: 'SYSTEM',
      //   performedAt: new Date(),
      //   success: false
      // })

    } catch (logError) {
      console.error('Error logging discount expiration failure:', logError)
      // Don't throw here - logging failure shouldn't stop the process
    }
  }

  /**
   * Log discount expiration for audit trail
   */
  private async logDiscountExpiration(discount: ExpiringDiscount): Promise<void> {
    try {
      // This would typically log to an audit table or external logging service
      console.log(`📝 [Audit Log] Discount Expiration: {
        subscriptionId: "${discount.subscriptionId}",
        schoolName: "${discount.schoolName}",
        discountPercentage: ${discount.discountPercentage}%,
        originalAmount: ₹${discount.originalAmount},
        discountedAmount: ₹${discount.discountedAmount},
        monthlySavings: ₹${discount.monthlySavings},
        expiredOn: "${new Date().toISOString()}",
        action: "AUTOMATIC_EXPIRATION"
      }`)

      // TODO: Implement proper audit logging to database table
      // await db.insert(auditLogs).values({
      //   action: 'DISCOUNT_EXPIRED',
      //   entityType: 'subscription',
      //   entityId: discount.subscriptionId,
      //   details: JSON.stringify(discount),
      //   performedBy: 'SYSTEM',
      //   performedAt: new Date()
      // })

    } catch (error) {
      console.error('Error logging discount expiration:', error)
      // Don't throw here - logging failure shouldn't stop the expiration process
    }
  }

  /**
   * Send notifications about discount expirations
   */
  private async sendExpirationNotifications(result: ExpirationResult): Promise<void> {
    try {
      // TODO: Implement email notifications
      console.log(`📧 [Notification] Discount Expiration Summary:
        - Total Processed: ${result.totalChecked}
        - Successfully Expired: ${result.totalExpired}
        - Errors: ${result.totalErrors}
        - Expired Subscriptions: ${result.expiredSubscriptions.join(', ')}
        ${result.errors.length > 0 ? `- Error Details: ${result.errors.join('; ')}` : ''}
      `)

      // TODO: Send email to admin team
      // await emailService.sendDiscountExpirationSummary({
      //   to: '<EMAIL>',
      //   subject: `Discount Expiration Summary - ${new Date().toDateString()}`,
      //   data: result
      // })

    } catch (error) {
      console.error('Error sending expiration notifications:', error)
      // Don't throw here - notification failure shouldn't stop the process
    }
  }

  /**
   * Get discounts expiring in the next N days
   */
  async getExpiringDiscounts(daysAhead: number = 7): Promise<ExpiringDiscount[]> {
    try {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + daysAhead)
      const futureDateStr = futureDate.toISOString().split('T')[0]

      const today = new Date().toISOString().split('T')[0]

      const expiringDiscounts = await db.select({
        subscriptionId: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        schoolName: clients.schoolName,
        discountPercentage: billingSubscriptions.currentDiscountPercentage,
        discountEndDate: billingSubscriptions.discountEndDate,
        originalAmount: billingSubscriptions.originalMonthlyAmount,
        discountedAmount: billingSubscriptions.monthlyAmount
      })
      .from(billingSubscriptions)
      .innerJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(and(
        eq(billingSubscriptions.hasActiveDiscount, true),
        lte(billingSubscriptions.discountEndDate, futureDateStr),
        sql`${billingSubscriptions.discountEndDate} > ${today}`
      ))

      return expiringDiscounts
        .filter(discount => discount.clientId !== null) // Filter out null clientIds
        .map(discount => ({
          subscriptionId: discount.subscriptionId,
          clientId: discount.clientId!,
          schoolName: discount.schoolName,
          discountPercentage: parseFloat(discount.discountPercentage || '0'),
          discountEndDate: new Date(discount.discountEndDate!),
          originalAmount: parseFloat(discount.originalAmount || '0'),
          discountedAmount: parseFloat(discount.discountedAmount),
          monthlySavings: parseFloat(discount.originalAmount || '0') - parseFloat(discount.discountedAmount)
        }))

    } catch (error) {
      console.error('Error fetching expiring discounts:', error)
      throw error
    }
  }

  /**
   * Get discount statistics for admin dashboard
   */
  async getDiscountStatistics(): Promise<{
    totalActiveDiscounts: number
    totalMonthlySavings: number
    averageDiscountPercentage: number
    expiringThisWeek: number
    expiringThisMonth: number
  }> {
    try {
      const [stats] = await db.select({
        totalActiveDiscounts: sql<number>`COUNT(*)`,
        totalMonthlySavings: sql<number>`SUM(CAST(${billingSubscriptions.originalMonthlyAmount} AS DECIMAL) - CAST(${billingSubscriptions.monthlyAmount} AS DECIMAL))`,
        averageDiscountPercentage: sql<number>`AVG(CAST(${billingSubscriptions.currentDiscountPercentage} AS DECIMAL))`
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.hasActiveDiscount, true))

      const expiringThisWeek = await this.getExpiringDiscounts(7)
      const expiringThisMonth = await this.getExpiringDiscounts(30)

      return {
        totalActiveDiscounts: stats.totalActiveDiscounts || 0,
        totalMonthlySavings: stats.totalMonthlySavings || 0,
        averageDiscountPercentage: stats.averageDiscountPercentage || 0,
        expiringThisWeek: expiringThisWeek.length,
        expiringThisMonth: expiringThisMonth.length
      }

    } catch (error) {
      console.error('Error getting discount statistics:', error)
      return {
        totalActiveDiscounts: 0,
        totalMonthlySavings: 0,
        averageDiscountPercentage: 0,
        expiringThisWeek: 0,
        expiringThisMonth: 0
      }
    }
  }
}

export const discountExpirationService = new DiscountExpirationService()
