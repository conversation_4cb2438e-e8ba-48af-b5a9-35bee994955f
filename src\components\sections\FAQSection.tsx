'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  HelpCircle, 
  ChevronDown, 
  ChevronUp, 
  Clock, 
  Database, 
  GraduationCap, 
  Shield,
  MessageCircle,
  ArrowRight
} from 'lucide-react'

const FAQSection = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(0)

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const faqs = [
    {
      icon: Clock,
      question: "How long does implementation take?",
      answer: "Complete implementation takes just 14 days. Day 1-3: Setup & configuration, Day 4-7: Data migration, Day 8-10: Staff training, Day 11-14: Go live with full support. We guarantee your school will be operational within 2 weeks or we work for free until completion.",
      category: "Implementation"
    },
    {
      icon: Database,
      question: "What happens to our existing data?",
      answer: "We provide 100% secure data migration at no extra cost. Our team handles the complete transfer of student records, fee data, academic history, and all other information. We maintain full backups and offer rollback options. Zero data loss guaranteed with bank-level security throughout the process.",
      category: "Data Security"
    },
    {
      icon: GraduationCap,
      question: "Do you provide training for our staff?",
      answer: "Yes! Comprehensive training is included for all staff levels - administrators, teachers, accountants, and support staff. We provide role-based training modules, video tutorials library, live training sessions, and ongoing support. 95% staff adoption rate guaranteed within 30 days.",
      category: "Training"
    },
    {
      icon: Shield,
      question: "How secure is our school data?",
      answer: "Schopio uses bank-level encryption and is ISO 27001 certified. We're GDPR compliant with regular security audits. Your data is stored in secure Indian data centers with 99.9% uptime guarantee. We never share or sell your data - complete privacy protection.",
      category: "Security"
    },
    {
      icon: MessageCircle,
      question: "What support do you provide after implementation?",
      answer: "24/7 dedicated support team with priority response times. You get a dedicated account manager, multi-language support, regular system updates, and ongoing training. Average response time is under 2 hours for any issues or questions.",
      category: "Support"
    },
    {
      icon: Database,
      question: "Can we integrate with our existing systems?",
      answer: "Yes, Schopio integrates with most existing systems including accounting software, library management, transport systems, and payment gateways. Our API allows custom integrations, and our team handles all technical setup at no extra cost.",
      category: "Integration"
    },
    {
      icon: Clock,
      question: "What if we're not satisfied with the system?",
      answer: "We offer a 30-day satisfaction guarantee. If you&apos;re not completely satisfied, we&apos;ll work to resolve any issues or provide a full refund. Our commitment is to ensure you achieve measurable improvements in your school operations.",
      category: "Guarantee"
    },
    {
      icon: GraduationCap,
      question: "What&apos;s included in the complete platform?",
      answer: "Everything you need for comprehensive school management: all 8+ modules, AI-powered analytics, web-based platform, training, support, updates, and data migration. No feature restrictions or module limitations - complete functionality from day one. Biometric integration available on demand.",
      category: "Features"
    }
  ]

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index)
  }

  const categories = [...new Set(faqs.map(faq => faq.category))]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <HelpCircle className="w-4 h-4" />
            Frequently Asked Questions
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Got Questions? 
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> We Have Answers</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Everything you need to know about implementing Schopio in your school. Can&apos;t find what you&apos;re looking for? Contact our team.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          {/* FAQ Items */}
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="space-y-4"
          >
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className={`border-2 transition-all duration-300 cursor-pointer ${
                  openFAQ === index 
                    ? 'border-blue-500 shadow-lg bg-blue-50' 
                    : 'border-slate-200 hover:border-blue-300 hover:shadow-md'
                }`}>
                  <CardContent padding="none">
                    <button
                      onClick={() => toggleFAQ(index)}
                      className="w-full p-6 text-left flex items-center justify-between group"
                    >
                      <div className="flex items-center gap-4">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                          openFAQ === index 
                            ? 'bg-blue-600 text-white' 
                            : 'bg-slate-100 text-slate-600 group-hover:bg-blue-100 group-hover:text-blue-600'
                        }`}>
                          <faq.icon className="w-5 h-5" />
                        </div>
                        <div>
                          <h3 className={`text-lg font-bold transition-colors ${
                            openFAQ === index ? 'text-blue-900' : 'text-slate-900'
                          }`}>
                            {faq.question}
                          </h3>
                          <div className="text-sm text-blue-600 font-medium mt-1">
                            {faq.category}
                          </div>
                        </div>
                      </div>
                      <div className={`transition-transform duration-300 ${
                        openFAQ === index ? 'rotate-180' : ''
                      }`}>
                        <ChevronDown className="w-5 h-5 text-slate-400" />
                      </div>
                    </button>
                    
                    <AnimatePresence>
                      {openFAQ === index && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 pt-0">
                            <div className="pl-14">
                              <p className="text-slate-600 leading-relaxed">
                                {faq.answer}
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Still Have Questions CTA */}
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
            className="mt-16 text-center"
          >
            <Card className="bg-gradient-to-r from-blue-600 to-emerald-600 border-0">
              <CardContent padding="xl">
                <div className="text-white">
                  <h3 className="text-2xl font-bold mb-4">Still Have Questions?</h3>
                  <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
                    Our education specialists are here to help. Get personalized answers about implementing Schopio in your school.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button
                      size="lg"
                      icon={MessageCircle}
                      iconPosition="right"
                      className="bg-white text-blue-600 hover:bg-blue-50 font-bold px-8 py-4"
                    >
                      Chat with Education Specialist
                    </Button>
                    <Button
                      variant="outline"
                      size="lg"
                      icon={ArrowRight}
                      iconPosition="right"
                      className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4"
                    >
                      Schedule 15-Min Call
                    </Button>
                  </div>
                  <div className="mt-6 text-sm text-blue-100">
                    💬 Average response time: Under 2 hours • 📞 Free consultation calls available
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default FAQSection
