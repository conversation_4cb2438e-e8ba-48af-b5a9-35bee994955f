# Security & Authentication Strategy

## 🔐 Security Architecture Overview

Multi-layered security approach ensuring data protection, access control, and compliance for our SaaS platform.

## 🎯 Authentication Flows

### 1. Admin Dashboard Authentication
```typescript
interface AdminAuthFlow {
  email: string;
  password: string;
  mfaCode?: string;
}

const adminLogin = async (credentials: AdminAuth<PERSON>low) => {
  // 1. Validate credentials
  const admin = await validateAdminCredentials(credentials.email, credentials.password);
  
  // 2. Check MFA if enabled
  if (admin.mfaEnabled && !credentials.mfaCode) {
    return { requiresMFA: true, tempToken: generateTempToken(admin.id) };
  }
  
  // 3. Generate JWT with admin permissions
  const token = generateJWT({
    userId: admin.id,
    email: admin.email,
    role: admin.role,
    permissions: admin.permissions,
    type: 'admin'
  });
  
  // 4. Log authentication event
  await logAuthEvent('admin_login', admin.id);
  
  return { token, user: admin };
};
```

### 2. School Portal Authentication
```typescript
interface SchoolAuthFlow {
  schoolCode: string;
  email: string;
  password: string;
}

const schoolLogin = async (credentials: SchoolAuthFlow) => {
  // 1. Validate school exists and is active
  const client = await getClientBySchoolCode(credentials.schoolCode);
  if (!client || client.status !== 'active') {
    throw new Error('School not found or inactive');
  }
  
  // 2. Validate user credentials
  const user = await validateClientUser(credentials.email, credentials.password, client.id);
  
  // 3. Check subscription status
  const subscription = await getActiveSubscription(client.id);
  if (!subscription || subscription.status !== 'active') {
    throw new Error('Subscription inactive');
  }
  
  // 4. Generate JWT with client context
  const token = generateJWT({
    userId: user.id,
    clientId: client.id,
    email: user.email,
    role: user.role,
    schoolCode: client.schoolCode,
    type: 'client'
  });
  
  return { token, user, client, subscription };
};
```

## 🛡️ Role-Based Access Control (RBAC)

### Admin Roles & Permissions
```typescript
interface AdminRole {
  role: string;
  permissions: string[];
  description: string;
}

const adminRoles: AdminRole[] = [
  {
    role: 'super_admin',
    permissions: ['*'], // All permissions
    description: 'Full system access'
  },
  {
    role: 'sales',
    permissions: [
      'leads:read', 'leads:write', 'leads:convert',
      'clients:read', 'clients:write',
      'demos:read', 'demos:write'
    ],
    description: 'Sales team access'
  },
  {
    role: 'support',
    permissions: [
      'clients:read', 'tickets:read', 'tickets:write',
      'billing:read', 'subscriptions:read'
    ],
    description: 'Customer support access'
  },
  {
    role: 'billing',
    permissions: [
      'billing:read', 'billing:write', 'invoices:read', 'invoices:write',
      'payments:read', 'subscriptions:read', 'subscriptions:write'
    ],
    description: 'Billing and finance access'
  }
];
```

### Client Roles & Permissions
```typescript
const clientRoles: AdminRole[] = [
  {
    role: 'client_admin',
    permissions: [
      'subscription:read', 'subscription:write',
      'billing:read', 'payments:read', 'payments:write',
      'support:read', 'support:write',
      'users:read', 'users:write'
    ],
    description: 'Full school portal access'
  },
  {
    role: 'client_billing',
    permissions: [
      'subscription:read', 'billing:read',
      'payments:read', 'payments:write'
    ],
    description: 'Billing and payment access only'
  },
  {
    role: 'client_viewer',
    permissions: [
      'subscription:read', 'billing:read', 'support:read'
    ],
    description: 'Read-only access'
  }
];
```

### Permission Middleware
```typescript
const requirePermission = (permission: string) => {
  return async (c: Context, next: Next) => {
    const user = c.get('user');
    
    // Super admin has all permissions
    if (user.permissions?.includes('*')) {
      await next();
      return;
    }
    
    // Check specific permission
    if (!user.permissions?.includes(permission)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }
    
    await next();
  };
};

// Usage in routes
app.get('/admin/clients', 
  authMiddleware,
  requirePermission('clients:read'),
  getClients
);
```

## 🏢 Multi-Tenant Data Isolation

### Database-Level Isolation
```typescript
// Row Level Security (RLS) policies
const enableRLS = async () => {
  await db.execute(`
    -- Enable RLS on client-specific tables
    ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
    ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
    ALTER TABLE billing_cycles ENABLE ROW LEVEL SECURITY;
    ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
    ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
    ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;
    
    -- Create policies for client isolation
    CREATE POLICY client_isolation ON clients
      FOR ALL TO authenticated
      USING (id = current_setting('app.current_client_id')::UUID);
      
    CREATE POLICY subscription_isolation ON subscriptions
      FOR ALL TO authenticated
      USING (client_id = current_setting('app.current_client_id')::UUID);
  `);
};
```

### Application-Level Isolation
```typescript
const tenantMiddleware = async (c: Context, next: Next) => {
  const user = c.get('user');
  
  if (user.type === 'client') {
    // Set client context for database queries
    c.set('clientId', user.clientId);
    
    // Set database session variable for RLS
    await db.execute(`SET app.current_client_id = '${user.clientId}'`);
  }
  
  await next();
};

// Tenant-aware database queries
const getClientInvoices = async (clientId: string) => {
  // This query will automatically be filtered by RLS
  return await db.select().from(invoices).where(eq(invoices.clientId, clientId));
};
```

## 🔒 Data Encryption & Security

### Password Security
```typescript
import bcrypt from 'bcrypt';
import crypto from 'crypto';

const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  return await bcrypt.compare(password, hash);
};

// Password strength validation
const validatePasswordStrength = (password: string): boolean => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
};
```

### JWT Security
```typescript
import jwt from 'jsonwebtoken';

interface JWTPayload {
  userId: string;
  clientId?: string;
  email: string;
  role: string;
  type: 'admin' | 'client';
  permissions?: string[];
}

const generateJWT = (payload: JWTPayload): string => {
  return jwt.sign(payload, process.env.JWT_SECRET!, {
    expiresIn: '24h',
    issuer: 'school-management-saas',
    audience: payload.type
  });
};

const verifyJWT = (token: string): JWTPayload => {
  return jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
};

// Refresh token mechanism
const generateRefreshToken = (userId: string): string => {
  return jwt.sign({ userId }, process.env.REFRESH_TOKEN_SECRET!, {
    expiresIn: '7d'
  });
};
```

### API Security Headers
```typescript
const securityHeaders = async (c: Context, next: Next) => {
  // Security headers
  c.header('X-Content-Type-Options', 'nosniff');
  c.header('X-Frame-Options', 'DENY');
  c.header('X-XSS-Protection', '1; mode=block');
  c.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  c.header('Content-Security-Policy', "default-src 'self'");
  c.header('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  await next();
};
```

## 🚨 Security Monitoring & Logging

### Authentication Event Logging
```typescript
interface AuthEvent {
  type: 'login' | 'logout' | 'failed_login' | 'password_change';
  userId: string;
  clientId?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  failureReason?: string;
}

const logAuthEvent = async (event: AuthEvent) => {
  await db.insert(authLogs).values(event);
  
  // Alert on suspicious activity
  if (event.type === 'failed_login') {
    await checkForBruteForce(event.userId, event.ipAddress);
  }
};

const checkForBruteForce = async (userId: string, ipAddress: string) => {
  const recentFailures = await getRecentFailedLogins(userId, ipAddress);
  
  if (recentFailures.length >= 5) {
    // Lock account temporarily
    await lockAccount(userId, '15 minutes');
    
    // Send security alert
    await sendSecurityAlert(userId, 'Multiple failed login attempts');
  }
};
```

### Rate Limiting
```typescript
const rateLimiter = async (c: Context, next: Next) => {
  const clientIP = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
  const key = `rate_limit:${clientIP}`;
  
  const current = await redis.get(key);
  const requests = current ? parseInt(current) : 0;
  
  if (requests >= 100) { // 100 requests per minute
    return c.json({ error: 'Rate limit exceeded' }, 429);
  }
  
  await redis.setex(key, 60, requests + 1);
  await next();
};
```

## 🔐 Payment Security (PCI Compliance)

### Razorpay Security Integration
```typescript
const securePaymentProcessing = {
  // Never store card details
  cardDataHandling: 'PCI_COMPLIANT_GATEWAY_ONLY',
  
  // Webhook signature verification
  verifyWebhookSignature: (payload: string, signature: string): boolean => {
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET!)
      .update(payload)
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  },
  
  // Secure order creation
  createSecureOrder: async (amount: number, clientId: string) => {
    const order = await razorpay.orders.create({
      amount: amount * 100, // Convert to paise
      currency: 'INR',
      receipt: `order_${Date.now()}`,
      notes: {
        clientId: clientId,
        timestamp: new Date().toISOString()
      }
    });
    
    // Log order creation
    await logPaymentEvent('order_created', clientId, order.id);
    
    return order;
  }
};
```

This comprehensive security strategy ensures robust protection across all system components while maintaining usability and compliance requirements.
