'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { CheckCircle, CreditCard, Shield, Clock, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
// import { toast } from 'sonner' // Temporarily commented out

interface SubscriptionDetails {
  id: string
  planName: string
  studentCount: number
  pricePerStudent: number
  monthlyAmount: number
  billingCycle: 'monthly' | 'yearly'
  status: string
  authRequired: boolean
}

interface AuthOrderDetails {
  authOrderId: string
  amount: number
  currency: string
  subscriptionId: string
}

declare global {
  interface Window {
    Razorpay: any
  }
}

function SubscriptionCheckoutContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const subscriptionId = searchParams.get('subscription_id')

  const [subscription, setSubscription] = useState<SubscriptionDetails | null>(null)
  const [authOrder, setAuthOrder] = useState<AuthOrderDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!subscriptionId) {
      setError('Subscription ID is required')
      setLoading(false)
      return
    }

    fetchSubscriptionDetails()
  }, [subscriptionId])

  const fetchSubscriptionDetails = async () => {
    try {
      const response = await fetch(`/api/subscriptions/${subscriptionId}/auth-status`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch subscription details')
      }

      setSubscription(data.subscription)

      // If authentication is required, create auth order
      if (data.authRequired) {
        await createAuthOrder()
      }

    } catch (error) {
      console.error('Failed to fetch subscription details:', error)
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const createAuthOrder = async () => {
    try {
      const response = await fetch('/api/subscriptions/create-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscriptionId,
          amount: 100, // ₹1 authentication amount in paise
          currency: 'INR',
          description: 'Subscription Authentication'
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create authentication order')
      }

      setAuthOrder(data)

    } catch (error) {
      console.error('Failed to create auth order:', error)
      setError(error instanceof Error ? error.message : 'Failed to create authentication order')
    }
  }

  const handleSubscriptionAuth = async () => {
    if (!authOrder || !subscription) {
      alert('Authentication order not ready')
      return
    }

    setProcessing(true)

    try {
      // Load Razorpay script if not already loaded
      if (!window.Razorpay) {
        const script = document.createElement('script')
        script.src = 'https://checkout.razorpay.com/v1/checkout.js'
        script.async = true
        document.body.appendChild(script)
        
        await new Promise((resolve) => {
          script.onload = resolve
        })
      }

      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: authOrder.amount,
        currency: authOrder.currency,
        name: 'Schopio',
        description: 'Subscription Authentication - Authorize automatic billing',
        order_id: authOrder.authOrderId,
        handler: async (response: any) => {
          try {
            // Verify payment and activate subscription
            const verifyResponse = await fetch('/api/subscriptions/verify-auth', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature
              })
            })

            const verifyData = await verifyResponse.json()

            if (verifyResponse.ok) {
              // toast.success('Subscription activated successfully!')
              alert('Subscription activated successfully!')
              router.push('/profile/subscription?activated=true')
            } else {
              throw new Error(verifyData.error || 'Payment verification failed')
            }

          } catch (error) {
            console.error('Payment verification failed:', error)
            alert('Payment verification failed. Please contact support.')
          }
        },
        prefill: {
          name: subscription.planName,
          email: '', // Will be filled from client data
        },
        notes: {
          subscription_id: subscriptionId,
          auth_type: 'subscription_activation'
        },
        theme: {
          color: '#3B82F6'
        },
        modal: {
          ondismiss: () => {
            setProcessing(false)
            alert('Payment cancelled')
          }
        }
      }

      const razorpay = new window.Razorpay(options)
      razorpay.open()

    } catch (error) {
      console.error('Failed to initialize payment:', error)
      alert('Failed to initialize payment')
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  if (!subscription) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>Subscription not found</AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Activate Your Subscription</h1>
          <p className="text-gray-600 mt-2">
            Complete the authentication to enable automatic billing
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-600" />
              Subscription Details
            </CardTitle>
            <CardDescription>
              Review your subscription before activation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Plan</p>
                <p className="font-semibold">{subscription.planName}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Status</p>
                <Badge variant={subscription.status === 'active' ? 'default' : 'secondary'}>
                  {subscription.status}
                </Badge>
              </div>
              <div>
                <p className="text-sm text-gray-600">Student Count</p>
                <p className="font-semibold">{subscription.studentCount} students</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Billing Cycle</p>
                <p className="font-semibold capitalize">{subscription.billingCycle}</p>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Price per student</span>
                <span>₹{subscription.pricePerStudent}/month</span>
              </div>
              <div className="flex justify-between font-semibold">
                <span>Total Monthly Amount</span>
                <span>₹{subscription.monthlyAmount}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {subscription.authRequired && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-green-600" />
                Authentication Required
              </CardTitle>
              <CardDescription>
                Authorize automatic billing with a ₹1 authentication charge
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  This is a one-time ₹1 authentication charge to verify your payment method. 
                  After successful authentication, Razorpay will automatically charge your 
                  account monthly without requiring manual payments.
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Secure payment processing by Razorpay</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <span>Automatic monthly billing - no manual payments needed</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Shield className="h-4 w-4 text-purple-500" />
                  <span>Cancel anytime from your subscription settings</span>
                </div>
              </div>

              <Button 
                onClick={handleSubscriptionAuth}
                disabled={processing || !authOrder}
                className="w-full"
                size="lg"
              >
                {processing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-2" />
                    Authorize Subscription (₹1)
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {!subscription.authRequired && subscription.status === 'active' && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
                <div>
                  <h3 className="text-lg font-semibold text-green-700">
                    Subscription Already Active
                  </h3>
                  <p className="text-gray-600">
                    Your subscription is active and automatic billing is enabled.
                  </p>
                </div>
                <Button onClick={() => router.push('/profile/subscription')}>
                  View Subscription Details
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

export default function SubscriptionCheckoutPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <Clock className="h-12 w-12 text-blue-500 mx-auto animate-spin" />
                <div>
                  <h3 className="text-lg font-semibold">Loading...</h3>
                  <p className="text-gray-600">Please wait while we load your subscription details.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    }>
      <SubscriptionCheckoutContent />
    </Suspense>
  )
}
