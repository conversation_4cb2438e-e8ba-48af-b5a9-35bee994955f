#!/usr/bin/env tsx

/**
 * Admin User Seeding Script
 *
 * This script creates an initial super admin user for the Schopio admin system.
 * Run this script after setting up the database to create the first admin user.
 *
 * Usage:
 * npm run seed:admin
 * or
 * npx tsx scripts/seed-admin.ts
 */

import { config } from 'dotenv'
import { resolve } from 'path'

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') })

import bcrypt from 'bcryptjs'
import { db } from '../src/db'
import { adminUsers } from '../src/db/schema'
import { eq } from 'drizzle-orm'

// Default admin user configuration
const DEFAULT_ADMIN = {
  email: '<EMAIL>',
  name: 'Super Administrator',
  role: 'super_admin' as const,
  password: 'Admin@123456', // Change this in production!
  permissions: ['*'] // Super admin has all permissions
}

async function seedAdminUser() {
  try {
    console.log('🌱 Starting admin user seeding...')
    
    // Check if any admin users already exist
    const [existingAdmin] = await db
      .select()
      .from(adminUsers)
      .where(eq(adminUsers.email, DEFAULT_ADMIN.email))
      .limit(1)
    
    if (existingAdmin) {
      console.log('⚠️  Admin user already exists with email:', DEFAULT_ADMIN.email)
      console.log('   If you need to reset the password, please use the update script or database directly.')
      return
    }
    
    // Hash the password
    console.log('🔐 Hashing password...')
    const passwordHash = await bcrypt.hash(DEFAULT_ADMIN.password, 12)
    
    // Create the admin user
    console.log('👤 Creating admin user...')
    const [newAdmin] = await db.insert(adminUsers).values({
      email: DEFAULT_ADMIN.email,
      name: DEFAULT_ADMIN.name,
      role: DEFAULT_ADMIN.role,
      passwordHash,
      permissions: DEFAULT_ADMIN.permissions,
      isActive: true
    }).returning()
    
    console.log('✅ Admin user created successfully!')
    console.log('')
    console.log('📋 Admin User Details:')
    console.log('   ID:', newAdmin.id)
    console.log('   Email:', newAdmin.email)
    console.log('   Name:', newAdmin.name)
    console.log('   Role:', newAdmin.role)
    console.log('   Password:', DEFAULT_ADMIN.password)
    console.log('')
    console.log('🔒 IMPORTANT SECURITY NOTES:')
    console.log('   1. Change the default password immediately after first login')
    console.log('   2. The admin login URL is: /api/admin/login (POST request)')
    console.log('   3. Admin routes are not publicly accessible via frontend')
    console.log('   4. Use proper authentication headers: Authorization: Bearer <token>')
    console.log('')
    console.log('🚀 You can now login to the admin system!')
    
  } catch (error) {
    console.error('❌ Error seeding admin user:', error)
    process.exit(1)
  }
}

// Additional admin users for different roles (optional)
const ADDITIONAL_ADMINS = [
  {
    email: '<EMAIL>',
    name: 'Sales Manager',
    role: 'sales' as const,
    password: 'Sales@123456',
    permissions: [
      'leads:read', 'leads:write', 'leads:convert',
      'clients:read', 'clients:write',
      'demos:read', 'demos:write'
    ]
  },
  {
    email: '<EMAIL>',
    name: 'Support Agent',
    role: 'support' as const,
    password: 'Support@123456',
    permissions: [
      'clients:read', 'tickets:read', 'tickets:write',
      'billing:read', 'subscriptions:read'
    ]
  },
  {
    email: '<EMAIL>',
    name: 'Billing Manager',
    role: 'billing' as const,
    password: 'Billing@123456',
    permissions: [
      'billing:read', 'billing:write', 'invoices:read', 'invoices:write',
      'payments:read', 'subscriptions:read', 'subscriptions:write'
    ]
  }
]

async function seedAdditionalAdmins() {
  console.log('🌱 Creating additional admin users...')
  
  for (const adminData of ADDITIONAL_ADMINS) {
    try {
      // Check if user already exists
      const [existing] = await db
        .select()
        .from(adminUsers)
        .where(eq(adminUsers.email, adminData.email))
        .limit(1)
      
      if (existing) {
        console.log(`⚠️  Admin user already exists: ${adminData.email}`)
        continue
      }
      
      // Hash password and create user
      const passwordHash = await bcrypt.hash(adminData.password, 12)
      
      const [newAdmin] = await db.insert(adminUsers).values({
        email: adminData.email,
        name: adminData.name,
        role: adminData.role,
        passwordHash,
        permissions: adminData.permissions,
        isActive: true
      }).returning()
      
      console.log(`✅ Created ${adminData.role} admin: ${adminData.email}`)
      
    } catch (error) {
      console.error(`❌ Error creating admin ${adminData.email}:`, error)
    }
  }
}

async function main() {
  console.log('🚀 Schopio Admin User Seeding Script')
  console.log('=====================================')
  console.log('')
  
  // Seed the main super admin
  await seedAdminUser()
  
  // Ask if user wants to create additional admin users
  const args = process.argv.slice(2)
  const createAll = args.includes('--all') || args.includes('-a')
  
  if (createAll) {
    console.log('')
    await seedAdditionalAdmins()
  } else {
    console.log('')
    console.log('💡 To create additional admin users for different roles, run:')
    console.log('   npx tsx scripts/seed-admin.ts --all')
  }
  
  console.log('')
  console.log('🎉 Admin seeding completed!')
  process.exit(0)
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Fatal error:', error)
    process.exit(1)
  })
}

export { seedAdminUser, seedAdditionalAdmins }
