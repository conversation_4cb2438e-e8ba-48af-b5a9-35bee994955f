'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Clock, 
  CheckCircle, 
  ArrowRight,
  Calendar,
  Users,
  Settings,
  BookOpen,
  Zap,
  Target,
  Award,
  TrendingUp
} from 'lucide-react'

interface TimelinePhase {
  id: string
  name: string
  duration: number
  description: string
  icon: any
  activities: string[]
}

const ImplementationTimelineSection = () => {
  const [schoolSize, setSchoolSize] = useState(1000)
  const [currentSystems, setCurrentSystems] = useState('manual')
  const [urgency, setUrgency] = useState('normal')
  const [timeline, setTimeline] = useState({
    totalWeeks: 0,
    phases: [] as (TimelinePhase & { startWeek: number; endWeek: number })[]
  })

  const systemTypes = [
    { id: 'manual', label: 'Manual/Paper-based', multiplier: 1.0 },
    { id: 'basic', label: 'Basic Software', multiplier: 0.9 },
    { id: 'multiple', label: 'Multiple Systems', multiplier: 1.0 },
    { id: 'integrated', label: 'Some Integration', multiplier: 0.8 }
  ]

  const urgencyLevels = [
    { id: 'urgent', label: 'Urgent (Fast-track)', multiplier: 0.7 },
    { id: 'normal', label: 'Standard Timeline', multiplier: 1.0 },
    { id: 'planned', label: 'Planned Rollout', multiplier: 1.0 }
  ]

  const basePhases: TimelinePhase[] = [
    {
      id: 'planning',
      name: 'Planning & Setup',
      duration: 1,
      description: 'Initial consultation, requirements gathering, and system configuration',
      icon: Target,
      activities: [
        'Detailed requirements analysis',
        'System configuration and customization',
        'Data migration planning',
        'Team training schedule setup'
      ]
    },
    {
      id: 'migration',
      name: 'Data Migration',
      duration: 1,
      description: 'Secure transfer of existing data to Schopio platform',
      icon: Settings,
      activities: [
        'Data extraction and cleaning',
        'Secure data transfer',
        'Data validation and verification',
        'Backup and rollback preparation'
      ]
    },
    {
      id: 'launch',
      name: 'Go-Live & Support',
      duration: 1,
      description: 'System launch with dedicated support and training',
      icon: Zap,
      activities: [
        'Phased system rollout',
        'Live staff training sessions',
        'Real-time support and monitoring',
        'Success metrics tracking'
      ]
    }
  ]

  useEffect(() => {
    const systemMultiplier = systemTypes.find(s => s.id === currentSystems)?.multiplier || 1.0
    const urgencyMultiplier = urgencyLevels.find(u => u.id === urgency)?.multiplier || 1.0
    const sizeMultiplier = schoolSize > 2000 ? 1.0 : schoolSize > 1000 ? 1.0 : 0.9

    const totalMultiplier = Math.min(systemMultiplier * urgencyMultiplier * sizeMultiplier, 1.0)

    let currentWeek = 0
    const phasesWithTiming = basePhases.map(phase => {
      const adjustedDuration = Math.max(1, Math.ceil(phase.duration * totalMultiplier))
      const startWeek = currentWeek
      const endWeek = currentWeek + adjustedDuration
      currentWeek = endWeek

      return {
        ...phase,
        duration: adjustedDuration,
        startWeek,
        endWeek
      }
    })

    // Ensure maximum 3 weeks total
    const maxWeeks = 3
    const totalWeeks = Math.min(currentWeek, maxWeeks)

    setTimeline({
      totalWeeks,
      phases: phasesWithTiming
    })
  }, [schoolSize, currentSystems, urgency, basePhases, systemTypes, urgencyLevels])

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const benefits = [
    {
      icon: Clock,
      title: 'Faster Implementation',
      description: 'Get operational weeks faster than traditional systems'
    },
    {
      icon: Users,
      title: 'Dedicated Support',
      description: 'Personal implementation manager throughout the process'
    },
    {
      icon: Award,
      title: 'Proven Process',
      description: 'Battle-tested methodology used by 500+ schools'
    },
    {
      icon: TrendingUp,
      title: 'Immediate ROI',
      description: "Start seeing efficiency gains from week 1"
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-emerald-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Calendar className="w-4 h-4" />
            Implementation Timeline
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Your School Could Be Fully Operational in
            <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent"> Maximum 3 Weeks</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Lightning-fast implementation with our proven process. Get operational quickly and start seeing results immediately.
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          {/* Calculator Inputs */}
          <motion.div variants={fadeInUp} initial="initial" whileInView="animate" viewport={{ once: true }}>
            <Card className="bg-white border-0 shadow-xl mb-12">
              <CardHeader padding="lg">
                <h3 className="text-2xl font-bold text-slate-900 mb-2">
                  Calculate Your Implementation Timeline
                </h3>
                <p className="text-slate-600">
                  Adjust the parameters below to get a personalized timeline for your school.
                </p>
              </CardHeader>

              <CardContent padding="lg">
                <div className="grid md:grid-cols-3 gap-8">
                  {/* School Size */}
                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Number of Students
                    </label>
                    <div className="space-y-3">
                      <input
                        type="range"
                        min="100"
                        max="3000"
                        step="100"
                        value={schoolSize}
                        onChange={(e) => setSchoolSize(parseInt(e.target.value))}
                        className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <div className="text-center">
                        <span className="text-2xl font-bold text-emerald-600">{schoolSize.toLocaleString()}</span>
                        <span className="text-slate-600 ml-1">students</span>
                      </div>
                    </div>
                  </div>

                  {/* Current Systems */}
                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Current System Type
                    </label>
                    <div className="space-y-2">
                      {systemTypes.map((system) => (
                        <label key={system.id} className="flex items-center gap-3 cursor-pointer">
                          <input
                            type="radio"
                            name="currentSystems"
                            value={system.id}
                            checked={currentSystems === system.id}
                            onChange={(e) => setCurrentSystems(e.target.value)}
                            className="w-4 h-4 text-emerald-600 border-slate-300 focus:ring-emerald-500"
                          />
                          <span className="text-sm text-slate-700">{system.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Urgency */}
                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Implementation Urgency
                    </label>
                    <div className="space-y-2">
                      {urgencyLevels.map((level) => (
                        <label key={level.id} className="flex items-center gap-3 cursor-pointer">
                          <input
                            type="radio"
                            name="urgency"
                            value={level.id}
                            checked={urgency === level.id}
                            onChange={(e) => setUrgency(e.target.value)}
                            className="w-4 h-4 text-emerald-600 border-slate-300 focus:ring-emerald-500&"
                          />
                          <span className="text-sm text-slate-700">{level.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Timeline Visualization */}
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
          >
            <Card className="bg-white border-0 shadow-xl mb-12">
              <CardHeader padding="lg">
                <h3 className="text-2xl font-bold text-slate-900 mb-2">
                  Your Implementation Roadmap
                </h3>
                <p className="text-slate-600">
                  Lightning-fast setup from start to full operation
                </p>
              </CardHeader>

              <CardContent padding="lg">
                <div className="space-y-6">
                  {timeline.phases.map((phase, index) => (
                    <motion.div
                      key={phase.id}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="flex items-start gap-6"
                    >
                      {/* Timeline Indicator */}
                      <div className="flex flex-col items-center">
                        <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-blue-500 rounded-xl flex items-center justify-center text-white">
                          <phase.icon className="w-6 h-6" />
                        </div>
                        {index < timeline.phases.length - 1 && (
                          <div className="w-0.5 h-16 bg-slate-200 mt-4" />
                        )}
                      </div>

                      {/* Phase Content */}
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-2">
                          <h4 className="text-xl font-bold text-slate-900">{phase.name}</h4>
                          <div className="flex items-center gap-2 bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full text-sm font-medium">
                            <Clock className="w-3 h-3" />
                            {phase.duration} week{phase.duration > 1 ? 's' : ""}
                          </div>
                        </div>
                        <p className="text-slate-600 mb-4">{phase.description}</p>
                        <div className="grid md:grid-cols-2 gap-2">
                          {phase.activities.map((activity, actIndex) => (
                            <div key={actIndex} className="flex items-center gap-2 text-sm text-slate-600">
                              <CheckCircle className="w-4 h-4 text-emerald-500 flex-shrink-0" />
                              {activity}
                            </div>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Benefits Grid */}
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
          >
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="h-full bg-white border border-slate-200 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardContent padding="lg">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <benefit.icon className="w-6 h-6 text-emerald-600" />
                        </div>
                        <h4 className="text-lg font-bold text-slate-900 mb-2">{benefit.title}</h4>
                        <p className="text-sm text-slate-600">{benefit.description}</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ delay: 0.6 }}
          >
            <Card className="bg-gradient-to-r from-slate-900 to-emerald-900 border-0 shadow-xl">
              <CardContent padding="xl">
                <div className="text-center text-white">
                  <h3 className="text-2xl font-bold mb-4">
                    Ready to Start Your {timeline.totalWeeks}-Week Transformation?
                  </h3>
                  <p className="text-blue-200 mb-6 max-w-2xl mx-auto">
                    Our implementation specialists will create a detailed project plan tailored to your school&apos;s specific needs and timeline.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button
                      size="lg"
                      icon={ArrowRight}
                      iconPosition="right"
                      className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:opacity-90 text-white font-bold px-8 py-4"
                    >
                      Schedule Implementation Planning Call
                    </Button>
                    <Button
                      variant="outline"
                      size="lg"
                      className="border-white text-white hover:bg-white/10 px-6 py-4"
                    >
                      Download Implementation Guide
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default ImplementationTimelineSection
