// Create referral codes for existing partners
// Run with: node tests/create-referral-codes-for-existing-partners.js

const BASE_URL = 'http://localhost:3000'

async function createReferralCodesForExistingPartners() {
  console.log('🔧 Creating referral codes for existing partners...\n')

  try {
    // Step 1: Login as admin
    console.log('1. Logging in as admin...')
    const loginResponse = await fetch(`${BASE_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    })

    if (!loginResponse.ok) {
      console.log('   ❌ Admin login failed')
      return
    }

    const loginData = await loginResponse.json()
    const adminToken = loginData.token
    console.log('   ✅ Admin login successful')

    // Step 2: Get all partners
    console.log('\n2. Getting all partners...')
    const partnersResponse = await fetch(`${BASE_URL}/api/admin/partners?limit=50`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    })

    if (!partnersResponse.ok) {
      console.log('   ❌ Failed to get partners')
      return
    }

    const partnersData = await partnersResponse.json()
    console.log(`   ✅ Found ${partnersData.partners?.length || 0} partners`)

    if (!partnersData.partners || partnersData.partners.length === 0) {
      console.log('\n❌ No partners found in the system!')
      return
    }

    // Step 3: Create referral codes manually using direct database approach
    console.log('\n3. Creating referral codes for each partner...')
    
    // Since there's no direct API endpoint to create referral codes for existing partners,
    // let's create them by calling a custom endpoint or using the database directly
    
    const testCodes = [
      'ABC123', 'XYZ789', 'DEF456', 'EDU001', 'SCH002', 'DIG003', 
      'TEST01', 'DEMO02', 'PART03', 'REFER1', 'CODE01', 'SCHL01'
    ]

    let codeIndex = 0
    const createdCodes = []

    for (const partner of partnersData.partners) {
      console.log(`\n   📝 Creating codes for: ${partner.name}`)
      
      // Create 2 codes per partner
      const partnerCodes = testCodes.slice(codeIndex, codeIndex + 2)
      codeIndex += 2

      for (const code of partnerCodes) {
        console.log(`      📋 Creating referral code: ${code}`)
        
        // Try to create referral code using a direct database insert approach
        // Since we don't have a direct API endpoint, let's use a workaround
        
        // First, let's try to validate if the code already exists
        const validateResponse = await fetch(`${BASE_URL}/api/auth/referral/validate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ code })
        })

        if (validateResponse.ok) {
          const validateData = await validateResponse.json()
          if (validateData.valid) {
            console.log(`         ✅ Code ${code} already exists and is valid`)
            createdCodes.push({ code, partnerName: partner.name })
            continue
          }
        }

        // If code doesn't exist, we need to create it
        // Since there's no direct API, let's document this for manual creation
        console.log(`         📝 Code ${code} needs to be created for ${partner.name}`)
        createdCodes.push({ code, partnerName: partner.name, needsCreation: true })
      }
    }

    // Step 4: Provide manual creation instructions
    console.log('\n4. Manual referral code creation needed...')
    console.log('\n📋 Referral codes that need to be created:')
    
    const needsCreation = createdCodes.filter(c => c.needsCreation)
    
    if (needsCreation.length > 0) {
      console.log('\n🔧 SQL commands to create referral codes:')
      console.log('   Run these in your database console:\n')
      
      for (const codeInfo of needsCreation) {
        const partner = partnersData.partners.find(p => p.name === codeInfo.partnerName)
        if (partner) {
          console.log(`-- Create referral code ${codeInfo.code} for ${codeInfo.partnerName}`)
          console.log(`INSERT INTO referral_codes (partner_id, code, is_active, usage_count, created_at)`)
          console.log(`VALUES ('${partner.id}', '${codeInfo.code}', true, 0, NOW());`)
          console.log('')
        }
      }
      
      console.log('\n💡 Alternative: Use the admin dashboard to create partners with referral codes')
      console.log('   Or run the database seeding script with proper environment setup')
    }

    // Step 5: Test any existing codes
    console.log('\n5. Testing any existing referral codes...')
    
    const existingCodes = createdCodes.filter(c => !c.needsCreation)
    
    if (existingCodes.length > 0) {
      console.log('\n✅ Existing valid codes:')
      existingCodes.forEach(code => {
        console.log(`   🎫 ${code.code} - ${code.partnerName}`)
      })
    } else {
      console.log('\n❌ No existing valid referral codes found')
    }

    // Step 6: Provide quick test codes
    console.log('\n6. Quick solution - Create test referral codes...')
    console.log('\nFor immediate testing, you can manually insert these codes into the database:')
    
    const quickTestCodes = ['TEST123', 'DEMO456', 'QUICK01']
    const firstPartner = partnersData.partners[0]
    
    if (firstPartner) {
      console.log(`\n-- Quick test codes for ${firstPartner.name}:`)
      quickTestCodes.forEach(code => {
        console.log(`INSERT INTO referral_codes (partner_id, code, is_active, usage_count, created_at)`)
        console.log(`VALUES ('${firstPartner.id}', '${code}', true, 0, NOW());`)
      })
      
      console.log('\n🎯 After running the above SQL, you can test with these codes:')
      quickTestCodes.forEach(code => {
        console.log(`   🎫 ${code}`)
      })
    }

  } catch (error) {
    console.error('❌ Error creating referral codes:', error.message)
  }
}

// Run the creation
createReferralCodesForExistingPartners()
