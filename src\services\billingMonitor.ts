import { db } from '@/src/db'
import {
  subscriptions,
  billingSubscriptions,
  billingInvoices,
  billingPayments,
  billingPaymentReminders,
  clients,
  auditLogs,
  securityEvents
} from '@/src/db/schema'
import { eq, and, desc, count, sql, lte, gte, isNull, inArray } from 'drizzle-orm'
import { auditLogger } from './auditLogger'
import { securityMonitor } from './securityMonitor'

export interface BillingAlert {
  id: string
  type: 'payment_failure' | 'overdue_invoice' | 'subscription_suspension' | 'billing_error' | 'high_churn' | 'revenue_drop'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  data: Record<string, any>
  clientId?: string
  subscriptionId?: string
  invoiceId?: string
  createdAt: Date
  resolved: boolean
  resolvedAt?: Date
  resolvedBy?: string
}

export interface BillingMetrics {
  revenue: {
    currentMonth: number
    previousMonth: number
    growth: number
    target: number
    achievement: number
  }
  subscriptions: {
    active: number
    suspended: number
    cancelled: number
    churnRate: number
  }
  payments: {
    successful: number
    failed: number
    pending: number
    successRate: number
  }
  invoices: {
    sent: number
    paid: number
    overdue: number
    collectionRate: number
  }
  alerts: {
    critical: number
    high: number
    medium: number
    low: number
  }
}

export interface DunningAction {
  type: 'email_reminder' | 'phone_call' | 'suspension_warning' | 'service_suspension' | 'account_termination'
  daysOverdue: number
  description: string
  automated: boolean
}

class BillingMonitor {
  private static instance: BillingMonitor
  private alerts: BillingAlert[] = []
  private dunningSequence: DunningAction[] = [
    { type: 'email_reminder', daysOverdue: 1, description: 'First payment reminder email', automated: true },
    { type: 'email_reminder', daysOverdue: 3, description: 'Second payment reminder email', automated: true },
    { type: 'phone_call', daysOverdue: 7, description: 'Phone call to client', automated: false },
    { type: 'suspension_warning', daysOverdue: 10, description: 'Service suspension warning', automated: true },
    { type: 'service_suspension', daysOverdue: 15, description: 'Suspend service access', automated: true },
    { type: 'account_termination', daysOverdue: 30, description: 'Terminate account', automated: false }
  ]

  private constructor() {}

  public static getInstance(): BillingMonitor {
    if (!BillingMonitor.instance) {
      BillingMonitor.instance = new BillingMonitor()
    }
    return BillingMonitor.instance
  }

  /**
   * Monitor billing health and generate alerts
   */
  public async monitorBillingHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical'
    metrics: BillingMetrics
    alerts: BillingAlert[]
    recommendations: string[]
  }> {
    try {
      console.log('🔍 Monitoring billing health...')

      // Get comprehensive billing metrics
      const metrics = await this.getBillingMetrics()
      
      // Check for issues and generate alerts
      const alerts = await this.checkForIssues(metrics)
      
      // Determine overall health status
      const status = this.determineHealthStatus(alerts)
      
      // Generate recommendations
      const recommendations = this.generateRecommendations(metrics, alerts)

      // Log monitoring activity
      await auditLogger.logAdmin('billing_health_check', {
        adminId: 'system',
        resource: 'billing_monitor',
        details: {
          status,
          alertCount: alerts.length,
          criticalAlerts: alerts.filter(a => a.severity === 'critical').length,
          metrics: {
            revenue: metrics.revenue.currentMonth,
            activeSubscriptions: metrics.subscriptions.active,
            successRate: metrics.payments.successRate
          }
        },
        ipAddress: 'system',
        userAgent: 'billing-monitor',
        success: true
      })

      return {
        status,
        metrics,
        alerts,
        recommendations
      }

    } catch (error) {
      console.error('❌ Billing health monitoring failed:', error)
      
      // Log error
      await securityMonitor.logSecurityEvent({
        type: 'system_error',
        severity: 'high',

        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        },
        ipAddress: 'system',
        userAgent: 'billing-monitor',

      })

      throw error
    }
  }

  /**
   * Get comprehensive billing metrics
   */
  private async getBillingMetrics(): Promise<BillingMetrics> {
    const now = new Date()
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1)

    // Revenue metrics
    const [currentRevenue] = await db
      .select({ total: sql<number>`COALESCE(SUM(CAST(${billingPayments.amount} AS DECIMAL)), 0)` })
      .from(billingPayments)
      .where(and(
        eq(billingPayments.status, 'completed'),
        gte(billingPayments.createdAt, currentMonth),
        lte(billingPayments.createdAt, now)
      ))

    const [previousRevenue] = await db
      .select({ total: sql<number>`COALESCE(SUM(CAST(${billingPayments.amount} AS DECIMAL)), 0)` })
      .from(billingPayments)
      .where(and(
        eq(billingPayments.status, 'completed'),
        gte(billingPayments.createdAt, previousMonth),
        lte(billingPayments.createdAt, currentMonth)
      ))

    const revenueGrowth = previousRevenue.total > 0 
      ? ((currentRevenue.total - previousRevenue.total) / previousRevenue.total) * 100 
      : 0

    // Subscription metrics
    const [activeSubscriptions] = await db
      .select({ count: count() })
      .from(subscriptions)
      .where(eq(subscriptions.status, 'active'))

    const [suspendedSubscriptions] = await db
      .select({ count: count() })
      .from(subscriptions)
      .where(eq(subscriptions.status, 'suspended'))

    const [cancelledSubscriptions] = await db
      .select({ count: count() })
      .from(subscriptions)
      .where(eq(subscriptions.status, 'cancelled'))

    // Payment metrics
    const [successfulPayments] = await db
      .select({ count: count() })
      .from(billingPayments)
      .where(and(
        eq(billingPayments.status, 'completed'),
        gte(billingPayments.createdAt, currentMonth)
      ))

    const [failedPayments] = await db
      .select({ count: count() })
      .from(billingPayments)
      .where(and(
        eq(billingPayments.status, 'failed'),
        gte(billingPayments.createdAt, currentMonth)
      ))

    const [pendingPayments] = await db
      .select({ count: count() })
      .from(billingPayments)
      .where(and(
        eq(billingPayments.status, 'pending'),
        gte(billingPayments.createdAt, currentMonth)
      ))

    const totalPayments = successfulPayments.count + failedPayments.count + pendingPayments.count
    const successRate = totalPayments > 0 ? (successfulPayments.count / totalPayments) * 100 : 100

    // Invoice metrics
    const [sentInvoices] = await db
      .select({ count: count() })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'sent'),
        gte(billingInvoices.issuedDate, currentMonth.toISOString().split('T')[0])
      ))

    const [paidInvoices] = await db
      .select({ count: count() })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'paid'),
        gte(billingInvoices.issuedDate, currentMonth.toISOString().split('T')[0])
      ))

    const [overdueInvoices] = await db
      .select({ count: count() })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'sent'),
        lte(billingInvoices.dueDate, now.toISOString().split('T')[0])
      ))

    const collectionRate = sentInvoices.count > 0 ? (paidInvoices.count / sentInvoices.count) * 100 : 100

    // Alert metrics
    const criticalAlerts = this.alerts.filter(a => a.severity === 'critical' && !a.resolved).length
    const highAlerts = this.alerts.filter(a => a.severity === 'high' && !a.resolved).length
    const mediumAlerts = this.alerts.filter(a => a.severity === 'medium' && !a.resolved).length
    const lowAlerts = this.alerts.filter(a => a.severity === 'low' && !a.resolved).length

    return {
      revenue: {
        currentMonth: currentRevenue.total,
        previousMonth: previousRevenue.total,
        growth: revenueGrowth,
        target: previousRevenue.total * 1.1, // 10% growth target
        achievement: previousRevenue.total > 0 ? (currentRevenue.total / (previousRevenue.total * 1.1)) * 100 : 100
      },
      subscriptions: {
        active: activeSubscriptions.count,
        suspended: suspendedSubscriptions.count,
        cancelled: cancelledSubscriptions.count,
        churnRate: activeSubscriptions.count > 0 ? (cancelledSubscriptions.count / activeSubscriptions.count) * 100 : 0
      },
      payments: {
        successful: successfulPayments.count,
        failed: failedPayments.count,
        pending: pendingPayments.count,
        successRate
      },
      invoices: {
        sent: sentInvoices.count,
        paid: paidInvoices.count,
        overdue: overdueInvoices.count,
        collectionRate
      },
      alerts: {
        critical: criticalAlerts,
        high: highAlerts,
        medium: mediumAlerts,
        low: lowAlerts
      }
    }
  }

  /**
   * Check for billing issues and generate alerts
   */
  private async checkForIssues(metrics: BillingMetrics): Promise<BillingAlert[]> {
    const alerts: BillingAlert[] = []

    // Check payment success rate
    if (metrics.payments.successRate < 85) {
      alerts.push({
        id: `payment-success-rate-${Date.now()}`,
        type: 'payment_failure',
        severity: metrics.payments.successRate < 70 ? 'critical' : 'high',
        title: 'Low Payment Success Rate',
        message: `Payment success rate is ${metrics.payments.successRate.toFixed(1)}%, below acceptable threshold`,
        data: { successRate: metrics.payments.successRate, threshold: 85 },
        createdAt: new Date(),
        resolved: false
      })
    }

    // Check revenue growth
    if (metrics.revenue.growth < -10) {
      alerts.push({
        id: `revenue-drop-${Date.now()}`,
        type: 'revenue_drop',
        severity: metrics.revenue.growth < -25 ? 'critical' : 'high',
        title: 'Revenue Decline',
        message: `Revenue dropped by ${Math.abs(metrics.revenue.growth).toFixed(1)}% compared to last month`,
        data: { growth: metrics.revenue.growth, currentRevenue: metrics.revenue.currentMonth },
        createdAt: new Date(),
        resolved: false
      })
    }

    // Check churn rate
    if (metrics.subscriptions.churnRate > 5) {
      alerts.push({
        id: `high-churn-${Date.now()}`,
        type: 'high_churn',
        severity: metrics.subscriptions.churnRate > 10 ? 'critical' : 'high',
        title: 'High Churn Rate',
        message: `Subscription churn rate is ${metrics.subscriptions.churnRate.toFixed(1)}%, above acceptable threshold`,
        data: { churnRate: metrics.subscriptions.churnRate, threshold: 5 },
        createdAt: new Date(),
        resolved: false
      })
    }

    // Check overdue invoices
    if (metrics.invoices.overdue > 10) {
      alerts.push({
        id: `overdue-invoices-${Date.now()}`,
        type: 'overdue_invoice',
        severity: metrics.invoices.overdue > 25 ? 'critical' : 'medium',
        title: 'High Number of Overdue Invoices',
        message: `${metrics.invoices.overdue} invoices are overdue`,
        data: { overdueCount: metrics.invoices.overdue, threshold: 10 },
        createdAt: new Date(),
        resolved: false
      })
    }

    // Store alerts
    this.alerts.push(...alerts)

    return alerts
  }

  /**
   * Determine overall health status
   */
  private determineHealthStatus(alerts: BillingAlert[]): 'healthy' | 'warning' | 'critical' {
    const criticalAlerts = alerts.filter(a => a.severity === 'critical').length
    const highAlerts = alerts.filter(a => a.severity === 'high').length

    if (criticalAlerts > 0) return 'critical'
    if (highAlerts > 0) return 'warning'
    return 'healthy'
  }

  /**
   * Generate recommendations based on metrics and alerts
   */
  private generateRecommendations(metrics: BillingMetrics, alerts: BillingAlert[]): string[] {
    const recommendations: string[] = []

    if (metrics.payments.successRate < 85) {
      recommendations.push('Review payment gateway configuration and retry failed payments')
      recommendations.push('Contact clients with failed payments to update payment methods')
    }

    if (metrics.revenue.growth < 0) {
      recommendations.push('Analyze revenue decline causes and implement retention strategies')
      recommendations.push('Consider promotional offers for existing clients')
    }

    if (metrics.subscriptions.churnRate > 5) {
      recommendations.push('Implement customer success programs to reduce churn')
      recommendations.push('Conduct exit interviews with cancelled clients')
    }

    if (metrics.invoices.overdue > 10) {
      recommendations.push('Intensify collection efforts for overdue invoices')
      recommendations.push('Review and optimize dunning management process')
    }

    if (recommendations.length === 0) {
      recommendations.push('Billing system is operating within normal parameters')
      recommendations.push('Continue monitoring key metrics for early issue detection')
    }

    return recommendations
  }
}

export const billingMonitor = BillingMonitor.getInstance()
