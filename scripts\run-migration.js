#!/usr/bin/env node

/**
 * Database Migration Runner
 * Runs the operational expenses migration for billingSubscriptions table
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration from environment
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

async function runMigration() {
  const pool = new Pool(dbConfig);
  
  try {
    console.log('🔄 Starting database migration...');
    console.log('📊 Database:', process.env.DATABASE_URL?.split('@')[1]?.split('/')[0] || 'Unknown');
    
    // Read migration file
    const migrationPath = path.join(__dirname, '../migrations/add-operational-expenses.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Migration file loaded:', migrationPath);
    
    // Execute migration
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      console.log('🚀 Executing migration...');
      
      // Split and execute each statement
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        if (statement) {
          console.log(`   Executing statement ${i + 1}/${statements.length}...`);
          await client.query(statement);
        }
      }
      
      await client.query('COMMIT');
      console.log('✅ Migration completed successfully!');
      
      // Verify migration
      console.log('🔍 Verifying migration...');
      const verifyResult = await client.query(`
        SELECT 
          column_name, 
          data_type, 
          is_nullable, 
          column_default
        FROM information_schema.columns 
        WHERE table_name = 'billing_subscriptions' 
          AND column_name IN (
            'operational_expenses', 
            'database_costs', 
            'website_maintenance', 
            'support_costs', 
            'infrastructure_costs', 
            'total_operational_expenses',
            'notes',
            'setup_fee',
            'discount_start_date',
            'discount_end_date',
            'discount_reason',
            'created_by'
          )
        ORDER BY column_name;
      `);
      
      console.log('📋 Added columns:');
      verifyResult.rows.forEach(row => {
        console.log(`   ✓ ${row.column_name} (${row.data_type})`);
      });
      
      console.log(`\n🎉 Migration successful! Added ${verifyResult.rows.length} columns to billing_subscriptions table.`);
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('📝 Full error:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run migration if called directly
if (require.main === module) {
  runMigration().catch(console.error);
}

module.exports = { runMigration };
