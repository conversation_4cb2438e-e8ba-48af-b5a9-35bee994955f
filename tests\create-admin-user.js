/**
 * Create Admin User Script
 * Creates the initial admin user for testing
 */

require('dotenv').config({ path: '.env.local' })
const bcrypt = require('bcryptjs')
const { neon } = require('@neondatabase/serverless')

const sql = neon(process.env.DATABASE_URL)

// Default admin user configuration
const DEFAULT_ADMIN = {
  email: '<EMAIL>',
  name: 'Super Administrator',
  role: 'super_admin',
  password: 'Admin@123456',
  permissions: ['*'] // Super admin has all permissions
}

async function createAdminUser() {
  try {
    console.log('🌱 Creating admin user...')
    
    // Check if any admin users already exist
    const existingAdmins = await sql`
      SELECT * FROM admin_users WHERE email = ${DEFAULT_ADMIN.email} LIMIT 1
    `
    
    if (existingAdmins.length > 0) {
      console.log('⚠️ Admin user already exists with email:', DEFAULT_ADMIN.email)
      console.log('✅ Using existing admin credentials for testing')
      return DEFAULT_ADMIN
    }
    
    // Hash the password
    console.log('🔐 Hashing password...')
    const passwordHash = await bcrypt.hash(DEFAULT_ADMIN.password, 12)
    
    // Create the admin user
    console.log('👤 Creating admin user...')
    const newAdmins = await sql`
      INSERT INTO admin_users (email, name, role, password_hash, permissions, is_active)
      VALUES (${DEFAULT_ADMIN.email}, ${DEFAULT_ADMIN.name}, ${DEFAULT_ADMIN.role}, ${passwordHash}, ${JSON.stringify(DEFAULT_ADMIN.permissions)}, true)
      RETURNING *
    `
    
    const newAdmin = newAdmins[0]
    
    console.log('✅ Admin user created successfully!')
    console.log('')
    console.log('📋 Admin User Details:')
    console.log('   ID:', newAdmin.id)
    console.log('   Email:', newAdmin.email)
    console.log('   Name:', newAdmin.name)
    console.log('   Role:', newAdmin.role)
    console.log('   Password:', DEFAULT_ADMIN.password)
    console.log('')
    console.log('🚀 You can now login to the admin system!')
    
    return DEFAULT_ADMIN
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error)
    throw error
  }
}

// Run the script
createAdminUser()
  .then(() => {
    console.log('🎉 Admin user setup completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Failed to create admin user:', error)
    process.exit(1)
  })
