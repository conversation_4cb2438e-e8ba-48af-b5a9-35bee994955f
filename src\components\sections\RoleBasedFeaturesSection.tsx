'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import {
  Users,
  GraduationCap,
  BookOpen,
  Heart,
  BarChart3,
  Calendar,
  MessageSquare,
  Shield,
  CheckCircle,
  ArrowRight,
  Zap,
  Target,
  TrendingUp,
  Bus,
  UserPlus,
  Building,
  DollarSign,
  BookOpenCheck
} from 'lucide-react'

const RoleBasedFeaturesSection = () => {
  const [activeRole, setActiveRole] = useState('administrators')

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const roles = [
    {
      id: 'administrators',
      title: 'For Administrators',
      icon: Users,
      subtitle: 'Complete Operational Control',
      color: 'text-blue-600 bg-blue-100',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'teachers',
      title: 'For Teachers',
      icon: GraduationCap,
      subtitle: 'Intelligent Classroom Management',
      color: 'text-emerald-600 bg-emerald-100',
      gradient: 'from-emerald-500 to-green-500'
    },
    {
      id: 'students',
      title: 'For Students',
      icon: BookOpen,
      subtitle: 'Personalized Learning Experience',
      color: 'text-purple-600 bg-purple-100',
      gradient: 'from-purple-500 to-indigo-500'
    },
    {
      id: 'parents',
      title: 'For Parents',
      icon: Heart,
      subtitle: 'Real-time Progress Tracking',
      color: 'text-red-600 bg-red-100',
      gradient: 'from-red-500 to-pink-500'
    },
    {
      id: 'transport',
      title: 'Transport Manager',
      icon: Bus,
      subtitle: 'Vehicle and Route Management',
      color: 'text-cyan-600 bg-cyan-100',
      gradient: 'from-cyan-500 to-blue-500'
    },
    {
      id: 'admission',
      title: 'Admission Officer',
      icon: UserPlus,
      subtitle: 'Student Admissions and Registration',
      color: 'text-teal-600 bg-teal-100',
      gradient: 'from-teal-500 to-emerald-500'
    },
    {
      id: 'hostel',
      title: 'Hostel Manager',
      icon: Building,
      subtitle: 'Hostel and Accommodation Management',
      color: 'text-pink-600 bg-pink-100',
      gradient: 'from-pink-500 to-rose-500'
    },
    {
      id: 'finance',
      title: 'Finance Manager',
      icon: DollarSign,
      subtitle: 'Fee Management and Financial Operations',
      color: 'text-amber-600 bg-amber-100',
      gradient: 'from-amber-500 to-orange-500'
    },
    {
      id: 'librarian',
      title: 'Librarian',
      icon: BookOpenCheck,
      subtitle: 'Library Management and Book Operations',
      color: 'text-indigo-600 bg-indigo-100',
      gradient: 'from-indigo-500 to-purple-500'
    }
  ]

  const roleFeatures = {
    administrators: {
      headline: 'Streamline Operations with AI-Powered Insights',
      description: 'Complete control over your institution with predictive analytics and automated workflows.',
      features: [
        {
          icon: BarChart3,
          title: 'Advanced Analytics Dashboard',
          description: 'Real-time insights on student performance, teacher efficiency, and resource utilization',
          benefit: 'Reduce admin time by 60%'
        },
        {
          icon: Users,
          title: 'Staff Management System',
          description: 'Automated scheduling, performance tracking, and payroll integration',
          benefit: 'Improve staff efficiency by 40%'
        },
        {
          icon: Shield,
          title: 'Security & Compliance',
          description: 'Role-based access control, audit trails, and regulatory compliance tools',
          benefit: 'Ensure 100% data security'
        },
        {
          icon: TrendingUp,
          title: 'Predictive Planning',
          description: 'AI-powered forecasting for enrollment, budgeting, and resource allocation',
          benefit: 'Optimize costs by 30%'
        }
      ]
    },
    teachers: {
      headline: 'Empower Teaching with Smart Technology',
      description: 'Focus on what matters most - teaching - while AI handles the administrative tasks.',
      features: [
        {
          icon: Calendar,
          title: 'Smart Class Scheduling',
          description: 'AI-optimized timetables that maximize learning outcomes and teacher satisfaction',
          benefit: 'Save 5 hours per week'
        },
        {
          icon: BarChart3,
          title: 'Student Performance Analytics',
          description: 'Individual student insights, learning pattern analysis, and intervention recommendations',
          benefit: 'Improve student outcomes by 35%'
        },
        {
          icon: MessageSquare,
          title: 'Parent Communication Hub',
          description: 'Automated progress reports, instant messaging, and parent-teacher conference scheduling',
          benefit: 'Reduce communication time by 50%'
        },
        {
          icon: Target,
          title: 'Personalized Learning Plans',
          description: 'AI-generated lesson plans and assignments tailored to each student\'s needs',
          benefit: 'Increase engagement by 45%'
        }
      ]
    },
    students: {
      headline: 'Learn Smarter with Personalized AI Guidance',
      description: 'Every student gets a customized learning experience that adapts to their unique needs.',
      features: [
        {
          icon: Target,
          title: 'Adaptive Learning Path',
          description: 'AI creates personalized study plans based on learning style and performance',
          benefit: 'Accelerate learning by 40%'
        },
        {
          icon: BarChart3,
          title: 'Progress Tracking',
          description: 'Real-time performance analytics with actionable insights and recommendations',
          benefit: 'Improve grades by 25%'
        },
        {
          icon: Calendar,
          title: 'Smart Study Planner',
          description: 'AI-optimized schedules for assignments, exams, and extracurricular activities',
          benefit: 'Better time management'
        },
        {
          icon: MessageSquare,
          title: 'Peer Collaboration Tools',
          description: 'Safe, monitored platforms for group projects and peer-to-peer learning',
          benefit: 'Enhanced teamwork skills'
        }
      ]
    },
    parents: {
      headline: 'Stay Connected to Your Child\'s Educational Journey',
      description: 'Real-time insights and seamless communication keep you involved in every step.',
      features: [
        {
          icon: BarChart3,
          title: 'Real-time Progress Reports',
          description: 'Instant access to grades, attendance, behavior reports, and teacher feedback',
          benefit: 'Stay informed 24/7'
        },
        {
          icon: MessageSquare,
          title: 'Direct Teacher Communication',
          description: 'Secure messaging, video calls, and appointment scheduling with teachers',
          benefit: 'Stronger school partnership'
        },
        {
          icon: Calendar,
          title: 'Event & Schedule Management',
          description: 'Automated notifications for events, deadlines, and important announcements',
          benefit: 'Never miss important dates'
        },
        {
          icon: TrendingUp,
          title: 'Learning Analytics',
          description: 'Insights into learning patterns, strengths, and areas for improvement',
          benefit: 'Support child\'s growth'
        }
      ]
    },
    transport: {
      headline: 'Optimize Transport Operations with Smart Management',
      description: 'Comprehensive vehicle and route management with real-time tracking and safety protocols.',
      features: [
        {
          icon: Bus,
          title: 'Route Planning & Optimization',
          description: 'AI-powered route optimization for fuel efficiency and time management',
          benefit: 'Reduce fuel costs by 25%'
        },
        {
          icon: Shield,
          title: 'Safety Management System',
          description: 'Driver monitoring, vehicle maintenance tracking, and emergency protocols',
          benefit: 'Ensure 100% safety compliance'
        },
        {
          icon: MessageSquare,
          title: 'Parent Communication',
          description: 'Real-time notifications for pickup/drop-off times and route changes',
          benefit: 'Improve parent satisfaction'
        },
        {
          icon: BarChart3,
          title: 'Fleet Analytics',
          description: 'Vehicle performance tracking, maintenance scheduling, and cost analysis',
          benefit: 'Optimize fleet efficiency'
        }
      ]
    },
    admission: {
      headline: 'Streamline Admissions with Digital Excellence',
      description: 'Complete admission management from application to enrollment with automated workflows.',
      features: [
        {
          icon: UserPlus,
          title: 'Online Application Portal',
          description: 'Digital application forms with document upload and verification systems',
          benefit: 'Reduce paperwork by 90%'
        },
        {
          icon: BarChart3,
          title: 'Applicant Tracking System',
          description: 'Track application status, interview scheduling, and admission decisions',
          benefit: 'Improve process efficiency'
        },
        {
          icon: MessageSquare,
          title: 'Communication Hub',
          description: 'Automated notifications and updates to applicants and parents',
          benefit: 'Enhanced communication'
        },
        {
          icon: TrendingUp,
          title: 'Admission Analytics',
          description: 'Insights on application trends, conversion rates, and enrollment forecasting',
          benefit: 'Data-driven decisions'
        }
      ]
    },
    hostel: {
      headline: 'Comprehensive Hostel Management Made Simple',
      description: 'Complete accommodation management with room allocation, maintenance, and student welfare.',
      features: [
        {
          icon: Building,
          title: 'Room Management System',
          description: 'Room allocation, occupancy tracking, and maintenance scheduling',
          benefit: 'Optimize space utilization'
        },
        {
          icon: Users,
          title: 'Student Welfare Tracking',
          description: 'Health monitoring, disciplinary records, and welfare check-ins',
          benefit: 'Ensure student wellbeing'
        },
        {
          icon: MessageSquare,
          title: 'Parent Communication',
          description: 'Regular updates on student activities and hostel life',
          benefit: 'Keep parents informed'
        },
        {
          icon: BarChart3,
          title: 'Facility Analytics',
          description: 'Usage statistics, maintenance costs, and facility optimization insights',
          benefit: 'Reduce operational costs'
        }
      ]
    },
    finance: {
      headline: 'Smart Financial Management for Educational Excellence',
      description: 'Complete fee management and financial operations with automated reconciliation.',
      features: [
        {
          icon: DollarSign,
          title: 'Fee Management System',
          description: 'Automated fee collection, payment tracking, and receipt generation',
          benefit: 'Reduce collection time by 70%'
        },
        {
          icon: BarChart3,
          title: 'Financial Analytics',
          description: 'Revenue tracking, expense analysis, and budget forecasting',
          benefit: 'Improve financial planning'
        },
        {
          icon: Shield,
          title: 'Digital Reconciliation',
          description: 'Automated bank reconciliation and financial audit trails',
          benefit: 'Ensure 100% accuracy'
        },
        {
          icon: TrendingUp,
          title: 'Revenue Optimization',
          description: 'Payment trend analysis and collection strategy optimization',
          benefit: 'Increase collection efficiency'
        }
      ]
    },
    librarian: {
      headline: 'Modern Library Management with Digital Innovation',
      description: 'Complete library operations with book tracking, digital resources, and student engagement.',
      features: [
        {
          icon: BookOpenCheck,
          title: 'Digital Catalog System',
          description: 'Complete book inventory with search, reservation, and tracking capabilities',
          benefit: 'Streamline book management'
        },
        {
          icon: Users,
          title: 'Student Engagement Tools',
          description: 'Reading challenges, book recommendations, and progress tracking',
          benefit: 'Increase reading engagement'
        },
        {
          icon: BarChart3,
          title: 'Usage Analytics',
          description: 'Book popularity tracking, reading patterns, and collection optimization',
          benefit: 'Optimize library resources'
        },
        {
          icon: MessageSquare,
          title: 'Communication Hub',
          description: 'Overdue notifications, new arrivals alerts, and reading recommendations',
          benefit: 'Enhanced user experience'
        }
      ]
    }
  }

  const activeRoleData = roleFeatures[activeRole as keyof typeof roleFeatures]
  const activeRoleInfo = roles.find(role => role.id === activeRole)

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Zap className="w-4 h-4" />
            Designed for Every User
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Tailored Solutions for 
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Every Role</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            From administrators to students, everyone gets tools designed specifically for their needs and workflows.
          </p>
        </motion.div>

        {/* Role Selector */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-16"
        >
          {roles.map((role, index) => (
            <motion.button
              key={role.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              onClick={() => setActiveRole(role.id)}
              className={`p-6 rounded-xl border-2 transition-all duration-300 text-left ${
                activeRole === role.id
                  ? 'border-blue-500 bg-white shadow-lg scale-105'
                  : 'border-slate-200 bg-white/50 hover:border-slate-300 hover:bg-white'
              }`}
            >
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-3 ${
                activeRole === role.id ? role.color : 'text-slate-400 bg-slate-100'
              }`}>
                <role.icon className="w-6 h-6" />
              </div>
              <h3 className={`font-bold mb-1 ${
                activeRole === role.id ? 'text-slate-900' : 'text-slate-600'
              }`}>
                {role.title}
              </h3>
              <p className={`text-sm ${
                activeRole === role.id ? 'text-slate-600' : 'text-slate-500'
              }`}>
                {role.subtitle}
              </p>
            </motion.button>
          ))}
        </motion.div>

        {/* Active Role Content */}
        <motion.div
          key={activeRole}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-6xl mx-auto"
        >
          <Card className="bg-white border-0 shadow-xl overflow-hidden">
            <CardHeader padding="xl" className={`bg-gradient-to-r ${activeRoleInfo?.gradient} text-white`}>
              <div className="flex items-center gap-4 mb-4">
                <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center">
                  {activeRoleInfo && <activeRoleInfo.icon className="w-8 h-8 text-white" />}
                </div>
                <div>
                  <h3 className="text-3xl font-bold">{activeRoleData.headline}</h3>
                  <p className="text-lg text-white/90 mt-2">{activeRoleData.description}</p>
                </div>
              </div>
            </CardHeader>
            <CardContent padding="xl">
              <div className="grid md:grid-cols-2 gap-8">
                {activeRoleData.features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="flex gap-4"
                  >
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0 ${activeRoleInfo?.color}`}>
                      <feature.icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-slate-900 mb-2">{feature.title}</h4>
                      <p className="text-slate-600 mb-3">{feature.description}</p>
                      <div className={`inline-flex items-center gap-2 bg-gradient-to-r ${activeRoleInfo?.gradient} text-white px-3 py-1 rounded-full text-xs font-bold`}>
                        <CheckCircle className="w-3 h-3" />
                        {feature.benefit}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
              
              <div className="mt-8 pt-8 border-t border-slate-200 text-center">
                <Button
                  size="lg"
                  icon={ArrowRight}
                  iconPosition="right"
                  className={`bg-gradient-to-r ${activeRoleInfo?.gradient} hover:opacity-90 text-white font-bold px-8 py-4`}
                >
                  See {activeRoleInfo?.title} Demo
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default RoleBasedFeaturesSection
