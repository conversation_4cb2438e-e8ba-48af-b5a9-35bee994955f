import SavingsEstimatorSection from '@/components/sections/SavingsEstimatorSection'
import SchoolReadinessAssessment from '@/components/sections/SchoolReadinessAssessment'
import ImpactVisualizerSection from '@/components/sections/ImpactVisualizerSection'
import FeatureFitAssessment from '@/components/sections/FeatureFitAssessment'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'School Management System Pricing & ERP Software Packages | Schopio',
  description: 'Affordable school management system pricing and ERP software packages. Complete educational management solution starting from ₹20 per student. Compare plans and features.',
  keywords: 'school management system pricing, school ERP software cost, educational management software price, school administration software packages, best ERP system for schools pricing, school management system cost India',
  openGraph: {
    title: 'School Management System Pricing & ERP Software Packages | Schopio',
    description: 'Affordable school management system pricing and ERP software packages. Complete educational management solution starting from ₹20 per student. Compare plans and features.',
    type: 'website',
  },
}

export default function PackagesPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-br from-emerald-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-6">
              <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
              Affordable School ERP Pricing
            </div>
            <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 mb-6">
              Best School Management System
              <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Pricing in India</span>
            </h1>
            <p className="text-xl text-slate-600 leading-relaxed">
              Discover affordable school management system pricing starting from ₹20 per student per month.
              Complete ERP software packages with all educational management features included.
              No hidden costs, transparent pricing for schools of all sizes.
            </p>
          </div>
        </div>
      </section>

      {/* Assessment Tools */}
      <SavingsEstimatorSection />
      <SchoolReadinessAssessment />
      <ImpactVisualizerSection />
      <FeatureFitAssessment />

      {/* Summary Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6">
              Ready to Get Started?
            </h2>
            <p className="text-lg text-slate-600 mb-8">
              Based on your assessment results, our team can provide personalized recommendations 
              and help you implement the perfect Schopio configuration for your school.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/demo" 
                className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-emerald-600 text-white font-bold rounded-lg hover:from-blue-700 hover:to-emerald-700 transition-all duration-300"
              >
                Schedule Your Demo
              </a>
              <a 
                href="/solutions" 
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-slate-300 text-slate-700 font-bold rounded-lg hover:border-blue-500 hover:text-blue-600 transition-all duration-300"
              >
                Explore Solutions
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
