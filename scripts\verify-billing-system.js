#!/usr/bin/env node

/**
 * Comprehensive Billing System Verification
 * Verifies all components of the automated billing system
 */

console.log('🔍 SCHOPIO AUTOMATED BILLING SYSTEM VERIFICATION')
console.log('=' .repeat(60))

// Test 1: TypeScript Compilation
console.log('🔄 Test 1: TypeScript Compilation')
try {
  const { execSync } = require('child_process')
  execSync('bunx tsc --noEmit', { stdio: 'pipe' })
  console.log('✅ TypeScript compilation successful')
} catch (error) {
  console.error('❌ TypeScript compilation failed:', error.message)
  process.exit(1)
}

// Test 2: Service File Verification
console.log('\n🔄 Test 2: Service File Verification')
const fs = require('fs')
const path = require('path')

const requiredFiles = [
  'src/services/startup.ts',
  'src/services/billingScheduler.ts',
  'src/services/billingHealthMonitor.ts',
  'src/services/missedBillDetector.ts',
  'src/services/productionErrorHandler.ts',
  'src/services/emailService.ts',
  'ecosystem.config.js',
  '.env.production.example'
]

let allFilesExist = true
for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
}

if (!allFilesExist) {
  console.error('❌ Some required files are missing')
  process.exit(1)
}

// Test 3: Service Dependencies
console.log('\n🔄 Test 3: Service Dependencies')
try {
  // Check if services can be imported
  const startupPath = path.resolve('src/services/startup.ts')
  const billingSchedulerPath = path.resolve('src/services/billingScheduler.ts')
  
  console.log('✅ Service files can be accessed')
  
  // Check for required methods in startup service
  const startupContent = fs.readFileSync(startupPath, 'utf8')
  const requiredMethods = [
    'initializeServices',
    'getServiceStatus', 
    'isHealthy',
    'shutdownServices'
  ]
  
  for (const method of requiredMethods) {
    if (startupContent.includes(method)) {
      console.log(`✅ startup.ts has ${method}`)
    } else {
      console.log(`❌ startup.ts missing ${method}`)
      allFilesExist = false
    }
  }
  
  // Check billing scheduler methods
  const schedulerContent = fs.readFileSync(billingSchedulerPath, 'utf8')
  const schedulerMethods = [
    'init',
    'stop',
    'generateMonthlyBilling',
    'scheduleMonthlyBilling'
  ]
  
  for (const method of schedulerMethods) {
    if (schedulerContent.includes(method)) {
      console.log(`✅ billingScheduler.ts has ${method}`)
    } else {
      console.log(`❌ billingScheduler.ts missing ${method}`)
      allFilesExist = false
    }
  }
  
} catch (error) {
  console.error('❌ Service dependency check failed:', error.message)
  process.exit(1)
}

// Test 4: Configuration Verification
console.log('\n🔄 Test 4: Configuration Verification')
try {
  const envExample = fs.readFileSync('.env.production.example', 'utf8')
  
  const requiredEnvVars = [
    'AUTO_START_SERVICES',
    'BILLING_SCHEDULER_ENABLED',
    'HEALTH_MONITORING_ENABLED',
    'MISSED_BILL_DETECTION_ENABLED',
    'BILLING_SCHEDULER_TIMEZONE'
  ]
  
  for (const envVar of requiredEnvVars) {
    if (envExample.includes(envVar)) {
      console.log(`✅ Environment variable ${envVar} configured`)
    } else {
      console.log(`❌ Environment variable ${envVar} missing`)
      allFilesExist = false
    }
  }
  
} catch (error) {
  console.error('❌ Configuration verification failed:', error.message)
  process.exit(1)
}

// Test 5: PM2 Configuration
console.log('\n🔄 Test 5: PM2 Configuration')
try {
  const pm2Config = require(path.resolve('ecosystem.config.js'))
  
  if (pm2Config.apps && pm2Config.apps.length > 0) {
    console.log('✅ PM2 configuration exists')
    
    const mainApp = pm2Config.apps.find(app => app.name === 'schopio-main')
    if (mainApp) {
      console.log('✅ Main application configuration found')
      
      if (mainApp.env && mainApp.env.AUTO_START_SERVICES === 'true') {
        console.log('✅ Auto-start services enabled in PM2 config')
      } else {
        console.log('⚠️ Auto-start services not enabled in PM2 config')
      }
    } else {
      console.log('❌ Main application configuration missing')
      allFilesExist = false
    }
  } else {
    console.log('❌ PM2 configuration invalid')
    allFilesExist = false
  }
  
} catch (error) {
  console.error('❌ PM2 configuration check failed:', error.message)
  process.exit(1)
}

// Test 6: Admin API Integration
console.log('\n🔄 Test 6: Admin API Integration')
try {
  const adminRoutes = fs.readFileSync('app/api/[[...route]]/admin.ts', 'utf8')
  
  const requiredEndpoints = [
    '/system/init',
    '/health',
    '/health/simple'
  ]
  
  for (const endpoint of requiredEndpoints) {
    if (adminRoutes.includes(endpoint)) {
      console.log(`✅ Admin API has ${endpoint} endpoint`)
    } else {
      console.log(`❌ Admin API missing ${endpoint} endpoint`)
      allFilesExist = false
    }
  }
  
  // Check for startup service import
  if (adminRoutes.includes('@/src/services/startup')) {
    console.log('✅ Admin API imports startup service')
  } else {
    console.log('❌ Admin API missing startup service import')
    allFilesExist = false
  }
  
} catch (error) {
  console.error('❌ Admin API integration check failed:', error.message)
  process.exit(1)
}

// Test 7: Documentation Verification
console.log('\n🔄 Test 7: Documentation Verification')
try {
  const requiredDocs = [
    'docs/PRODUCTION_DEPLOYMENT_GUIDE.md',
    'docs/PRODUCTION_REALITY_ASSESSMENT.md',
    'docs/COMPREHENSIVE_HANDOVER_DOCUMENTATION.md',
    'docs/SCHOPIO_MANUAL_BILLING_ARCHITECTURE.md'
  ]
  
  for (const doc of requiredDocs) {
    if (fs.existsSync(doc)) {
      console.log(`✅ ${doc}`)
    } else {
      console.log(`❌ ${doc} - MISSING`)
      allFilesExist = false
    }
  }
  
} catch (error) {
  console.error('❌ Documentation verification failed:', error.message)
  process.exit(1)
}

// Final Results
console.log('\n' + '=' .repeat(60))
console.log('📊 VERIFICATION RESULTS')
console.log('=' .repeat(60))

if (allFilesExist) {
  console.log('🎉 ALL VERIFICATION TESTS PASSED!')
  console.log('')
  console.log('✅ TypeScript compilation successful')
  console.log('✅ All required service files present')
  console.log('✅ Service dependencies verified')
  console.log('✅ Configuration files complete')
  console.log('✅ PM2 configuration ready')
  console.log('✅ Admin API integration complete')
  console.log('✅ Documentation complete')
  console.log('')
  console.log('🚀 AUTOMATED BILLING SYSTEM IS READY FOR PRODUCTION!')
  console.log('')
  console.log('Next Steps:')
  console.log('1. Configure .env.production with your settings')
  console.log('2. Run: npm run build')
  console.log('3. Deploy: pm2 start ecosystem.config.js --env production')
  console.log('4. Verify: curl http://localhost:3000/api/admin/health')
  console.log('')
  process.exit(0)
} else {
  console.log('❌ VERIFICATION FAILED!')
  console.log('')
  console.log('Some components are missing or incomplete.')
  console.log('Please review the failed tests above and fix the issues.')
  console.log('')
  process.exit(1)
}
