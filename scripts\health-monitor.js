#!/usr/bin/env node

/**
 * External Health Monitor for Schopio
 * Monitors the main application health and triggers recovery actions
 */

const http = require('http')
const https = require('https')
const { exec } = require('child_process')
const fs = require('fs')
const path = require('path')

class HealthMonitor {
  constructor() {
    this.config = {
      interval: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 30000, // 30 seconds
      timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 5000,    // 5 seconds
      mainAppUrl: process.env.MAIN_APP_URL || 'http://localhost:3000',
      maxFailures: parseInt(process.env.MAX_HEALTH_FAILURES) || 3,
      alertEmail: process.env.ALERT_EMAIL || '<EMAIL>',
      logFile: path.join(__dirname, '../logs/health-monitor.log')
    }
    
    this.consecutiveFailures = 0
    this.lastSuccessTime = new Date()
    this.isRecovering = false
    
    console.log('🏥 Health Monitor starting with config:', this.config)
  }

  start() {
    console.log('🚀 Starting health monitoring...')
    
    // Initial health check
    this.performHealthCheck()
    
    // Schedule regular checks
    setInterval(() => {
      this.performHealthCheck()
    }, this.config.interval)
    
    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('📡 Health monitor received SIGTERM, shutting down...')
      process.exit(0)
    })
    
    process.on('SIGINT', () => {
      console.log('📡 Health monitor received SIGINT, shutting down...')
      process.exit(0)
    })
  }

  async performHealthCheck() {
    try {
      const startTime = Date.now()
      
      // Check main application health
      const healthResult = await this.checkApplicationHealth()
      
      const responseTime = Date.now() - startTime
      
      if (healthResult.healthy) {
        this.handleHealthyResponse(responseTime, healthResult)
      } else {
        this.handleUnhealthyResponse(healthResult)
      }
      
    } catch (error) {
      this.handleHealthCheckError(error)
    }
  }

  checkApplicationHealth() {
    return new Promise((resolve, reject) => {
      const url = new URL(`${this.config.mainAppUrl}/api/admin/health/simple`)
      const client = url.protocol === 'https:' ? https : http
      
      const req = client.get(url, {
        timeout: this.config.timeout,
        headers: {
          'User-Agent': 'Schopio-Health-Monitor/1.0'
        }
      }, (res) => {
        let data = ''
        
        res.on('data', (chunk) => {
          data += chunk
        })
        
        res.on('end', () => {
          const healthy = res.statusCode === 200 && data.trim() === 'OK'
          
          resolve({
            healthy,
            statusCode: res.statusCode,
            response: data.trim(),
            headers: res.headers
          })
        })
      })
      
      req.on('timeout', () => {
        req.destroy()
        reject(new Error('Health check timeout'))
      })
      
      req.on('error', (error) => {
        reject(error)
      })
      
      req.end()
    })
  }

  handleHealthyResponse(responseTime, healthResult) {
    if (this.consecutiveFailures > 0) {
      console.log(`✅ Application recovered after ${this.consecutiveFailures} failures`)
      this.logEvent('RECOVERY', `Application healthy again. Response time: ${responseTime}ms`)
      
      if (this.consecutiveFailures >= this.config.maxFailures) {
        this.sendRecoveryAlert(responseTime)
      }
    }
    
    this.consecutiveFailures = 0
    this.lastSuccessTime = new Date()
    this.isRecovering = false
    
    // Log periodic health status
    if (new Date().getMinutes() % 15 === 0 && new Date().getSeconds() < 30) {
      console.log(`💓 Application healthy. Response time: ${responseTime}ms`)
    }
  }

  handleUnhealthyResponse(healthResult) {
    this.consecutiveFailures++
    
    console.error(`❌ Health check failed (${this.consecutiveFailures}/${this.config.maxFailures}):`, {
      statusCode: healthResult.statusCode,
      response: healthResult.response
    })
    
    this.logEvent('FAILURE', `Health check failed. Status: ${healthResult.statusCode}, Response: ${healthResult.response}`)
    
    if (this.consecutiveFailures >= this.config.maxFailures && !this.isRecovering) {
      this.triggerRecoveryActions()
    }
  }

  handleHealthCheckError(error) {
    this.consecutiveFailures++
    
    console.error(`💥 Health check error (${this.consecutiveFailures}/${this.config.maxFailures}):`, error.message)
    
    this.logEvent('ERROR', `Health check error: ${error.message}`)
    
    if (this.consecutiveFailures >= this.config.maxFailures && !this.isRecovering) {
      this.triggerRecoveryActions()
    }
  }

  async triggerRecoveryActions() {
    if (this.isRecovering) {
      return
    }
    
    this.isRecovering = true
    
    console.log('🚨 Triggering recovery actions...')
    this.logEvent('RECOVERY_START', 'Starting automatic recovery procedures')
    
    try {
      // Step 1: Try graceful restart
      console.log('🔄 Attempting graceful restart...')
      await this.executeCommand('pm2 reload schopio-main')
      
      // Wait for restart
      await this.delay(10000)
      
      // Check if restart helped
      const healthAfterRestart = await this.checkApplicationHealth()
      
      if (healthAfterRestart.healthy) {
        console.log('✅ Graceful restart successful')
        this.logEvent('RECOVERY_SUCCESS', 'Graceful restart resolved the issue')
        this.sendRecoveryAlert(0, 'Graceful restart')
        return
      }
      
      // Step 2: Force restart if graceful didn't work
      console.log('🔄 Attempting force restart...')
      await this.executeCommand('pm2 restart schopio-main')
      
      // Wait for restart
      await this.delay(15000)
      
      // Check if force restart helped
      const healthAfterForceRestart = await this.checkApplicationHealth()
      
      if (healthAfterForceRestart.healthy) {
        console.log('✅ Force restart successful')
        this.logEvent('RECOVERY_SUCCESS', 'Force restart resolved the issue')
        this.sendRecoveryAlert(0, 'Force restart')
        return
      }
      
      // Step 3: Alert for manual intervention
      console.log('🚨 Automatic recovery failed, alerting administrators')
      this.logEvent('RECOVERY_FAILED', 'All automatic recovery attempts failed')
      this.sendCriticalAlert()
      
    } catch (error) {
      console.error('💥 Recovery action failed:', error)
      this.logEvent('RECOVERY_ERROR', `Recovery action failed: ${error.message}`)
      this.sendCriticalAlert(error)
    }
  }

  executeCommand(command) {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(error)
        } else {
          resolve({ stdout, stderr })
        }
      })
    })
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  logEvent(type, message) {
    const timestamp = new Date().toISOString()
    const logEntry = `${timestamp} [${type}] ${message}\n`
    
    try {
      // Ensure log directory exists
      const logDir = path.dirname(this.config.logFile)
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true })
      }
      
      fs.appendFileSync(this.config.logFile, logEntry)
    } catch (error) {
      console.error('Failed to write to log file:', error)
    }
  }

  sendRecoveryAlert(responseTime, method = 'automatic') {
    const message = `
✅ SCHOPIO RECOVERY SUCCESS

The Schopio application has recovered from health check failures.

Recovery Details:
- Method: ${method}
- Response Time: ${responseTime}ms
- Downtime: ${Math.round((Date.now() - this.lastSuccessTime.getTime()) / 1000)} seconds
- Previous Failures: ${this.consecutiveFailures}

The system is now operating normally.

Timestamp: ${new Date().toISOString()}
    `
    
    console.log('📧 Sending recovery alert...')
    // TODO: Implement actual email sending
    // For now, just log the message
    console.log(message)
  }

  sendCriticalAlert(error = null) {
    const message = `
🚨 CRITICAL: SCHOPIO APPLICATION DOWN

The Schopio application is not responding to health checks and automatic recovery has failed.

Status:
- Consecutive Failures: ${this.consecutiveFailures}
- Last Success: ${this.lastSuccessTime.toISOString()}
- Downtime: ${Math.round((Date.now() - this.lastSuccessTime.getTime()) / 1000)} seconds

${error ? `Recovery Error: ${error.message}` : 'All recovery attempts failed'}

IMMEDIATE MANUAL INTERVENTION REQUIRED

Please check:
1. Server resources (CPU, memory, disk)
2. Database connectivity
3. External service status (Razorpay, email)
4. Application logs
5. Network connectivity

Timestamp: ${new Date().toISOString()}
    `
    
    console.log('🚨 Sending critical alert...')
    // TODO: Implement actual email sending and possibly SMS/webhook alerts
    // For now, just log the message
    console.log(message)
  }
}

// Start the health monitor
const monitor = new HealthMonitor()
monitor.start()

console.log('🏥 Health Monitor is running...')
