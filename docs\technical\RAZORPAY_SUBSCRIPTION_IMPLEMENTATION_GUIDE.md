# Razorpay Manual Billing Implementation Guide

**Last Updated:** July 8, 2025
**Implementation Status:** Complete
**Integration Type:** Manual Monthly Billing with Razorpay Orders

## 🎯 **Overview**

This document details the implementation of Razorpay's manual billing system in the Schopio platform. The system has transitioned from automatic subscriptions to manual monthly payments to overcome payment method limitations and provide schools with maximum payment flexibility.

## 🏗️ **Architecture Overview**

### **Manual Billing Flow**
```
Admin Creates Subscription → Monthly Due Date Set → School Receives Notification → Manual Payment → Payment Verification → Status Update
```

### **Key Components**
1. **Admin Subscription Management** - Creates billing subscriptions with due dates and penalty settings
2. **School Manual Payment Interface** - Handles one-time Razorpay order payments
3. **Payment Verification System** - Processes payment confirmations and updates subscription status
4. **Database Integration** - Tracks payment status, due dates, and penalty calculations

## 🔧 **Technical Implementation**

### **1. Admin Subscription Creation**

**File:** `app/api/[[...route]]/admin.ts`
**Enhanced for Manual Billing**

**Database Integration:**
```typescript
// Create billing subscription with manual billing fields
const [newSubscription] = await db.insert(billingSubscriptions).values({
  clientId: request.clientId,
  planId: subscriptionData.planId,
  studentCount: subscriptionData.studentCount,
  pricePerStudent: subscriptionData.pricePerStudent,
  monthlyAmount: subscriptionData.monthlyAmount,

  // Manual billing specific fields
  dueDate: calculateCurrentDueDate(), // 1st of current month
  nextBillingDate: calculateNextBillingDate(), // 1st of next month
  paymentStatus: 'pending',
  currentPenaltyAmount: '0.00',
  gracePeriodDays: 3,
  penaltyRate: '2.00', // 2% daily penalty
  autoPenaltyEnabled: true,

  status: 'active',
  createdBy: adminUser.id
}).returning()
```

**Manual Billing Setup:**
```typescript
// No Razorpay subscription creation - using manual payments instead
// Create initial billing transaction for tracking
await db.insert(billingTransactions).values({
  subscriptionId: newSubscription.id,
  clientId: request.clientId,
  amount: subscriptionData.monthlyAmount,
  penaltyAmount: '0.00',
  totalAmount: subscriptionData.monthlyAmount,
  dueDate: calculateCurrentDueDate(),
  status: 'pending'
})
```

### **2. School Manual Payment Interface**

**File:** `app/profile/billing/page.tsx`
**Updated for Manual Billing**

**Manual Payment Flow:**
```typescript
// Create manual payment order
const orderResponse = await fetch('/api/subscriptions/create-manual-payment-order', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    subscriptionId: subscription.id,
    amount: billingSummary.outstandingAmount
  })
})

const orderData = await orderResponse.json()
```

**Razorpay Manual Payment Configuration:**
```typescript
const options = {
  key: orderData.data.keyId,
  amount: orderData.data.amount * 100, // Convert to paise
  currency: orderData.data.currency,
  name: 'Schopio',
  description: orderData.data.description,
  order_id: orderData.data.orderId, // Key field for manual payment
  image: '/logo.png',
  handler: function (response) {
    // Handle successful payment
    verifyManualPayment(response.razorpay_payment_id, response.razorpay_order_id, response.razorpay_signature)
  }
}
```

### **3. Manual Payment API Endpoints**

**File:** `app/api/[[...route]]/subscriptions.ts`

**Create Manual Payment Order Endpoint:**
```typescript
.post('/create-manual-payment-order', async (c) => {
  const { subscriptionId, amount } = await c.req.json()

  // Verify subscription belongs to client
  const [subscription] = await db.select()
    .from(billingSubscriptions)
    .where(and(
      eq(billingSubscriptions.id, subscriptionId),
      eq(billingSubscriptions.clientId, clientId)
    ))

  // Create Razorpay order for manual payment
  const orderData = {
    amount: Math.round(amount * 100), // Convert to paise
    currency: 'INR',
    receipt: `manual_${subscriptionId}_${Date.now()}`,
    notes: {
      subscription_id: subscriptionId,
      client_id: clientId,
      payment_type: 'manual_subscription'
    }
  }

  const razorpayOrder = await paymentService.createOrder(orderData)

  return c.json({
    success: true,
    data: {
      orderId: razorpayOrder.id,
      amount: amount,
      currency: 'INR',
      keyId: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID
    }
  })
})
```

**Verify Manual Payment Endpoint:**
```typescript
.post('/verify-manual-payment', async (c) => {
  const { razorpay_payment_id, razorpay_order_id, razorpay_signature, subscriptionId } = await c.req.json()

  // Verify payment signature
  const isValid = paymentService.verifyPaymentSignature({
    razorpayOrderId: razorpay_order_id,
    razorpayPaymentId: razorpay_payment_id,
    razorpaySignature: razorpay_signature
  })

  if (isValid) {
    // Update subscription payment status
    await db.update(billingSubscriptions)
      .set({
        paymentStatus: 'paid',
        lastPaymentDate: new Date().toISOString().split('T')[0],
        currentPenaltyAmount: '0.00', // Reset penalty after payment
        updatedAt: new Date()
      })
      .where(eq(billingSubscriptions.id, subscriptionId))

    // Record payment in billing_payments table
    await db.insert(billingPayments).values({
      subscriptionId: subscriptionId,
      clientId: clientId,
      amount: amountPaid.toString(),
      razorpayPaymentId: razorpay_payment_id,
      status: 'succeeded',
      paymentMethod: paymentDetails.payment.method || 'card'
    })
  }

  return c.json({ success: isValid })
})
```

## 🔒 **Security Implementation**

### **Payment Signature Verification**
```typescript
// Razorpay signature verification for manual payments
const crypto = require('crypto')

function verifyPaymentSignature(orderId, paymentId, signature) {
  const body = orderId + '|' + paymentId
  const expectedSignature = crypto
    .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
    .update(body.toString())
    .digest('hex')

  return expectedSignature === signature
}
```

### **Authentication & Authorization**
- JWT token validation for all payment endpoints
- Client ownership verification for subscription access
- Audit logging for all payment operations
- Payment amount validation and verification

## 🗄️ **Database Schema**

### **Manual Billing Tables**
```sql
-- Enhanced billing_subscriptions table for manual billing
billing_subscriptions {
  id: uuid (primary key)
  clientId: uuid (foreign key to clients.id)
  planId: uuid (foreign key to billing_plans.id)
  studentCount: integer
  pricePerStudent: decimal(10,2)
  monthlyAmount: decimal(10,2)

  -- Manual billing specific fields
  dueDate: date
  currentPenaltyAmount: decimal(10,2) DEFAULT 0.00
  lastPaymentDate: date
  paymentStatus: varchar(20) DEFAULT 'pending' -- 'pending', 'paid', 'overdue', 'grace_period'
  autoPenaltyEnabled: boolean DEFAULT true
  gracePeriodDays: integer DEFAULT 3
  penaltyRate: decimal(5,2) DEFAULT 2.00

  status: varchar(20) DEFAULT 'active'
  nextBillingDate: date

  -- Legacy Razorpay fields (for reference)
  razorpaySubscriptionId: varchar(100)
  razorpayCustomerId: varchar(100)
  razorpayCustomerId: varchar(100)

  createdAt: timestamp
  updatedAt: timestamp
}

-- New billing_transactions table for payment tracking
billing_transactions {
  id: uuid (primary key)
  subscriptionId: uuid (foreign key to billing_subscriptions.id)
  clientId: uuid (foreign key to clients.id)

  amount: decimal(10,2) NOT NULL
  penaltyAmount: decimal(10,2) DEFAULT 0.00
  totalAmount: decimal(10,2) NOT NULL
  dueDate: date NOT NULL
  paymentDate: timestamp

  -- Razorpay Integration
  razorpayPaymentId: varchar(255)
  razorpayOrderId: varchar(255)
  paymentMethod: varchar(50)

  status: varchar(20) DEFAULT 'pending' -- 'pending', 'paid', 'failed', 'overdue'
  failureReason: text
  notes: text

  createdAt: timestamp
  updatedAt: timestamp
}
```

## 🔄 **Payment Processing Flow**

### **Manual Payment Workflow**
1. **Due Date Notification** - System notifies school of upcoming payment
2. **Payment Initiation** - School clicks "Pay Now" in billing interface
3. **Order Creation** - System creates Razorpay order for exact amount
4. **Payment Processing** - School completes payment via Razorpay checkout
5. **Payment Verification** - System verifies payment signature and updates status

### **Payment Status Management**
```typescript
// Payment status lifecycle
'pending' → 'paid' (successful payment)
'pending' → 'overdue' (after due date + grace period)
'overdue' → 'paid' (late payment with penalty)
```

## 🧪 **Testing Guide**

### **End-to-End Manual Payment Testing**
1. **Admin Subscription Creation Test**
   - Create software request in admin dashboard
   - Approve with manual billing subscription setup
   - Verify billing subscription creation with due dates

2. **School Manual Payment Test**
   - Login as school user
   - Navigate to billing page
   - View outstanding amount and due date
   - Click "Pay Now" and complete Razorpay payment
   - Verify payment status updates to "paid"

3. **Penalty Calculation Test**
   - Set subscription due date to past date
   - Verify penalty calculation after grace period
   - Test payment with penalty amount included

### **Razorpay Dashboard Verification**
- **Orders:** Check created orders for manual payments
- **Payments:** Verify successful payment processing
- **Webhooks:** Monitor payment events (if configured)

## 🚀 **Production Deployment**

### **Environment Configuration**
```env
# Razorpay Configuration (Manual Payments)
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_xxxxxxxxxx
RAZORPAY_KEY_SECRET=xxxxxxxxxx
```

### **Manual Billing Setup**
1. Configure Razorpay test/live mode keys
2. Set up payment notification system
3. Configure penalty calculation automation
4. Test manual payment flow end-to-end

### **Go-Live Checklist**
- [ ] Replace test keys with live Razorpay keys
- [ ] Test complete manual payment flow in production
- [ ] Set up automated penalty calculation system
- [ ] Configure payment reminder notifications
- [ ] Set up monitoring for failed payments and system errors

## 📚 **References**

- **Razorpay Payment Documentation:** https://razorpay.com/docs/payments/
- **Razorpay Orders API:** https://razorpay.com/docs/api/orders/
- **Manual Billing System Documentation:** `docs/manual-billing-system.md`

## 🔄 **Migration from Subscription System**

This implementation replaces the previous automatic Razorpay subscription system with manual monthly billing to provide:
- **Payment Flexibility:** All payment methods available without restrictions
- **Better Success Rates:** No UPI/debit card amount limitations
- **Professional Experience:** Reliable payment processing for schools
- **Operational Control:** Manual oversight of billing and payments

**Migration Status:** ✅ Complete - All existing subscriptions migrated to manual billing system
- **Razorpay Checkout Integration:** https://razorpay.com/docs/checkout/
- **Webhook Integration Guide:** https://razorpay.com/docs/webhooks/
- **Signature Verification:** https://razorpay.com/docs/webhooks/validate-test/

---

**Implementation Complete:** The system now uses proper Razorpay subscription flow with real authentication and automatic billing capabilities.
