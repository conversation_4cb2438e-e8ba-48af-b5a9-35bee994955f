'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Gift, 
  Download, 
  Calendar, 
  Phone, 
  FileText, 
  Calculator, 
  Users, 
  CheckCircle,
  ArrowRight,
  Star,
  Clock,
  Target,
  Zap
} from 'lucide-react'

const LeadMagnetsSection = () => {
  const [selectedMagnet, setSelectedMagnet] = useState(0)

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const leadMagnets = [
    {
      icon: FileText,
      title: "Free School Efficiency Audit",
      subtitle: "Discover Hidden Savings",
      description: "Get a personalized 15-page report analyzing your current operations and identifying significant efficiency improvements",
      benefits: [
        "Administrative efficiency analysis",
        "Efficiency improvement opportunities",
        "Technology gap assessment",
        "Custom improvement roadmap"
      ],
      cta: "Get Free Audit",
      value: "Comprehensive Analysis",
      timeframe: "Delivered in 48 hours",
      color: "from-blue-500 to-blue-600",
      popular: true
    },
    {
      icon: Calendar,
      title: "Custom Demo & Consultation",
      subtitle: "See Schopio in Action",
      description: "30-minute personalized demo tailored to your school size and specific needs, plus 15-minute consultation with implementation expert",
      benefits: [
        "Live system demonstration",
        "Custom use case scenarios",
        "Efficiency assessment for your school",
        "Implementation timeline planning"
      ],
      cta: "Book Demo",
      value: "Personalized Session",
      timeframe: "Available this week",
      color: "from-emerald-500 to-emerald-600",
      popular: false
    },
    {
      icon: Phone,
      title: "Education Specialist Consultation",
      subtitle: "Expert Guidance",
      description: "45-minute one-on-one consultation with our education technology specialist to discuss your challenges and solutions",
      benefits: [
        "Technology strategy planning",
        "Best practices sharing",
        "Vendor comparison guidance",
        "Implementation roadmap"
      ],
      cta: "Schedule Call",
      value: "Expert Guidance",
      timeframe: "Same day booking",
      color: "from-purple-500 to-purple-600",
      popular: false
    }
  ]

  const quickActions = [
    {
      icon: Download,
      title: "Download Feature Guide",
      description: "Complete 24-page guide with screenshots",
      action: "Instant Download"
    },
    {
      icon: Calculator,
      title: "Efficiency Calculator",
      description: "Assess time savings for your school size",
      action: "Try Calculator"
    },
    {
      icon: Users,
      title: "Join Demo Webinar",
      description: "Next session: Tomorrow 3 PM IST",
      action: "Reserve Spot"
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Gift className="w-4 h-4" />
            Free Resources & Consultations
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Get Expert Help 
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Choosing the Right Solution</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Don&apos;t make expensive mistakes. Get personalized guidance from education technology experts who&apos;ve helped 500+ schools.
          </p>
        </motion.div>

        {/* Main Lead Magnets */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid lg:grid-cols-3 gap-8 mb-16"
        >
          {leadMagnets.map((magnet, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative"
            >
              {magnet.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-slate-900 px-3 py-1 rounded-full text-xs font-bold shadow-lg flex items-center gap-1">
                    <Star className="w-3 h-3 fill-current" />
                    MOST POPULAR
                  </div>
                </div>
              )}
              
              <Card className="h-full bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <CardHeader padding="lg">
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${magnet.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <magnet.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-slate-900">{magnet.title}</h3>
                      <p className="text-sm text-blue-600 font-medium">{magnet.subtitle}</p>
                    </div>
                  </div>
                  <p className="text-slate-600 mb-4">{magnet.description}</p>
                  
                  <div className="flex items-center justify-between text-sm mb-4">
                    <span className="bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full font-bold">{magnet.value}</span>
                    <div className="flex items-center gap-1 text-slate-500">
                      <Clock className="w-4 h-4" />
                      <span>{magnet.timeframe}</span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent padding="lg">
                  <div className="space-y-3 mb-6">
                    <h4 className="text-sm font-semibold text-slate-700">What You&apos;ll Get:</h4>
                    {magnet.benefits.map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-emerald-500 flex-shrink-0" />
                        <span className="text-sm text-slate-600">{benefit}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    size="lg"
                    icon={ArrowRight}
                    iconPosition="right"
                    className={`w-full bg-gradient-to-r ${magnet.color} hover:opacity-90 text-white font-bold py-3`}
                  >
                    {magnet.cta}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-slate-900 text-center mb-8">Quick Actions</h3>
          <div className="grid md:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-white border border-slate-200 shadow-md hover:shadow-lg transition-all duration-300 group cursor-pointer">
                  <CardContent padding="lg">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-gradient-to-br from-slate-100 to-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <action.icon className="w-6 h-6 text-blue-600" />
                      </div>
                      <h4 className="text-lg font-bold text-slate-900 mb-2">{action.title}</h4>
                      <p className="text-sm text-slate-600 mb-4">{action.description}</p>
                      <div className="text-blue-600 text-sm font-medium flex items-center justify-center gap-1 group-hover:gap-2 transition-all">
                        <span>{action.action}</span>
                        <ArrowRight className="w-4 h-4" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="text-center"
        >
          <Card className="bg-gradient-to-r from-blue-600 to-emerald-600 border-0 max-w-3xl mx-auto">
            <CardContent padding="xl">
              <div className="text-white">
                <div className="flex items-center justify-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  ))}
                  <span className="ml-2 text-blue-100">4.9/5 from 500+ schools</span>
                </div>
                <h3 className="text-2xl font-bold mb-4">Join Schools Already Saving ₹10L+ Annually</h3>
                <p className="text-blue-100 mb-6">
                  &quot;The free audit alone saved us ₹3L in the first quarter. The implementation was seamless and ROI was immediate.&quot;
                </p>
                <div className="text-sm text-blue-200">
                  <strong>Dr. Rajesh Kumar</strong> - Principal, ABC International School
                </div>
                
                <div className="grid md:grid-cols-3 gap-6 mt-8 pt-8 border-t border-white/20">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-300">500+</div>
                    <div className="text-sm text-blue-200">Schools Served</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-300">₹50Cr+</div>
                    <div className="text-sm text-blue-200">Total Savings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-300">14 Days</div>
                    <div className="text-sm text-blue-200">Avg Implementation</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default LeadMagnetsSection
