import { db } from '@/src/db'
import { subscriptions, billingSubscriptions, billingInvoices, clients } from '@/src/db/schema'
import { eq, and, desc, sql } from 'drizzle-orm'

export type SubscriptionStatus = 'active' | 'suspended' | 'cancelled' | 'expired' | 'pending' | 'overdue'

export interface StatusTransition {
  id: string
  fromStatus: SubscriptionStatus
  toStatus: SubscriptionStatus
  reason: string
  triggeredBy?: string
  triggeredAt: Date
  metadata?: Record<string, any>
}

export interface SubscriptionStatusInfo {
  id: string
  currentStatus: SubscriptionStatus
  canTransitionTo: SubscriptionStatus[]
  restrictions: string[]
  nextBillingDate: string
  daysUntilDue: number
  outstandingAmount: number
  gracePeriodEnd?: Date
}

export class SubscriptionStatusManager {
  
  /**
   * Define valid status transitions
   */
  private static readonly STATUS_TRANSITIONS: Record<SubscriptionStatus, SubscriptionStatus[]> = {
    pending: ['active', 'cancelled'],
    active: ['suspended', 'cancelled', 'overdue', 'expired'],
    overdue: ['active', 'suspended', 'cancelled'],
    suspended: ['active', 'cancelled'],
    cancelled: [], // Terminal state
    expired: ['active'] // Can be reactivated
  }

  /**
   * Get current status information for a subscription
   */
  static async getSubscriptionStatusInfo(id: string): Promise<SubscriptionStatusInfo | null> {
    try {
      const [subscription] = await db.select({
        id: subscriptions.id,
        status: subscriptions.status,
        nextBillingDate: subscriptions.nextBillingDate,
        gracePeriodDays: subscriptions.gracePeriodDays
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, id))
      .limit(1)

      if (!subscription) return null

      // Calculate days until due
      const nextBilling = new Date(subscription.nextBillingDate || new Date())
      const today = new Date()
      const daysUntilDue = Math.ceil((nextBilling.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

      // Get outstanding amount from unpaid invoices
      const [outstandingResult] = await db.select({
        total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.clientId, subscription.id),
        sql`${billingInvoices.status} IN ('sent', 'overdue')`
      ))

      const currentStatus = subscription.status as SubscriptionStatus
      const canTransitionTo = this.STATUS_TRANSITIONS[currentStatus] || []
      const restrictions = this.getStatusRestrictions(currentStatus)

      // Calculate grace period end if applicable
      let gracePeriodEnd: Date | undefined
      if (currentStatus === 'overdue' && subscription.gracePeriodDays) {
        gracePeriodEnd = new Date(nextBilling)
        gracePeriodEnd.setDate(gracePeriodEnd.getDate() + subscription.gracePeriodDays)
      }

      return {
        id: subscription.id,
        currentStatus,
        canTransitionTo,
        restrictions,
        nextBillingDate: subscription.nextBillingDate ? new Date(subscription.nextBillingDate).toISOString() : new Date().toISOString(),
        daysUntilDue,
        outstandingAmount: outstandingResult.total || 0,
        gracePeriodEnd
      }

    } catch (error) {
      console.error('Error getting subscription status info:', error)
      return null
    }
  }

  /**
   * Transition subscription to new status
   */
  static async transitionStatus(
    id: string,
    newStatus: SubscriptionStatus,
    reason: string,
    triggeredBy?: string,
    metadata?: Record<string, any>
  ): Promise<{ success: boolean; error?: string; transition?: StatusTransition }> {
    try {
      // Get current subscription
      const [subscription] = await db.select({
        id: subscriptions.id,
        status: subscriptions.status,
        clientId: subscriptions.clientId
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, id))
      .limit(1)

      if (!subscription) {
        return { success: false, error: 'Subscription not found' }
      }

      const currentStatus = subscription.status as SubscriptionStatus
      const validTransitions = this.STATUS_TRANSITIONS[currentStatus] || []

      // Check if transition is valid
      if (!validTransitions.includes(newStatus)) {
        return {
          success: false,
          error: `Invalid transition from ${currentStatus} to ${newStatus}`
        }
      }

      // Perform pre-transition validations
      const validationResult = await this.validateTransition(id, currentStatus, newStatus)
      if (!validationResult.valid) {
        return { success: false, error: validationResult.error }
      }

      // Update subscription status
      await db.update(subscriptions)
        .set({ status: newStatus })
        .where(eq(subscriptions.id, id))

      // Perform post-transition actions
      await this.performPostTransitionActions(id, currentStatus, newStatus, metadata)

      const transition: StatusTransition = {
        id: id,
        fromStatus: currentStatus,
        toStatus: newStatus,
        reason,
        triggeredBy,
        triggeredAt: new Date(),
        metadata
      }

      // Log the transition (you might want to store this in a transitions table)
      console.log(`📊 Subscription status transition:`, transition)

      return { success: true, transition }

    } catch (error) {
      console.error('Error transitioning subscription status:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Suspend subscription
   */
  static async suspendSubscription(
    id: string,
    reason: string,
    triggeredBy?: string
  ): Promise<{ success: boolean; error?: string }> {
    const result = await this.transitionStatus(id, 'suspended', reason, triggeredBy)
    return { success: result.success, error: result.error }
  }

  /**
   * Reactivate subscription
   */
  static async reactivateSubscription(
    id: string,
    reason: string,
    triggeredBy?: string
  ): Promise<{ success: boolean; error?: string }> {
    const result = await this.transitionStatus(id, 'active', reason, triggeredBy)
    return { success: result.success, error: result.error }
  }

  /**
   * Cancel subscription
   */
  static async cancelSubscription(
    id: string,
    reason: string,
    triggeredBy?: string,
    effectiveDate?: Date
  ): Promise<{ success: boolean; error?: string }> {
    const metadata = effectiveDate ? { effectiveDate: effectiveDate.toISOString() } : undefined
    const result = await this.transitionStatus(id, 'cancelled', reason, triggeredBy, metadata)
    return { success: result.success, error: result.error }
  }

  /**
   * Mark subscription as overdue
   */
  static async markOverdue(
    id: string,
    triggeredBy?: string
  ): Promise<{ success: boolean; error?: string }> {
    const result = await this.transitionStatus(
      id,
      'overdue',
      'Payment overdue beyond grace period',
      triggeredBy
    )
    return { success: result.success, error: result.error }
  }

  /**
   * Get status restrictions for a given status
   */
  private static getStatusRestrictions(status: SubscriptionStatus): string[] {
    const restrictions: Record<SubscriptionStatus, string[]> = {
      pending: ['No billing until activated', 'Limited access to services'],
      active: [],
      overdue: ['Service access may be limited', 'Late payment penalties apply'],
      suspended: ['Service access blocked', 'No new billing cycles'],
      cancelled: ['No service access', 'No billing', 'Cannot be reactivated'],
      expired: ['Service access blocked', 'Requires renewal']
    }

    return restrictions[status] || []
  }

  /**
   * Validate if a status transition is allowed
   */
  private static async validateTransition(
    id: string,
    fromStatus: SubscriptionStatus,
    toStatus: SubscriptionStatus
  ): Promise<{ valid: boolean; error?: string }> {
    // Custom validation logic based on business rules
    
    if (toStatus === 'active') {
      // Check if there are outstanding payments for reactivation
      const [outstandingInvoices] = await db.select({ count: sql<number>`COUNT(*)` })
        .from(billingInvoices)
        .leftJoin(subscriptions, eq(billingInvoices.clientId, subscriptions.clientId))
        .where(and(
          eq(subscriptions.id, id),
          sql`${billingInvoices.status} IN ('overdue')`
        ))

      if (outstandingInvoices.count > 0 && fromStatus === 'suspended') {
        return {
          valid: false,
          error: 'Cannot reactivate subscription with outstanding overdue payments'
        }
      }
    }

    if (toStatus === 'cancelled' && fromStatus === 'active') {
      // Could add validation for active billing cycles, etc.
    }

    return { valid: true }
  }

  /**
   * Perform actions after status transition
   */
  private static async performPostTransitionActions(
    id: string,
    fromStatus: SubscriptionStatus,
    toStatus: SubscriptionStatus,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Update related billing cycles if needed
      if (toStatus === 'suspended' || toStatus === 'cancelled') {
        await db.update(billingSubscriptions)
          .set({ status: 'cancelled' })
          .where(and(
            eq(billingSubscriptions.id, id),
            sql`${billingSubscriptions.status} IN ('pending', 'active')`
          ))
      }

      // Send notifications (implement based on your notification system)
      // await this.sendStatusChangeNotification(subscriptionId, fromStatus, toStatus)

      // Update client access permissions if needed
      // await this.updateClientAccess(subscriptionId, toStatus)

    } catch (error) {
      console.error('Error in post-transition actions:', error)
      // Don't throw here to avoid rolling back the main transition
    }
  }

  /**
   * Get subscription renewal information
   */
  static async getRenewalInfo(id: string): Promise<{
    isEligible: boolean
    nextRenewalDate: Date
    renewalAmount: number
    restrictions: string[]
  } | null> {
    try {
      const statusInfo = await this.getSubscriptionStatusInfo(id)
      if (!statusInfo) return null

      const isEligible = ['active', 'expired'].includes(statusInfo.currentStatus)
      const nextRenewalDate = new Date(statusInfo.nextBillingDate)
      
      // Get renewal amount from subscription
      const [subscription] = await db.select({
        monthlyAmount: subscriptions.monthlyAmount,
        billingCycle: subscriptions.billingCycle
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, id))
      .limit(1)

      const renewalAmount = subscription ? parseFloat(subscription.monthlyAmount) : 0
      const restrictions = isEligible ? [] : ['Subscription must be active or expired to renew']

      return {
        isEligible,
        nextRenewalDate,
        renewalAmount,
        restrictions
      }

    } catch (error) {
      console.error('Error getting renewal info:', error)
      return null
    }
  }
}
