/**
 * Comprehensive Authentication Flow Testing Script
 * Tests all authentication scenarios for school, admin, and partner users
 */

const { neon } = require('@neondatabase/serverless')
const jwt = require('jsonwebtoken')
const crypto = require('crypto')

const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const sql = neon(connectionString)

// Test data for authentication flows
const TEST_DATA = {
  school: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    schoolName: 'Auth Test School',
    role: 'admin'
  },
  admin: {
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    name: 'Test Admin',
    role: 'super_admin'
  },
  partner: {
    email: '<EMAIL>',
    password: 'PartnerPassword123!',
    name: 'Test Partner',
    companyName: 'Auth Test Company'
  }
}

// Token generation functions (matching the actual implementation)
function generateSchoolToken(userId, email, role, clientId, schoolName, permissions = []) {
  const payload = {
    userId,
    email,
    role,
    clientId,
    schoolName,
    permissions,
    type: 'school',
    jti: crypto.randomUUID(),
    iat: Math.floor(Date.now() / 1000),
    iss: 'schopio-school',
    aud: 'schopio-school-portal'
  }

  return jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', {
    expiresIn: '12h',
    algorithm: 'HS256'
  })
}

function generateAdminToken(adminId, email, role, permissions) {
  const payload = {
    userId: adminId,
    email,
    role,
    permissions,
    type: 'admin',
    jti: crypto.randomUUID(),
    iat: Math.floor(Date.now() / 1000),
    iss: 'schopio-admin',
    aud: 'schopio-admin-panel'
  }

  return jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', {
    expiresIn: '8h',
    algorithm: 'HS256'
  })
}

function generatePartnerToken(partnerId, email, companyName, partnerCode) {
  const payload = {
    userId: partnerId,
    email,
    companyName,
    partnerCode,
    type: 'partner',
    jti: crypto.randomUUID(),
    iat: Math.floor(Date.now() / 1000),
    iss: 'schopio-partner',
    aud: 'schopio-partner-portal'
  }

  return jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret-key', {
    expiresIn: '7d',
    algorithm: 'HS256'
  })
}

// Token validation function
function validateToken(token, expectedType) {
  try {
    const issuerMap = {
      school: 'schopio-school',
      admin: 'schopio-admin',
      partner: 'schopio-partner'
    }

    const audienceMap = {
      school: 'schopio-school-portal',
      admin: 'schopio-admin-panel',
      partner: 'schopio-partner-portal'
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key', {
      algorithms: ['HS256'],
      issuer: issuerMap[expectedType],
      audience: audienceMap[expectedType]
    })

    if (decoded.type !== expectedType) {
      return null
    }

    return decoded
  } catch (error) {
    return null
  }
}

async function testAuthenticationFlows() {
  try {
    console.log('🔐 Testing Comprehensive Authentication Flows...')
    console.log('=' .repeat(60))

    // 1. Test School User Authentication
    console.log('\n🏫 Testing School User Authentication...')
    await testSchoolAuthentication()

    // 2. Test Admin User Authentication
    console.log('\n👨‍💼 Testing Admin User Authentication...')
    await testAdminAuthentication()

    // 3. Test Partner User Authentication
    console.log('\n🤝 Testing Partner User Authentication...')
    await testPartnerAuthentication()

    // 4. Test Token Validation
    console.log('\n🔍 Testing Token Validation...')
    await testTokenValidation()

    // 5. Test Route Protection Logic
    console.log('\n🛡️ Testing Route Protection Logic...')
    await testRouteProtection()

    // 6. Test Edge Cases
    console.log('\n⚠️ Testing Edge Cases...')
    await testEdgeCases()

    console.log('\n🎉 All Authentication Flow Tests Completed!')

  } catch (error) {
    console.error('❌ Authentication flow testing failed:', error)
    process.exit(1)
  }
}

async function testSchoolAuthentication() {
  const timestamp = Date.now().toString().slice(-4)
  
  try {
    // Create test client and user with unique emails
    const schoolEmail = `school${timestamp}@auth.test`

    const [testClient] = await sql`
      INSERT INTO clients (school_code, school_name, email, phone, status, actual_student_count)
      VALUES (${`AUTH${timestamp}`}, ${TEST_DATA.school.schoolName}, ${schoolEmail}, '9876543210', 'active', 100)
      RETURNING id, school_name
    `

    const [testUser] = await sql`
      INSERT INTO client_users (client_id, name, email, password_hash, role, is_active, email_verified)
      VALUES (${testClient.id}, 'Test School User', ${schoolEmail}, 'hashed_password', ${TEST_DATA.school.role}, true, true)
      RETURNING id, email, role
    `

    // Generate and test token
    const token = generateSchoolToken(
      testUser.id,
      testUser.email,
      testUser.role,
      testClient.id,
      testClient.school_name,
      []
    )

    const decoded = validateToken(token, 'school')
    
    console.log(`   ✅ School user created: ${testUser.email}`)
    console.log(`   ✅ Token generated and validated: ${decoded ? 'Valid' : 'Invalid'}`)
    console.log(`   ✅ Token type: ${decoded?.type}`)
    console.log(`   ✅ Token expires: ${new Date(decoded?.exp * 1000).toLocaleString()}`)

    // Test token structure
    const tokenParts = token.split('.')
    console.log(`   ✅ Token structure: ${tokenParts.length === 3 ? 'Valid JWT' : 'Invalid'}`)

    return { testClient, testUser, token }

  } catch (error) {
    console.error('   ❌ School authentication test failed:', error)
    throw error
  }
}

async function testAdminAuthentication() {
  const timestamp = Date.now().toString().slice(-4)
  
  try {
    // Create test admin user
    const [testAdmin] = await sql`
      INSERT INTO admin_users (name, email, password_hash, role, permissions, is_active)
      VALUES (${TEST_DATA.admin.name}, ${`admin${timestamp}@auth.test`}, 'hashed_password', ${TEST_DATA.admin.role}, '["*"]', true)
      RETURNING id, email, role, permissions
    `

    // Generate and test token
    const token = generateAdminToken(
      testAdmin.id,
      testAdmin.email,
      testAdmin.role,
      testAdmin.permissions || ['*']
    )

    const decoded = validateToken(token, 'admin')
    
    console.log(`   ✅ Admin user created: ${testAdmin.email}`)
    console.log(`   ✅ Token generated and validated: ${decoded ? 'Valid' : 'Invalid'}`)
    console.log(`   ✅ Token type: ${decoded?.type}`)
    console.log(`   ✅ Admin role: ${decoded?.role}`)
    console.log(`   ✅ Token expires: ${new Date(decoded?.exp * 1000).toLocaleString()}`)

    return { testAdmin, token }

  } catch (error) {
    console.error('   ❌ Admin authentication test failed:', error)
    throw error
  }
}

async function testPartnerAuthentication() {
  const timestamp = Date.now().toString().slice(-4)
  
  try {
    // Get admin user for created_by field
    const [adminUser] = await sql`SELECT id FROM admin_users LIMIT 1`
    
    // Create test partner (partner_code has 8 char limit)
    const [testPartner] = await sql`
      INSERT INTO partners (partner_code, name, email, password_hash, company_name, phone, address, profit_share_percentage, is_active, created_by)
      VALUES (${`PA${timestamp.slice(-4)}`}, ${TEST_DATA.partner.name}, ${`partner${timestamp}@auth.test`}, 'hashed_password', ${TEST_DATA.partner.companyName}, '9876543210', 'Test Address', 25.00, true, ${adminUser.id})
      RETURNING id, email, company_name, partner_code
    `

    // Generate and test token
    const token = generatePartnerToken(
      testPartner.id,
      testPartner.email,
      testPartner.company_name,
      testPartner.partner_code
    )

    const decoded = validateToken(token, 'partner')
    
    console.log(`   ✅ Partner created: ${testPartner.email}`)
    console.log(`   ✅ Token generated and validated: ${decoded ? 'Valid' : 'Invalid'}`)
    console.log(`   ✅ Token type: ${decoded?.type}`)
    console.log(`   ✅ Partner code: ${decoded?.partnerCode}`)
    console.log(`   ✅ Token expires: ${new Date(decoded?.exp * 1000).toLocaleString()}`)

    return { testPartner, token }

  } catch (error) {
    console.error('   ❌ Partner authentication test failed:', error)
    throw error
  }
}

async function testTokenValidation() {
  try {
    // Test valid tokens
    const schoolToken = generateSchoolToken('test-id', '<EMAIL>', 'admin', 'client-id', 'Test School')
    const adminToken = generateAdminToken('admin-id', '<EMAIL>', 'super_admin', ['*'])
    const partnerToken = generatePartnerToken('partner-id', '<EMAIL>', 'Test Company', 'P123')

    console.log(`   ✅ School token validation: ${validateToken(schoolToken, 'school') ? 'Valid' : 'Invalid'}`)
    console.log(`   ✅ Admin token validation: ${validateToken(adminToken, 'admin') ? 'Valid' : 'Invalid'}`)
    console.log(`   ✅ Partner token validation: ${validateToken(partnerToken, 'partner') ? 'Valid' : 'Invalid'}`)

    // Test cross-type validation (should fail)
    console.log(`   ✅ Cross-type validation (school->admin): ${validateToken(schoolToken, 'admin') ? 'Invalid Test' : 'Correctly Rejected'}`)
    console.log(`   ✅ Cross-type validation (admin->partner): ${validateToken(adminToken, 'partner') ? 'Invalid Test' : 'Correctly Rejected'}`)

    // Test expired token
    const expiredToken = jwt.sign(
      { userId: 'test', email: '<EMAIL>', type: 'school', exp: Math.floor(Date.now() / 1000) - 3600 },
      process.env.JWT_SECRET || 'fallback-secret-key'
    )
    console.log(`   ✅ Expired token validation: ${validateToken(expiredToken, 'school') ? 'Invalid Test' : 'Correctly Rejected'}`)

    // Test malformed token
    console.log(`   ✅ Malformed token validation: ${validateToken('invalid.token.here', 'school') ? 'Invalid Test' : 'Correctly Rejected'}`)

  } catch (error) {
    console.error('   ❌ Token validation test failed:', error)
    throw error
  }
}

async function testRouteProtection() {
  try {
    // Test route matching logic
    const protectedRoutes = {
      school: ['/school/dashboard', '/school/students', '/school/settings'],
      admin: ['/admin/dashboard', '/admin/clients', '/admin/analytics'],
      partner: ['/partner/dashboard', '/partner/analytics', '/partner/support']
    }

    const publicRoutes = {
      school: ['/school/login', '/school/signup', '/school/forgot-password'],
      admin: ['/admin/login'],
      partner: ['/partner/login', '/partner/signup', '/partner/forgot-password']
    }

    function matchesRoute(pathname, patterns) {
      return patterns.some(pattern => {
        if (pattern.endsWith('*')) {
          return pathname.startsWith(pattern.slice(0, -1))
        }
        return pathname === pattern || pathname.startsWith(pattern + '/')
      })
    }

    // Test protected route matching
    console.log(`   ✅ School protected route match: ${matchesRoute('/school/dashboard', ['/school']) ? 'Matched' : 'Not Matched'}`)
    console.log(`   ✅ Admin protected route match: ${matchesRoute('/admin/analytics', ['/admin']) ? 'Matched' : 'Not Matched'}`)
    console.log(`   ✅ Partner protected route match: ${matchesRoute('/partner/support', ['/partner']) ? 'Matched' : 'Not Matched'}`)

    // Test public route exclusion
    console.log(`   ✅ School login exclusion: ${matchesRoute('/school/login', publicRoutes.school) ? 'Correctly Excluded' : 'Not Excluded'}`)
    console.log(`   ✅ Admin login exclusion: ${matchesRoute('/admin/login', publicRoutes.admin) ? 'Correctly Excluded' : 'Not Excluded'}`)
    console.log(`   ✅ Partner signup exclusion: ${matchesRoute('/partner/signup', publicRoutes.partner) ? 'Correctly Excluded' : 'Not Excluded'}`)

  } catch (error) {
    console.error('   ❌ Route protection test failed:', error)
    throw error
  }
}

async function testEdgeCases() {
  try {
    // Test empty token
    console.log(`   ✅ Empty token validation: ${validateToken('', 'school') ? 'Invalid Test' : 'Correctly Rejected'}`)
    
    // Test null token
    console.log(`   ✅ Null token validation: ${validateToken(null, 'school') ? 'Invalid Test' : 'Correctly Rejected'}`)
    
    // Test token with wrong secret
    const wrongSecretToken = jwt.sign(
      { userId: 'test', email: '<EMAIL>', type: 'school' },
      'wrong-secret'
    )
    console.log(`   ✅ Wrong secret token: ${validateToken(wrongSecretToken, 'school') ? 'Invalid Test' : 'Correctly Rejected'}`)

    // Test token without required fields
    const incompleteToken = jwt.sign(
      { email: '<EMAIL>' }, // Missing userId and type
      process.env.JWT_SECRET || 'fallback-secret-key'
    )
    console.log(`   ✅ Incomplete token: ${validateToken(incompleteToken, 'school') ? 'Invalid Test' : 'Correctly Rejected'}`)

    // Test token expiration check
    const almostExpiredToken = jwt.sign(
      { 
        userId: 'test', 
        email: '<EMAIL>', 
        type: 'school',
        exp: Math.floor(Date.now() / 1000) + 60 // Expires in 1 minute
      },
      process.env.JWT_SECRET || 'fallback-secret-key'
    )
    
    const decoded = validateToken(almostExpiredToken, 'school')
    const timeUntilExpiry = decoded ? decoded.exp - Math.floor(Date.now() / 1000) : 0
    console.log(`   ✅ Almost expired token: Valid for ${timeUntilExpiry} seconds`)

  } catch (error) {
    console.error('   ❌ Edge cases test failed:', error)
    throw error
  }
}

// Run the tests
testAuthenticationFlows()
