import RoleBasedFeaturesSection from '@/components/sections/RoleBasedFeaturesSection'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Complete School Management Solutions & ERP System | Schopio',
  description: 'Comprehensive school management solutions and ERP system for educational institutions. Student information system, academic management, billing, and administration tools in one platform.',
  keywords: 'school management solutions, educational ERP system, school administration software, student information system, school management system, educational management software, school ERP software, academic management system',
  openGraph: {
    title: 'Complete School Management Solutions & ERP System | Schopio',
    description: 'Comprehensive school management solutions and ERP system for educational institutions. Student information system, academic management, billing, and administration tools in one platform.',
    type: 'website',
  },
}

export default function SolutionsPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-br from-blue-50 to-emerald-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-6">
              <span className="w-2 h-2 bg-emerald-600 rounded-full"></span>
              Complete School Management Solutions
            </div>
            <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 mb-6">
              Best School Management System &
              <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> ERP Software</span>
            </h1>
            <p className="text-xl text-slate-600 leading-relaxed">
              Schopio is India&apos;s leading school management system and educational ERP software.
              Complete school administration software with student information system, academic management,
              billing, and comprehensive educational management tools designed for modern schools.
            </p>
          </div>
        </div>
      </section>

      {/* Role-Based Features Section */}
      <RoleBasedFeaturesSection />

      {/* Additional Benefits Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
              One Platform, Multiple Perspectives
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Schopio&apos;s unified platform ensures seamless communication and data flow between all stakeholders 
              while providing each role with the specific tools and insights they need to excel.
            </p>
          </div>
        </div>
      </section>
    </main>
  )
}
