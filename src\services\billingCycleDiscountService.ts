import { db } from '@/src/db'
import {
  billingSubscriptions,
  billingTransactions
} from '@/src/db/schema'
import { eq, and } from 'drizzle-orm'

export interface BillingCycleDiscountResult {
  discountExpired: boolean
  completedCycles: number
  totalCycles: number
  remainingCycles: number
}

class BillingCycleDiscountService {
  /**
   * Track billing cycle completion and check for discount expiration
   * This should be called whenever a subscription payment is completed
   */
  async trackBillingCycleCompletion(subscriptionId: string, transactionId: string): Promise<BillingCycleDiscountResult> {
    try {
      console.log(`🔄 [Billing Cycle] Tracking cycle completion for subscription ${subscriptionId}`)

      // Get current subscription discount status
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        hasActiveDiscount: billingSubscriptions.hasActiveDiscount,
        discountDurationCycles: billingSubscriptions.discountDurationCycles,
        discountCompletedCycles: billingSubscriptions.discountCompletedCycles,
        originalMonthlyAmount: billingSubscriptions.originalMonthlyAmount,
        currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage,
        discountReason: billingSubscriptions.discountReason
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      // If no active discount, nothing to track
      if (!subscription.hasActiveDiscount || !subscription.discountDurationCycles) {
        return {
          discountExpired: false,
          completedCycles: 0,
          totalCycles: 0,
          remainingCycles: 0
        }
      }

      const totalCycles = subscription.discountDurationCycles
      const currentCompletedCycles = subscription.discountCompletedCycles || 0
      const newCompletedCycles = currentCompletedCycles + 1

      console.log(`📊 [Billing Cycle] Discount progress: ${newCompletedCycles}/${totalCycles} cycles completed`)

      // Check if discount should expire
      const discountExpired = newCompletedCycles >= totalCycles

      await db.transaction(async (tx) => {
        if (discountExpired) {
          // Expire the discount and restore original amount
          console.log(`⏰ [Discount Expired] Restoring original amount for subscription ${subscriptionId}`)
          
          await tx.update(billingSubscriptions)
            .set({
              hasActiveDiscount: false,
              currentDiscountPercentage: null,
              discountStartDate: null,
              discountEndDate: null,
              monthlyAmount: subscription.originalMonthlyAmount,
              originalMonthlyAmount: null,
              discountReason: null,
              discountDurationCycles: null,
              discountCompletedCycles: null,
              updatedAt: new Date()
            })
            .where(eq(billingSubscriptions.id, subscriptionId))

          console.log(`✅ [Discount Expired] Subscription ${subscriptionId} restored to original amount: ${subscription.originalMonthlyAmount}`)
        } else {
          // Update completed cycles count
          await tx.update(billingSubscriptions)
            .set({
              discountCompletedCycles: newCompletedCycles,
              updatedAt: new Date()
            })
            .where(eq(billingSubscriptions.id, subscriptionId))

          console.log(`📈 [Billing Cycle] Updated completed cycles: ${newCompletedCycles}/${totalCycles}`)
        }
      })

      return {
        discountExpired,
        completedCycles: newCompletedCycles,
        totalCycles,
        remainingCycles: Math.max(0, totalCycles - newCompletedCycles)
      }

    } catch (error) {
      console.error('Error tracking billing cycle completion:', error)
      throw error
    }
  }

  /**
   * Get discount progress for a subscription
   */
  async getDiscountProgress(subscriptionId: string): Promise<BillingCycleDiscountResult> {
    try {
      const [subscription] = await db.select({
        hasActiveDiscount: billingSubscriptions.hasActiveDiscount,
        discountDurationCycles: billingSubscriptions.discountDurationCycles,
        discountCompletedCycles: billingSubscriptions.discountCompletedCycles
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription || !subscription.hasActiveDiscount) {
        return {
          discountExpired: false,
          completedCycles: 0,
          totalCycles: 0,
          remainingCycles: 0
        }
      }

      const totalCycles = subscription.discountDurationCycles || 0
      const completedCycles = subscription.discountCompletedCycles || 0
      const remainingCycles = Math.max(0, totalCycles - completedCycles)

      return {
        discountExpired: completedCycles >= totalCycles,
        completedCycles,
        totalCycles,
        remainingCycles
      }

    } catch (error) {
      console.error('Error getting discount progress:', error)
      throw error
    }
  }

  /**
   * Check all subscriptions for discount expiration based on billing cycles
   * This can be used as a backup check or for manual verification
   */
  async checkAllDiscountExpirations(): Promise<{
    expiredCount: number
    expiredSubscriptions: string[]
  }> {
    try {
      console.log('🔍 [Billing Cycle] Checking all subscriptions for discount expiration')

      // Get all subscriptions with active discounts
      const subscriptionsWithDiscounts = await db.select({
        id: billingSubscriptions.id,
        discountDurationCycles: billingSubscriptions.discountDurationCycles,
        discountCompletedCycles: billingSubscriptions.discountCompletedCycles,
        originalMonthlyAmount: billingSubscriptions.originalMonthlyAmount
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.hasActiveDiscount, true))

      const expiredSubscriptions: string[] = []

      for (const subscription of subscriptionsWithDiscounts) {
        const totalCycles = subscription.discountDurationCycles || 0
        const completedCycles = subscription.discountCompletedCycles || 0

        if (completedCycles >= totalCycles && totalCycles > 0) {
          // This discount should be expired
          await db.update(billingSubscriptions)
            .set({
              hasActiveDiscount: false,
              currentDiscountPercentage: null,
              discountStartDate: null,
              discountEndDate: null,
              monthlyAmount: subscription.originalMonthlyAmount,
              originalMonthlyAmount: null,
              discountReason: null,
              discountDurationCycles: null,
              discountCompletedCycles: null,
              updatedAt: new Date()
            })
            .where(eq(billingSubscriptions.id, subscription.id))

          expiredSubscriptions.push(subscription.id)
          console.log(`⏰ [Expired] Subscription ${subscription.id} discount expired after ${completedCycles} cycles`)
        }
      }

      console.log(`✅ [Billing Cycle Check] Processed ${subscriptionsWithDiscounts.length} subscriptions, expired ${expiredSubscriptions.length}`)

      return {
        expiredCount: expiredSubscriptions.length,
        expiredSubscriptions
      }

    } catch (error) {
      console.error('Error checking discount expirations:', error)
      throw error
    }
  }

  /**
   * Manually expire a discount (admin action)
   */
  async manuallyExpireDiscount(subscriptionId: string, adminId?: string): Promise<void> {
    try {
      console.log(`🛑 [Manual Expiration] Admin expiring discount for subscription ${subscriptionId}`)

      const [subscription] = await db.select({
        originalMonthlyAmount: billingSubscriptions.originalMonthlyAmount,
        hasActiveDiscount: billingSubscriptions.hasActiveDiscount
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription || !subscription.hasActiveDiscount) {
        throw new Error('No active discount found for this subscription')
      }

      await db.update(billingSubscriptions)
        .set({
          hasActiveDiscount: false,
          currentDiscountPercentage: null,
          discountStartDate: null,
          discountEndDate: null,
          monthlyAmount: subscription.originalMonthlyAmount,
          originalMonthlyAmount: null,
          discountReason: null,
          discountDurationCycles: null,
          discountCompletedCycles: null,
          updatedAt: new Date()
        })
        .where(eq(billingSubscriptions.id, subscriptionId))

      console.log(`✅ [Manual Expiration] Discount manually expired for subscription ${subscriptionId}`)

    } catch (error) {
      console.error('Error manually expiring discount:', error)
      throw error
    }
  }
}

export const billingCycleDiscountService = new BillingCycleDiscountService()
export default billingCycleDiscountService
