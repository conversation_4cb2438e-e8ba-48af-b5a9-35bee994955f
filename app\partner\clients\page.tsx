'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Users, 
  Search, 
  School,
  Calendar,
  DollarSign,
  TrendingUp,
  Eye,
  Mail,
  Phone,
  MapPin,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'

interface Client {
  id: string
  school: {
    name: string
    email: string
    contactPerson: string
    phone: string
    address: string
    studentCount: number
    status: 'active' | 'inactive' | 'pending'
  }
  referralDate: string
  subscription: {
    id: string
    status: 'active' | 'inactive' | 'cancelled'
    plan: string
    pricePerStudent: number
    monthlyAmount: number
    startDate: string
    endDate: string | null
    studentsCount: number
  } | null
  earnings: {
    totalEarned: number
    monthlyEarning: number
    lastPaymentDate: string | null
  }
  supportTickets: {
    total: number
    open: number
    resolved: number
  }
}

interface ClientsResponse {
  success: boolean
  data: {
    clients: Client[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    summary: {
      totalClients: number
      activeSubscriptions: number
      totalMonthlyRevenue: number
      totalEarnings: number
    }
  }
}

export default function PartnerClientsPage() {
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [subscriptionFilter, setSubscriptionFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<any>(null)
  const [summary, setSummary] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem('partnerToken')
    if (!token) {
      router.push('/partner/login')
      return
    }

    fetchClients()
  }, [router, currentPage, statusFilter, subscriptionFilter, searchTerm])

  const fetchClients = async () => {
    try {
      const token = localStorage.getItem('partnerToken')
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(statusFilter !== 'all' && { status: statusFilter }),
        ...(subscriptionFilter !== 'all' && { subscription: subscriptionFilter }),
        ...(searchTerm && { search: searchTerm })
      })

      const response = await fetch(`/api/partner/clients?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('partnerToken')
          localStorage.removeItem('partner')
          router.push('/partner/login')
          return
        }
        throw new Error('Failed to fetch clients')
      }

      const data: ClientsResponse = await response.json()
      setClients(data.data.clients)
      setPagination(data.data.pagination)
      setSummary(data.data.summary)
    } catch (error) {
      console.error('Clients error:', error)
      setError('Failed to load clients')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      active: 'default',
      inactive: 'secondary',
      pending: 'outline',
      cancelled: 'destructive'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'inactive':
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading clients...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Referred Schools</h1>
          <p className="text-gray-600 mt-1">Manage your referred schools and track their subscriptions</p>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Schools</p>
                  <p className="text-3xl font-bold text-gray-900">{summary.totalClients}</p>
                </div>
                <School className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Subscriptions</p>
                  <p className="text-3xl font-bold text-gray-900">{summary.activeSubscriptions}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                  <p className="text-3xl font-bold text-gray-900">₹{summary.totalMonthlyRevenue}</p>
                </div>
                <DollarSign className="w-8 h-8 text-emerald-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                  <p className="text-3xl font-bold text-gray-900">₹{summary.totalEarnings}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search schools..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-4">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="School Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
              <Select value={subscriptionFilter} onValueChange={setSubscriptionFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Subscription" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subscriptions</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="none">No Subscription</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Clients List */}
      <Card>
        <CardHeader>
          <CardTitle>Schools</CardTitle>
          <CardDescription>
            {pagination && `Showing ${((pagination.page - 1) * pagination.limit) + 1}-${Math.min(pagination.page * pagination.limit, pagination.total)} of ${pagination.total} schools`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={fetchClients}>Try Again</Button>
            </div>
          ) : clients.length > 0 ? (
            <div className="space-y-6">
              {clients.map((client) => (
                <div key={client.id} className="border rounded-lg p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStatusIcon(client.school.status)}
                        <h3 className="text-xl font-semibold text-gray-900">{client.school.name}</h3>
                        {getStatusBadge(client.school.status)}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4" />
                          {client.school.email}
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4" />
                          {client.school.phone}
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          {client.school.studentCount} students
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          {client.school.address}
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          Referred {new Date(client.referralDate).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Subscription Info */}
                    <Card className="bg-blue-50 border-blue-200">
                      <CardContent className="p-4">
                        <h4 className="font-medium text-blue-900 mb-2">Subscription</h4>
                        {client.subscription ? (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-blue-700">Status:</span>
                              {getStatusBadge(client.subscription.status)}
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-blue-700">Plan:</span>
                              <span className="text-sm font-medium text-blue-900">{client.subscription.plan}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-blue-700">Price/Student:</span>
                              <span className="text-sm font-medium text-blue-900">₹{client.subscription.pricePerStudent || 'N/A'}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-blue-700">Monthly:</span>
                              <span className="text-sm font-medium text-blue-900">₹{client.subscription.monthlyAmount}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-blue-700">Students:</span>
                              <span className="text-sm font-medium text-blue-900">{client.subscription.studentsCount}</span>
                            </div>
                          </div>
                        ) : (
                          <p className="text-sm text-blue-700">No active subscription</p>
                        )}
                      </CardContent>
                    </Card>

                    {/* Earnings Info */}
                    <Card className="bg-green-50 border-green-200">
                      <CardContent className="p-4">
                        <h4 className="font-medium text-green-900 mb-2">Your Earnings</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-green-700">Total Earned:</span>
                            <span className="text-sm font-medium text-green-900">₹{client.earnings.totalEarned}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-green-700">Monthly:</span>
                            <span className="text-sm font-medium text-green-900">₹{client.earnings.monthlyEarning}</span>
                          </div>
                          {client.earnings.lastPaymentDate && (
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-green-700">Last Payment:</span>
                              <span className="text-sm font-medium text-green-900">
                                {new Date(client.earnings.lastPaymentDate).toLocaleDateString()}
                              </span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Support Info */}
                    <Card className="bg-purple-50 border-purple-200">
                      <CardContent className="p-4">
                        <h4 className="font-medium text-purple-900 mb-2">Support Tickets</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-purple-700">Total:</span>
                            <span className="text-sm font-medium text-purple-900">{client.supportTickets.total}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-purple-700">Open:</span>
                            <span className="text-sm font-medium text-purple-900">{client.supportTickets.open}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-purple-700">Resolved:</span>
                            <span className="text-sm font-medium text-purple-900">{client.supportTickets.resolved}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <School className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No schools found</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(pagination.page - 1)}
            disabled={pagination.page <= 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4 text-sm text-gray-600">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(pagination.page + 1)}
            disabled={pagination.page >= pagination.totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
