#!/usr/bin/env node

/**
 * Comprehensive System Test Suite for Schopio
 * 
 * This test suite covers all major functionality including:
 * - Authentication & Authorization
 * - Billing System & Subscription Management
 * - Partner Management & Commission System
 * - School Portal Functionality
 * - Admin Dashboard Operations
 * - Payment Gateway Integration
 * - Email & PDF Generation Services
 * - Error Handling & Edge Cases
 */

require('dotenv').config({ path: '.env.local' });
const fetch = require('node-fetch');

// Test Configuration
const TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
  adminEmail: process.env.TEST_ADMIN_EMAIL || '<EMAIL>',
  adminPassword: process.env.TEST_ADMIN_PASSWORD || 'Admin@123456',
  schoolEmail: process.env.TEST_SCHOOL_EMAIL || '<EMAIL>',
  schoolPassword: process.env.TEST_SCHOOL_PASSWORD || 'password123',
  partnerEmail: process.env.TEST_PARTNER_EMAIL || '<EMAIL>',
  partnerPassword: process.env.TEST_PARTNER_PASSWORD || 'partner123',
  timeout: 30000 // 30 seconds timeout for each test
};

// Test Results Tracking
let testResults = {
  passed: 0,
  failed: 0,
  skipped: 0,
  errors: [],
  startTime: Date.now(),
  categories: {
    authentication: { passed: 0, failed: 0, total: 0 },
    billing: { passed: 0, failed: 0, total: 0 },
    partner: { passed: 0, failed: 0, total: 0 },
    school: { passed: 0, failed: 0, total: 0 },
    admin: { passed: 0, failed: 0, total: 0 },
    integration: { passed: 0, failed: 0, total: 0 }
  }
};

// Test tokens
let adminToken = '';
let schoolToken = '';
let partnerToken = '';
let testData = {
  subscriptionId: '',
  invoiceId: '',
  partnerId: '',
  clientId: ''
};

/**
 * Utility function to make HTTP requests with timeout
 */
async function makeRequest(url, options = {}) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), TEST_CONFIG.timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error(`Request timeout after ${TEST_CONFIG.timeout}ms`);
    }
    throw error;
  }
}

/**
 * Test runner function
 */
async function runTest(testName, testFunction, category = 'general') {
  testResults.categories[category].total++;
  
  try {
    console.log(`🧪 Running: ${testName}`);
    await testFunction();
    console.log(`✅ PASSED: ${testName}`);
    testResults.passed++;
    testResults.categories[category].passed++;
    return true;
  } catch (error) {
    console.log(`❌ FAILED: ${testName}`);
    console.log(`   Error: ${error.message}`);
    testResults.failed++;
    testResults.categories[category].failed++;
    testResults.errors.push({
      test: testName,
      error: error.message,
      category
    });
    return false;
  }
}

/**
 * Authentication Tests
 */
async function testAdminAuthentication() {
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/admin/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: TEST_CONFIG.adminEmail,
      password: TEST_CONFIG.adminPassword
    })
  });

  const data = await response.json();
  
  if (!response.ok || !data.token) {
    throw new Error(`Admin login failed: ${data.error || 'Unknown error'}`);
  }
  
  adminToken = data.token;
  console.log(`   Admin token acquired: ${adminToken.substring(0, 20)}...`);
}

async function testSchoolAuthentication() {
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/school/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: TEST_CONFIG.schoolEmail,
      password: TEST_CONFIG.schoolPassword
    })
  });

  const data = await response.json();
  
  if (!response.ok || !data.token) {
    throw new Error(`School login failed: ${data.error || 'Unknown error'}`);
  }
  
  schoolToken = data.token;
  testData.clientId = data.user?.clientId;
  console.log(`   School token acquired: ${schoolToken.substring(0, 20)}...`);
}

async function testPartnerAuthentication() {
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/partner/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: TEST_CONFIG.partnerEmail,
      password: TEST_CONFIG.partnerPassword
    })
  });

  const data = await response.json();
  
  if (!response.ok || !data.token) {
    throw new Error(`Partner login failed: ${data.error || 'Unknown error'}`);
  }
  
  partnerToken = data.token;
  testData.partnerId = data.partner?.id;
  console.log(`   Partner token acquired: ${partnerToken.substring(0, 20)}...`);
}

/**
 * Billing System Tests
 */
async function testSubscriptionCreation() {
  if (!adminToken) throw new Error('Admin token required');
  
  const subscriptionData = {
    clientId: testData.clientId || 'test-client-id',
    studentCount: 100,
    pricePerStudent: 50,
    billingCycle: 'monthly',
    startDate: new Date().toISOString().split('T')[0],
    dueDate: 15,
    gracePeriodDays: 3,
    operationalExpenses: {
      databaseCosts: 500,
      websiteMaintenance: 300,
      supportCosts: 200,
      infrastructureCosts: 400
    }
  };

  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/admin/subscriptions`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(subscriptionData)
  });

  const data = await response.json();
  
  if (!response.ok || !data.success) {
    throw new Error(`Subscription creation failed: ${data.error || 'Unknown error'}`);
  }
  
  testData.subscriptionId = data.subscription?.id;
  console.log(`   Subscription created: ${testData.subscriptionId}`);
}

async function testInvoiceGeneration() {
  if (!adminToken || !testData.subscriptionId) {
    throw new Error('Admin token and subscription ID required');
  }
  
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/admin/billing/generate-invoice`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      subscriptionId: testData.subscriptionId
    })
  });

  const data = await response.json();
  
  if (!response.ok || !data.success) {
    throw new Error(`Invoice generation failed: ${data.error || 'Unknown error'}`);
  }
  
  testData.invoiceId = data.invoice?.id;
  console.log(`   Invoice generated: ${testData.invoiceId}`);
}

/**
 * Partner System Tests
 */
async function testPartnerDashboard() {
  if (!partnerToken) throw new Error('Partner token required');
  
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/partner/dashboard`, {
    headers: { 'Authorization': `Bearer ${partnerToken}` }
  });

  const data = await response.json();
  
  if (!response.ok || !data.success) {
    throw new Error(`Partner dashboard access failed: ${data.error || 'Unknown error'}`);
  }
  
  console.log(`   Partner dashboard data retrieved successfully`);
}

async function testPartnerEarnings() {
  if (!partnerToken) throw new Error('Partner token required');
  
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/partner/earnings`, {
    headers: { 'Authorization': `Bearer ${partnerToken}` }
  });

  const data = await response.json();
  
  if (!response.ok || !data.success) {
    throw new Error(`Partner earnings access failed: ${data.error || 'Unknown error'}`);
  }
  
  console.log(`   Partner earnings data retrieved successfully`);
}

/**
 * School Portal Tests
 */
async function testSchoolBilling() {
  if (!schoolToken) throw new Error('School token required');
  
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/school/billing`, {
    headers: { 'Authorization': `Bearer ${schoolToken}` }
  });

  const data = await response.json();
  
  if (!response.ok || !data.success) {
    throw new Error(`School billing access failed: ${data.error || 'Unknown error'}`);
  }
  
  console.log(`   School billing data retrieved successfully`);
}

async function testInvoiceDownload() {
  if (!schoolToken || !testData.invoiceId) {
    throw new Error('School token and invoice ID required');
  }
  
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/school/billing/invoice/${testData.invoiceId}/pdf`, {
    headers: { 'Authorization': `Bearer ${schoolToken}` }
  });

  if (!response.ok) {
    throw new Error(`Invoice download failed: ${response.status} ${response.statusText}`);
  }
  
  const contentType = response.headers.get('content-type');
  if (!contentType || !contentType.includes('application/pdf')) {
    throw new Error('Invalid PDF response');
  }
  
  console.log(`   Invoice PDF downloaded successfully`);
}

/**
 * Admin Dashboard Tests
 */
async function testAdminFinancials() {
  if (!adminToken) throw new Error('Admin token required');
  
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/admin/earnings`, {
    headers: { 'Authorization': `Bearer ${adminToken}` }
  });

  const data = await response.json();
  
  if (!response.ok || !data.success) {
    throw new Error(`Admin financials access failed: ${data.error || 'Unknown error'}`);
  }
  
  console.log(`   Admin financial data retrieved successfully`);
}

async function testBillingDashboard() {
  if (!adminToken) throw new Error('Admin token required');
  
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/admin/billing/dashboard`, {
    headers: { 'Authorization': `Bearer ${adminToken}` }
  });

  const data = await response.json();
  
  if (!response.ok || !data.success) {
    throw new Error(`Billing dashboard access failed: ${data.error || 'Unknown error'}`);
  }
  
  console.log(`   Billing dashboard data retrieved successfully`);
}

/**
 * Integration Tests
 */
async function testSystemHealth() {
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/health`);
  
  if (response.status !== 200 && response.status !== 404) {
    throw new Error(`System health check failed: ${response.status}`);
  }
  
  console.log(`   System health check passed`);
}

async function testDatabaseConnection() {
  if (!adminToken) throw new Error('Admin token required');
  
  // Test database connection by fetching admin dashboard data
  const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/admin/dashboard`, {
    headers: { 'Authorization': `Bearer ${adminToken}` }
  });

  if (!response.ok) {
    throw new Error(`Database connection test failed: ${response.status}`);
  }
  
  console.log(`   Database connection test passed`);
}

/**
 * Main test execution function
 */
async function runComprehensiveSystemTests() {
  console.log('🚀 Starting Comprehensive System Test Suite');
  console.log('='.repeat(80));
  console.log(`🌐 Base URL: ${TEST_CONFIG.baseUrl}`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
  console.log('='.repeat(80));

  // Test Categories
  const testCategories = [
    {
      name: '🔐 Authentication & Authorization Tests',
      category: 'authentication',
      tests: [
        { name: 'Admin Authentication', fn: testAdminAuthentication },
        { name: 'School Authentication', fn: testSchoolAuthentication },
        { name: 'Partner Authentication', fn: testPartnerAuthentication }
      ]
    },
    {
      name: '💳 Billing System Tests',
      category: 'billing',
      tests: [
        { name: 'Subscription Creation', fn: testSubscriptionCreation },
        { name: 'Invoice Generation', fn: testInvoiceGeneration }
      ]
    },
    {
      name: '🤝 Partner System Tests',
      category: 'partner',
      tests: [
        { name: 'Partner Dashboard Access', fn: testPartnerDashboard },
        { name: 'Partner Earnings Access', fn: testPartnerEarnings }
      ]
    },
    {
      name: '🏫 School Portal Tests',
      category: 'school',
      tests: [
        { name: 'School Billing Access', fn: testSchoolBilling },
        { name: 'Invoice Download', fn: testInvoiceDownload }
      ]
    },
    {
      name: '👨‍💼 Admin Dashboard Tests',
      category: 'admin',
      tests: [
        { name: 'Admin Financials Access', fn: testAdminFinancials },
        { name: 'Billing Dashboard Access', fn: testBillingDashboard }
      ]
    },
    {
      name: '🔗 Integration Tests',
      category: 'integration',
      tests: [
        { name: 'System Health Check', fn: testSystemHealth },
        { name: 'Database Connection Test', fn: testDatabaseConnection }
      ]
    }
  ];

  // Run all test categories
  for (const category of testCategories) {
    console.log(`\n${category.name}`);
    console.log('-'.repeat(60));
    
    for (const test of category.tests) {
      await runTest(test.name, test.fn, category.category);
    }
  }

  // Print final results
  printTestResults();
}

/**
 * Print comprehensive test results
 */
function printTestResults() {
  const duration = Date.now() - testResults.startTime;
  const total = testResults.passed + testResults.failed;
  const successRate = total > 0 ? ((testResults.passed / total) * 100).toFixed(1) : 0;

  console.log('\n' + '='.repeat(80));
  console.log('📊 COMPREHENSIVE TEST RESULTS');
  console.log('='.repeat(80));
  console.log(`⏱️  Duration: ${(duration / 1000).toFixed(2)} seconds`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`⚠️  Skipped: ${testResults.skipped}`);
  console.log(`📈 Success Rate: ${successRate}%`);
  
  // Category breakdown
  console.log('\n📋 Category Breakdown:');
  for (const [categoryName, stats] of Object.entries(testResults.categories)) {
    if (stats.total > 0) {
      const categoryRate = ((stats.passed / stats.total) * 100).toFixed(1);
      console.log(`   ${categoryName}: ${stats.passed}/${stats.total} (${categoryRate}%)`);
    }
  }

  // Error details
  if (testResults.errors.length > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error.test} (${error.category})`);
      console.log(`      ${error.error}`);
    });
  }

  console.log('='.repeat(80));
  
  // Exit with appropriate code
  const exitCode = testResults.failed > 0 ? 1 : 0;
  if (exitCode === 0) {
    console.log('🎉 All tests passed successfully!');
  } else {
    console.log('💥 Some tests failed. Please review the errors above.');
  }
  
  return exitCode;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runComprehensiveSystemTests()
    .then(exitCode => {
      process.exit(exitCode);
    })
    .catch(error => {
      console.error('❌ Test suite execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runComprehensiveSystemTests,
  testResults,
  TEST_CONFIG
};
