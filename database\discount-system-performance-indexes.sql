-- =====================================================
-- DISCOUNT SYSTEM PERFORMANCE INDEXES
-- Critical indexes for optimal query performance
-- Deploy Date: July 8, 2025
-- =====================================================

-- ===== SUBSCRIPTION DISCOUNTS INDEXES =====

-- Primary lookup: Active discounts for a subscription
CREATE INDEX IF NOT EXISTS idx_subscription_discounts_active 
ON subscription_discounts(subscription_id, is_active) 
WHERE is_active = true;

-- Date range queries for discount expiration
CREATE INDEX IF NOT EXISTS idx_subscription_discounts_dates 
ON subscription_discounts(end_date, is_active) 
WHERE is_active = true;

-- Admin audit queries
CREATE INDEX IF NOT EXISTS idx_subscription_discounts_created_by 
ON subscription_discounts(created_by, created_at);

-- ===== SUBSCRIPTION EXPENSES INDEXES =====

-- Primary lookup: Active expenses for a subscription
CREATE INDEX IF NOT EXISTS idx_subscription_expenses_active 
ON subscription_expenses(subscription_id, is_active) 
WHERE is_active = true;

-- Effective date range queries
CREATE INDEX IF NOT EXISTS idx_subscription_expenses_effective 
ON subscription_expenses(effective_from, effective_until, is_active);

-- Category-based filtering
CREATE INDEX IF NOT EXISTS idx_subscription_expenses_category 
ON subscription_expenses(category, is_active) 
WHERE is_active = true;

-- ===== PARTNER COMMISSION CONFIG INDEXES =====

-- Primary lookup: Active commission config for partner-subscription pair
CREATE INDEX IF NOT EXISTS idx_partner_commission_config_active 
ON partner_commission_config(partner_id, subscription_id, is_active) 
WHERE is_active = true;

-- Partner-based queries
CREATE INDEX IF NOT EXISTS idx_partner_commission_config_partner 
ON partner_commission_config(partner_id, is_active) 
WHERE is_active = true;

-- Subscription-based queries
CREATE INDEX IF NOT EXISTS idx_partner_commission_config_subscription 
ON partner_commission_config(subscription_id, is_active) 
WHERE is_active = true;

-- ===== PARTNER COMMISSION TRANSACTIONS INDEXES =====

-- Status-based queries (most critical for payouts)
CREATE INDEX IF NOT EXISTS idx_commission_transactions_status 
ON partner_commission_transactions(status, eligible_date);

-- Partner payout queries
CREATE INDEX IF NOT EXISTS idx_commission_transactions_partner_status 
ON partner_commission_transactions(partner_id, status, eligible_date);

-- Subscription commission tracking
CREATE INDEX IF NOT EXISTS idx_commission_transactions_subscription 
ON partner_commission_transactions(subscription_id, created_at);

-- Payment reference lookups
CREATE INDEX IF NOT EXISTS idx_commission_transactions_payment 
ON partner_commission_transactions(payment_id);

-- Hold period management
CREATE INDEX IF NOT EXISTS idx_commission_transactions_hold_date 
ON partner_commission_transactions(hold_until_date, status) 
WHERE status = 'held';

-- ===== ADVANCE PAYMENTS INDEXES =====

-- Primary lookup: Remaining advance payments for subscription
CREATE INDEX IF NOT EXISTS idx_advance_payments_remaining 
ON advance_payments(subscription_id, remaining_months) 
WHERE remaining_months > 0;

-- Payment reference lookups
CREATE INDEX IF NOT EXISTS idx_advance_payments_payment 
ON advance_payments(payment_id);

-- Date range queries for advance payment periods
CREATE INDEX IF NOT EXISTS idx_advance_payments_period 
ON advance_payments(start_month, end_month, remaining_months);

-- ===== ENHANCED BILLING SUBSCRIPTIONS INDEXES =====

-- Discount-enabled subscriptions
CREATE INDEX IF NOT EXISTS idx_billing_subscriptions_discount 
ON billing_subscriptions(has_active_discount, discount_end_date) 
WHERE has_active_discount = true;

-- Advance payment balance tracking
CREATE INDEX IF NOT EXISTS idx_billing_subscriptions_advance 
ON billing_subscriptions(advance_months_remaining, client_id) 
WHERE advance_months_remaining > 0;

-- ===== ENHANCED BILLING TRANSACTIONS INDEXES =====

-- Invoice number lookups (for PDF generation)
CREATE INDEX IF NOT EXISTS idx_billing_transactions_invoice 
ON billing_transactions(invoice_number) 
WHERE invoice_number IS NOT NULL;

-- Receipt number lookups
CREATE INDEX IF NOT EXISTS idx_billing_transactions_receipt 
ON billing_transactions(receipt_number) 
WHERE receipt_number IS NOT NULL;

-- Advance payment transactions
CREATE INDEX IF NOT EXISTS idx_billing_transactions_advance 
ON billing_transactions(is_advance_payment, advance_months_covered) 
WHERE is_advance_payment = true;

-- Discount amount tracking
CREATE INDEX IF NOT EXISTS idx_billing_transactions_discount 
ON billing_transactions(subscription_id, discount_amount) 
WHERE discount_amount > 0;

-- ===== COMPOSITE INDEXES FOR COMPLEX QUERIES =====

-- Commission calculation queries (most performance-critical)
CREATE INDEX IF NOT EXISTS idx_commission_calc_complex 
ON partner_commission_transactions(
    partner_id, 
    subscription_id, 
    status, 
    eligible_date, 
    commission_amount
) WHERE status IN ('eligible', 'held');

-- Discount system overview queries
CREATE INDEX IF NOT EXISTS idx_discount_overview 
ON subscription_discounts(
    subscription_id, 
    is_active, 
    discount_percentage, 
    end_date
) WHERE is_active = true;

-- Partner earnings summary queries
CREATE INDEX IF NOT EXISTS idx_partner_earnings_summary 
ON partner_commission_transactions(
    partner_id, 
    status, 
    commission_amount, 
    eligible_date
) WHERE status = 'eligible';

-- ===== PARTIAL INDEXES FOR SPECIFIC USE CASES =====

-- Only active discounts with remaining months
CREATE INDEX IF NOT EXISTS idx_active_discounts_remaining 
ON subscription_discounts(subscription_id, remaining_months, end_date) 
WHERE is_active = true AND remaining_months > 0;

-- Only held commissions approaching release
CREATE INDEX IF NOT EXISTS idx_held_commissions_approaching 
ON partner_commission_transactions(hold_until_date, partner_id, commission_amount) 
WHERE status = 'held' AND hold_until_date <= CURRENT_DATE + INTERVAL '7 days';

-- Only advance payments with significant remaining balance
CREATE INDEX IF NOT EXISTS idx_significant_advance_payments 
ON advance_payments(subscription_id, remaining_months, amount_per_month) 
WHERE remaining_months > 3 AND amount_per_month > 1000;

-- ===== PERFORMANCE MONITORING QUERIES =====

-- Query to check index usage
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch 
-- FROM pg_stat_user_indexes 
-- WHERE indexname LIKE 'idx_%discount%' OR indexname LIKE 'idx_%commission%'
-- ORDER BY idx_scan DESC;

-- Query to identify slow queries on discount tables
-- SELECT query, mean_time, calls, total_time 
-- FROM pg_stat_statements 
-- WHERE query LIKE '%subscription_discounts%' 
--    OR query LIKE '%partner_commission%'
--    OR query LIKE '%advance_payments%'
-- ORDER BY mean_time DESC;

-- ===== INDEX MAINTENANCE =====

-- Analyze tables after index creation for optimal query planning
ANALYZE subscription_discounts;
ANALYZE subscription_expenses;
ANALYZE partner_commission_config;
ANALYZE partner_commission_transactions;
ANALYZE advance_payments;
ANALYZE billing_subscriptions;
ANALYZE billing_transactions;

-- =====================================================
-- DEPLOYMENT NOTES:
-- 1. These indexes are designed for the discount system
-- 2. All indexes use IF NOT EXISTS for safe deployment
-- 3. Partial indexes reduce storage and improve performance
-- 4. Composite indexes support complex business queries
-- 5. Monitor pg_stat_user_indexes for usage statistics
-- =====================================================
