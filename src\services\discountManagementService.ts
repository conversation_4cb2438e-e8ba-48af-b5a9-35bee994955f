import { db } from '@/src/db'
import {
  subscriptionDiscounts,
  subscriptionExpenses,
  billingSubscriptions,
  billingTransactions,
  clients
} from '@/src/db/schema'
import { eq, and, lte, gte, sql, desc } from 'drizzle-orm'
import { discountAuditService } from './discountAuditService'

export interface DiscountApplication {
  hasDiscount: boolean
  originalAmount: number
  discountAmount: number
  finalAmount: number
  discountPercentage?: number
  remainingMonths?: number
}

export interface DiscountDetails {
  id: string
  discountPercentage: number
  remainingMonths: number
  endDate: Date
  monthlySavings: number
  totalSavingsToDate: number
}

export interface ExpenseDetails {
  id: string
  monthlyOperationalCost: number
  description?: string
  category: string
  effectiveFrom: Date
  isActive: boolean
}

class DiscountManagementService {
  /**
   * Apply a new discount to a subscription with enhanced tracking and audit logging
   */
  async applyDiscount(subscriptionId: string, discountData: {
    discountPercentage: number
    durationMonths: number
    startDate: Date
    reason?: string
    adminId?: string
    adminName?: string
  }): Promise<void> {
    try {
      // Get current subscription details
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        hasActiveDiscount: billingSubscriptions.hasActiveDiscount
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      if (subscription.hasActiveDiscount) {
        throw new Error('Subscription already has an active discount')
      }

      const originalAmount = parseFloat(subscription.monthlyAmount)
      const discountAmount = (originalAmount * discountData.discountPercentage) / 100
      const discountedAmount = originalAmount - discountAmount

      const endDate = new Date(discountData.startDate)
      endDate.setMonth(endDate.getMonth() + discountData.durationMonths)

      // ✅ FIXED: Use transaction for atomicity
      await db.transaction(async (tx) => {
        // Update billing subscription with enhanced discount tracking
        await tx.update(billingSubscriptions)
          .set({
            hasActiveDiscount: true,
            currentDiscountPercentage: discountData.discountPercentage.toString(),
            discountStartDate: discountData.startDate.toISOString().split('T')[0],
            discountEndDate: endDate.toISOString().split('T')[0],
            originalMonthlyAmount: originalAmount.toString(),
            monthlyAmount: discountedAmount.toString(),
            discountReason: discountData.reason || 'Admin applied discount',
            updatedAt: new Date()
          })
          .where(eq(billingSubscriptions.id, subscriptionId))

        console.log(`✅ [Discount Applied] Subscription ${subscriptionId}: ${originalAmount} → ${discountedAmount} (${discountData.discountPercentage}% off for ${discountData.durationMonths} months)`)

        // Log the discount application for audit trail within transaction
        if (discountData.adminId && discountData.adminName) {
          await discountAuditService.logDiscountApplication(
            subscriptionId,
            discountData.adminId,
            discountData.adminName,
            {
              percentage: discountData.discountPercentage,
              durationMonths: discountData.durationMonths,
              startDate: discountData.startDate,
              reason: discountData.reason
            }
          )
        }
      })

    } catch (error) {
      console.error('Error applying discount:', error)

      // Log the error for audit trail
      await discountAuditService.logDiscountError(
        subscriptionId,
        'DISCOUNT_APPLIED',
        error instanceof Error ? error.message : 'Unknown error'
      )

      throw error
    }
  }

  /**
   * Expire a discount and revert to original amount
   */
  async expireDiscount(subscriptionId: string): Promise<void> {
    try {
      // Get current subscription with discount details
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        originalMonthlyAmount: billingSubscriptions.originalMonthlyAmount,
        hasActiveDiscount: billingSubscriptions.hasActiveDiscount,
        currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      if (!subscription.hasActiveDiscount || !subscription.originalMonthlyAmount) {
        console.log(`⚠️ [Discount Expiration] Subscription ${subscriptionId} has no active discount to expire`)
        return
      }

      // ✅ FIXED: Use transaction for atomic expiration
      await db.transaction(async (tx) => {
        // Revert to original amount
        await tx.update(billingSubscriptions)
          .set({
            hasActiveDiscount: false,
            currentDiscountPercentage: '0.00',
            discountStartDate: null,
            discountEndDate: null,
            monthlyAmount: subscription.originalMonthlyAmount || subscription.monthlyAmount,
            originalMonthlyAmount: null,
            discountReason: null,
            updatedAt: new Date()
          })
          .where(eq(billingSubscriptions.id, subscriptionId))

        // ✅ UNIFIED: No need to update subscriptionDiscounts table
      })

      console.log(`✅ [Discount Expired] Subscription ${subscriptionId}: Reverted to original amount ${subscription.originalMonthlyAmount}`)

    } catch (error) {
      console.error('Error expiring discount:', error)
      throw error
    }
  }

  /**
   * Check for expired discounts and automatically revert them
   */
  async checkAndExpireDiscounts(): Promise<{ expired: number; errors: string[] }> {
    try {
      const today = new Date().toISOString().split('T')[0]

      // Find subscriptions with expired discounts
      const expiredDiscounts = await db.select({
        id: billingSubscriptions.id,
        discountEndDate: billingSubscriptions.discountEndDate,
        currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage
      })
      .from(billingSubscriptions)
      .where(and(
        eq(billingSubscriptions.hasActiveDiscount, true),
        lte(billingSubscriptions.discountEndDate, today)
      ))

      const errors: string[] = []
      let expiredCount = 0

      for (const subscription of expiredDiscounts) {
        try {
          await this.expireDiscount(subscription.id)
          expiredCount++
        } catch (error) {
          const errorMsg = `Failed to expire discount for subscription ${subscription.id}: ${error instanceof Error ? error.message : 'Unknown error'}`
          console.error(errorMsg)
          errors.push(errorMsg)
        }
      }

      console.log(`🔄 [Discount Expiration Check] Processed ${expiredDiscounts.length} expired discounts. Success: ${expiredCount}, Errors: ${errors.length}`)

      return { expired: expiredCount, errors }

    } catch (error) {
      console.error('Error checking expired discounts:', error)
      throw error
    }
  }

  /**
   * Apply monthly discount to a subscription amount
   */
  async applyMonthlyDiscount(subscriptionId: string): Promise<DiscountApplication> {
    try {
      // Get active discount
      const activeDiscount = await this.getActiveDiscount(subscriptionId)
      if (!activeDiscount) {
        return { 
          hasDiscount: false, 
          originalAmount: 0, 
          discountAmount: 0, 
          finalAmount: 0 
        }
      }

      // Get subscription details
      const [subscription] = await db.select({
        monthlyAmount: billingSubscriptions.monthlyAmount
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      const originalAmount = parseFloat(subscription.monthlyAmount)
      const discountAmount = (originalAmount * parseFloat(activeDiscount.discountPercentage.toString())) / 100
      const finalAmount = originalAmount - discountAmount

      // ✅ UNIFIED: Discount expiration handled by automated service based on dates

      return {
        hasDiscount: true,
        originalAmount,
        discountAmount,
        finalAmount,
        discountPercentage: parseFloat(activeDiscount.discountPercentage.toString()),
        remainingMonths: activeDiscount.remainingMonths - 1
      }

    } catch (error) {
      console.error('Error applying monthly discount:', error)
      throw error
    }
  }

  /**
   * Get comprehensive discount summary for a subscription
   */
  async getDiscountSummary(subscriptionId: string): Promise<{
    hasActiveDiscount: boolean
    originalAmount?: number
    discountedAmount?: number
    discountPercentage?: number
    discountStartDate?: Date
    discountEndDate?: Date
    remainingMonths?: number
    monthlySavings?: number
    totalSavingsToDate?: number
    discountReason?: string
  }> {
    try {
      const [subscription] = await db.select({
        hasActiveDiscount: billingSubscriptions.hasActiveDiscount,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        originalMonthlyAmount: billingSubscriptions.originalMonthlyAmount,
        currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage,
        discountStartDate: billingSubscriptions.discountStartDate,
        discountEndDate: billingSubscriptions.discountEndDate,
        discountReason: billingSubscriptions.discountReason
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      if (!subscription.hasActiveDiscount) {
        return { hasActiveDiscount: false }
      }

      const originalAmount = parseFloat(subscription.originalMonthlyAmount || subscription.monthlyAmount)
      const discountedAmount = parseFloat(subscription.monthlyAmount)
      const discountPercentage = parseFloat(subscription.currentDiscountPercentage || '0')
      const monthlySavings = originalAmount - discountedAmount

      // Calculate remaining months and total savings
      let remainingMonths = 0
      let totalSavingsToDate = 0

      if (subscription.discountStartDate && subscription.discountEndDate) {
        const startDate = new Date(subscription.discountStartDate)
        const endDate = new Date(subscription.discountEndDate)
        const today = new Date()

        // Calculate total months in discount period
        const totalMonths = (endDate.getFullYear() - startDate.getFullYear()) * 12 +
                           (endDate.getMonth() - startDate.getMonth())

        // Calculate months elapsed
        const monthsElapsed = Math.max(0, (today.getFullYear() - startDate.getFullYear()) * 12 +
                                          (today.getMonth() - startDate.getMonth()))

        remainingMonths = Math.max(0, totalMonths - monthsElapsed)
        totalSavingsToDate = monthlySavings * Math.min(monthsElapsed, totalMonths)
      }

      return {
        hasActiveDiscount: true,
        originalAmount,
        discountedAmount,
        discountPercentage,
        discountStartDate: subscription.discountStartDate ? new Date(subscription.discountStartDate) : undefined,
        discountEndDate: subscription.discountEndDate ? new Date(subscription.discountEndDate) : undefined,
        remainingMonths,
        monthlySavings,
        totalSavingsToDate,
        discountReason: subscription.discountReason || undefined
      }

    } catch (error) {
      console.error('Error getting discount summary:', error)
      throw error
    }
  }

  /**
   * Get active discount for a subscription (unified system)
   */
  async getActiveDiscount(subscriptionId: string): Promise<DiscountDetails | null> {
    try {
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        hasActiveDiscount: billingSubscriptions.hasActiveDiscount,
        currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage,
        discountStartDate: billingSubscriptions.discountStartDate,
        discountEndDate: billingSubscriptions.discountEndDate,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        originalMonthlyAmount: billingSubscriptions.originalMonthlyAmount
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription || !subscription.hasActiveDiscount || !subscription.discountEndDate) {
        return null
      }

      // Check if discount is still valid
      const today = new Date()
      const endDate = new Date(subscription.discountEndDate)
      if (today > endDate) {
        return null
      }

      // Calculate remaining months
      const remainingMonths = Math.max(0,
        (endDate.getFullYear() - today.getFullYear()) * 12 +
        (endDate.getMonth() - today.getMonth())
      )

      const originalAmount = parseFloat(subscription.originalMonthlyAmount || subscription.monthlyAmount)
      const discountedAmount = parseFloat(subscription.monthlyAmount)
      const monthlySavings = originalAmount - discountedAmount

      return {
        id: subscription.id,
        discountPercentage: parseFloat(subscription.currentDiscountPercentage || '0'),
        remainingMonths,
        endDate,
        monthlySavings,
        totalSavingsToDate: monthlySavings * (remainingMonths > 0 ? 1 : 0) // Simplified calculation
      }

    } catch (error) {
      console.error('Error getting active discount:', error)
      return null
    }
  }

  /**
   * ✅ UNIFIED: No longer needed - discount expiration handled by date-based system
   */

  /**
   * Expire discounts that have reached their end date
   */
  async expireDiscounts(): Promise<number> {
    try {
      const today = new Date().toISOString().split('T')[0]

      // Get discounts to expire
      const expiredDiscounts = await db.select({
        id: subscriptionDiscounts.id,
        subscriptionId: subscriptionDiscounts.subscriptionId
      })
      .from(subscriptionDiscounts)
      .where(and(
        eq(subscriptionDiscounts.isActive, true),
        lte(subscriptionDiscounts.endDate, today)
      ))

      if (expiredDiscounts.length === 0) {
        return 0
      }

      // Deactivate expired discounts
      await db.update(subscriptionDiscounts)
        .set({ 
          isActive: false, 
          updatedAt: new Date() 
        })
        .where(and(
          eq(subscriptionDiscounts.isActive, true),
          lte(subscriptionDiscounts.endDate, today)
        ))

      // Update subscription discount status
      const subscriptionIds = expiredDiscounts.map(d => d.subscriptionId)
      await db.update(billingSubscriptions)
        .set({
          hasActiveDiscount: false,
          currentDiscountPercentage: '0',
          discountEndDate: null,
          updatedAt: new Date()
        })
        .where(sql`${billingSubscriptions.id} = ANY(${subscriptionIds})`)

      return expiredDiscounts.length

    } catch (error) {
      console.error('Error expiring discounts:', error)
      throw error
    }
  }

  /**
   * Get current operational expenses for a subscription
   */
  async getOperationalExpenses(subscriptionId: string): Promise<ExpenseDetails | null> {
    try {
      const [expense] = await db.select({
        id: subscriptionExpenses.id,
        monthlyOperationalCost: subscriptionExpenses.monthlyOperationalCost,
        description: subscriptionExpenses.description,
        category: subscriptionExpenses.category,
        effectiveFrom: subscriptionExpenses.effectiveFrom,
        isActive: subscriptionExpenses.isActive
      })
      .from(subscriptionExpenses)
      .where(and(
        eq(subscriptionExpenses.subscriptionId, subscriptionId),
        eq(subscriptionExpenses.isActive, true),
        lte(subscriptionExpenses.effectiveFrom, new Date().toISOString().split('T')[0])
      ))
      .limit(1)

      return expense ? {
        id: expense.id,
        monthlyOperationalCost: parseFloat(expense.monthlyOperationalCost),
        description: expense.description || '',
        category: expense.category || '',
        effectiveFrom: new Date(expense.effectiveFrom),
        isActive: expense.isActive || false
      } : null

    } catch (error) {
      console.error('Error getting operational expenses:', error)
      return null
    }
  }

  /**
   * Calculate discounted amount for a given base amount
   */
  calculateDiscountedAmount(
    baseAmount: number,
    discountPercentage: number
  ): { originalAmount: number; discountAmount: number; finalAmount: number } {
    const discountAmount = (baseAmount * discountPercentage) / 100
    const finalAmount = baseAmount - discountAmount

    return {
      originalAmount: baseAmount,
      discountAmount,
      finalAmount
    }
  }

  /**
   * Get discount statistics for admin dashboard
   */
  async getDiscountStatistics(): Promise<{
    totalActiveDiscounts: number
    totalMonthlySavings: number
    averageDiscountPercentage: number
    expiringThisMonth: number
  }> {
    try {
      const [stats] = await db.select({
        totalActiveDiscounts: sql<number>`COUNT(*)`,
        averageDiscountPercentage: sql<number>`AVG(${subscriptionDiscounts.discountPercentage})`,
        expiringThisMonth: sql<number>`COUNT(CASE WHEN ${subscriptionDiscounts.endDate} <= DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day' THEN 1 END)`
      })
      .from(subscriptionDiscounts)
      .where(eq(subscriptionDiscounts.isActive, true))

      // Calculate total monthly savings
      const activeDiscountsWithAmounts = await db.select({
        discountPercentage: subscriptionDiscounts.discountPercentage,
        monthlyAmount: billingSubscriptions.monthlyAmount
      })
      .from(subscriptionDiscounts)
      .innerJoin(billingSubscriptions, eq(subscriptionDiscounts.subscriptionId, billingSubscriptions.id))
      .where(eq(subscriptionDiscounts.isActive, true))

      const totalMonthlySavings = activeDiscountsWithAmounts.reduce((total, discount) => {
        return total + (parseFloat(discount.monthlyAmount) * parseFloat(discount.discountPercentage)) / 100
      }, 0)

      return {
        totalActiveDiscounts: stats.totalActiveDiscounts || 0,
        totalMonthlySavings,
        averageDiscountPercentage: stats.averageDiscountPercentage || 0,
        expiringThisMonth: stats.expiringThisMonth || 0
      }

    } catch (error) {
      console.error('Error getting discount statistics:', error)
      return {
        totalActiveDiscounts: 0,
        totalMonthlySavings: 0,
        averageDiscountPercentage: 0,
        expiringThisMonth: 0
      }
    }
  }
}

export const discountManagementService = new DiscountManagementService()
