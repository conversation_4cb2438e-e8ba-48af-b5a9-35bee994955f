import { db } from '@/src/db'
import {
  subscriptionDiscounts,
  subscriptionExpenses,
  billingSubscriptions,
  billingTransactions,
  clients
} from '@/src/db/schema'
import { eq, and, lte, gte, sql } from 'drizzle-orm'

export interface DiscountApplication {
  hasDiscount: boolean
  originalAmount: number
  discountAmount: number
  finalAmount: number
  discountPercentage?: number
  remainingMonths?: number
}

export interface DiscountDetails {
  id: string
  discountPercentage: number
  remainingMonths: number
  endDate: Date
  monthlySavings: number
  totalSavingsToDate: number
}

export interface ExpenseDetails {
  id: string
  monthlyOperationalCost: number
  description?: string
  category: string
  effectiveFrom: Date
  isActive: boolean
}

class DiscountManagementService {
  /**
   * Apply monthly discount to a subscription amount
   */
  async applyMonthlyDiscount(subscriptionId: string): Promise<DiscountApplication> {
    try {
      // Get active discount
      const activeDiscount = await this.getActiveDiscount(subscriptionId)
      if (!activeDiscount) {
        return { 
          hasDiscount: false, 
          originalAmount: 0, 
          discountAmount: 0, 
          finalAmount: 0 
        }
      }

      // Get subscription details
      const [subscription] = await db.select({
        monthlyAmount: billingSubscriptions.monthlyAmount
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      const originalAmount = parseFloat(subscription.monthlyAmount)
      const discountAmount = (originalAmount * parseFloat(activeDiscount.discountPercentage.toString())) / 100
      const finalAmount = originalAmount - discountAmount

      // Update remaining months (this should be called when billing is processed)
      if (activeDiscount.remainingMonths > 0) {
        await this.updateDiscountRemainingMonths(activeDiscount.id)
      }

      return {
        hasDiscount: true,
        originalAmount,
        discountAmount,
        finalAmount,
        discountPercentage: parseFloat(activeDiscount.discountPercentage.toString()),
        remainingMonths: activeDiscount.remainingMonths - 1
      }

    } catch (error) {
      console.error('Error applying monthly discount:', error)
      throw error
    }
  }

  /**
   * Get active discount for a subscription
   */
  async getActiveDiscount(subscriptionId: string): Promise<DiscountDetails | null> {
    try {
      const [discount] = await db.select({
        id: subscriptionDiscounts.id,
        discountPercentage: subscriptionDiscounts.discountPercentage,
        remainingMonths: subscriptionDiscounts.remainingMonths,
        endDate: subscriptionDiscounts.endDate,
        startDate: subscriptionDiscounts.startDate,
        durationMonths: subscriptionDiscounts.discountDurationMonths
      })
      .from(subscriptionDiscounts)
      .where(and(
        eq(subscriptionDiscounts.subscriptionId, subscriptionId),
        eq(subscriptionDiscounts.isActive, true),
        lte(subscriptionDiscounts.startDate, new Date().toISOString().split('T')[0]),
        gte(subscriptionDiscounts.endDate, new Date().toISOString().split('T')[0])
      ))
      .limit(1)

      if (!discount) {
        return null
      }

      // Get subscription monthly amount for calculations
      const [subscription] = await db.select({
        monthlyAmount: billingSubscriptions.monthlyAmount
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        return null
      }

      const monthlySavings = (parseFloat(subscription.monthlyAmount) * parseFloat(discount.discountPercentage)) / 100
      const monthsUsed = discount.durationMonths - discount.remainingMonths
      const totalSavingsToDate = monthlySavings * monthsUsed

      return {
        id: discount.id,
        discountPercentage: parseFloat(discount.discountPercentage),
        remainingMonths: discount.remainingMonths,
        endDate: new Date(discount.endDate),
        monthlySavings,
        totalSavingsToDate
      }

    } catch (error) {
      console.error('Error getting active discount:', error)
      return null
    }
  }

  /**
   * Update remaining months for a discount (called during billing)
   */
  async updateDiscountRemainingMonths(discountId: string): Promise<void> {
    try {
      const [discount] = await db.select({
        remainingMonths: subscriptionDiscounts.remainingMonths,
        subscriptionId: subscriptionDiscounts.subscriptionId
      })
      .from(subscriptionDiscounts)
      .where(eq(subscriptionDiscounts.id, discountId))
      .limit(1)

      if (!discount) {
        throw new Error('Discount not found')
      }

      const newRemainingMonths = Math.max(0, discount.remainingMonths - 1)

      // Update discount
      await db.update(subscriptionDiscounts)
        .set({
          remainingMonths: newRemainingMonths,
          isActive: newRemainingMonths > 0,
          updatedAt: new Date()
        })
        .where(eq(subscriptionDiscounts.id, discountId))

      // Update subscription if discount is expired
      if (newRemainingMonths === 0) {
        await db.update(billingSubscriptions)
          .set({
            hasActiveDiscount: false,
            currentDiscountPercentage: '0',
            discountEndDate: null,
            updatedAt: new Date()
          })
          .where(eq(billingSubscriptions.id, discount.subscriptionId!))
      }

    } catch (error) {
      console.error('Error updating discount remaining months:', error)
      throw error
    }
  }

  /**
   * Expire discounts that have reached their end date
   */
  async expireDiscounts(): Promise<number> {
    try {
      const today = new Date().toISOString().split('T')[0]

      // Get discounts to expire
      const expiredDiscounts = await db.select({
        id: subscriptionDiscounts.id,
        subscriptionId: subscriptionDiscounts.subscriptionId
      })
      .from(subscriptionDiscounts)
      .where(and(
        eq(subscriptionDiscounts.isActive, true),
        lte(subscriptionDiscounts.endDate, today)
      ))

      if (expiredDiscounts.length === 0) {
        return 0
      }

      // Deactivate expired discounts
      await db.update(subscriptionDiscounts)
        .set({ 
          isActive: false, 
          updatedAt: new Date() 
        })
        .where(and(
          eq(subscriptionDiscounts.isActive, true),
          lte(subscriptionDiscounts.endDate, today)
        ))

      // Update subscription discount status
      const subscriptionIds = expiredDiscounts.map(d => d.subscriptionId)
      await db.update(billingSubscriptions)
        .set({
          hasActiveDiscount: false,
          currentDiscountPercentage: '0',
          discountEndDate: null,
          updatedAt: new Date()
        })
        .where(sql`${billingSubscriptions.id} = ANY(${subscriptionIds})`)

      return expiredDiscounts.length

    } catch (error) {
      console.error('Error expiring discounts:', error)
      throw error
    }
  }

  /**
   * Get current operational expenses for a subscription
   */
  async getOperationalExpenses(subscriptionId: string): Promise<ExpenseDetails | null> {
    try {
      const [expense] = await db.select({
        id: subscriptionExpenses.id,
        monthlyOperationalCost: subscriptionExpenses.monthlyOperationalCost,
        description: subscriptionExpenses.description,
        category: subscriptionExpenses.category,
        effectiveFrom: subscriptionExpenses.effectiveFrom,
        isActive: subscriptionExpenses.isActive
      })
      .from(subscriptionExpenses)
      .where(and(
        eq(subscriptionExpenses.subscriptionId, subscriptionId),
        eq(subscriptionExpenses.isActive, true),
        lte(subscriptionExpenses.effectiveFrom, new Date().toISOString().split('T')[0])
      ))
      .limit(1)

      return expense ? {
        id: expense.id,
        monthlyOperationalCost: parseFloat(expense.monthlyOperationalCost),
        description: expense.description || '',
        category: expense.category || '',
        effectiveFrom: new Date(expense.effectiveFrom),
        isActive: expense.isActive || false
      } : null

    } catch (error) {
      console.error('Error getting operational expenses:', error)
      return null
    }
  }

  /**
   * Calculate discounted amount for a given base amount
   */
  calculateDiscountedAmount(
    baseAmount: number,
    discountPercentage: number
  ): { originalAmount: number; discountAmount: number; finalAmount: number } {
    const discountAmount = (baseAmount * discountPercentage) / 100
    const finalAmount = baseAmount - discountAmount

    return {
      originalAmount: baseAmount,
      discountAmount,
      finalAmount
    }
  }

  /**
   * Get discount statistics for admin dashboard
   */
  async getDiscountStatistics(): Promise<{
    totalActiveDiscounts: number
    totalMonthlySavings: number
    averageDiscountPercentage: number
    expiringThisMonth: number
  }> {
    try {
      const [stats] = await db.select({
        totalActiveDiscounts: sql<number>`COUNT(*)`,
        averageDiscountPercentage: sql<number>`AVG(${subscriptionDiscounts.discountPercentage})`,
        expiringThisMonth: sql<number>`COUNT(CASE WHEN ${subscriptionDiscounts.endDate} <= DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day' THEN 1 END)`
      })
      .from(subscriptionDiscounts)
      .where(eq(subscriptionDiscounts.isActive, true))

      // Calculate total monthly savings
      const activeDiscountsWithAmounts = await db.select({
        discountPercentage: subscriptionDiscounts.discountPercentage,
        monthlyAmount: billingSubscriptions.monthlyAmount
      })
      .from(subscriptionDiscounts)
      .innerJoin(billingSubscriptions, eq(subscriptionDiscounts.subscriptionId, billingSubscriptions.id))
      .where(eq(subscriptionDiscounts.isActive, true))

      const totalMonthlySavings = activeDiscountsWithAmounts.reduce((total, discount) => {
        return total + (parseFloat(discount.monthlyAmount) * parseFloat(discount.discountPercentage)) / 100
      }, 0)

      return {
        totalActiveDiscounts: stats.totalActiveDiscounts || 0,
        totalMonthlySavings,
        averageDiscountPercentage: stats.averageDiscountPercentage || 0,
        expiringThisMonth: stats.expiringThisMonth || 0
      }

    } catch (error) {
      console.error('Error getting discount statistics:', error)
      return {
        totalActiveDiscounts: 0,
        totalMonthlySavings: 0,
        averageDiscountPercentage: 0,
        expiringThisMonth: 0
      }
    }
  }
}

export const discountManagementService = new DiscountManagementService()
