// Seed script for partners and referral codes
// Run with: npx tsx scripts/seed-partners-referrals.ts

import { config } from 'dotenv'
import { resolve } from 'path'

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') })

import bcrypt from 'bcryptjs'
import { db } from '../src/db'
import { partners, referralCodes, adminUsers } from '../src/db/schema'
import { eq } from 'drizzle-orm'

// Test partners data
const TEST_PARTNERS = [
  {
    partnerCode: 'PART001',
    email: '<EMAIL>',
    password: 'Partner@123',
    name: '<PERSON>',
    companyName: 'EduTech Solutions',
    phone: '+91-9876543210',
    address: '123 Business Park, Mumbai, Maharashtra 400001',
    profitSharePercentage: '40.00',
    referralCodes: ['ABC123', 'EDU001']
  },
  {
    partnerCode: 'PART002', 
    email: '<EMAIL>',
    password: 'Partner@456',
    name: '<PERSON>',
    companyName: 'School Connect India',
    phone: '+91-9876543211',
    address: '456 Tech Hub, Bangalore, Karnataka 560001',
    profitSharePercentage: '45.00',
    referralCodes: ['XYZ789', 'SCH002']
  },
  {
    partnerCode: 'PART003',
    email: '<EMAIL>', 
    password: 'Partner@789',
    name: 'Rajesh Kumar',
    companyName: 'Digital Education Partners',
    phone: '+91-9876543212',
    address: '789 Innovation Center, Delhi, Delhi 110001',
    profitSharePercentage: '42.50',
    referralCodes: ['DEF456', 'DIG003']
  }
]

async function seedPartnersAndReferrals() {
  try {
    console.log('🌱 Starting to seed partners and referral codes...\n')

    // Get admin user to set as creator
    const [adminUser] = await db.select().from(adminUsers).limit(1)
    
    if (!adminUser) {
      console.error('❌ No admin user found. Please run seed-admin.ts first.')
      process.exit(1)
    }

    console.log(`✅ Found admin user: ${adminUser.email}`)

    // Check if partners already exist
    const existingPartners = await db.select().from(partners).limit(1)
    if (existingPartners.length > 0) {
      console.log('⚠️  Partners already exist. Skipping partner creation.')
      
      // Still create referral codes if they don't exist
      const existingCodes = await db.select().from(referralCodes).limit(1)
      if (existingCodes.length > 0) {
        console.log('⚠️  Referral codes already exist. Nothing to seed.')
        return
      }
    }

    // Create partners and referral codes
    for (const partnerData of TEST_PARTNERS) {
      console.log(`\n📝 Creating partner: ${partnerData.name}`)
      
      // Hash password
      const passwordHash = await bcrypt.hash(partnerData.password, 12)
      
      // Create partner
      const [newPartner] = await db.insert(partners).values({
        partnerCode: partnerData.partnerCode,
        email: partnerData.email,
        passwordHash,
        name: partnerData.name,
        companyName: partnerData.companyName,
        phone: partnerData.phone,
        address: partnerData.address,
        profitSharePercentage: partnerData.profitSharePercentage,
        createdBy: adminUser.id,
        isActive: true,
        emailVerified: true
      }).returning()

      console.log(`   ✅ Partner created with ID: ${newPartner.id}`)
      
      // Create referral codes for this partner
      for (const codeValue of partnerData.referralCodes) {
        console.log(`   📋 Creating referral code: ${codeValue}`)
        
        const [newCode] = await db.insert(referralCodes).values({
          partnerId: newPartner.id,
          code: codeValue,
          isActive: true,
          usageCount: 0,
          maxUsage: null // Unlimited usage
        }).returning()
        
        console.log(`      ✅ Referral code created with ID: ${newCode.id}`)
      }
    }

    console.log('\n🎉 Successfully seeded partners and referral codes!')
    console.log('\n📋 Available referral codes for testing:')
    
    // Display all available codes
    const allCodes = await db
      .select({
        code: referralCodes.code,
        partnerName: partners.name,
        companyName: partners.companyName,
        isActive: referralCodes.isActive
      })
      .from(referralCodes)
      .innerJoin(partners, eq(referralCodes.partnerId, partners.id))
      .where(eq(referralCodes.isActive, true))

    allCodes.forEach(code => {
      console.log(`   🎫 ${code.code} - ${code.partnerName} (${code.companyName})`)
    })

    console.log('\n💡 You can now test referral code functionality with any of the above codes!')
    console.log('\n🔐 Partner login credentials:')
    TEST_PARTNERS.forEach(partner => {
      console.log(`   📧 ${partner.email} / ${partner.password}`)
    })

  } catch (error) {
    console.error('❌ Error seeding partners and referral codes:', error)
    process.exit(1)
  }
}

// Run the seeding
seedPartnersAndReferrals()
