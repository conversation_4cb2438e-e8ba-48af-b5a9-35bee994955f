'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import {
  MessageCircle,
  X,
  Send,
  User,
  Bot,

  CheckCircle,
  Minimize2,
  Maximize2
} from 'lucide-react'

interface Message {
  id: number
  type: 'user' | 'bot'
  message: string
  timestamp: Date
  status: 'delivered' | 'sending'
}

const LiveChat = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [message, setMessage] = useState('')
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      type: 'bot',
      message: "Hi! I'm your AI assistant for Schopio. I'm here to help you understand our school management platform and answer any questions you have. How can I assist you today?",
      timestamp: new Date(),
      status: 'delivered'
    }
  ])
  const [isTyping, setIsTyping] = useState(false)
  const [isStreaming, setIsStreaming] = useState(false)
  const [showInitialPrompt, setShowInitialPrompt] = useState(true)
  const [showQuickOptions, setShowQuickOptions] = useState(true)
  const [conversationHistory, setConversationHistory] = useState<Array<{type: string, content: string}>>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-hide initial prompt after 10 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowInitialPrompt(false)
    }, 10000)
    return () => clearTimeout(timer)
  }, [])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Reset chat when opening
  const openChat = () => {
    setIsOpen(true)
    setShowInitialPrompt(false)
    setIsMinimized(false)
  }

  // Close chat and reset state
  const closeChat = () => {
    setIsOpen(false)
    setIsMinimized(false)
    setShowQuickOptions(true)
  }

  const quickQuestions = [
    "How long does implementation take?",
    "What modules are included?",
    "Do you provide training?",
    "Can I see a demo?",
    "How does the AI work?"
  ]

  const sendMessage = async (text?: string) => {
    const messageText = text || message
    if (!messageText.trim() || isStreaming) return

    // Hide quick options after first user message
    setShowQuickOptions(false)

    const newMessage: Message = {
      id: messages.length + 1,
      type: 'user',
      message: messageText,
      timestamp: new Date(),
      status: 'delivered'
    }

    setMessages(prev => [...prev, newMessage])
    setMessage('')
    setIsTyping(true)
    setIsStreaming(true)

    // Update conversation history
    const updatedHistory = [...conversationHistory, { type: 'user', content: messageText }]
    setConversationHistory(updatedHistory)

    try {
      // Call AI chat API with streaming
      const response = await fetch('/api/ai-chat/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageText,
          conversationHistory: updatedHistory
        })
      })

      if (!response.ok) {
        throw new Error('Failed to get AI response')
      }

      setIsTyping(false)

      // Create bot message for streaming
      const botMessage: Message = {
        id: messages.length + 2,
        type: 'bot',
        message: '',
        timestamp: new Date(),
        status: 'delivered'
      }

      setMessages(prev => [...prev, botMessage])
      setIsTyping(true)

      // Stream the response with real-time character updates
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      let streamedText = ''

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          streamedText += chunk

          // Update the bot message with streamed text in real-time
          setMessages(prev =>
            prev.map(msg =>
              msg.id === botMessage.id
                ? { ...msg, message: streamedText }
                : msg
            )
          )

          // Scroll to bottom as new text appears
          setTimeout(() => {
            const chatContainer = document.querySelector('.overflow-y-auto')
            if (chatContainer) {
              chatContainer.scrollTop = chatContainer.scrollHeight
            }
          }, 10)
        }
      }

      // Update conversation history with bot response
      setConversationHistory(prev => [...prev, { type: 'bot', content: streamedText }])
      setIsTyping(false)

    } catch (error) {
      console.error('Error sending message:', error)
      setIsTyping(false)

      const errorMessage: Message = {
        id: messages.length + 2,
        type: 'bot',
        message: 'Sorry, I encountered an error. Please try again or contact our support team.',
        timestamp: new Date(),
        status: 'delivered'
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsStreaming(false)
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  // Helper function to render text with bold formatting
  const renderFormattedText = (text: string) => {
    // Handle **bold** text
    const parts = text.split(/(\*\*.*?\*\*)/g)
    return parts.map((part, index) => {
      if (part.startsWith('**') && part.endsWith('**')) {
        return (
          <strong key={index} className="font-semibold">
            {part.slice(2, -2)}
          </strong>
        )
      }
      return part
    })
  }

  return (
    <>
      {/* Initial Prompt */}
      <AnimatePresence>
        {showInitialPrompt && !isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-24 right-6 z-40 max-w-sm"
          >
            <Card className="bg-white border border-blue-200 shadow-xl">
              <CardContent padding="md">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-slate-700 mb-2">
                      <strong>Questions about Schopio?</strong>
                    </p>
                    <p className="text-xs text-slate-600 mb-3">
                      Chat with our AI assistant - instant responses about features, implementation, and more!
                    </p>
                    <Button
                      size="sm"
                      onClick={openChat}
                      className="bg-gradient-to-r from-blue-500 to-emerald-500 text-white text-xs px-3 py-1"
                    >
                      Start Chat
                    </Button>
                  </div>
                  <button
                    onClick={() => setShowInitialPrompt(false)}
                    className="text-slate-400 hover:text-slate-600 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Widget */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className={`fixed bottom-6 right-6 z-50 ${
              isMinimized ? 'w-80 md:w-96' : 'w-96 md:w-[450px] lg:w-[500px]'
            } ${isMinimized ? 'h-16' : 'h-[500px] md:h-[600px]'}`}
          >
            <Card className="h-full bg-white border-0 shadow-2xl overflow-hidden">
              {/* Header */}
              <CardHeader padding="md" className="bg-gradient-to-r from-blue-600 to-emerald-600 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                      <MessageCircle className="w-4 h-4" />
                    </div>
                    <div>
                      <h3 className="font-bold text-sm">Schopio AI Assistant</h3>
                      <div className="flex items-center gap-1 text-xs text-emerald-200">
                        <div className="w-2 h-2 bg-emerald-300 rounded-full animate-pulse" />
                        <span>AI powered</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setIsMinimized(!isMinimized)}
                      className="text-white/70 hover:text-white transition-colors"
                    >
                      {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
                    </button>
                    <button
                      onClick={closeChat}
                      className="text-white/70 hover:text-white transition-colors p-1 hover:bg-white/10 rounded"
                      title="Close chat"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </CardHeader>

              {!isMinimized && (
                <CardContent padding="none" className="flex flex-col h-full">
                  {/* Messages */}
                  <div className="flex-1 p-4 space-y-4 overflow-y-auto max-h-80 md:max-h-96 bg-gray-50">
                    {messages.map((msg) => (
                      <div
                        key={msg.id}
                        className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`flex items-start gap-2 max-w-[80%] ${
                          msg.type === 'user' ? 'flex-row-reverse' : 'flex-row'
                        }`}>
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                            msg.type === 'user' 
                              ? 'bg-blue-500 text-white' 
                              : 'bg-gradient-to-r from-blue-500 to-emerald-500 text-white'
                          }`}>
                            {msg.type === 'user' ? <User className="w-3 h-3" /> : <Bot className="w-3 h-3" />}
                          </div>
                          <div className={`rounded-lg p-3 ${
                            msg.type === 'user'
                              ? 'bg-blue-500 text-white'
                              : 'bg-white text-gray-900 border border-gray-200 shadow-sm'
                          }`}>
                            <div className="text-sm leading-relaxed">
                              {msg.message.split('\n').map((line, index) => {
                                const trimmedLine = line.trim()

                                // Handle bullet points
                                if (trimmedLine.startsWith('•')) {
                                  return (
                                    <div key={index} className="flex items-start gap-2 mb-1">
                                      <span className="text-blue-500 font-bold mt-0.5">•</span>
                                      <span className="flex-1">{renderFormattedText(trimmedLine.substring(1).trim())}</span>
                                    </div>
                                  )
                                }

                                // Handle numbered lists
                                if (/^\d+\./.test(trimmedLine)) {
                                  return (
                                    <div key={index} className="flex items-start gap-2 mb-1">
                                      <span className="text-blue-500 font-bold mt-0.5">{trimmedLine.match(/^\d+\./)?.[0]}</span>
                                      <span className="flex-1">{renderFormattedText(trimmedLine.replace(/^\d+\.\s*/, ''))}</span>
                                    </div>
                                  )
                                }

                                // Handle empty lines for spacing
                                if (trimmedLine === '') {
                                  return <div key={index} className="h-2" />
                                }

                                // Regular paragraphs
                                return (
                                  <div key={index} className="mb-2">
                                    {renderFormattedText(line)}
                                  </div>
                                )
                              })}
                            </div>
                            <div className={`flex items-center gap-1 mt-1 text-xs ${
                              msg.type === 'user' ? 'text-blue-200' : 'text-gray-600'
                            }`}>
                              <span>{formatTime(msg.timestamp)}</span>
                              {msg.type === 'user' && <CheckCircle className="w-3 h-3" />}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {(isTyping || isStreaming) && (
                      <div className="flex justify-start">
                        <div className="flex items-start gap-2">
                          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
                            <Bot className="w-3 h-3 text-white" />
                          </div>
                          <div className="bg-white border border-gray-200 shadow-sm rounded-lg p-3">
                            <div className="flex gap-1">
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" />
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>

                  {/* Quick Questions */}
                  {showQuickOptions && (
                    <div className="p-3 border-t border-slate-200 bg-slate-50">
                      <p className="text-xs text-slate-600 mb-2 font-medium">Quick questions:</p>
                      <div className="grid grid-cols-1 gap-1">
                        {quickQuestions.slice(0, 3).map((question, index) => (
                          <button
                            key={index}
                            onClick={() => sendMessage(question)}
                            className="text-left text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-100 p-2 rounded-md transition-colors border border-blue-200 bg-white"
                          >
                            {question}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Input - Always visible */}
                  <div className="p-4 border-t border-slate-200 bg-white">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && sendMessage()}
                        placeholder="Type your message..."
                        disabled={isStreaming}
                        className="flex-1 px-3 py-2.5 border border-slate-300 rounded-lg text-sm text-gray-900 placeholder-gray-500 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 transition-all"
                      />
                      <Button
                        size="sm"
                        onClick={() => sendMessage()}
                        disabled={!message.trim() || isStreaming}
                        className="bg-gradient-to-r from-blue-500 to-emerald-500 text-white px-3 py-2.5 disabled:opacity-50 hover:from-blue-600 hover:to-emerald-600 transition-all"
                      >
                        <Send className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Button */}
      {!isOpen && (
        <motion.button
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={openChat}
          className="fixed bottom-6 right-6 z-40 w-14 h-14 bg-gradient-to-r from-blue-500 to-emerald-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center"
        >
          <MessageCircle className="w-6 h-6" />
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-bold">1</span>
          </div>
        </motion.button>
      )}
    </>
  )
}

export default LiveChat
