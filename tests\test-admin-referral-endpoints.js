/**
 * Test Admin Referral Endpoints
 * Direct API endpoint testing for referral verification system
 */

const BASE_URL = 'http://localhost:3000'

async function testAdminReferralEndpoints() {
  console.log('🧪 Testing Admin Referral Endpoints...\n')

  try {
    // Step 1: Test Admin Login (wait for rate limit to reset)
    console.log('1️⃣ Testing Admin Login (waiting for rate limit reset)...')
    
    // Wait 30 seconds for rate limit to reset
    console.log('⏳ Waiting 30 seconds for rate limit to reset...')
    await new Promise(resolve => setTimeout(resolve, 30000))
    
    const adminLoginResponse = await fetch(`${BASE_URL}/api/admin/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    })

    if (!adminLoginResponse.ok) {
      const errorData = await adminLoginResponse.json()
      console.log(`❌ Admin login failed: ${adminLoginResponse.status} - ${errorData.error}`)
      
      // If still rate limited, skip login-dependent tests
      if (adminLoginResponse.status === 429) {
        console.log('⚠️ Still rate limited. Testing endpoint structure only...')
        await testEndpointStructure()
        return
      }
      throw new Error(`Admin login failed: ${adminLoginResponse.status}`)
    }

    const adminLoginData = await adminLoginResponse.json()
    const adminToken = adminLoginData.token
    console.log('✅ Admin login successful')

    // Step 2: Test Pending Referrals Endpoint
    console.log('\n2️⃣ Testing Pending Referrals Endpoint...')
    const pendingResponse = await fetch(`${BASE_URL}/api/admin/referrals/pending`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })

    if (!pendingResponse.ok) {
      console.log(`❌ Pending referrals failed: ${pendingResponse.status}`)
      const errorData = await pendingResponse.json()
      console.log(`📄 Error: ${errorData.error}`)
    } else {
      const pendingData = await pendingResponse.json()
      console.log('✅ Pending referrals endpoint working')
      console.log(`📊 Found ${pendingData.referrals?.length || 0} pending referrals`)
      
      if (pendingData.referrals && pendingData.referrals.length > 0) {
        const testReferral = pendingData.referrals[0]
        console.log(`🎯 Sample referral: ${testReferral.schoolName} → ${testReferral.partnerName}`)
        
        // Step 3: Test Verification Endpoint
        console.log('\n3️⃣ Testing Verification Endpoint...')
        const verifyResponse = await fetch(`${BASE_URL}/api/admin/referrals/${testReferral.id}/verify`, {
          method: 'PUT',
          headers: { 'Authorization': `Bearer ${adminToken}` }
        })

        if (!verifyResponse.ok) {
          const verifyError = await verifyResponse.json()
          console.log(`⚠️ Verification response: ${verifyResponse.status} - ${verifyError.error}`)
        } else {
          const verifyData = await verifyResponse.json()
          console.log('✅ Verification endpoint working')
          console.log(`📝 Response: ${verifyData.message}`)
        }

        // Step 4: Test Rejection Endpoint
        console.log('\n4️⃣ Testing Rejection Endpoint...')
        const rejectResponse = await fetch(`${BASE_URL}/api/admin/referrals/${testReferral.id}/reject`, {
          method: 'PUT',
          headers: { 
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            reason: 'Test rejection - API endpoint validation'
          })
        })

        if (!rejectResponse.ok) {
          const rejectError = await rejectResponse.json()
          console.log(`⚠️ Rejection response: ${rejectResponse.status} - ${rejectError.error}`)
        } else {
          const rejectData = await rejectResponse.json()
          console.log('✅ Rejection endpoint working')
          console.log(`📝 Response: ${rejectData.message}`)
        }
      }
    }

    console.log('\n🎉 Admin Referral Endpoints Test Completed!')
    console.log('📊 Test Results Summary:')
    console.log('   ✅ Admin authentication: PASSED')
    console.log('   ✅ Pending referrals API: TESTED')
    console.log('   ✅ Verification API: TESTED')
    console.log('   ✅ Rejection API: TESTED')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

async function testEndpointStructure() {
  console.log('\n🔍 Testing Endpoint Structure (without authentication)...')
  
  // Test if endpoints exist (should return 401 without auth)
  const endpoints = [
    '/api/admin/referrals/pending',
    '/api/admin/referrals/test-id/verify',
    '/api/admin/referrals/test-id/reject'
  ]

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`)
      console.log(`📍 ${endpoint}: ${response.status} (${response.status === 401 ? 'Auth required - GOOD' : 'Unexpected'})`)
    } catch (error) {
      console.log(`📍 ${endpoint}: ERROR - ${error.message}`)
    }
  }
}

// Run the test
testAdminReferralEndpoints()
