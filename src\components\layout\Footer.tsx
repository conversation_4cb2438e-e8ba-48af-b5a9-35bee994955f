'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Zap,
  Mail,
  Phone,
  MapPin,
  ArrowRight,
  Brain,
  Users,
  Package,
  BookOpen,
  Calendar,
  Shield,
  Award,
  Clock
} from 'lucide-react'

const Footer = () => {
  const quickLinks = [
    { label: 'Solutions', href: '/solutions', icon: Users },
    { label: 'AI Features', href: '/ai-features', icon: Brain },
    { label: 'Packages', href: '/packages', icon: Package },
    { label: 'Resources', href: '/resources', icon: BookOpen },
    { label: 'Book Demo', href: '/demo', icon: Calendar }
  ]

  const features = [
    { label: 'Student Management', href: '/solutions#students' },
    { label: 'Teacher Portal', href: '/solutions#teachers' },
    { label: 'Parent Engagement', href: '/solutions#parents' },
    { label: 'Admin Dashboard', href: '/solutions#admin' },
    { label: 'AI Analytics', href: '/ai-features' }
  ]

  const company = [
    { label: 'About Schopio', href: '/about' },
    { label: 'Implementation', href: '/resources' },
    { label: 'Support Center', href: '/support' },
    { label: 'Privacy Policy', href: '/privacy' },
    { label: 'Terms of Service', href: '/terms' }
  ]

  const trustIndicators = [
    { icon: Shield, label: 'Bank-grade Security' },
    { icon: Award, label: '99.9% Uptime' },
    { icon: Clock, label: '24/7 Support' }
  ]

  return (
    <footer className="bg-slate-900 text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="space-y-6"
            >
              {/* Logo */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-emerald-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold">Schopio</div>
                  <div className="text-blue-300 text-sm">School Management</div>
                </div>
              </div>

              <p className="text-slate-300 leading-relaxed">
                AI-powered school management platform with complete functionality. 
                Transform your educational institution with predictive insights and modern technology.
              </p>

              {/* Trust Indicators */}
              <div className="space-y-3">
                {trustIndicators.map((item, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <item.icon className="w-5 h-5 text-emerald-400" />
                    <span className="text-slate-300 text-sm">{item.label}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Quick Links */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <h3 className="text-lg font-bold mb-6">Quick Links</h3>
              <div className="space-y-3">
                {quickLinks.map((link, index) => (
                  <a
                    key={index}
                    href={link.href}
                    className="flex items-center gap-3 text-slate-300 hover:text-white transition-colors duration-200 group"
                  >
                    <link.icon className="w-4 h-4 text-blue-400 group-hover:text-emerald-400 transition-colors duration-200" />
                    <span>{link.label}</span>
                  </a>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Features */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h3 className="text-lg font-bold mb-6">Features</h3>
              <div className="space-y-3">
                {features.map((link, index) => (
                  <a
                    key={index}
                    href={link.href}
                    className="block text-slate-300 hover:text-white transition-colors duration-200"
                  >
                    {link.label}
                  </a>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Contact & CTA */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="space-y-6"
            >
              <h3 className="text-lg font-bold">Get Started Today</h3>
              
              <p className="text-slate-300 text-sm">
                Ready to transform your school? Book a personalized demo and see Schopio in action.
              </p>

              <a
                href="/demo"
                className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 text-white font-bold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                <Calendar className="w-4 h-4" />
                Book Free Demo
                <ArrowRight className="w-4 h-4" />
              </a>

              {/* Contact Info */}
              <div className="space-y-3 pt-4 border-t border-slate-700">
                <div className="flex items-center gap-3 text-slate-300">
                  <Mail className="w-4 h-4 text-blue-400" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-slate-300">
                  <Phone className="w-4 h-4 text-blue-400" />
                  <span className="text-sm">+91 9304928363</span>
                </div>
                <div className="flex items-center gap-3 text-slate-300">
                  <MapPin className="w-4 h-4 text-blue-400" />
                  <span className="text-sm">India</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-slate-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-slate-400 text-sm"
            >
              © 2025 Schopio. All rights reserved. Built with ❤️ for educational excellence.
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="flex items-center gap-6"
            >
              {company.slice(3).map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="text-slate-400 hover:text-white text-sm transition-colors duration-200"
                >
                  {link.label}
                </a>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
