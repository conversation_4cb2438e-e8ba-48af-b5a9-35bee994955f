import { db } from '@/db'
import { subscriptions, clients, razorpayCustomers, subscriptionAuth } from '@/db/schema'
import { eq, and } from 'drizzle-orm'
import { razorpayService } from './razorpayService'
import { auditLogger } from './auditLogger'
import { emailService } from './emailService'

interface SubscriptionAuthData {
  subscriptionId: string
  clientId: string
  amount: number // Amount in paise for authentication
  currency: string
  description: string
}

interface AuthTransactionResult {
  success: boolean
  authOrderId?: string
  razorpayOrderId?: string
  subscriptionId?: string
  error?: string
}

interface SubscriptionActivationResult {
  success: boolean
  subscriptionStatus?: string
  message?: string
  error?: string
}

export class SubscriptionAuthService {
  /**
   * Create authentication transaction for subscription
   * This is the initial payment that authorizes future automatic charges
   */
  static async createAuthTransaction(authData: SubscriptionAuthData): Promise<AuthTransactionResult> {
    try {
      // Validate subscription exists and is in correct state
      const [subscription] = await db.select()
        .from(subscriptions)
        .leftJoin(clients, eq(subscriptions.clientId, clients.id))
        .where(eq(subscriptions.id, authData.subscriptionId))
        .limit(1)

      if (!subscription.subscriptions) {
        return {
          success: false,
          error: 'Subscription not found'
        }
      }

      if (subscription.subscriptions.status !== 'created') {
        return {
          success: false,
          error: `Subscription must be in 'created' status for authentication. Current status: ${subscription.subscriptions.status}`
        }
      }

      // Get or create Razorpay customer
      let razorpayCustomerId = subscription.subscriptions.razorpayCustomerId

      if (!razorpayCustomerId) {
        const customerResult = await razorpayService.createCustomer({
          name: subscription.clients?.schoolName || 'School Customer',
          email: subscription.clients?.email || '',
          contact: subscription.clients?.phone || '',
          notes: {
            client_id: authData.clientId,
            subscription_id: authData.subscriptionId,
            school_name: subscription.clients?.schoolName || ''
          }
        })

        if (!customerResult.success) {
          return {
            success: false,
            error: `Failed to create Razorpay customer: ${customerResult.error}`
          }
        }

        razorpayCustomerId = customerResult.customer.id

        // Update subscription with customer ID
        await db.update(subscriptions)
          .set({ razorpayCustomerId })
          .where(eq(subscriptions.id, authData.subscriptionId))
      }

      // Ensure razorpayCustomerId is not null
      if (!razorpayCustomerId) {
        return {
          success: false,
          error: 'Failed to get or create Razorpay customer ID'
        }
      }

      // Create authentication order
      const authResult = await razorpayService.createSubscriptionAuth({
        subscriptionId: authData.subscriptionId,
        customerId: razorpayCustomerId,
        amount: authData.amount,
        currency: authData.currency,
        description: authData.description,
        notes: {
          auth_type: 'subscription_authentication',
          client_id: authData.clientId,
          school_name: subscription.clients?.schoolName || ''
        }
      })

      if (!authResult.success) {
        return {
          success: false,
          error: `Failed to create authentication order: ${authResult.error}`
        }
      }

      // Store authentication transaction details
      await db.insert(subscriptionAuth).values({
        subscriptionId: authData.subscriptionId,
        clientId: authData.clientId,
        razorpayOrderId: authResult.authOrder.id,
        razorpayCustomerId: razorpayCustomerId,
        amount: authData.amount.toString(),
        currency: authData.currency,
        status: 'pending',
        authType: 'initial_authentication',
        createdAt: new Date()
      })

      // Log the authentication transaction creation
      await auditLogger.logPayment('auth_transaction_created', {
        clientId: authData.clientId,
        amount: authData.amount / 100, // Convert paise to rupees
        currency: authData.currency,
        orderId: authResult.authOrder.id,
        success: true
      })

      return {
        success: true,
        authOrderId: authResult.authOrder.id,
        razorpayOrderId: authResult.authOrder.id,
        subscriptionId: authData.subscriptionId
      }

    } catch (error) {
      console.error('❌ Failed to create authentication transaction:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Process successful authentication transaction
   * This activates the subscription for automatic billing
   */
  static async processAuthSuccess(
    razorpayOrderId: string,
    razorpayPaymentId: string,
    razorpaySignature: string
  ): Promise<SubscriptionActivationResult> {
    try {
      // Verify payment signature
      const isValidSignature = razorpayService.verifyPaymentSignature({
        razorpayOrderId,
        razorpayPaymentId,
        razorpaySignature
      })

      if (!isValidSignature) {
        return {
          success: false,
          error: 'Invalid payment signature'
        }
      }

      // Find authentication record
      const [authRecord] = await db.select()
        .from(subscriptionAuth)
        .leftJoin(subscriptions, eq(subscriptionAuth.subscriptionId, subscriptions.id))
        .where(eq(subscriptionAuth.razorpayOrderId, razorpayOrderId))
        .limit(1)

      if (!authRecord.subscription_auth) {
        return {
          success: false,
          error: 'Authentication record not found'
        }
      }

      // Update authentication status
      await db.update(subscriptionAuth)
        .set({
          status: 'completed',
          razorpayPaymentId,
          completedAt: new Date()
        })
        .where(eq(subscriptionAuth.id, authRecord.subscription_auth.id))

      // Activate subscription
      const subscriptionId = authRecord.subscription_auth.subscriptionId
      await db.update(subscriptions)
        .set({
          status: 'active',
          activatedAt: new Date(),
          nextBillingDate: this.calculateNextBillingDate(
            authRecord.subscriptions?.billingCycle as 'monthly' | 'yearly' || 'monthly'
          ).toISOString().split('T')[0]
        })
        .where(eq(subscriptions.id, subscriptionId))

      // Log successful activation
      await auditLogger.logPayment('subscription_activated', {
        clientId: authRecord.subscription_auth.clientId,
        amount: parseFloat(authRecord.subscription_auth.amount) / 100, // Convert paise to rupees
        currency: authRecord.subscription_auth.currency || 'INR',
        paymentId: razorpayPaymentId,
        success: true
      })

      // Send activation confirmation email
      if (authRecord.subscriptions?.clientId) {
        await this.sendActivationEmail(authRecord.subscriptions.clientId, subscriptionId)
      }

      return {
        success: true,
        subscriptionStatus: 'active',
        message: 'Subscription activated successfully for automatic billing'
      }

    } catch (error) {
      console.error('❌ Failed to process authentication success:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Handle authentication failure
   */
  static async processAuthFailure(razorpayOrderId: string, reason: string): Promise<void> {
    try {
      // Find and update authentication record
      const [authRecord] = await db.select()
        .from(subscriptionAuth)
        .where(eq(subscriptionAuth.razorpayOrderId, razorpayOrderId))
        .limit(1)

      if (authRecord) {
        await db.update(subscriptionAuth)
          .set({
            status: 'failed',
            failureReason: reason,
            failedAt: new Date()
          })
          .where(eq(subscriptionAuth.id, authRecord.id))

        // Log the failure
        await auditLogger.logPayment('auth_transaction_failed', {
          clientId: authRecord.clientId,
          amount: parseFloat(authRecord.amount) / 100, // Convert paise to rupees
          currency: authRecord.currency || 'INR',
          orderId: razorpayOrderId,
          success: false,
          errorMessage: reason
        })
      }
    } catch (error) {
      console.error('❌ Failed to process authentication failure:', error)
    }
  }

  /**
   * Calculate next billing date based on billing cycle
   */
  private static calculateNextBillingDate(billingCycle: 'monthly' | 'yearly'): Date {
    const now = new Date()
    const nextBilling = new Date(now)

    if (billingCycle === 'yearly') {
      nextBilling.setFullYear(now.getFullYear() + 1)
    } else {
      nextBilling.setMonth(now.getMonth() + 1)
    }

    return nextBilling
  }

  /**
   * Send activation confirmation email
   */
  private static async sendActivationEmail(clientId: string, subscriptionId: string): Promise<void> {
    try {
      const [client] = await db.select()
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1)

      if (client && client.email) {
        await emailService.sendEmail({
          to: client.email,
          subject: 'Subscription Activated Successfully - Schopio',
          html: `
            <h2>Subscription Activated Successfully!</h2>
            <p>Dear ${client.schoolName || 'School Admin'},</p>
            <p>Your Schopio subscription has been successfully activated.</p>
            <p><strong>Subscription ID:</strong> ${subscriptionId}</p>
            <p><strong>Activated At:</strong> ${new Date().toLocaleString('en-IN')}</p>
            <p>You can now access all Schopio features through your dashboard.</p>
            <p>Best regards,<br>The Schopio Team</p>
          `
        })
      }
    } catch (error) {
      console.error('❌ Failed to send activation email:', error)
    }
  }

  /**
   * Get subscription authentication status
   */
  static async getAuthStatus(subscriptionId: string): Promise<{
    success: boolean
    authStatus?: string
    authRequired?: boolean
    error?: string
  }> {
    try {
      const [subscription] = await db.select()
        .from(subscriptions)
        .where(eq(subscriptions.id, subscriptionId))
        .limit(1)

      if (!subscription) {
        return {
          success: false,
          error: 'Subscription not found'
        }
      }

      const authRequired = subscription.status === 'created'
      
      return {
        success: true,
        authStatus: subscription.status || 'pending',
        authRequired
      }

    } catch (error) {
      console.error('❌ Failed to get auth status:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}
