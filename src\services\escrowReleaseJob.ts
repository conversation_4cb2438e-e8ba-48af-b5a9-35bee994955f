import { db } from '@/src/db'
import { partnerCommissionEscrow, commissionReleaseAudit, partners } from '@/src/db/schema'
import { eq, and, lte, desc } from 'drizzle-orm'
import { commissionProcessor } from './commissionProcessor'

/**
 * Automated Escrow Release Job
 * Runs periodically to process eligible commission releases
 * with comprehensive safety checks and audit logging
 */

export interface ReleaseJobResult {
  success: boolean
  processedCount: number
  successfulReleases: number
  failedReleases: number
  totalAmountReleased: number
  errors: string[]
  executionTime: number
  nextRunTime?: Date
}

export interface ReleaseJobConfig {
  maxBatchSize: number
  maxDailyAmount: number
  emergencyStopEnabled: boolean
  dryRun: boolean
  riskThreshold: number
}

class EscrowReleaseJobService {
  private isRunning = false
  private lastRunTime: Date | null = null
  private dailyReleaseAmount = 0
  private dailyReleaseDate: string | null = null

  /**
   * Main job execution method
   */
  async executeReleaseJob(config: Partial<ReleaseJobConfig> = {}): Promise<ReleaseJobResult> {
    const startTime = Date.now()
    
    // Prevent concurrent execution
    if (this.isRunning) {
      throw new Error('Release job is already running')
    }

    this.isRunning = true

    try {
      const jobConfig: ReleaseJobConfig = {
        maxBatchSize: 50,
        maxDailyAmount: 500000, // ₹5 lakh daily limit
        emergencyStopEnabled: false,
        dryRun: false,
        riskThreshold: 70,
        ...config
      }

      console.log('🚀 Starting escrow release job with config:', jobConfig)

      // Reset daily counter if new day
      this.resetDailyCounterIfNeeded()

      // Check emergency stop
      if (jobConfig.emergencyStopEnabled) {
        console.log('🛑 Emergency stop enabled - skipping release job')
        return this.createJobResult(startTime, 0, 0, 0, 0, ['Emergency stop enabled'])
      }

      // Check daily limit
      if (this.dailyReleaseAmount >= jobConfig.maxDailyAmount) {
        console.log(`💰 Daily release limit reached: ₹${this.dailyReleaseAmount}`)
        return this.createJobResult(startTime, 0, 0, 0, 0, ['Daily release limit reached'])
      }

      // Get eligible escrows
      const eligibleEscrows = await this.getEligibleEscrows(jobConfig)
      
      if (eligibleEscrows.length === 0) {
        console.log('✅ No escrows eligible for release')
        return this.createJobResult(startTime, 0, 0, 0, 0, [])
      }

      console.log(`📋 Found ${eligibleEscrows.length} escrows eligible for release`)

      // Process releases in batches
      const results = await this.processBatchReleases(eligibleEscrows, jobConfig)

      // Update daily counter
      this.dailyReleaseAmount += results.totalAmountReleased

      // Log job completion
      await this.logJobExecution(results, jobConfig)

      console.log(`✅ Release job completed: ${results.successfulReleases}/${results.processedCount} successful`)

      return results

    } catch (error) {
      console.error('❌ Release job failed:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return this.createJobResult(startTime, 0, 0, 0, 0, [errorMessage])
    } finally {
      this.isRunning = false
      this.lastRunTime = new Date()
    }
  }

  /**
   * Get escrows eligible for release
   */
  private async getEligibleEscrows(config: ReleaseJobConfig) {
    const currentTime = new Date()
    
    return await db
      .select({
        id: partnerCommissionEscrow.id,
        partnerId: partnerCommissionEscrow.partnerId,
        schoolId: partnerCommissionEscrow.schoolId,
        commissionAmount: partnerCommissionEscrow.commissionAmount,
        riskScore: partnerCommissionEscrow.riskScore,
        holdUntilDate: partnerCommissionEscrow.holdUntilDate,
        escrowStatus: partnerCommissionEscrow.escrowStatus,
        autoReleaseEnabled: partnerCommissionEscrow.autoReleaseEnabled,
        monthYear: partnerCommissionEscrow.monthYear
      })
      .from(partnerCommissionEscrow)
      .where(and(
        eq(partnerCommissionEscrow.escrowStatus, 'school_paid'),
        eq(partnerCommissionEscrow.autoReleaseEnabled, true),
        lte(partnerCommissionEscrow.holdUntilDate, currentTime),
        lte(partnerCommissionEscrow.riskScore, config.riskThreshold)
      ))
      .orderBy(desc(partnerCommissionEscrow.createdAt))
      .limit(config.maxBatchSize)
  }

  /**
   * Process releases in batches with safety checks
   */
  private async processBatchReleases(escrows: any[], config: ReleaseJobConfig): Promise<ReleaseJobResult> {
    const startTime = Date.now()
    let successfulReleases = 0
    let failedReleases = 0
    let totalAmountReleased = 0
    const errors: string[] = []

    for (const escrow of escrows) {
      try {
        // Check remaining daily limit
        const remainingLimit = config.maxDailyAmount - this.dailyReleaseAmount - totalAmountReleased
        const escrowAmount = parseFloat(escrow.commissionAmount)

        if (escrowAmount > remainingLimit) {
          console.log(`💰 Skipping escrow ${escrow.id} - would exceed daily limit`)
          errors.push(`Escrow ${escrow.id} skipped - daily limit`)
          continue
        }

        // Dry run mode
        if (config.dryRun) {
          console.log(`🧪 DRY RUN: Would release ₹${escrowAmount} from escrow ${escrow.id}`)
          successfulReleases++
          totalAmountReleased += escrowAmount
          continue
        }

        // Process actual release
        const releaseResult = await commissionProcessor.releaseCommissionFromEscrow(
          escrow.id,
          'system', // System user for automated releases
          'automatic'
        )

        if (releaseResult.success) {
          successfulReleases++
          totalAmountReleased += releaseResult.releasedAmount
          console.log(`✅ Released ₹${releaseResult.releasedAmount} from escrow ${escrow.id}`)
        } else {
          failedReleases++
          errors.push(`Escrow ${escrow.id}: ${releaseResult.error || 'Unknown error'}`)
          console.error(`❌ Failed to release escrow ${escrow.id}:`, releaseResult.error)
        }

      } catch (error) {
        failedReleases++
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        errors.push(`Escrow ${escrow.id}: ${errorMessage}`)
        console.error(`❌ Error processing escrow ${escrow.id}:`, error)
      }
    }

    return this.createJobResult(
      startTime,
      escrows.length,
      successfulReleases,
      failedReleases,
      totalAmountReleased,
      errors
    )
  }

  /**
   * Reset daily counter if new day
   */
  private resetDailyCounterIfNeeded() {
    const today = new Date().toISOString().split('T')[0]
    if (this.dailyReleaseDate !== today) {
      this.dailyReleaseAmount = 0
      this.dailyReleaseDate = today
      console.log(`📅 Reset daily release counter for ${today}`)
    }
  }

  /**
   * Create standardized job result
   */
  private createJobResult(
    startTime: number,
    processedCount: number,
    successfulReleases: number,
    failedReleases: number,
    totalAmountReleased: number,
    errors: string[]
  ): ReleaseJobResult {
    const executionTime = Date.now() - startTime
    
    return {
      success: errors.length === 0,
      processedCount,
      successfulReleases,
      failedReleases,
      totalAmountReleased,
      errors,
      executionTime,
      nextRunTime: new Date(Date.now() + 60 * 60 * 1000) // Next hour
    }
  }

  /**
   * Log job execution for audit
   */
  private async logJobExecution(result: ReleaseJobResult, config: ReleaseJobConfig) {
    try {
      // This would typically go to a job execution log table
      console.log('📊 Job execution summary:', {
        timestamp: new Date().toISOString(),
        result,
        config,
        dailyTotal: this.dailyReleaseAmount
      })
    } catch (error) {
      console.error('Failed to log job execution:', error)
    }
  }

  /**
   * Get job status and statistics
   */
  async getJobStatus() {
    return {
      isRunning: this.isRunning,
      lastRunTime: this.lastRunTime,
      dailyReleaseAmount: this.dailyReleaseAmount,
      dailyReleaseDate: this.dailyReleaseDate,
      nextScheduledRun: this.lastRunTime ? new Date(this.lastRunTime.getTime() + 60 * 60 * 1000) : null
    }
  }

  /**
   * Emergency stop all releases
   */
  async emergencyStop(reason: string, adminUserId: string) {
    console.log(`🚨 EMERGENCY STOP activated by ${adminUserId}: ${reason}`)
    
    // This would typically update a global configuration
    // For now, we'll just log it
    
    return {
      success: true,
      message: 'Emergency stop activated',
      timestamp: new Date(),
      reason,
      activatedBy: adminUserId
    }
  }
}

// Export singleton instance
export const escrowReleaseJob = new EscrowReleaseJobService()
