'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Calculator, 
  TrendingUp, 
  Clock, 
  DollarSign, 
  Users, 
  ArrowRight,
  CheckCircle,
  Zap
} from 'lucide-react'

const ROICalculatorSection = () => {
  const [studentCount, setStudentCount] = useState(1000)
  const [currentSystem, setCurrentSystem] = useState('manual')
  const [calculations, setCalculations] = useState({
    adminTimeSaved: 0,
    costSavings: 0,
    revenueProtection: 0,
    totalROI: 0,
    paybackMonths: 0
  })

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const systemTypes = [
    { id: 'manual', label: 'Manual/Paper-based', multiplier: 1.0 },
    { id: 'basic', label: 'Basic Software', multiplier: 0.7 },
    { id: 'multiple&apos;, label: &apos;Multiple Systems', multiplier: 0.8 }
  ]

  useEffect(() => {
    const multiplier = systemTypes.find(s => s.id === currentSystem)?.multiplier || 1.0
    
    // Calculate admin time savings (hours per month)
    const adminTimeSaved = Math.round((studentCount * 0.5 * multiplier) / 10) * 10
    
    // Calculate cost savings (₹ per year)
    const costSavings = Math.round((studentCount * 2400 * multiplier) / 1000) * 1000
    
    // Calculate revenue protection (reduced dropouts)
    const avgFeePerStudent = 50000 // Average annual fee
    const dropoutReduction = 0.05 // 5% dropout reduction
    const revenueProtection = Math.round((studentCount * avgFeePerStudent * dropoutReduction * multiplier) / 100000) * 100000
    
    // Calculate total ROI
    const totalROI = costSavings + revenueProtection
    
    // Calculate payback period (assuming ₹100 per student per month)
    const annualCost = studentCount * 1200 // ₹100 * 12 months
    const paybackMonths = Math.round((annualCost / (totalROI / 12)) * 10) / 10

    setCalculations({
      adminTimeSaved,
      costSavings,
      revenueProtection,
      totalROI,
      paybackMonths
    })
  }, [studentCount, currentSystem, systemTypes])

  const formatCurrency = (amount: number) => {
    if (amount >= 10000000) {
      return `₹${(amount / 10000000).toFixed(1)}Cr`
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`
    } else {
      return `₹${amount.toLocaleString()}`
    }
  }

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-emerald-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Calculator className="w-4 h-4" />
            ROI Calculator
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Calculate Your School&apos;s 
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Return on Investment</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            See exactly how much time and money Schopio can save your school. Adjust the inputs below to get personalized results.
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Input Section */}
            <motion.div
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
            >
              <Card className="bg-white border-0 shadow-xl">
                <CardHeader padding="lg">
                  <h3 className="text-2xl font-bold text-slate-900 mb-2">Your School Details</h3>
                  <p className="text-slate-600">Enter your school information to calculate potential savings</p>
                </CardHeader>
                <CardContent padding="lg">
                  <div className="space-y-6">
                    {/* Student Count */}
                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-3">
                        Number of Students
                      </label>
                      <div className="relative">
                        <input
                          type="range"
                          min="100"
                          max="5000"
                          step="100"
                          value={studentCount}
                          onChange={(e) => setStudentCount(Number(e.target.value))}
                          className="w-full h-3 bg-blue-200 rounded-lg appearance-none cursor-pointer slider"
                        />
                        <div className="flex justify-between text-sm text-slate-500 mt-2">
                          <span>100</span>
                          <span className="font-bold text-blue-600">{studentCount} students</span>
                          <span>5000+</span>
                        </div>
                      </div>
                    </div>

                    {/* Current System */}
                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-3">
                        Current Management System
                      </label>
                      <div className="space-y-2">
                        {systemTypes.map((system) => (
                          <label key={system.id} className="flex items-center gap-3 p-3 border border-slate-200 rounded-lg cursor-pointer hover:bg-blue-50 transition-colors">
                            <input
                              type="radio"
                              name="currentSystem"
                              value={system.id}
                              checked={currentSystem === system.id}
                              onChange={(e) => setCurrentSystem(e.target.value)}
                              className="w-4 h-4 text-blue-600"
                            />
                            <span className="text-slate-700 font-medium">{system.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* Cost Estimate */}
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <DollarSign className="w-5 h-5 text-blue-600" />
                        <span className="font-semibold text-slate-900">Estimated Monthly Cost</span>
                      </div>
                      <div className="text-2xl font-bold text-blue-600">
                        ₹{(studentCount * 100).toLocaleString()}/month
                      </div>
                      <div className="text-sm text-slate-600 mt-1">
                        ₹100 per student per month • No setup fees
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Results Section */}
            <motion.div
              variants={fadeInUp}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
            >
              <Card className="bg-gradient-to-br from-emerald-600 to-blue-600 border-0 shadow-xl text-white">
                <CardHeader padding="lg">
                  <h3 className="text-2xl font-bold mb-2">Your Potential Savings</h3>
                  <p className="text-emerald-100">Based on data from 500+ schools using Schopio</p>
                </CardHeader>
                <CardContent padding="lg">
                  <div className="space-y-6">
                    {/* Key Metrics */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-white/10 p-4 rounded-lg backdrop-blur-sm">
                        <div className="flex items-center gap-2 mb-2">
                          <Clock className="w-5 h-5 text-yellow-300" />
                          <span className="text-sm font-medium">Time Saved</span>
                        </div>
                        <div className="text-2xl font-bold">{calculations.adminTimeSaved}h</div>
                        <div className="text-xs text-emerald-200">per month</div>
                      </div>
                      
                      <div className="bg-white/10 p-4 rounded-lg backdrop-blur-sm">
                        <div className="flex items-center gap-2 mb-2">
                          <TrendingUp className="w-5 h-5 text-yellow-300" />
                          <span className="text-sm font-medium">ROI Timeline</span>
                        </div>
                        <div className="text-2xl font-bold">{calculations.paybackMonths}mo</div>
                        <div className="text-xs text-emerald-200">payback period</div>
                      </div>
                    </div>

                    {/* Savings Breakdown */}
                    <div className="space-y-4">
                      <div className="flex justify-between items-center py-3 border-b border-white/20">
                        <span className="text-emerald-100">Operational Cost Savings</span>
                        <span className="font-bold text-xl">{formatCurrency(calculations.costSavings)}</span>
                      </div>
                      
                      <div className="flex justify-between items-center py-3 border-b border-white/20">
                        <span className="text-emerald-100">Revenue Protection</span>
                        <span className="font-bold text-xl">{formatCurrency(calculations.revenueProtection)}</span>
                      </div>
                      
                      <div className="flex justify-between items-center py-4 bg-white/10 px-4 rounded-lg">
                        <span className="font-bold text-lg">Total Annual ROI</span>
                        <span className="font-bold text-3xl text-yellow-300">{formatCurrency(calculations.totalROI)}</span>
                      </div>
                    </div>

                    {/* Benefits List */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-emerald-300" />
                        <span>60% reduction in administrative workload</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-emerald-300" />
                        <span>35% improvement in student retention</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-emerald-300" />
                        <span>Free implementation and data migration</span>
                      </div>
                    </div>

                    {/* CTA */}
                    <div className="pt-4">
                      <Button
                        size="lg"
                        icon={ArrowRight}
                        iconPosition="right"
                        className="w-full bg-white text-emerald-600 hover:bg-emerald-50 font-bold py-4"
                      >
                        Get Detailed ROI Report
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ROICalculatorSection
