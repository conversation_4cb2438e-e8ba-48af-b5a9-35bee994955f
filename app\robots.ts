import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = 'https://schopio.orionixtech.com'

  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/solutions',
          '/packages',
          '/ai-features',
          '/resources',
          '/demo',
          '/auth',
          // SEO landing pages
          '/school-management-system',
          '/school-erp-software',
          '/educational-management-software',
          '/student-information-system',
        ],
        disallow: [
          '/admin/*',
          '/profile/*',
          '/partner/*',
          '/api/*',
          '/test-*',
          '/debug-*',
          '/clear-*',
          '/cookie-*',
          '/admin-login-debug',
          '/*.disabled/*',
          '/client/*',
        ],
        crawlDelay: 1,
      },
      {
        userAgent: 'Googlebot',
        allow: [
          '/',
          '/solutions',
          '/packages',
          '/ai-features',
          '/resources',
          '/demo',
          // SEO landing pages for Google
          '/school-management-system',
          '/school-erp-software',
          '/educational-management-software',
          '/student-information-system',
        ],
        disallow: [
          '/admin/*',
          '/profile/*',
          '/partner/*',
          '/api/*',
          '/test-*',
          '/debug-*',
          '/clear-*',
          '/cookie-*',
          '/admin-login-debug',
          '/*.disabled/*',
          '/client/*',
        ],
      },
      {
        userAgent: 'Bingbot',
        allow: [
          '/',
          '/solutions',
          '/packages',
          '/ai-features',
          '/resources',
          '/demo',
          '/school-management-system',
          '/school-erp-software',
          '/educational-management-software',
          '/student-information-system',
        ],
        disallow: [
          '/admin/*',
          '/profile/*',
          '/partner/*',
          '/api/*',
        ],
        crawlDelay: 2,
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  }
}
