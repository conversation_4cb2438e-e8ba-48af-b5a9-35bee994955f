/**
 * School Portal Billing Workflow Test
 * 
 * This test simulates the complete billing workflow from a school user's perspective:
 * 1. School login authentication
 * 2. Navigate to billing section
 * 3. View subscription details and billing information
 * 4. Test "Pay Now" button functionality
 * 5. Verify payment flow and error handling
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const fetch = require('node-fetch');

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
  schoolEmail: process.env.TEST_SCHOOL_EMAIL || '<EMAIL>',
  schoolPassword: process.env.TEST_SCHOOL_PASSWORD || 'password123',
  testAmount: 5000 // ₹5000 for testing
};

let schoolToken = null;
let clientId = null;

/**
 * Test school authentication
 */
async function testSchoolAuthentication() {
  console.log('\n🔐 Testing School Authentication...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: TEST_CONFIG.schoolEmail,
        password: TEST_CONFIG.schoolPassword
      })
    });

    const data = await response.json();

    if (response.ok && data.token) {
      schoolToken = data.token;
      clientId = data.user?.clientId;
      console.log(`✅ School authentication successful`);
      console.log(`   Token: ${schoolToken.substring(0, 20)}...`);
      console.log(`   Client ID: ${clientId}`);
      return true;
    } else {
      console.error(`❌ School authentication failed: ${data.error || 'Unknown error'}`);
      console.log('💡 Note: This test requires a valid school user in the database');
      return false;
    }
  } catch (error) {
    console.error(`❌ School authentication error: ${error.message}`);
    return false;
  }
}

/**
 * Test school billing API access
 */
async function testSchoolBillingAccess() {
  console.log('\n💰 Testing School Billing API Access...');
  
  if (!schoolToken) {
    console.error('❌ No school token available');
    return false;
  }

  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/school/billing`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${schoolToken}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    if (response.ok) {
      console.log('✅ School billing API access successful');
      console.log(`   Subscription Status: ${data.subscription?.status || 'N/A'}`);
      console.log(`   Monthly Amount: ₹${data.subscription?.monthlyAmount || 'N/A'}`);
      console.log(`   Outstanding Amount: ₹${data.outstandingAmount || 0}`);
      console.log(`   Payment Status: ${data.paymentStatus || 'N/A'}`);
      return true;
    } else {
      console.error(`❌ School billing API access failed: ${data.error || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ School billing API error: ${error.message}`);
    return false;
  }
}

/**
 * Test subscription payment order creation
 */
async function testPaymentOrderCreation() {
  console.log('\n📄 Testing Payment Order Creation...');
  
  if (!schoolToken) {
    console.error('❌ No school token available');
    return false;
  }

  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/subscriptions/create-payment-order`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${schoolToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount: TEST_CONFIG.testAmount
      })
    });

    const data = await response.json();

    if (response.ok && data.orderId) {
      console.log('✅ Payment order creation successful');
      console.log(`   Order ID: ${data.orderId}`);
      console.log(`   Amount: ₹${data.amount}`);
      console.log(`   Currency: ${data.currency}`);
      return { success: true, orderId: data.orderId };
    } else {
      console.error(`❌ Payment order creation failed: ${data.error || 'Unknown error'}`);
      return { success: false };
    }
  } catch (error) {
    console.error(`❌ Payment order creation error: ${error.message}`);
    return { success: false };
  }
}

/**
 * Test referral code functionality
 */
async function testReferralCodeFunctionality() {
  console.log('\n🔗 Testing Referral Code Functionality...');
  
  if (!schoolToken) {
    console.error('❌ No school token available');
    return false;
  }

  try {
    // Test referral status endpoint
    const statusResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/auth/referral/status`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${schoolToken}`,
        'Content-Type': 'application/json'
      }
    });

    const statusData = await statusResponse.json();

    if (statusResponse.ok) {
      console.log('✅ Referral status API working');
      console.log(`   Has Referral: ${statusData.hasReferral || false}`);
      console.log(`   Partner Name: ${statusData.partnerName || 'N/A'}`);
      
      // Test referral code validation (with a test code)
      const validateResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/auth/referral/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          code: 'TEST123'
        })
      });

      const validateData = await validateResponse.json();
      
      if (validateResponse.ok) {
        console.log('✅ Referral validation API working');
        console.log(`   Test Code Valid: ${validateData.valid || false}`);
      } else {
        console.log('⚠️ Referral validation API returned error (expected for test code)');
      }
      
      return true;
    } else {
      console.error(`❌ Referral status API failed: ${statusData.error || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Referral code functionality error: ${error.message}`);
    return false;
  }
}

/**
 * Test school profile access
 */
async function testSchoolProfileAccess() {
  console.log('\n👤 Testing School Profile Access...');
  
  if (!schoolToken) {
    console.error('❌ No school token available');
    return false;
  }

  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/school/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${schoolToken}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    if (response.ok) {
      console.log('✅ School profile API access successful');
      console.log(`   School Name: ${data.schoolName || 'N/A'}`);
      console.log(`   Contact Person: ${data.contactPerson || 'N/A'}`);
      console.log(`   Email: ${data.email || 'N/A'}`);
      console.log(`   Phone: ${data.phone || 'N/A'}`);
      return true;
    } else {
      console.error(`❌ School profile API access failed: ${data.error || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ School profile API error: ${error.message}`);
    return false;
  }
}

/**
 * Test authentication token validation
 */
async function testTokenValidation() {
  console.log('\n🔍 Testing Authentication Token Validation...');
  
  if (!schoolToken) {
    console.error('❌ No school token available');
    return false;
  }

  try {
    // Test with valid token
    const validResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/school/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${schoolToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (validResponse.ok) {
      console.log('✅ Valid token authentication working');
    } else {
      console.error('❌ Valid token authentication failed');
      return false;
    }

    // Test with invalid token
    const invalidResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/school/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer invalid_token_123`,
        'Content-Type': 'application/json'
      }
    });

    if (invalidResponse.status === 401) {
      console.log('✅ Invalid token properly rejected');
      return true;
    } else {
      console.error('❌ Invalid token not properly rejected');
      return false;
    }
  } catch (error) {
    console.error(`❌ Token validation test error: ${error.message}`);
    return false;
  }
}

/**
 * Run complete school billing workflow test
 */
async function runSchoolBillingWorkflowTest() {
  console.log('🚀 Starting School Portal Billing Workflow Test');
  console.log('='.repeat(60));
  console.log(`🌐 Base URL: ${TEST_CONFIG.baseUrl}`);
  console.log(`📧 Test School Email: ${TEST_CONFIG.schoolEmail}`);
  
  const tests = [
    { name: 'School Authentication', fn: testSchoolAuthentication },
    { name: 'School Billing API Access', fn: testSchoolBillingAccess },
    { name: 'Payment Order Creation', fn: testPaymentOrderCreation },
    { name: 'Referral Code Functionality', fn: testReferralCodeFunctionality },
    { name: 'School Profile Access', fn: testSchoolProfileAccess },
    { name: 'Authentication Token Validation', fn: testTokenValidation }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passedTests++;
      }
      
      // Wait 1 second between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`❌ Test "${test.name}" failed with error: ${error.message}`);
    }
  }
  
  // Print summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 School Billing Workflow Test Summary');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests} tests`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All school billing workflow tests passed!');
    console.log('✅ School portal billing system is working correctly.');
  } else {
    console.log('\n⚠️ Some school billing workflow tests failed.');
    console.log('💡 This may be expected if test data is not set up in the database.');
  }
  
  return passedTests === totalTests;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runSchoolBillingWorkflowTest()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ School billing workflow test suite failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runSchoolBillingWorkflowTest,
  testSchoolAuthentication,
  testSchoolBillingAccess,
  testPaymentOrderCreation,
  testReferralCodeFunctionality,
  testSchoolProfileAccess,
  testTokenValidation
};
