# 🛠️ **DISCOUNT SYSTEM CORRECTIVE ACTION PLAN**

## **🎯 MISSION: FIX ALL CRITICAL BUGS FOR PRODUCTION DEPLOYMENT**

**Objective:** Transform the broken discount system into a fully functional, production-ready solution  
**Timeline:** 2-3 weeks  
**Priority:** CRITICAL - Business blocking

---

## **📋 CORRECTIVE ACTION ROADMAP**

### **🔥 PHASE 1: CRITICAL BUG FIXES (Week 1)**

#### **Day 1-2: Fix Core Discount Application Logic**

**Problem:** Discounts don't actually change billing amounts  
**Solution:** Implement proper amount storage and updates

```typescript
// ✅ CORRECTED: Proper discount application
async function applyDiscount(subscriptionId: string, discountData: DiscountData) {
  await db.transaction(async (tx) => {
    // 1. Get current subscription
    const [subscription] = await tx.select({
      id: billingSubscriptions.id,
      monthlyAmount: billingSubscriptions.monthlyAmount,
      hasActiveDiscount: billingSubscriptions.hasActiveDiscount
    })
    .from(billingSubscriptions)
    .where(eq(billingSubscriptions.id, subscriptionId))
    .limit(1)

    if (!subscription) {
      throw new Error('Subscription not found')
    }

    if (subscription.hasActiveDiscount) {
      throw new Error('Subscription already has an active discount')
    }

    // 2. Calculate amounts
    const originalAmount = parseFloat(subscription.monthlyAmount)
    const discountAmount = (originalAmount * discountData.percentage) / 100
    const discountedAmount = originalAmount - discountAmount

    // 3. Update subscription with proper amounts
    await tx.update(billingSubscriptions)
      .set({
        originalMonthlyAmount: originalAmount.toString(),
        monthlyAmount: discountedAmount.toString(),
        hasActiveDiscount: true,
        currentDiscountPercentage: discountData.percentage.toString(),
        discountStartDate: discountData.startDate.toISOString().split('T')[0],
        discountEndDate: discountData.endDate.toISOString().split('T')[0],
        discountReason: discountData.reason,
        updatedAt: new Date()
      })
      .where(eq(billingSubscriptions.id, subscriptionId))

    // 4. Create audit log
    await auditService.logDiscountApplication(subscriptionId, discountData)
  })
}
```

#### **Day 3-4: Fix Discount Expiration Service**

**Problem:** Expiration service cannot revert amounts  
**Solution:** Ensure proper amount reversion logic

```typescript
// ✅ CORRECTED: Proper discount expiration
async function expireDiscount(subscriptionId: string) {
  await db.transaction(async (tx) => {
    const [subscription] = await tx.select({
      id: billingSubscriptions.id,
      monthlyAmount: billingSubscriptions.monthlyAmount,
      originalMonthlyAmount: billingSubscriptions.originalMonthlyAmount,
      hasActiveDiscount: billingSubscriptions.hasActiveDiscount
    })
    .from(billingSubscriptions)
    .where(eq(billingSubscriptions.id, subscriptionId))
    .limit(1)

    if (!subscription || !subscription.hasActiveDiscount || !subscription.originalMonthlyAmount) {
      return // Nothing to expire
    }

    // Revert to original amount
    await tx.update(billingSubscriptions)
      .set({
        monthlyAmount: subscription.originalMonthlyAmount,
        originalMonthlyAmount: null,
        hasActiveDiscount: false,
        currentDiscountPercentage: '0.00',
        discountStartDate: null,
        discountEndDate: null,
        discountReason: null,
        updatedAt: new Date()
      })
      .where(eq(billingSubscriptions.id, subscriptionId))

    // Log expiration
    await auditService.logDiscountExpiration(subscriptionId)
  })
}
```

#### **Day 5-7: Integrate with Billing System**

**Problem:** Invoices and payments ignore discount system  
**Solution:** Update billing logic to use discounted amounts

```typescript
// ✅ CORRECTED: Invoice generation with discounts
async function generateInvoice(subscriptionId: string) {
  const [subscription] = await db.select({
    id: billingSubscriptions.id,
    monthlyAmount: billingSubscriptions.monthlyAmount,
    originalMonthlyAmount: billingSubscriptions.originalMonthlyAmount,
    hasActiveDiscount: billingSubscriptions.hasActiveDiscount,
    currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage
  })
  .from(billingSubscriptions)
  .where(eq(billingSubscriptions.id, subscriptionId))
  .limit(1)

  if (!subscription) {
    throw new Error('Subscription not found')
  }

  const baseAmount = parseFloat(subscription.originalMonthlyAmount || subscription.monthlyAmount)
  const finalAmount = parseFloat(subscription.monthlyAmount)
  const discountAmount = baseAmount - finalAmount

  await db.insert(billingInvoices).values({
    subscriptionId,
    baseAmount: baseAmount.toString(),
    discountAmount: discountAmount.toString(),
    totalAmount: finalAmount.toString(),
    discountPercentage: subscription.currentDiscountPercentage || '0.00',
    hasDiscount: subscription.hasActiveDiscount || false,
    // ... other fields
  })
}
```

### **🔧 PHASE 2: SYSTEM UNIFICATION (Week 2)**

#### **Day 8-10: Unify Dual Discount Systems**

**Problem:** Two conflicting discount systems  
**Solution:** Migrate to single unified system

1. **Data Migration Script:**
```sql
-- Migrate subscriptionDiscounts to billingSubscriptions
UPDATE billing_subscriptions 
SET 
  has_active_discount = true,
  current_discount_percentage = sd.discount_percentage,
  discount_start_date = sd.start_date,
  discount_end_date = sd.end_date,
  discount_reason = sd.reason
FROM subscription_discounts sd 
WHERE billing_subscriptions.id = sd.subscription_id 
  AND sd.is_active = true;
```

2. **Remove Legacy System References**
3. **Update All APIs to Use Unified System**

#### **Day 11-12: Fix Partner Commission Calculations**

**Problem:** Partners receive incorrect commissions  
**Solution:** Update all commission calculation logic

```typescript
// ✅ CORRECTED: Partner commission calculation
function calculatePartnerCommission(subscription: Subscription, payment: Payment) {
  const actualAmount = subscription.hasActiveDiscount 
    ? parseFloat(subscription.monthlyAmount)  // Discounted amount
    : parseFloat(subscription.originalMonthlyAmount || subscription.monthlyAmount)

  const operationalExpenses = calculateOperationalExpenses(subscription)
  const netAmount = actualAmount - operationalExpenses
  const commission = Math.max(0, netAmount * (subscription.partnerCommissionPercentage / 100))

  return {
    baseAmount: actualAmount,
    operationalExpenses,
    netAmount,
    commissionPercentage: subscription.partnerCommissionPercentage,
    commissionAmount: commission
  }
}
```

#### **Day 13-14: Fix School Portal Display**

**Problem:** Misleading discount information  
**Solution:** Update display logic to show accurate data

```typescript
// ✅ CORRECTED: School portal discount display
function DiscountDisplay({ subscription }: { subscription: Subscription }) {
  if (!subscription.hasActiveDiscount || !subscription.originalMonthlyAmount) {
    return <span>No active discount</span>
  }

  const originalAmount = parseFloat(subscription.originalMonthlyAmount)
  const discountedAmount = parseFloat(subscription.monthlyAmount)
  const monthlySavings = originalAmount - discountedAmount
  const discountPercentage = parseFloat(subscription.currentDiscountPercentage || '0')

  return (
    <div className="discount-info">
      <Badge className="bg-green-100 text-green-800">
        {discountPercentage}% OFF
      </Badge>
      <div className="savings-info">
        <p className="original-amount line-through">
          Original: ₹{originalAmount.toLocaleString()}
        </p>
        <p className="discounted-amount">
          Discounted: ₹{discountedAmount.toLocaleString()}
        </p>
        <p className="monthly-savings text-green-600">
          Monthly Savings: ₹{monthlySavings.toLocaleString()}
        </p>
        {subscription.discountEndDate && (
          <p className="validity">
            Valid until: {new Date(subscription.discountEndDate).toLocaleDateString()}
          </p>
        )}
      </div>
    </div>
  )
}
```

### **🧪 PHASE 3: TESTING & VALIDATION (Week 3)**

#### **Day 15-17: Comprehensive Testing**

1. **Unit Tests:** Test all discount functions individually
2. **Integration Tests:** Test complete discount workflows
3. **End-to-End Tests:** Test across all portals
4. **Edge Case Tests:** Test failure scenarios and recovery

#### **Day 18-19: Financial Validation**

1. **Amount Calculation Tests:** Verify all mathematical operations
2. **Commission Accuracy Tests:** Validate partner commission calculations
3. **Billing Integration Tests:** Ensure invoices reflect discounts
4. **Expiration Tests:** Verify automatic discount expiration

#### **Day 20-21: Production Readiness**

1. **Performance Testing:** Ensure system handles load
2. **Security Testing:** Validate access controls and permissions
3. **Documentation Update:** Complete all documentation
4. **Deployment Preparation:** Prepare production deployment plan

---

## **✅ SUCCESS CRITERIA**

### **Functional Requirements:**
- [ ] Discounts actually reduce billing amounts
- [ ] School portal shows accurate discount information
- [ ] Partner commissions calculated on correct amounts
- [ ] Discounts expire automatically and revert amounts
- [ ] Invoices reflect discount information
- [ ] All portals show consistent data

### **Technical Requirements:**
- [ ] Zero TypeScript compilation errors
- [ ] All tests passing (unit, integration, e2e)
- [ ] Database transactions properly implemented
- [ ] Error handling comprehensive
- [ ] Audit logging complete

### **Business Requirements:**
- [ ] Financial calculations accurate
- [ ] Customer experience professional
- [ ] Partner transparency maintained
- [ ] Revenue protection ensured
- [ ] Compliance requirements met

---

## **🚀 DEPLOYMENT PLAN**

### **Pre-Deployment Checklist:**
- [ ] All 14 critical bugs fixed
- [ ] Comprehensive testing completed
- [ ] Financial calculations validated
- [ ] Stakeholder approval obtained
- [ ] Rollback plan prepared

### **Deployment Steps:**
1. **Database Migration:** Apply schema updates and data migration
2. **Service Deployment:** Deploy updated services with new logic
3. **Validation:** Verify all systems working correctly
4. **Monitoring:** Monitor for any issues or errors

### **Post-Deployment:**
1. **Monitor System Performance**
2. **Validate Financial Calculations**
3. **Collect User Feedback**
4. **Address Any Issues Immediately**

---

## **🎯 CONCLUSION**

This corrective action plan addresses all 14 critical bugs identified in the audit and provides a clear path to a fully functional, production-ready discount system.

**Timeline:** 3 weeks  
**Effort:** High  
**Risk:** Medium (with proper testing)  
**Business Impact:** High (enables discount functionality)

**The plan ensures that the discount system will work correctly across all portals with accurate financial calculations and proper automation.**
