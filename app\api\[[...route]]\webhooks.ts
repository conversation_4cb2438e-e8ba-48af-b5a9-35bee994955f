import { Hono } from 'hono'
import { db } from '@/src/db'
import { billingPayments, billingInvoices, subscriptions, clients, schoolReferrals, partners, operationalExpenses, partnerEarnings, partnerTransactions, billingSubscriptions, webhookIdempotency, partnerCommissionEscrow, commissionReleaseAudit } from '@/src/db/schema'
import { eq, and, sql } from 'drizzle-orm'
import { razorpayService } from '@/src/services/razorpayService'
import { emailService } from '@/src/services/emailService'
import { commissionProcessor } from '@/src/services/commissionProcessor'

const app = new Hono()

/**
 * Verify Razorpay webhook signature using the service
 */
function verifyWebhookSignature(body: string, signature: string): boolean {
  return razorpayService.verifyWebhookSignature(body, signature)
}

/**
 * Generate idempotency key for webhook processing
 */
function generateIdempotencyKey(webhookData: any): string {
  const { event, payload } = webhookData

  if (event.startsWith('payment.')) {
    const payment = payload.payment?.entity || payload.payment
    return `razorpay_${event}_${payment.id}_${payment.order_id}`
  }

  if (event.startsWith('subscription.')) {
    const subscription = payload.subscription?.entity || payload.subscription
    return `razorpay_${event}_${subscription.id}`
  }

  // Fallback for other events
  return `razorpay_${event}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

/**
 * Check and record webhook idempotency to prevent duplicate processing
 */
async function checkWebhookIdempotency(webhookData: any): Promise<{ shouldProcess: boolean; existingResult?: any }> {
  const idempotencyKey = generateIdempotencyKey(webhookData)

  try {
    // Check if this webhook has already been processed
    const [existingRecord] = await db.select()
      .from(webhookIdempotency)
      .where(eq(webhookIdempotency.idempotencyKey, idempotencyKey))
      .limit(1)

    if (existingRecord) {
      console.log('Webhook already processed:', {
        idempotencyKey,
        createdAt: existingRecord.processedAt,
        event: webhookData.event
      })

      return {
        shouldProcess: false,
        existingResult: existingRecord.processingResult
      }
    }

    // Record this webhook for idempotency tracking
    await db.insert(webhookIdempotency).values({
      idempotencyKey,
      webhookSource: 'razorpay',
      eventType: webhookData.event,
      webhookData: webhookData,
      createdAt: new Date()
    })

    console.log('Webhook recorded for processing:', {
      idempotencyKey,
      event: webhookData.event
    })

    return { shouldProcess: true }

  } catch (error) {
    console.error('Error checking webhook idempotency:', error)
    // If idempotency check fails, allow processing but log the error
    return { shouldProcess: true }
  }
}

/**
 * Update webhook processing result
 */
async function updateWebhookResult(webhookData: any, result: any): Promise<void> {
  const idempotencyKey = generateIdempotencyKey(webhookData)

  try {
    await db.update(webhookIdempotency)
      .set({
        processingResult: result
      })
      .where(eq(webhookIdempotency.idempotencyKey, idempotencyKey))

    console.log('Webhook result updated:', {
      idempotencyKey,
      success: result.success
    })
  } catch (error) {
    console.error('Error updating webhook result:', error)
  }
}

/**
 * Enhanced commission escrow processing using Commission Processor Service
 */
async function processCommissionEscrow(payment: any, partnerAttribution: any, tx: any): Promise<void> {
  try {
    const grossAmount = parseFloat(payment.amount)

    // Use the new commission processor service
    const result = await commissionProcessor.processCommissionForPayment(
      payment.id,
      partnerAttribution.partnerId,
      payment.clientId,
      grossAmount
    )

    if (!result.success) {
      throw new Error(result.error || 'Commission processing failed')
    }

    console.log('✅ Commission escrow created via processor:', {
      escrowId: result.escrowId,
      partnerId: partnerAttribution.partnerId,
      schoolId: payment.clientId,
      commissionAmount: result.commissionAmount,
      riskScore: result.riskScore,
      holdUntilDate: result.holdUntilDate?.toISOString()
    })

  } catch (error) {
    console.error('❌ Error processing commission escrow:', error)
    throw error
  }
}



/**
 * Handle payment success webhook
 */
async function handlePaymentSuccess(paymentData: any) {
  try {
    const { payment } = paymentData

    // Use database transaction to ensure atomicity
    const result = await db.transaction(async (tx) => {
      // Find and lock the invoice by order ID to prevent concurrent updates
      const [invoice] = await tx.select()
        .from(billingInvoices)
        .where(eq(billingInvoices.razorpayOrderId, payment.order_id))
        .for('update')
        .limit(1)

      if (!invoice) {
        console.error('Invoice not found for order ID:', payment.order_id)
        throw new Error('Invoice not found')
      }

      // Check if payment already exists (idempotency check)
      const [existingPayment] = await tx.select()
        .from(billingPayments)
        .where(eq(billingPayments.razorpayPaymentId, payment.id))
        .limit(1)

      if (existingPayment) {
        console.log('Payment already processed:', payment.id)
        return { payment: existingPayment, isExisting: true }
      }

      // Create payment record
      const [newPayment] = await tx.insert(billingPayments).values({
        invoiceId: invoice.id,
        clientId: invoice.clientId,
        razorpayPaymentId: payment.id,
        razorpayOrderId: payment.order_id,
        amount: (payment.amount / 100).toString(), // Convert from paise
        currency: payment.currency,
        status: 'succeeded',
        paymentMethod: payment.method,
        createdAt: new Date(payment.created_at * 1000)
      }).returning()

      // Update invoice status
      await tx.update(billingInvoices)
        .set({
          status: 'paid',
          paidDate: new Date(payment.created_at * 1000).toISOString().split('T')[0] // Convert to date string
        })
        .where(eq(billingInvoices.id, invoice.id))

      // Update billing cycle status if applicable (with row-level locking)
      if (invoice.subscriptionId) {
        // Lock the billing cycle row to prevent concurrent updates
        const [billingCycle] = await tx.select()
          .from(billingSubscriptions)
          .where(eq(billingSubscriptions.id, invoice.subscriptionId))
          .for('update')
          .limit(1)

        if (billingCycle) {
          await tx.update(billingSubscriptions)
            .set({ status: 'paid' })
            .where(eq(billingSubscriptions.id, invoice.subscriptionId))
        }
      }

      // Check for partner attribution and calculate earnings (only if invoice has clientId)
      if (!invoice.clientId) {
        console.log('Payment processed successfully (no client attribution):', {
          paymentId: newPayment.id,
          invoiceId: invoice.id,
          monthlyAmount: newPayment.amount
        })
        return { payment: newPayment, isExisting: false }
      }

      const [partnerAttribution] = await tx
      .select({
        partnerId: schoolReferrals.partnerId,
        partnerName: partners.name,
        partnerCode: partners.partnerCode,
        profitSharePercentage: partners.profitSharePercentage,
        clientName: clients.schoolName
      })
      .from(schoolReferrals)
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .leftJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .where(and(
        eq(schoolReferrals.clientId, invoice.clientId),
        eq(schoolReferrals.isActive, true),
        eq(partners.isActive, true)
      ))
      .limit(1)

    if (partnerAttribution) {
      try {
        // Process commission through new escrow system
        await processCommissionEscrow(newPayment, partnerAttribution, tx)

        // Legacy partner earnings for backward compatibility (marked as escrow)
        const grossAmount = parseFloat(newPayment.amount)
        const expenses = await tx.select()
          .from(operationalExpenses)
          .where(eq(operationalExpenses.isActive, true))

        const totalExpenses = expenses.reduce((sum, expense) => {
          if (expense.amountPerSchool) {
            return sum + parseFloat(expense.amountPerSchool)
          }
          return sum
        }, 0)
        const netProfit = Math.max(0, grossAmount - totalExpenses)
        const partnerSharePercentage = parseFloat(partnerAttribution.profitSharePercentage?.toString() || '40')
        const partnerEarning = (netProfit * partnerSharePercentage) / 100

        if (partnerEarning > 0) {
          // Create legacy partner earning record (now marked as in escrow)
          await tx.insert(partnerEarnings).values({
            partnerId: partnerAttribution.partnerId,
            clientId: invoice.clientId,
            invoiceId: invoice.id,
            paymentId: newPayment.id,
            grossAmount: grossAmount.toString(),
            totalExpenses: totalExpenses.toString(),
            netProfit: netProfit.toString(),
            partnerSharePercentage: partnerSharePercentage.toString(),
            partnerEarning: partnerEarning.toString(),
            status: 'in_escrow', // Updated status to reflect escrow system
            escrowStatus: 'pending_release'
          }).returning()

          console.log('✅ Commission processed through escrow system:', {
            partnerId: partnerAttribution.partnerId,
            partnerName: partnerAttribution.partnerName,
            clientName: partnerAttribution.clientName,
            grossAmount,
            partnerEarning,
            status: 'in_escrow'
          })
        }
      } catch (earningsError) {
        console.error('❌ Error processing commission escrow:', earningsError)
        // Don't fail the payment processing if commission processing fails
      }
    }

      return { payment: newPayment, isExisting: false }
    })

    console.log('Payment processed successfully:', {
      paymentId: result.payment.id,
      invoiceId: result.payment.invoiceId,
      monthlyAmount: result.payment.amount,
      isExisting: result.isExisting
    })

    // Send payment confirmation email (only for new payments, not existing ones)
    if (!result.isExisting && result.payment.invoiceId) {
      try {
        // Get invoice and client details for email
        const [invoiceDetails] = await db.select({
          invoiceNumber: billingInvoices.invoiceNumber,
          totalAmount: billingInvoices.totalAmount,
          clientId: billingInvoices.clientId
        })
        .from(billingInvoices)
        .where(eq(billingInvoices.id, result.payment.invoiceId))
        .limit(1)

        if (invoiceDetails && invoiceDetails.clientId) {
          const [clientDetails] = await db.select({
            schoolName: clients.schoolName,
            email: clients.email,
            contactPerson: clients.contactPerson
          })
          .from(clients)
          .where(eq(clients.id, invoiceDetails.clientId))
          .limit(1)

          if (clientDetails && clientDetails.email && invoiceDetails.clientId) {
            const emailResult = await emailService.sendPaymentConfirmationWithPDF({
              schoolName: clientDetails.schoolName,
              contactPerson: clientDetails.contactPerson,
              email: clientDetails.email,
              invoiceNumber: invoiceDetails.invoiceNumber,
              amount: invoiceDetails.totalAmount,
              dueDate: new Date().toISOString().split('T')[0], // Payment date
              receiptUrl: `${process.env.FRONTEND_URL || 'https://schopio.com'}/billing/receipt/${result.payment.id}`,
              invoiceId: result.payment.invoiceId
            })

            if (emailResult.success) {
              console.log(`✅ Payment confirmation email sent to ${clientDetails.email} (ID: ${'id' in emailResult ? emailResult.id : 'unknown'})`)
            } else {
              console.error(`❌ Failed to send payment confirmation email: ${'error' in emailResult ? emailResult.error : 'Unknown error'}`)
            }
          }
        }
      } catch (emailError) {
        console.error('❌ Error sending payment confirmation email:', emailError)
        // Don't fail the webhook if email fails
      }
    }

    return { success: true, payment: result.payment }
  } catch (error) {
    console.error('Error handling payment success:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Handle payment failure webhook
 */
async function handlePaymentFailure(paymentData: any) {
  try {
    const { payment } = paymentData

    // Use database transaction to ensure atomicity
    const result = await db.transaction(async (tx) => {
      // Find and lock the invoice by order ID to prevent concurrent updates
      const [invoice] = await tx.select()
        .from(billingInvoices)
        .where(eq(billingInvoices.razorpayOrderId, payment.order_id))
        .for('update')
        .limit(1)

      if (!invoice) {
        console.error('Invoice not found for order ID:', payment.order_id)
        throw new Error('Invoice not found')
      }

      // Check if payment failure already recorded (idempotency check)
      const [existingPayment] = await tx.select()
        .from(billingPayments)
        .where(eq(billingPayments.razorpayPaymentId, payment.id))
        .limit(1)

      if (existingPayment) {
        console.log('Payment failure already recorded:', payment.id)
        return { payment: existingPayment, isExisting: true }
      }

      // Create failed payment record
      const [failedPayment] = await tx.insert(billingPayments).values({
        invoiceId: invoice.id,
        clientId: invoice.clientId,
        razorpayPaymentId: payment.id,
        razorpayOrderId: payment.order_id,
        amount: (payment.amount / 100).toString(),
        currency: payment.currency,
        status: 'failed',
        paymentMethod: payment.method,
        failureReason: payment.error_description || 'Payment failed',
        createdAt: new Date(payment.created_at * 1000)
      }).returning()

      return { payment: failedPayment, isExisting: false }
    })

    console.log('Payment failure recorded:', {
      paymentId: result.payment.id,
      invoiceId: result.payment.invoiceId,
      reason: payment.error_description,
      isExisting: result.isExisting
    })

    return { success: true, payment: result.payment }
  } catch (error) {
    console.error('Error handling payment failure:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Handle subscription webhook events
 */
async function handleSubscriptionEvent(subscriptionData: any, eventType: string) {
  try {
    const { subscription } = subscriptionData
    
    // Find and lock subscription by Razorpay subscription ID to prevent concurrent updates
    const [existingSubscription] = await db.select()
      .from(subscriptions)
      .where(eq(subscriptions.razorpaySubscriptionId, subscription.id))
      .for('update')
      .limit(1)

    if (!existingSubscription) {
      console.error('Subscription not found:', subscription.id)
      return { success: false, error: 'Subscription not found' }
    }

    let updateData: any = {}

    switch (eventType) {
      case 'subscription.activated':
        updateData.status = 'active'
        break
      case 'subscription.cancelled':
        updateData.status = 'cancelled'
        break
      case 'subscription.completed':
        updateData.status = 'completed'
        break
      case 'subscription.charged':
        // Handle successful automatic subscription charge
        updateData.status = 'active'
        updateData.nextBillingDate = calculateNextBillingDate(
          existingSubscription.billingCycle as 'monthly' | 'yearly',
          new Date()
        ).toISOString().split('T')[0]

        // Create billing cycle and invoice for the automatic charge
        await createAutomaticBillingRecord(subscriptionData, existingSubscription)
        break
      case 'subscription.pending':
        updateData.status = 'pending'
        break
      case 'subscription.halted':
        updateData.status = 'suspended'
        break
      case 'subscription.charge_failed':
        // Handle failed automatic subscription charge
        updateData.status = 'payment_failed'

        // Trigger payment failure handling workflow
        await handleSubscriptionPaymentFailure(subscriptionData, existingSubscription)
        break
      default:
        console.log('Unhandled subscription event:', eventType)
        return { success: true, message: 'Event ignored' }
    }

    // Update subscription
    await db.update(subscriptions)
      .set(updateData)
      .where(eq(subscriptions.id, existingSubscription.id))

    console.log('Subscription updated:', {
      id: existingSubscription.id,
      event: eventType,
      status: updateData.status
    })

    return { success: true, subscription: existingSubscription }
  } catch (error) {
    console.error('Error handling subscription event:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Handle subscription payment failure
 */
async function handleSubscriptionPaymentFailure(webhookPayload: any, subscription: any): Promise<void> {
  try {
    console.log(`🚨 Processing payment failure for subscription ${subscription.id}`)

    // Calculate grace period end date
    const gracePeriodEnd = new Date()
    gracePeriodEnd.setDate(gracePeriodEnd.getDate() + subscription.gracePeriodDays)

    // Create billing cycle record for the failed payment
    const billingCycleStart = new Date()
    billingCycleStart.setDate(1) // Start of current month

    const billingCycleEnd = new Date(billingCycleStart)
    billingCycleEnd.setMonth(billingCycleEnd.getMonth() + 1)
    billingCycleEnd.setDate(0) // Last day of current month

    const [billingCycle] = await db.insert(billingSubscriptions).values({
      currentPeriodStart: billingCycleStart.toISOString().split('T')[0],
      currentPeriodEnd: billingCycleEnd.toISOString().split('T')[0],
      studentCount: subscription.studentCount,
      pricePerStudent: (parseFloat(subscription.monthlyAmount) / subscription.studentCount).toString(),
      monthlyAmount: subscription.monthlyAmount,
      status: 'payment_failed',
      nextBillingDate: new Date().toISOString().split('T')[0], // Due immediately
      gracePeriodDays: subscription.gracePeriodDays
    }).returning()

    // Create invoice for the failed payment
    const [invoice] = await db.insert(billingInvoices).values({
      subscriptionId: billingCycle.id,
      clientId: subscription.clientId,
      invoiceNumber: `INV-${Date.now()}-${subscription.clientId.slice(-6)}`,
      subtotal: subscription.monthlyAmount,
      taxAmount: '0',
      totalAmount: subscription.monthlyAmount,
      issuedDate: new Date().toISOString().split('T')[0],
      dueDate: gracePeriodEnd.toISOString().split('T')[0],
      status: 'open',
      periodStart: billingCycleStart.toISOString().split('T')[0],
      periodEnd: billingCycleEnd.toISOString().split('T')[0],
      razorpayOrderId: webhookPayload.payment?.entity?.order_id || null
    }).returning()

    // Send payment failure notification
    try {
      const client = await db.select({
        schoolName: clients.schoolName,
        email: clients.email
      }).from(clients).where(eq(clients.id, subscription.clientId)).limit(1)

      if (client.length > 0) {
        const failureEmailHtml = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #dc2626; margin-bottom: 20px;">⚠️ Payment Failed - Action Required</h2>

            <p>Dear ${client[0].schoolName} Team,</p>

            <p>We were unable to process your automatic subscription payment. Please take immediate action to avoid service interruption.</p>

            <div style="background-color: #fef2f2; border-left: 4px solid #dc2626; padding: 20px; margin: 20px 0;">
              <h3 style="color: #dc2626; margin-top: 0;">Payment Details</h3>
              <p><strong>Amount:</strong> ₹${subscription.monthlyAmount}</p>
              <p><strong>Due Date:</strong> ${gracePeriodEnd.toLocaleDateString()}</p>
              <p><strong>Grace Period:</strong> ${subscription.gracePeriodDays} days</p>
              <p><strong>Invoice:</strong> ${invoice.invoiceNumber}</p>
            </div>

            <div style="background-color: #fffbeb; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0;">
              <h3 style="color: #f59e0b; margin-top: 0;">Important Notice</h3>
              <p>• You have ${subscription.gracePeriodDays} days to make payment</p>
              <p>• After grace period: 2% daily penalty charges apply</p>
              <p>• Service may be suspended after 15 days</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/profile/billing" style="background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Pay Now</a>
            </div>

            <p>Please log in to your school portal to make payment or contact support if you need assistance.</p>

            <p>Best regards,<br>Schopio Billing Team</p>
          </div>
        `

        await emailService.sendEmail({
          to: client[0].email,
          subject: `⚠️ Payment Failed - ${client[0].schoolName} - Action Required`,
          html: failureEmailHtml
        })

        console.log(`📧 Payment failure notification sent to ${client[0].email}`)
      }
    } catch (emailError) {
      console.error('Failed to send payment failure notification:', emailError)
    }

    console.log(`✅ Payment failure processed for subscription ${subscription.id}`)

  } catch (error) {
    console.error('Error handling subscription payment failure:', error)
    throw error
  }
}

/**
 * Commission escrow release endpoint (for automated and manual releases)
 */
app.post('/commission-release', async (c) => {
  try {
    const body = await c.req.json()
    const { escrowId, releaseType = 'automatic', adminUserId = null } = body

    if (!escrowId) {
      return c.json({ error: 'Missing escrowId' }, 400)
    }

    const result = await db.transaction(async (tx) => {
      // Get and lock escrow record
      const [escrowRecord] = await tx.select()
        .from(partnerCommissionEscrow)
        .where(eq(partnerCommissionEscrow.id, escrowId))
        .for('update')
        .limit(1)

      if (!escrowRecord) {
        throw new Error('Escrow record not found')
      }

      if (escrowRecord.escrowStatus !== 'school_paid') {
        throw new Error(`Cannot release commission in status: ${escrowRecord.escrowStatus}`)
      }

      // Check if automatic release is allowed
      if (releaseType === 'automatic' && !escrowRecord.autoReleaseEnabled) {
        throw new Error('Automatic release not enabled for this escrow record')
      }

      // Check hold period for automatic releases
      if (releaseType === 'automatic' && escrowRecord.holdUntilDate && new Date() < escrowRecord.holdUntilDate) {
        throw new Error('Hold period not yet expired')
      }

      // Update escrow status to released
      await tx.update(partnerCommissionEscrow)
        .set({
          escrowStatus: 'released',
          releasedAt: new Date(),
          releasedBy: adminUserId
        })
        .where(eq(partnerCommissionEscrow.id, escrowId))

      // Update partner earnings status (if schoolPaymentId exists)
      if (escrowRecord.schoolPaymentId) {
        await tx.update(partnerEarnings)
          .set({
            status: 'available',
            escrowStatus: 'released'
          })
          .where(and(
            eq(partnerEarnings.partnerId, escrowRecord.partnerId),
            eq(partnerEarnings.paymentId, escrowRecord.schoolPaymentId)
          ))
      }

      // Create audit trail
      await tx.insert(commissionReleaseAudit).values({
        escrowId: escrowRecord.id,
        actionType: 'commission_released',
        previousStatus: 'school_paid',
        newStatus: 'released',
        actionReason: releaseType === 'automatic' ? 'Automatic release after hold period' : 'Manual release by admin',
        amountAffected: escrowRecord.commissionAmount,
        triggeredBy: releaseType === 'automatic' ? 'system' : 'admin',
        triggeredByUser: adminUserId,
        riskFactors: {
          riskScore: escrowRecord.riskScore,
          releaseType
        },
        metadata: {
          releaseType,
          adminUserId,
          commissionAmount: escrowRecord.commissionAmount
        }
      })

      // Create partner transaction for the released commission
      const commissionAmount = parseFloat(escrowRecord.commissionAmount)

      // Get current partner balance
      const [balanceResult] = await tx
        .select({
          balance: sql<number>`COALESCE(SUM(CASE
            WHEN ${partnerTransactions.transactionType} IN ('EARNING', 'BONUS') THEN ${partnerTransactions.amount}
            WHEN ${partnerTransactions.transactionType} IN ('WITHDRAWAL', 'PENALTY') THEN -${partnerTransactions.amount}
            ELSE 0
          END), 0)`
        })
        .from(partnerTransactions)
        .where(eq(partnerTransactions.partnerId, escrowRecord.partnerId))

      const currentBalance = balanceResult?.balance || 0
      const newBalance = currentBalance + commissionAmount

      await tx.insert(partnerTransactions).values({
        partnerId: escrowRecord.partnerId,
        transactionType: 'EARNING',
        amount: commissionAmount.toString(),
        description: `Commission released from escrow - ${escrowRecord.monthYear}`,
        referenceId: escrowRecord.id,
        referenceType: 'commission_escrow',
        balanceBefore: currentBalance.toString(),
        balanceAfter: newBalance.toString(),
        createdBy: adminUserId
      })

      return {
        success: true,
        escrowId: escrowRecord.id,
        commissionAmount,
        newBalance,
        releaseType
      }
    })

    console.log('✅ Commission released from escrow:', result)
    return c.json(result)

  } catch (error) {
    console.error('❌ Error releasing commission:', error)
    return c.json({
      error: 'Commission release failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

/**
 * Main webhook endpoint
 */
app.post('/razorpay', async (c) => {
  try {
    const body = await c.req.text()
    const signature = c.req.header('x-razorpay-signature')

    if (!signature) {
      console.error('Webhook received without signature')
      return c.json({ error: 'Missing signature' }, 400)
    }

    // Verify webhook signature
    const isValid = verifyWebhookSignature(body, signature)

    if (!isValid) {
      console.error('Invalid webhook signature received')
      return c.json({ error: 'Invalid signature' }, 401)
    }

    let webhookData
    try {
      webhookData = JSON.parse(body)
    } catch (parseError) {
      console.error('Invalid JSON in webhook body:', parseError)
      return c.json({ error: 'Invalid JSON payload' }, 400)
    }

    const { event, payload } = webhookData

    if (!event || !payload) {
      console.error('Missing required webhook fields:', { event, payload: !!payload })
      return c.json({ error: 'Invalid webhook payload structure' }, 400)
    }

    console.log('Webhook received:', { event, entity: payload.payment?.entity || payload.subscription?.entity })

    // Check webhook idempotency to prevent duplicate processing
    const idempotencyCheck = await checkWebhookIdempotency(webhookData)

    if (!idempotencyCheck.shouldProcess) {
      console.log('Webhook already processed, returning cached result')
      return c.json(idempotencyCheck.existingResult || { message: 'Webhook already processed' }, 200)
    }

    let result

    switch (event) {
      case 'payment.captured':
      case 'payment.authorized':
        result = await handlePaymentSuccess(payload)
        break

      case 'payment.failed':
        result = await handlePaymentFailure(payload)
        break

      case 'subscription.activated':
      case 'subscription.cancelled':
      case 'subscription.completed':
      case 'subscription.charged':
      case 'subscription.charge_failed':
      case 'subscription.pending':
      case 'subscription.halted':
        result = await handleSubscriptionEvent(payload, event)
        break

      default:
        console.log('Unhandled webhook event:', event)
        return c.json({ message: 'Event received but not processed' }, 200)
    }

    // Update webhook processing result for idempotency tracking
    await updateWebhookResult(webhookData, result)

    if (result.success) {
      return c.json({ message: 'Webhook processed successfully', data: result }, 200)
    } else {
      console.error('Webhook processing failed:', result.error)
      return c.json({ error: 'Webhook processing failed', details: result.error }, 500)
    }

  } catch (error) {
    console.error('Webhook error:', error)
    return c.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, 500)
  }
})

/**
 * Test webhook endpoint for development
 */
app.post('/test', async (c) => {
  try {
    const body = await c.req.json()
    
    console.log('Test webhook received:', body)
    
    // Simulate webhook processing
    const result = await handlePaymentSuccess({
      payment: {
        id: 'pay_test_123',
        order_id: body.order_id || 'order_test_123',
        monthlyAmount: body.amount || 1500000, // 15000 INR in paise
        currency: 'INR',
        method: 'card',
        created_at: Math.floor(Date.now() / 1000)
      }
    })

    return c.json({ 
      message: 'Test webhook processed', 
      result 
    }, 200)

  } catch (error) {
    console.error('Test webhook error:', error)
    return c.json({ 
      error: 'Test webhook failed', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, 500)
  }
})

/**
 * Webhook health check endpoint
 */
app.get('/health', async (c) => {
  try {
    // Check database connectivity by querying a simple table
    await db.select({ count: sql<number>`1` }).from(webhookIdempotency).limit(1)

    return c.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected',
      services: {
        razorpay: process.env.RAZORPAY_KEY_ID ? 'configured' : 'missing',
        email: process.env.RESEND_API_KEY ? 'configured' : 'missing'
      }
    })
  } catch (error) {
    return c.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

/**
 * Webhook status endpoint
 */
app.get('/status', async (c) => {
  return c.json({
    status: 'active',
    timestamp: new Date().toISOString(),
    webhookSecret: process.env.RAZORPAY_WEBHOOK_SECRET ? 'configured' : 'missing',
    razorpayKeys: {
      keyId: process.env.RAZORPAY_KEY_ID ? 'configured' : 'missing',
      keySecret: process.env.RAZORPAY_KEY_SECRET ? 'configured' : 'missing'
    },
    endpoints: {
      main: '/api/webhooks/razorpay',
      test: '/api/webhooks/test',
      health: '/api/webhooks/health',
      status: '/api/webhooks/status'
    }
  })
})

/**
 * Calculate next billing date based on billing cycle
 */
function calculateNextBillingDate(billingCycle: 'monthly' | 'yearly', currentDate: Date): Date {
  const nextBilling = new Date(currentDate)

  if (billingCycle === 'yearly') {
    nextBilling.setFullYear(currentDate.getFullYear() + 1)
  } else {
    nextBilling.setMonth(currentDate.getMonth() + 1)
  }

  return nextBilling
}

/**
 * Create billing cycle and invoice for automatic subscription charge
 */
async function createAutomaticBillingRecord(webhookPayload: any, subscription: any): Promise<void> {
  try {
    const chargeDate = new Date()
    const cycleStart = new Date(chargeDate)
    cycleStart.setDate(1) // Start of current month

    const cycleEnd = new Date(cycleStart)
    if (subscription.billingCycle === 'yearly') {
      cycleEnd.setFullYear(cycleStart.getFullYear() + 1)
      cycleEnd.setDate(0) // Last day of the year
    } else {
      cycleEnd.setMonth(cycleStart.getMonth() + 1)
      cycleEnd.setDate(0) // Last day of the month
    }

    // Create billing cycle
    const [billingCycle] = await db.insert(billingSubscriptions).values({
      currentPeriodStart: cycleStart.toISOString().split('T')[0],
      currentPeriodEnd: cycleEnd.toISOString().split('T')[0],
      studentCount: subscription.studentCount,
      pricePerStudent: (parseFloat(subscription.monthlyAmount) / subscription.studentCount).toString(),
      monthlyAmount: subscription.monthlyAmount,
      status: 'paid',
      nextBillingDate: chargeDate.toISOString().split('T')[0]
    }).returning()

    // Generate invoice number
    const invoiceNumber = `INV-${Date.now()}-${subscription.clientId.slice(-6)}`

    // Create invoice for the automatic charge
    const [invoice] = await db.insert(billingInvoices).values({
      subscriptionId: billingCycle.id,
      clientId: subscription.clientId,
      invoiceNumber,
      subtotal: subscription.monthlyAmount,
      totalAmount: subscription.monthlyAmount,
      issuedDate: chargeDate.toISOString().split('T')[0],
      dueDate: chargeDate.toISOString().split('T')[0],
      paidDate: chargeDate.toISOString().split('T')[0],
      status: 'paid', // Automatically paid via Razorpay
      periodStart: cycleStart.toISOString().split('T')[0],
      periodEnd: cycleEnd.toISOString().split('T')[0]
    }).returning()

    // Create payment record for the automatic charge
    await db.insert(billingPayments).values({
      invoiceId: invoice.id,
      subscriptionId: billingCycle.id,
      clientId: subscription.clientId,
      razorpayPaymentId: webhookPayload.payload?.payment?.entity?.id || `AUTO-${Date.now()}`,
      razorpayOrderId: webhookPayload.payload?.subscription?.entity?.id || '',
      amount: subscription.monthlyAmount,
      status: 'succeeded',
      paymentMethod: 'automatic_subscription',
      createdAt: chargeDate
    })

    console.log('✅ Automatic billing record created:', {
      id: billingCycle.id,
      invoiceId: invoice.id,
      invoiceNumber,
      monthlyAmount: subscription.monthlyAmount
    })

  } catch (error) {
    console.error('❌ Failed to create automatic billing record:', error)
    throw error
  }
}

export default app
