/**
 * Quick verification test for subscription authentication fix
 * This test verifies that the authentication middleware is properly applied
 */

const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000'
};

/**
 * Test that subscription endpoints now require authentication
 */
async function testSubscriptionAuthenticationRequired() {
  console.log('\n🔐 Testing Subscription Authentication Requirement...');
  
  try {
    // Test without authentication token
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/subscriptions/create-payment-order`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        subscriptionId: 'test-id',
        paymentMethod: 'card'
      })
    });

    const data = await response.json();
    
    if (response.status === 401 && data.error === 'Authorization token required') {
      console.log('✅ Authentication middleware is working correctly');
      console.log('   - Status: 401 Unauthorized');
      console.log('   - Error: Authorization token required');
      return true;
    } else {
      console.log('❌ Authentication middleware not working as expected');
      console.log(`   - Status: ${response.status}`);
      console.log(`   - Response: ${JSON.stringify(data, null, 2)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Test error: ${error.message}`);
    return false;
  }
}

/**
 * Test with invalid token
 */
async function testSubscriptionInvalidToken() {
  console.log('\n🔐 Testing Subscription Invalid Token Handling...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/subscriptions/create-payment-order`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer invalid-token',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        subscriptionId: 'test-id',
        paymentMethod: 'card'
      })
    });

    const data = await response.json();
    
    if (response.status === 401 && (data.error === 'Invalid school token' || data.error.includes('token'))) {
      console.log('✅ Invalid token handling is working correctly');
      console.log(`   - Status: 401 Unauthorized`);
      console.log(`   - Error: ${data.error}`);
      return true;
    } else {
      console.log('❌ Invalid token handling not working as expected');
      console.log(`   - Status: ${response.status}`);
      console.log(`   - Response: ${JSON.stringify(data, null, 2)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Test error: ${error.message}`);
    return false;
  }
}

/**
 * Test health endpoint (should work without auth)
 */
async function testHealthEndpoint() {
  console.log('\n🏥 Testing Health Endpoint (No Auth Required)...');
  
  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/health`);
    const data = await response.json();
    
    if (response.ok && data.status === 'ok') {
      console.log('✅ Health endpoint working correctly');
      console.log(`   - Status: ${response.status}`);
      console.log(`   - Service: ${data.service}`);
      return true;
    } else {
      console.log('❌ Health endpoint not working');
      console.log(`   - Status: ${response.status}`);
      console.log(`   - Response: ${JSON.stringify(data, null, 2)}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Test error: ${error.message}`);
    return false;
  }
}

/**
 * Main test runner
 */
async function runAuthenticationVerificationTests() {
  console.log('🔒 Subscription Authentication Fix Verification');
  console.log('============================================================');
  console.log(`🌐 Base URL: ${TEST_CONFIG.baseUrl}`);
  
  const results = [];
  
  // Run tests
  results.push(await testHealthEndpoint());
  results.push(await testSubscriptionAuthenticationRequired());
  results.push(await testSubscriptionInvalidToken());
  
  // Summary
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n============================================================');
  console.log('🔒 Authentication Fix Verification Summary');
  console.log('============================================================');
  console.log(`✅ Passed: ${passed}/${total} tests`);
  console.log(`❌ Failed: ${total - passed}/${total} tests`);
  
  if (passed === total) {
    console.log('\n🎉 All authentication tests passed!');
    console.log('✅ The subscription authentication fix is working correctly.');
    console.log('✅ Payment endpoints are now properly secured.');
  } else {
    console.log('\n⚠️ Some authentication tests failed.');
    console.log('❌ The subscription authentication fix may need additional work.');
  }
  
  return passed === total;
}

// Run the tests
runAuthenticationVerificationTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
  });
