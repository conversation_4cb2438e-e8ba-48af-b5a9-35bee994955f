# Error Handling & Logging System

## 🚨 Error Handling Architecture

Comprehensive error handling strategy with structured logging, monitoring, and recovery mechanisms.

## 📊 Error Classification System

### Error Types & Severity Levels
```typescript
enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

enum ErrorCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  DATABASE = 'database',
  PAYMENT = 'payment',
  EXTERNAL_API = 'external_api',
  SYSTEM = 'system',
  BUSINESS_LOGIC = 'business_logic'
}

interface ErrorContext {
  userId?: string;
  clientId?: string;
  requestId: string;
  endpoint: string;
  method: string;
  userAgent?: string;
  ipAddress?: string;
  timestamp: Date;
  additionalData?: Record<string, any>;
}

class AppError extends Error {
  public readonly severity: ErrorSeverity;
  public readonly category: ErrorCategory;
  public readonly code: string;
  public readonly context: ErrorContext;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    severity: ErrorSeverity,
    category: ErrorCategory,
    code: string,
    context: ErrorContext,
    isOperational: boolean = true
  ) {
    super(message);
    this.severity = severity;
    this.category = category;
    this.code = code;
    this.context = context;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}
```

### Predefined Error Classes
```typescript
class ValidationError extends AppError {
  constructor(message: string, context: ErrorContext, field?: string) {
    super(
      message,
      ErrorSeverity.LOW,
      ErrorCategory.VALIDATION,
      'VALIDATION_ERROR',
      { ...context, field },
      true
    );
  }
}

class AuthenticationError extends AppError {
  constructor(message: string, context: ErrorContext) {
    super(
      message,
      ErrorSeverity.MEDIUM,
      ErrorCategory.AUTHENTICATION,
      'AUTH_ERROR',
      context,
      true
    );
  }
}

class PaymentError extends AppError {
  constructor(message: string, context: ErrorContext, paymentId?: string) {
    super(
      message,
      ErrorSeverity.HIGH,
      ErrorCategory.PAYMENT,
      'PAYMENT_ERROR',
      { ...context, paymentId },
      true
    );
  }
}

class DatabaseError extends AppError {
  constructor(message: string, context: ErrorContext, query?: string) {
    super(
      message,
      ErrorSeverity.HIGH,
      ErrorCategory.DATABASE,
      'DATABASE_ERROR',
      { ...context, query },
      false
    );
  }
}

class ExternalAPIError extends AppError {
  constructor(message: string, context: ErrorContext, service?: string, statusCode?: number) {
    super(
      message,
      ErrorSeverity.MEDIUM,
      ErrorCategory.EXTERNAL_API,
      'EXTERNAL_API_ERROR',
      { ...context, service, statusCode },
      true
    );
  }
}
```

## 📝 Structured Logging System

### Simple Logger Configuration
```typescript
import winston from 'winston';

interface LogEntry {
  level: string;
  message: string;
  timestamp: string;
  requestId: string;
  userId?: string;
  clientId?: string;
  service: string;
  environment: string;
  metadata?: Record<string, any>;
}

const createLogger = () => {
  const transports: winston.transport[] = [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.timestamp(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
        })
      )
    })
  ];

  // Add file transport for production
  if (process.env.NODE_ENV === 'production') {
    transports.push(
      new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        format: winston.format.json()
      }),
      new winston.transports.File({
        filename: 'logs/combined.log',
        format: winston.format.json()
      })
    );
  }

  return winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    ),
    defaultMeta: {
      service: 'schopio-saas',
      environment: process.env.NODE_ENV || 'development'
    },
    transports
  });
};

const logger = createLogger();
```

### Logging Utilities
```typescript
class Logger {
  private static instance: Logger;
  private winston: winston.Logger;

  private constructor() {
    this.winston = createLogger();
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  info(message: string, meta?: Record<string, any>) {
    this.winston.info(message, meta);
  }

  warn(message: string, meta?: Record<string, any>) {
    this.winston.warn(message, meta);
  }

  error(message: string, error?: Error, meta?: Record<string, any>) {
    this.winston.error(message, {
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : undefined,
      ...meta
    });
  }

  debug(message: string, meta?: Record<string, any>) {
    this.winston.debug(message, meta);
  }

  // Business event logging
  logBusinessEvent(event: string, data: Record<string, any>) {
    this.winston.info(`Business Event: ${event}`, {
      eventType: 'business',
      event,
      data
    });
  }

  // Security event logging
  logSecurityEvent(event: string, severity: 'low' | 'medium' | 'high', data: Record<string, any>) {
    this.winston.warn(`Security Event: ${event}`, {
      eventType: 'security',
      event,
      severity,
      data
    });
  }

  // Performance logging
  logPerformance(operation: string, duration: number, meta?: Record<string, any>) {
    this.winston.info(`Performance: ${operation}`, {
      eventType: 'performance',
      operation,
      duration,
      ...meta
    });
  }
}

const log = Logger.getInstance();
```

## 🔧 Hono.js Error Middleware

### Global Error Handler
```typescript
import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';

const errorHandler = async (err: Error, c: Context) => {
  const requestId = c.get('requestId') || generateRequestId();
  const userId = c.get('user')?.userId;
  const clientId = c.get('user')?.clientId;

  // Create error context
  const context: ErrorContext = {
    userId,
    clientId,
    requestId,
    endpoint: c.req.path,
    method: c.req.method,
    userAgent: c.req.header('user-agent'),
    ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
    timestamp: new Date()
  };

  // Handle different error types
  if (err instanceof AppError) {
    // Log application errors
    log.error(`Application Error: ${err.message}`, err, {
      severity: err.severity,
      category: err.category,
      code: err.code,
      context: err.context
    });

    // Send to monitoring service
    if (err.severity === ErrorSeverity.CRITICAL) {
      await sendCriticalAlert(err);
    }

    return c.json({
      error: {
        message: err.message,
        code: err.code,
        requestId
      }
    }, getHttpStatusFromSeverity(err.severity));

  } else if (err instanceof HTTPException) {
    // Handle Hono HTTP exceptions
    log.warn(`HTTP Exception: ${err.message}`, undefined, {
      status: err.status,
      context
    });

    return c.json({
      error: {
        message: err.message,
        requestId
      }
    }, err.status);

  } else {
    // Handle unexpected errors
    log.error(`Unexpected Error: ${err.message}`, err, { context });

    // Send critical alert for unexpected errors
    await sendCriticalAlert(new AppError(
      err.message,
      ErrorSeverity.CRITICAL,
      ErrorCategory.SYSTEM,
      'UNEXPECTED_ERROR',
      context,
      false
    ));

    return c.json({
      error: {
        message: 'Internal server error',
        requestId
      }
    }, 500);
  }
};

// Apply error handler to Hono app
const app = new Hono()
  .onError(errorHandler);
```

### Request Logging Middleware
```typescript
const requestLogger = async (c: Context, next: Next) => {
  const requestId = generateRequestId();
  const startTime = Date.now();
  
  c.set('requestId', requestId);
  
  // Log incoming request
  log.info('Incoming Request', {
    requestId,
    method: c.req.method,
    path: c.req.path,
    userAgent: c.req.header('user-agent'),
    ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
    userId: c.get('user')?.userId,
    clientId: c.get('user')?.clientId
  });

  try {
    await next();
    
    const duration = Date.now() - startTime;
    
    // Log successful response
    log.info('Request Completed', {
      requestId,
      method: c.req.method,
      path: c.req.path,
      status: c.res.status,
      duration,
      userId: c.get('user')?.userId,
      clientId: c.get('user')?.clientId
    });

    // Log slow requests
    if (duration > 1000) {
      log.warn('Slow Request Detected', {
        requestId,
        method: c.req.method,
        path: c.req.path,
        duration
      });
    }

  } catch (error) {
    const duration = Date.now() - startTime;
    
    log.error('Request Failed', error as Error, {
      requestId,
      method: c.req.method,
      path: c.req.path,
      duration,
      userId: c.get('user')?.userId,
      clientId: c.get('user')?.clientId
    });
    
    throw error;
  }
};
```

## 📊 Database Error Handling

### Database Operation Wrapper
```typescript
import { db } from './database';

class DatabaseService {
  static async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    context: Partial<ErrorContext>,
    operationName: string
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      log.debug(`Database Operation Started: ${operationName}`, { context });
      
      const result = await operation();
      
      const duration = Date.now() - startTime;
      log.logPerformance(`Database: ${operationName}`, duration, { context });
      
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      const dbError = new DatabaseError(
        `Database operation failed: ${operationName}`,
        context as ErrorContext,
        error.message
      );
      
      log.error(`Database Error: ${operationName}`, dbError, {
        duration,
        originalError: error.message
      });
      
      // Check for specific database errors
      if (error.code === '23505') { // Unique constraint violation
        throw new ValidationError('Duplicate entry detected', context as ErrorContext);
      }
      
      if (error.code === '23503') { // Foreign key constraint violation
        throw new ValidationError('Referenced record not found', context as ErrorContext);
      }
      
      throw dbError;
    }
  }

  // Example usage
  static async createClient(clientData: any, context: ErrorContext) {
    return this.executeWithErrorHandling(
      () => db.insert(clients).values(clientData).returning(),
      context,
      'createClient'
    );
  }

  static async getClientById(clientId: string, context: ErrorContext) {
    return this.executeWithErrorHandling(
      () => db.select().from(clients).where(eq(clients.id, clientId)).limit(1),
      context,
      'getClientById'
    );
  }
}
```

## 🚨 Monitoring & Alerting

### Critical Error Alerting
```typescript
interface AlertChannel {
  type: 'email' | 'slack' | 'sms' | 'webhook';
  config: Record<string, any>;
}

class AlertingService {
  private channels: AlertChannel[] = [
    {
      type: 'email',
      config: {
        recipients: ['<EMAIL>', '<EMAIL>']
      }
    },
    {
      type: 'slack',
      config: {
        webhookUrl: process.env.SLACK_WEBHOOK_URL,
        channel: '#alerts'
      }
    }
  ];

  async sendCriticalAlert(error: AppError) {
    const alertData = {
      title: `Critical Error: ${error.code}`,
      message: error.message,
      severity: error.severity,
      category: error.category,
      context: error.context,
      timestamp: new Date(),
      environment: process.env.NODE_ENV
    };

    for (const channel of this.channels) {
      try {
        await this.sendAlert(channel, alertData);
      } catch (alertError) {
        log.error('Failed to send alert', alertError as Error, {
          channel: channel.type,
          originalError: error.message
        });
      }
    }
  }

  private async sendAlert(channel: AlertChannel, data: any) {
    switch (channel.type) {
      case 'email':
        await this.sendEmailAlert(channel.config, data);
        break;
      case 'slack':
        await this.sendSlackAlert(channel.config, data);
        break;
      case 'sms':
        await this.sendSmsAlert(channel.config, data);
        break;
      case 'webhook':
        await this.sendWebhookAlert(channel.config, data);
        break;
    }
  }

  private async sendSlackAlert(config: any, data: any) {
    const payload = {
      channel: config.channel,
      username: 'School Management Alert',
      icon_emoji: ':rotating_light:',
      attachments: [{
        color: data.severity === ErrorSeverity.CRITICAL ? 'danger' : 'warning',
        title: data.title,
        text: data.message,
        fields: [
          { title: 'Environment', value: data.environment, short: true },
          { title: 'Category', value: data.category, short: true },
          { title: 'Timestamp', value: data.timestamp.toISOString(), short: true },
          { title: 'Request ID', value: data.context.requestId, short: true }
        ]
      }]
    };

    await fetch(config.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
  }
}

const alerting = new AlertingService();
const sendCriticalAlert = (error: AppError) => alerting.sendCriticalAlert(error);
```

## 📈 Error Analytics & Reporting

### Error Metrics Collection
```typescript
class ErrorMetrics {
  static async getErrorStats(timeframe: string = '24h') {
    const stats = await db.select({
      category: errorLogs.category,
      severity: errorLogs.severity,
      count: sql`COUNT(*)`,
      lastOccurrence: sql`MAX(timestamp)`
    })
    .from(errorLogs)
    .where(sql`timestamp > NOW() - INTERVAL '${timeframe}'`)
    .groupBy(errorLogs.category, errorLogs.severity)
    .orderBy(sql`COUNT(*) DESC`);

    return stats;
  }

  static async getTopErrors(limit: number = 10) {
    return await db.select({
      message: errorLogs.message,
      code: errorLogs.code,
      count: sql`COUNT(*)`,
      lastOccurrence: sql`MAX(timestamp)`
    })
    .from(errorLogs)
    .where(sql`timestamp > NOW() - INTERVAL '7 days'`)
    .groupBy(errorLogs.message, errorLogs.code)
    .orderBy(sql`COUNT(*) DESC`)
    .limit(limit);
  }

  static async getErrorTrends() {
    return await db.select({
      date: sql`DATE(timestamp)`,
      category: errorLogs.category,
      count: sql`COUNT(*)`
    })
    .from(errorLogs)
    .where(sql`timestamp > NOW() - INTERVAL '30 days'`)
    .groupBy(sql`DATE(timestamp)`, errorLogs.category)
    .orderBy(sql`DATE(timestamp) DESC`);
  }
}
```

This comprehensive error handling and logging system ensures robust error management, monitoring, and recovery capabilities for the SaaS platform.
