import { db } from '@/src/db'
import {
  billingSubscriptions,
  billingInvoices,
  billingPayments,
  billingPaymentReminders,
  clients,
  auditLogs
} from '@/src/db/schema'
import { eq, and, lte, gte, sql, count, desc } from 'drizzle-orm'
import { billingMonitor } from './billingMonitor'
import { dunningManager } from './dunningManager'
import { emailService } from './emailService'
import { auditLogger } from './auditLogger'

export interface PaymentAlert {
  id: string
  type: 'overdue_payment' | 'penalty_applied' | 'grace_period_ending' | 'payment_failure' | 'high_risk_account'
  severity: 'low' | 'medium' | 'high' | 'critical'
  clientId: string
  subscriptionId?: string
  invoiceId?: string
  title: string
  message: string
  amount: number
  daysOverdue?: number
  penaltyAmount?: number
  actionRequired: string
  createdAt: Date
  resolved: boolean
}

export interface PaymentMonitoringDashboard {
  summary: {
    totalOverdue: number
    totalPenalties: number
    accountsInGrace: number
    accountsSuspended: number
    totalAtRisk: number
  }
  alerts: PaymentAlert[]
  overdueAccounts: OverdueAccount[]
  penaltyCalculations: PenaltyCalculation[]
  recentActions: MonitoringAction[]
}

export interface OverdueAccount {
  clientId: string
  schoolName: string
  email: string
  contactPerson: string
  subscriptionId: string
  invoiceId: string
  monthlyAmount: number
  daysOverdue: number
  penaltyAmount: number
  status: 'grace_period' | 'overdue' | 'suspended'
  lastReminderSent?: Date
  nextActionDue: Date
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
}

export interface PenaltyCalculation {
  clientId: string
  schoolName: string
  subscriptionId: string
  baseAmount: number
  daysOverdue: number
  penaltyRate: number
  penaltyAmount: number
  totalAmount: number
  lastCalculated: Date
}

export interface MonitoringAction {
  id: string
  type: 'reminder_sent' | 'penalty_applied' | 'status_changed' | 'payment_received'
  clientId: string
  schoolName: string
  description: string
  amount?: number
  timestamp: Date
  automated: boolean
}

class PaymentMonitoringService {
  private static instance: PaymentMonitoringService

  public static getInstance(): PaymentMonitoringService {
    if (!PaymentMonitoringService.instance) {
      PaymentMonitoringService.instance = new PaymentMonitoringService()
    }
    return PaymentMonitoringService.instance
  }

  /**
   * Get comprehensive payment monitoring dashboard
   */
  public async getMonitoringDashboard(): Promise<PaymentMonitoringDashboard> {
    try {
      console.log('📊 Generating payment monitoring dashboard...')

      const [summary, alerts, overdueAccounts, penaltyCalculations, recentActions] = await Promise.all([
        this.getSummaryMetrics(),
        this.generatePaymentAlerts(),
        this.getOverdueAccounts(),
        this.calculatePenalties(),
        this.getRecentActions()
      ])

      return {
        summary,
        alerts,
        overdueAccounts,
        penaltyCalculations,
        recentActions
      }
    } catch (error) {
      console.error('Error generating monitoring dashboard:', error)
      throw error
    }
  }

  /**
   * Get summary metrics for payment monitoring
   */
  private async getSummaryMetrics() {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Get overdue invoices
    const [overdueStats] = await db
      .select({
        count: count(),
        totalAmount: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'sent'),
        lte(billingInvoices.dueDate, today.toISOString().split('T')[0])
      ))

    // Get accounts in grace period
    const [graceStats] = await db
      .select({
        count: count(),
        totalAmount: sql<number>`COALESCE(SUM(CAST(${billingSubscriptions.monthlyAmount} AS DECIMAL)), 0)`
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.status, 'active'))

    // Get suspended accounts
    const [suspendedStats] = await db
      .select({
        count: count(),
        totalAmount: sql<number>`COALESCE(SUM(CAST(${billingSubscriptions.monthlyAmount} AS DECIMAL)), 0)`
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.status, 'suspended'))

    // Calculate total penalties (simplified calculation)
    const totalPenalties = overdueStats.totalAmount * 0.02 * 5 // Estimated average

    return {
      totalOverdue: overdueStats.totalAmount || 0,
      totalPenalties,
      accountsInGrace: graceStats.count || 0,
      accountsSuspended: suspendedStats.count || 0,
      totalAtRisk: (overdueStats.count || 0) + (suspendedStats.count || 0)
    }
  }

  /**
   * Generate payment alerts based on current status
   */
  private async generatePaymentAlerts(): Promise<PaymentAlert[]> {
    const alerts: PaymentAlert[] = []
    const today = new Date()

    // Get overdue invoices for alerts
    const overdueInvoices = await db
      .select({
        clientId: billingInvoices.clientId,
        subscriptionId: billingInvoices.subscriptionId,
        invoiceId: billingInvoices.id,
        schoolName: clients.schoolName,
        amount: billingInvoices.totalAmount,
        dueDate: billingInvoices.dueDate,
        gracePeriodDays: billingSubscriptions.gracePeriodDays,
        penaltyRate: billingSubscriptions.penaltyRate
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .leftJoin(billingSubscriptions, eq(billingInvoices.subscriptionId, billingSubscriptions.id))
      .where(and(
        eq(billingInvoices.status, 'sent'),
        lte(billingInvoices.dueDate, today.toISOString().split('T')[0])
      ))
      .limit(50)

    for (const invoice of overdueInvoices) {
      const dueDate = new Date(invoice.dueDate)
      const daysOverdue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))
      const gracePeriodDays = invoice.gracePeriodDays || 3
      const penaltyRate = parseFloat(invoice.penaltyRate?.toString() || '0.02')
      const amount = parseFloat(invoice.amount)
      
      if (daysOverdue > gracePeriodDays) {
        const penaltyAmount = amount * penaltyRate * (daysOverdue - gracePeriodDays)
        
        alerts.push({
          id: `overdue-${invoice.invoiceId}-${Date.now()}`,
          type: 'overdue_payment',
          severity: daysOverdue > 15 ? 'critical' : daysOverdue > 7 ? 'high' : 'medium',
          clientId: invoice.clientId || 'unknown',
          subscriptionId: invoice.subscriptionId || undefined,
          invoiceId: invoice.invoiceId,
          title: `Payment Overdue - ${invoice.schoolName}`,
          message: `Payment is ${daysOverdue} days overdue. Penalty of ₹${penaltyAmount.toFixed(2)} applied.`,
          amount,
          daysOverdue,
          penaltyAmount,
          actionRequired: daysOverdue > 15 ? 'Immediate action required' : 'Send reminder',
          createdAt: new Date(),
          resolved: false
        })
      } else if (daysOverdue > 0) {
        alerts.push({
          id: `grace-${invoice.invoiceId}-${Date.now()}`,
          type: 'grace_period_ending',
          severity: 'medium',
          clientId: invoice.clientId || 'unknown',
          subscriptionId: invoice.subscriptionId || undefined,
          invoiceId: invoice.invoiceId,
          title: `Grace Period Ending - ${invoice.schoolName}`,
          message: `Payment is ${daysOverdue} days past due. Grace period ends in ${gracePeriodDays - daysOverdue} days.`,
          amount,
          daysOverdue,
          penaltyAmount: 0,
          actionRequired: 'Send payment reminder',
          createdAt: new Date(),
          resolved: false
        })
      }
    }

    return alerts
  }

  /**
   * Get detailed overdue accounts information
   */
  private async getOverdueAccounts(): Promise<OverdueAccount[]> {
    const today = new Date()
    
    const overdueAccounts = await db
      .select({
        clientId: clients.id,
        schoolName: clients.schoolName,
        email: clients.email,
        contactPerson: clients.contactPerson,
        subscriptionId: billingSubscriptions.id,
        invoiceId: billingInvoices.id,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        dueDate: billingInvoices.dueDate,
        gracePeriodDays: billingSubscriptions.gracePeriodDays,
        penaltyRate: billingSubscriptions.penaltyRate,
        status: billingSubscriptions.status
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .leftJoin(billingSubscriptions, eq(billingInvoices.subscriptionId, billingSubscriptions.id))
      .where(and(
        eq(billingInvoices.status, 'sent'),
        lte(billingInvoices.dueDate, today.toISOString().split('T')[0])
      ))
      .orderBy(desc(billingInvoices.dueDate))
      .limit(100)

    return overdueAccounts.map(account => {
      const dueDate = new Date(account.dueDate)
      const daysOverdue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))
      const gracePeriodDays = account.gracePeriodDays || 3
      const penaltyRate = parseFloat(account.penaltyRate?.toString() || '0.02')
      const monthlyAmount = parseFloat(account.monthlyAmount?.toString() || '0')
      
      const penaltyAmount = daysOverdue > gracePeriodDays 
        ? monthlyAmount * penaltyRate * (daysOverdue - gracePeriodDays)
        : 0

      const nextActionDue = new Date(dueDate)
      nextActionDue.setDate(nextActionDue.getDate() + gracePeriodDays + 1)

      let status: 'grace_period' | 'overdue' | 'suspended' = 'grace_period'
      let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low'

      if (daysOverdue > gracePeriodDays) {
        status = 'overdue'
        riskLevel = daysOverdue > 15 ? 'critical' : daysOverdue > 7 ? 'high' : 'medium'
      }

      if (account.status === 'suspended') {
        status = 'suspended'
        riskLevel = 'critical'
      }

      return {
        clientId: account.clientId || 'unknown',
        schoolName: account.schoolName || 'Unknown School',
        email: account.email || 'No email',
        contactPerson: account.contactPerson || 'N/A',
        subscriptionId: account.subscriptionId || 'unknown',
        invoiceId: account.invoiceId,
        monthlyAmount,
        daysOverdue,
        penaltyAmount,
        status,
        nextActionDue,
        riskLevel
      }
    })
  }

  /**
   * Calculate penalties for all overdue accounts
   */
  private async calculatePenalties(): Promise<PenaltyCalculation[]> {
    const overdueAccounts = await this.getOverdueAccounts()
    
    return overdueAccounts
      .filter(account => account.daysOverdue > (3)) // Only accounts past grace period
      .map(account => ({
        clientId: account.clientId,
        schoolName: account.schoolName,
        subscriptionId: account.subscriptionId,
        baseAmount: account.monthlyAmount,
        daysOverdue: account.daysOverdue,
        penaltyRate: 0.02, // 2% daily
        penaltyAmount: account.penaltyAmount,
        totalAmount: account.monthlyAmount + account.penaltyAmount,
        lastCalculated: new Date()
      }))
  }

  /**
   * Get recent monitoring actions
   */
  private async getRecentActions(): Promise<MonitoringAction[]> {
    // This would typically come from audit logs or a dedicated actions table
    // For now, return a simplified version
    return []
  }

  /**
   * Process overdue payments and send alerts
   */
  public async processOverduePayments(): Promise<{
    processed: number
    alertsSent: number
    penaltiesApplied: number
    errors: string[]
  }> {
    try {
      console.log('🔄 Processing overdue payments...')

      const dashboard = await this.getMonitoringDashboard()
      const errors: string[] = []
      let alertsSent = 0
      let penaltiesApplied = 0

      // Send alerts for critical overdue accounts
      for (const alert of dashboard.alerts.filter(a => a.severity === 'critical')) {
        try {
          // Send email alert to admin
          await emailService.sendOverduePaymentAlert({
            schoolName: alert.title.split(' - ')[1] || 'Unknown School',
            amount: alert.amount,
            daysOverdue: alert.daysOverdue || 0,
            penaltyAmount: alert.penaltyAmount || 0,
            adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>'
          })
          alertsSent++
        } catch (error) {
          errors.push(`Failed to send alert for ${alert.clientId}: ${error}`)
        }
      }

      // Apply penalties for overdue accounts
      for (const penalty of dashboard.penaltyCalculations) {
        try {
          // Update subscription with penalty amount (note: penaltyAmount field may not exist in schema)
          // This is a placeholder - actual implementation would depend on schema design
          console.log(`Would apply penalty of ₹${penalty.penaltyAmount} to subscription ${penalty.subscriptionId}`)
          
          penaltiesApplied++
        } catch (error) {
          errors.push(`Failed to apply penalty for ${penalty.clientId}: ${error}`)
        }
      }

      // Log the processing activity
      await auditLogger.logAdmin('payment_monitoring_process', {
        adminId: 'system',
        resource: 'payment_monitoring',
        details: {
          processed: dashboard.overdueAccounts.length,
          alertsSent,
          penaltiesApplied,
          errorCount: errors.length
        },
        ipAddress: 'system',
        userAgent: 'payment-monitor',
        success: errors.length === 0
      })

      return {
        processed: dashboard.overdueAccounts.length,
        alertsSent,
        penaltiesApplied,
        errors
      }
    } catch (error) {
      console.error('Error processing overdue payments:', error)
      throw error
    }
  }
}

export const paymentMonitoringService = PaymentMonitoringService.getInstance()
