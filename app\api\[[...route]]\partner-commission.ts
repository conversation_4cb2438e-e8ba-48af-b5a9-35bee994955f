import { Hono } from 'hono'
import { db } from '@/src/db'
import { 
  partnerCommissionEscrow,
  partnerTransactions,
  partnerFundAccounts,
  partners,
  clients,
  billingPayments
} from '@/src/db/schema'
import { eq, and, desc, count, sum, sql } from 'drizzle-orm'
import { commissionProcessor } from '@/src/services/commissionProcessor'

const app = new Hono()

// ===== PARTNER COMMISSION TRACKING API =====

// Get partner commission dashboard
app.get("/dashboard/:partnerId", async (c) => {
  try {
    const partnerId = c.req.param('partnerId')

    // Get commission summary
    const summary = await commissionProcessor.getPartnerCommissionSummary(partnerId)

    // Get recent transactions
    const recentTransactions = await db
      .select({
        id: partnerTransactions.id,
        type: partnerTransactions.transactionType,
        amount: partnerTransactions.amount,
        description: partnerTransactions.description,
        createdAt: partnerTransactions.createdAt
      })
      .from(partnerTransactions)
      .where(eq(partnerTransactions.partnerId, partnerId))
      .orderBy(desc(partnerTransactions.createdAt))
      .limit(10)

    // Get escrow status breakdown
    const [escrowStats] = await db
      .select({
        totalEscrows: count(),
        pendingAmount: sql<number>`COALESCE(SUM(CASE WHEN escrow_status = 'pending' THEN CAST(commission_amount AS DECIMAL) ELSE 0 END), 0)`,
        releasedAmount: sql<number>`COALESCE(SUM(CASE WHEN escrow_status = 'released' THEN CAST(commission_amount AS DECIMAL) ELSE 0 END), 0)`,
        onHoldAmount: sql<number>`COALESCE(SUM(CASE WHEN escrow_status = 'on_hold' THEN CAST(commission_amount AS DECIMAL) ELSE 0 END), 0)`
      })
      .from(partnerCommissionEscrow)
      .where(eq(partnerCommissionEscrow.partnerId, partnerId))

    return c.json({
      summary,
      recentTransactions,
      escrowStats,
      lastUpdated: new Date()
    })

  } catch (error) {
    console.error('Get partner dashboard error:', error)
    return c.json({ error: 'Failed to get partner dashboard' }, 500)
  }
})

// Get partner escrow details
app.get("/escrows/:partnerId", async (c) => {
  try {
    const partnerId = c.req.param('partnerId')
    const query = c.req.query()
    const page = parseInt(query.page || '1')
    const limit = parseInt(query.limit || '20')
    const status = query.status

    // Build filters
    const filters = [eq(partnerCommissionEscrow.partnerId, partnerId)]
    if (status) {
      filters.push(eq(partnerCommissionEscrow.escrowStatus, status))
    }

    // Get escrows with school details
    const escrows = await db
      .select({
        id: partnerCommissionEscrow.id,
        schoolId: partnerCommissionEscrow.schoolId,
        schoolName: clients.schoolName,
        monthYear: partnerCommissionEscrow.monthYear,
        baseAmount: partnerCommissionEscrow.baseAmount,
        commissionAmount: partnerCommissionEscrow.commissionAmount,
        netCommission: partnerCommissionEscrow.netCommission,
        status: partnerCommissionEscrow.escrowStatus,
        riskScore: partnerCommissionEscrow.riskScore,
        holdUntilDate: partnerCommissionEscrow.holdUntilDate,
        autoReleaseEnabled: partnerCommissionEscrow.autoReleaseEnabled,
        createdAt: partnerCommissionEscrow.createdAt,
        releasedAt: partnerCommissionEscrow.releasedAt
      })
      .from(partnerCommissionEscrow)
      .leftJoin(clients, eq(partnerCommissionEscrow.schoolId, clients.id))
      .where(and(...filters))
      .orderBy(desc(partnerCommissionEscrow.createdAt))
      .limit(limit)
      .offset((page - 1) * limit)

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(partnerCommissionEscrow)
      .where(and(...filters))

    return c.json({
      escrows,
      pagination: {
        page,
        limit,
        total: totalResult.count,
        pages: Math.ceil(totalResult.count / limit)
      }
    })

  } catch (error) {
    console.error('Get partner escrows error:', error)
    return c.json({ error: 'Failed to get partner escrows' }, 500)
  }
})

// Get partner transaction history
app.get("/transactions/:partnerId", async (c) => {
  try {
    const partnerId = c.req.param('partnerId')
    const query = c.req.query()
    const page = parseInt(query.page || '1')
    const limit = parseInt(query.limit || '20')
    const type = query.type

    // Build filters
    const filters = [eq(partnerTransactions.partnerId, partnerId)]
    if (type) {
      filters.push(eq(partnerTransactions.transactionType, type))
    }

    const transactions = await db
      .select()
      .from(partnerTransactions)
      .where(and(...filters))
      .orderBy(desc(partnerTransactions.createdAt))
      .limit(limit)
      .offset((page - 1) * limit)

    const [totalResult] = await db
      .select({ count: count() })
      .from(partnerTransactions)
      .where(and(...filters))

    return c.json({
      transactions,
      pagination: {
        page,
        limit,
        total: totalResult.count,
        pages: Math.ceil(totalResult.count / limit)
      }
    })

  } catch (error) {
    console.error('Get partner transactions error:', error)
    return c.json({ error: 'Failed to get partner transactions' }, 500)
  }
})

// Get partner fund accounts
app.get("/fund-accounts/:partnerId", async (c) => {
  try {
    const partnerId = c.req.param('partnerId')

    const fundAccounts = await db
      .select({
        id: partnerFundAccounts.id,
        accountNumber: partnerFundAccounts.accountNumber,
        ifscCode: partnerFundAccounts.ifscCode,
        accountHolderName: partnerFundAccounts.accountHolderName,
        bankName: partnerFundAccounts.bankName,
        validationStatus: partnerFundAccounts.validationStatus,
        validationDate: partnerFundAccounts.validationDate,
        isPrimary: partnerFundAccounts.isPrimary,
        isActive: partnerFundAccounts.isActive,
        createdAt: partnerFundAccounts.createdAt
      })
      .from(partnerFundAccounts)
      .where(eq(partnerFundAccounts.partnerId, partnerId))
      .orderBy(desc(partnerFundAccounts.isPrimary), desc(partnerFundAccounts.createdAt))

    return c.json({ fundAccounts })

  } catch (error) {
    console.error('Get partner fund accounts error:', error)
    return c.json({ error: 'Failed to get partner fund accounts' }, 500)
  }
})

// Get commission analytics
app.get("/analytics/:partnerId", async (c) => {
  try {
    const partnerId = c.req.param('partnerId')
    const query = c.req.query()
    const months = parseInt(query.months || '12') // Default 12 months

    // Monthly commission trends
    const monthlyTrends = await db
      .select({
        monthYear: partnerCommissionEscrow.monthYear,
        totalCommission: sql<number>`COALESCE(SUM(CAST(commission_amount AS DECIMAL)), 0)`,
        escrowCount: count(),
        avgRiskScore: sql<number>`COALESCE(AVG(risk_score), 0)`,
        releasedAmount: sql<number>`COALESCE(SUM(CASE WHEN escrow_status = 'released' THEN CAST(commission_amount AS DECIMAL) ELSE 0 END), 0)`
      })
      .from(partnerCommissionEscrow)
      .where(eq(partnerCommissionEscrow.partnerId, partnerId))
      .groupBy(partnerCommissionEscrow.monthYear)
      .orderBy(desc(partnerCommissionEscrow.monthYear))
      .limit(months)

    // School-wise performance
    const schoolPerformance = await db
      .select({
        schoolId: partnerCommissionEscrow.schoolId,
        schoolName: clients.schoolName,
        totalCommission: sql<number>`COALESCE(SUM(CAST(commission_amount AS DECIMAL)), 0)`,
        escrowCount: count(),
        avgRiskScore: sql<number>`COALESCE(AVG(risk_score), 0)`
      })
      .from(partnerCommissionEscrow)
      .leftJoin(clients, eq(partnerCommissionEscrow.schoolId, clients.id))
      .where(eq(partnerCommissionEscrow.partnerId, partnerId))
      .groupBy(partnerCommissionEscrow.schoolId, clients.schoolName)
      .orderBy(desc(sql`COALESCE(SUM(CAST(commission_amount AS DECIMAL)), 0)`))
      .limit(10)

    return c.json({
      monthlyTrends,
      schoolPerformance,
      generatedAt: new Date()
    })

  } catch (error) {
    console.error('Get partner analytics error:', error)
    return c.json({ error: 'Failed to get partner analytics' }, 500)
  }
})

// Get specific escrow details
app.get("/escrow/:escrowId/details", async (c) => {
  try {
    const escrowId = c.req.param('escrowId')
    const details = await commissionProcessor.getEscrowDetails(escrowId)
    return c.json(details)
  } catch (error) {
    console.error('Get escrow details error:', error)
    return c.json({ error: 'Failed to get escrow details' }, 500)
  }
})

export default app
