# 💰 Comprehensive Discount-Based Billing System with Partner Commission Management

**Last Updated:** July 8, 2025  
**System Status:** Design Phase - Implementation Ready  
**Integration:** Built on existing manual billing system  

## 🎯 **SYSTEM OVERVIEW**

This comprehensive billing system enhances Schopio's manual billing with advanced discount management, partner commission calculations, and automated invoice processing. The system maintains payment flexibility while adding sophisticated business logic for discounts and partner revenue sharing.

### **Key Features**
- **Time-Limited Discounts**: Admin-configurable discount percentages with automatic expiration
- **Expense Tracking**: Operational expense management per subscription for accurate profit calculation
- **Partner Commission Management**: Automated commission calculation with holding periods and manual payouts
- **Enhanced School Billing**: Comprehensive dashboard with discount visibility and advance payment support
- **Automated Invoice System**: Email automation with PDF generation and download portal
- **Payment Monitoring**: Overdue alerts, penalty calculations, and comprehensive admin monitoring

## 🏗️ **SYSTEM ARCHITECTURE**

### **Enhanced Billing Workflow**
```
Admin Creates Subscription with Discount → System Calculates Discounted Amount → School Receives Bill
                    ↓                                    ↓                              ↓
Partner Commission Calculated ← Operational Expenses Deducted ← School Makes Payment
                    ↓                                    ↓                              ↓
Holding Period Applied → Manual Payout Processing → Invoice/Receipt Generated
```

### **Core Components**
1. **Discount Management Engine**: Time-based discount application and expiration
2. **Commission Calculator**: Profit-based partner commission calculation
3. **Payment Processor**: Enhanced Razorpay integration with advance payment support
4. **Invoice Generator**: Automated PDF creation and email delivery
5. **Monitoring Dashboard**: Real-time payment status and overdue management

## 📊 **DATABASE SCHEMA ENHANCEMENTS**

### **New Tables Required**

#### **1. Subscription Discounts**
```sql
CREATE TABLE subscription_discounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  discount_percentage DECIMAL(5,2) NOT NULL, -- e.g., 20.00 for 20%
  discount_duration_months INTEGER NOT NULL, -- e.g., 6 months
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  remaining_months INTEGER NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **2. Subscription Expenses**
```sql
CREATE TABLE subscription_expenses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  monthly_operational_cost DECIMAL(10,2) NOT NULL,
  description TEXT,
  effective_from DATE NOT NULL,
  effective_until DATE,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **3. Partner Commission Configuration**
```sql
CREATE TABLE partner_commission_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id UUID REFERENCES partners(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  commission_percentage DECIMAL(5,2) NOT NULL, -- Applied to profit after expenses
  holding_period_days INTEGER DEFAULT 30,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **4. Enhanced Commission Tracking**
```sql
CREATE TABLE partner_commission_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id UUID REFERENCES partners(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  payment_id UUID REFERENCES billing_transactions(id) ON DELETE CASCADE,
  
  -- Financial Breakdown
  school_payment_amount DECIMAL(10,2) NOT NULL,
  discount_amount DECIMAL(10,2) DEFAULT 0.00,
  operational_expenses DECIMAL(10,2) NOT NULL,
  profit_amount DECIMAL(10,2) NOT NULL, -- school_payment - discount - expenses
  commission_percentage DECIMAL(5,2) NOT NULL,
  commission_amount DECIMAL(10,2) NOT NULL, -- profit_amount * commission_percentage
  
  -- Status Management
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'held', 'eligible', 'paid'
  hold_until_date DATE,
  eligible_date DATE,
  paid_date DATE,
  
  -- Payout Information
  payout_method VARCHAR(20), -- 'neft', 'bank_transfer'
  transaction_reference VARCHAR(100), -- TRN number
  payout_amount DECIMAL(10,2),
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **5. Advance Payment Tracking**
```sql
CREATE TABLE advance_payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  payment_id UUID REFERENCES billing_transactions(id) ON DELETE CASCADE,
  months_paid INTEGER NOT NULL,
  amount_per_month DECIMAL(10,2) NOT NULL,
  total_amount DECIMAL(10,2) NOT NULL,
  start_month DATE NOT NULL,
  end_month DATE NOT NULL,
  remaining_months INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **Enhanced Existing Tables**

#### **billing_subscriptions Enhancements**
```sql
-- Add new fields to existing billing_subscriptions table
ALTER TABLE billing_subscriptions ADD COLUMN:
- has_active_discount BOOLEAN DEFAULT false
- current_discount_percentage DECIMAL(5,2) DEFAULT 0.00
- discount_end_date DATE
- advance_payment_balance DECIMAL(10,2) DEFAULT 0.00
- advance_months_remaining INTEGER DEFAULT 0
```

#### **billing_transactions Enhancements**
```sql
-- Add new fields to existing billing_transactions table
ALTER TABLE billing_transactions ADD COLUMN:
- original_amount DECIMAL(10,2) -- Amount before discount
- discount_amount DECIMAL(10,2) DEFAULT 0.00
- final_amount DECIMAL(10,2) -- Amount after discount (same as total_amount)
- is_advance_payment BOOLEAN DEFAULT false
- advance_months_covered INTEGER DEFAULT 1
- invoice_number VARCHAR(50) UNIQUE
- receipt_number VARCHAR(50) UNIQUE
- invoice_pdf_path VARCHAR(500)
- receipt_pdf_path VARCHAR(500)
- email_sent_at TIMESTAMP
```

## 💼 **BUSINESS LOGIC SPECIFICATIONS**

### **Discount Calculation Logic**
```typescript
interface DiscountCalculation {
  originalAmount: number;
  discountPercentage: number;
  discountAmount: number;
  finalAmount: number;
  remainingMonths: number;
}

const calculateDiscountedAmount = (
  baseAmount: number,
  discountPercentage: number,
  remainingMonths: number
): DiscountCalculation => {
  const discountAmount = (baseAmount * discountPercentage) / 100;
  const finalAmount = baseAmount - discountAmount;
  
  return {
    originalAmount: baseAmount,
    discountPercentage,
    discountAmount,
    finalAmount,
    remainingMonths
  };
};
```

### **Partner Commission Calculation**
```typescript
interface CommissionCalculation {
  schoolPayment: number;
  discountAmount: number;
  operationalExpenses: number;
  profitAmount: number;
  commissionPercentage: number;
  commissionAmount: number;
  penaltyAmount: number; // Not included in commission
}

const calculatePartnerCommission = (
  schoolPayment: number,
  discountAmount: number,
  operationalExpenses: number,
  commissionPercentage: number,
  penaltyAmount: number = 0
): CommissionCalculation => {
  // Partner commission is calculated on profit (excluding penalties)
  const basePayment = schoolPayment - penaltyAmount;
  const profitAmount = basePayment - discountAmount - operationalExpenses;
  const commissionAmount = (profitAmount * commissionPercentage) / 100;
  
  return {
    schoolPayment: basePayment,
    discountAmount,
    operationalExpenses,
    profitAmount,
    commissionPercentage,
    commissionAmount,
    penaltyAmount
  };
};
```

### **Penalty Calculation (Enhanced)**
```typescript
const calculatePenalty = (
  baseAmount: number,
  daysOverdue: number,
  gracePeriodDays: number = 3,
  penaltyRate: number = 2.0
): number => {
  if (daysOverdue <= gracePeriodDays) return 0;
  
  const penaltyDays = daysOverdue - gracePeriodDays;
  const dailyPenalty = (baseAmount * penaltyRate) / 100;
  return dailyPenalty * penaltyDays;
};
```

## 🔄 **SYSTEM WORKFLOWS**

### **1. Subscription Creation with Discount**
```
Admin creates subscription → Sets discount (20%, 6 months) → System calculates discounted amounts
                                        ↓
Partner commission config applied → Operational expenses set → Subscription activated
```

### **2. Monthly Billing with Discount**
```
Billing date arrives → Check active discount → Calculate discounted amount → Generate invoice
                                    ↓
Send email notification → School makes payment → Commission calculated → Invoice/receipt sent
```

### **3. Partner Commission Processing**
```
School payment received → Calculate profit after expenses → Apply commission percentage
                                        ↓
Enter holding period → Become eligible after hold → Admin processes manual payout
```

### **4. Advance Payment Processing**
```
School pays multiple months → System allocates to future months → Updates advance balance
                                        ↓
Monthly billing checks advance balance → Deducts from advance → Continues normal cycle
```

## 📱 **API ENDPOINTS SPECIFICATION**

### **Admin Discount Management**
- `POST /api/admin/subscriptions/:id/discounts` - Create discount
- `GET /api/admin/subscriptions/:id/discounts` - List discounts
- `PUT /api/admin/discounts/:id` - Update discount
- `DELETE /api/admin/discounts/:id` - Remove discount

### **Expense Management**
- `POST /api/admin/subscriptions/:id/expenses` - Set operational expenses
- `GET /api/admin/subscriptions/:id/expenses` - Get expense details
- `PUT /api/admin/expenses/:id` - Update expenses

### **Commission Management**
- `GET /api/admin/commissions/overview` - Commission dashboard
- `GET /api/admin/commissions/eligible` - Eligible payouts
- `POST /api/admin/commissions/:id/payout` - Process manual payout

### **Enhanced School Billing**
- `GET /api/school/billing/dashboard` - Comprehensive billing info
- `POST /api/school/billing/advance-payment` - Create advance payment order
- `GET /api/school/billing/invoices` - Download invoices/receipts

### **Partner Dashboard**
- `GET /api/partner/commissions/dashboard` - Commission overview
- `GET /api/partner/commissions/history` - Commission history
- `GET /api/partner/schools/overdue` - Overdue payments from referred schools

## 🎨 **FRONTEND INTERFACE SPECIFICATIONS**

### **Admin Dashboard Enhancements**

#### **Discount Management Interface**
```typescript
interface DiscountCreationForm {
  subscriptionId: string;
  discountPercentage: number; // 1-100
  durationMonths: number; // 1-24
  startDate: Date;
  reason?: string;
}

interface DiscountDisplayCard {
  id: string;
  percentage: number;
  remainingMonths: number;
  endDate: Date;
  monthlySavings: number;
  totalSavings: number;
  isActive: boolean;
}
```

#### **Commission Management Dashboard**
```typescript
interface CommissionOverview {
  totalEligibleAmount: number;
  totalHeldAmount: number;
  totalPaidThisMonth: number;
  partnersWithEligiblePayouts: number;
  overdueSchoolsCount: number;
}

interface EligiblePayout {
  partnerId: string;
  partnerName: string;
  totalEligibleAmount: number;
  oldestEligibleDate: Date;
  schoolsCount: number;
  bankDetails: PartnerBankDetails;
}
```

### **School Portal Enhancements**

#### **Enhanced Billing Dashboard**
```typescript
interface SchoolBillingDashboard {
  currentBill: {
    originalAmount: number;
    discountAmount: number;
    finalAmount: number;
    dueDate: Date;
    daysUntilDue: number;
    penaltyAmount: number;
  };
  activeDiscount: {
    percentage: number;
    remainingMonths: number;
    endDate: Date;
    monthlySavings: number;
  } | null;
  advancePayment: {
    balance: number;
    monthsRemaining: number;
    nextDeductionDate: Date;
  } | null;
  paymentHistory: BillingTransaction[];
  upcomingBills: FutureBill[];
}
```

#### **Advance Payment Interface**
```typescript
interface AdvancePaymentOption {
  months: number;
  amountPerMonth: number;
  totalAmount: number;
  discountApplied: number;
  savings: number;
  description: string;
}

const advancePaymentOptions: AdvancePaymentOption[] = [
  { months: 3, amountPerMonth: 48000, totalAmount: 144000, discountApplied: 0, savings: 0, description: "3 Months" },
  { months: 6, amountPerMonth: 48000, totalAmount: 288000, discountApplied: 0, savings: 0, description: "6 Months" },
  { months: 12, amountPerMonth: 48000, totalAmount: 576000, discountApplied: 0, savings: 0, description: "1 Year" }
];
```

### **Partner Portal Enhancements**

#### **Commission Dashboard**
```typescript
interface PartnerCommissionDashboard {
  overview: {
    totalEarningsThisMonth: number;
    totalEarningsAllTime: number;
    pendingAmount: number;
    eligibleForPayout: number;
    averageCommissionPerSchool: number;
  };
  recentCommissions: CommissionTransaction[];
  overdueSchools: OverdueSchoolAlert[];
  payoutHistory: PayoutRecord[];
}

interface OverdueSchoolAlert {
  schoolName: string;
  daysOverdue: number;
  overdueAmount: number;
  affectedCommission: number;
  lastContactDate: Date;
}
```

## 🔧 **TECHNICAL IMPLEMENTATION DETAILS**

### **Database Migration Script**
```sql
-- Migration script for discount billing system
BEGIN;

-- 1. Create subscription_discounts table
CREATE TABLE subscription_discounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  discount_percentage DECIMAL(5,2) NOT NULL CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
  discount_duration_months INTEGER NOT NULL CHECK (discount_duration_months > 0),
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  remaining_months INTEGER NOT NULL CHECK (remaining_months >= 0),
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  -- Constraints
  CONSTRAINT valid_date_range CHECK (end_date > start_date),
  CONSTRAINT unique_active_discount_per_subscription UNIQUE (subscription_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- 2. Create subscription_expenses table
CREATE TABLE subscription_expenses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  monthly_operational_cost DECIMAL(10,2) NOT NULL CHECK (monthly_operational_cost >= 0),
  description TEXT,
  effective_from DATE NOT NULL,
  effective_until DATE,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  -- Constraints
  CONSTRAINT valid_effective_period CHECK (effective_until IS NULL OR effective_until > effective_from)
);

-- 3. Create partner_commission_config table
CREATE TABLE partner_commission_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id UUID REFERENCES partners(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  commission_percentage DECIMAL(5,2) NOT NULL CHECK (commission_percentage >= 0 AND commission_percentage <= 100),
  holding_period_days INTEGER DEFAULT 30 CHECK (holding_period_days >= 0),
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  -- Constraints
  CONSTRAINT unique_active_commission_config UNIQUE (partner_id, subscription_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- 4. Create enhanced partner_commission_transactions table
CREATE TABLE partner_commission_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id UUID REFERENCES partners(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  payment_id UUID REFERENCES billing_transactions(id) ON DELETE CASCADE,

  -- Financial Breakdown (amounts in paise for precision)
  school_payment_amount DECIMAL(10,2) NOT NULL CHECK (school_payment_amount >= 0),
  discount_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
  operational_expenses DECIMAL(10,2) NOT NULL CHECK (operational_expenses >= 0),
  profit_amount DECIMAL(10,2) NOT NULL,
  commission_percentage DECIMAL(5,2) NOT NULL CHECK (commission_percentage >= 0),
  commission_amount DECIMAL(10,2) NOT NULL CHECK (commission_amount >= 0),
  penalty_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (penalty_amount >= 0),

  -- Status Management
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'held', 'eligible', 'paid', 'cancelled')),
  hold_until_date DATE,
  eligible_date DATE,
  paid_date DATE,

  -- Payout Information
  payout_method VARCHAR(20) CHECK (payout_method IN ('neft', 'bank_transfer', 'upi')),
  transaction_reference VARCHAR(100),
  payout_amount DECIMAL(10,2),
  payout_notes TEXT,

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  -- Constraints
  CONSTRAINT valid_profit_calculation CHECK (profit_amount = school_payment_amount - discount_amount - operational_expenses),
  CONSTRAINT valid_commission_calculation CHECK (commission_amount = (profit_amount * commission_percentage / 100)),
  CONSTRAINT valid_status_dates CHECK (
    (status = 'held' AND hold_until_date IS NOT NULL) OR
    (status = 'eligible' AND eligible_date IS NOT NULL) OR
    (status = 'paid' AND paid_date IS NOT NULL AND payout_amount IS NOT NULL) OR
    status IN ('pending', 'cancelled')
  )
);

-- 5. Create advance_payments table
CREATE TABLE advance_payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  payment_id UUID REFERENCES billing_transactions(id) ON DELETE CASCADE,
  months_paid INTEGER NOT NULL CHECK (months_paid > 0),
  amount_per_month DECIMAL(10,2) NOT NULL CHECK (amount_per_month > 0),
  total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount > 0),
  start_month DATE NOT NULL,
  end_month DATE NOT NULL,
  remaining_months INTEGER NOT NULL CHECK (remaining_months >= 0),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  -- Constraints
  CONSTRAINT valid_advance_calculation CHECK (total_amount = amount_per_month * months_paid),
  CONSTRAINT valid_month_range CHECK (end_month > start_month)
);

-- 6. Enhance existing billing_subscriptions table
ALTER TABLE billing_subscriptions ADD COLUMN IF NOT EXISTS
  has_active_discount BOOLEAN DEFAULT false,
  current_discount_percentage DECIMAL(5,2) DEFAULT 0.00 CHECK (current_discount_percentage >= 0),
  discount_end_date DATE,
  advance_payment_balance DECIMAL(10,2) DEFAULT 0.00 CHECK (advance_payment_balance >= 0),
  advance_months_remaining INTEGER DEFAULT 0 CHECK (advance_months_remaining >= 0);

-- 7. Enhance existing billing_transactions table
ALTER TABLE billing_transactions ADD COLUMN IF NOT EXISTS
  original_amount DECIMAL(10,2), -- Amount before discount
  discount_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
  final_amount DECIMAL(10,2), -- Amount after discount
  is_advance_payment BOOLEAN DEFAULT false,
  advance_months_covered INTEGER DEFAULT 1 CHECK (advance_months_covered > 0),
  invoice_number VARCHAR(50) UNIQUE,
  receipt_number VARCHAR(50) UNIQUE,
  invoice_pdf_path VARCHAR(500),
  receipt_pdf_path VARCHAR(500),
  email_sent_at TIMESTAMP;

-- 8. Create indexes for performance
CREATE INDEX idx_subscription_discounts_subscription_id ON subscription_discounts(subscription_id);
CREATE INDEX idx_subscription_discounts_active ON subscription_discounts(subscription_id, is_active) WHERE is_active = true;
CREATE INDEX idx_subscription_expenses_subscription_id ON subscription_expenses(subscription_id);
CREATE INDEX idx_partner_commission_config_partner_subscription ON partner_commission_config(partner_id, subscription_id);
CREATE INDEX idx_partner_commission_transactions_partner_id ON partner_commission_transactions(partner_id);
CREATE INDEX idx_partner_commission_transactions_status ON partner_commission_transactions(status);
CREATE INDEX idx_partner_commission_transactions_eligible_date ON partner_commission_transactions(eligible_date) WHERE status = 'eligible';
CREATE INDEX idx_advance_payments_subscription_id ON advance_payments(subscription_id);
CREATE INDEX idx_billing_transactions_invoice_number ON billing_transactions(invoice_number) WHERE invoice_number IS NOT NULL;

-- 9. Create triggers for automatic updates
CREATE OR REPLACE FUNCTION update_subscription_discount_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Update subscription discount status when discount changes
  UPDATE billing_subscriptions
  SET
    has_active_discount = EXISTS(
      SELECT 1 FROM subscription_discounts
      WHERE subscription_id = NEW.subscription_id
      AND is_active = true
      AND CURRENT_DATE BETWEEN start_date AND end_date
    ),
    current_discount_percentage = COALESCE((
      SELECT discount_percentage FROM subscription_discounts
      WHERE subscription_id = NEW.subscription_id
      AND is_active = true
      AND CURRENT_DATE BETWEEN start_date AND end_date
      LIMIT 1
    ), 0),
    discount_end_date = (
      SELECT end_date FROM subscription_discounts
      WHERE subscription_id = NEW.subscription_id
      AND is_active = true
      AND CURRENT_DATE BETWEEN start_date AND end_date
      LIMIT 1
    )
  WHERE id = NEW.subscription_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_subscription_discount_status
  AFTER INSERT OR UPDATE ON subscription_discounts
  FOR EACH ROW
  EXECUTE FUNCTION update_subscription_discount_status();

COMMIT;

## 🚀 **API IMPLEMENTATION SPECIFICATIONS**

### **Admin Discount Management APIs**

#### **Create Subscription Discount**
```typescript
// POST /api/admin/subscriptions/:id/discounts
interface CreateDiscountRequest {
  discountPercentage: number; // 1-100
  durationMonths: number; // 1-24
  startDate: string; // ISO date
  reason?: string;
}

interface CreateDiscountResponse {
  success: boolean;
  data: {
    id: string;
    subscriptionId: string;
    discountPercentage: number;
    durationMonths: number;
    startDate: string;
    endDate: string;
    remainingMonths: number;
    monthlySavings: number;
    totalSavings: number;
    isActive: boolean;
  };
}

// Implementation
const createSubscriptionDiscount = async (c: Context) => {
  const subscriptionId = c.req.param('id');
  const { discountPercentage, durationMonths, startDate, reason } = await c.req.json();

  // Validate subscription exists and is active
  const subscription = await db.select()
    .from(billingSubscriptions)
    .where(eq(billingSubscriptions.id, subscriptionId))
    .limit(1);

  if (!subscription.length) {
    return c.json({ success: false, error: 'Subscription not found' }, 404);
  }

  // Check for existing active discount
  const existingDiscount = await db.select()
    .from(subscriptionDiscounts)
    .where(and(
      eq(subscriptionDiscounts.subscriptionId, subscriptionId),
      eq(subscriptionDiscounts.isActive, true)
    ))
    .limit(1);

  if (existingDiscount.length) {
    return c.json({ success: false, error: 'Active discount already exists' }, 409);
  }

  // Calculate end date
  const start = new Date(startDate);
  const end = new Date(start);
  end.setMonth(end.getMonth() + durationMonths);

  // Create discount
  const [discount] = await db.insert(subscriptionDiscounts)
    .values({
      subscriptionId,
      discountPercentage,
      discountDurationMonths: durationMonths,
      startDate: start,
      endDate: end,
      remainingMonths: durationMonths,
      isActive: true,
      createdBy: c.get('user').userId
    })
    .returning();

  // Calculate savings
  const monthlySavings = (subscription[0].monthlyAmount * discountPercentage) / 100;
  const totalSavings = monthlySavings * durationMonths;

  return c.json({
    success: true,
    data: {
      ...discount,
      monthlySavings,
      totalSavings
    }
  });
};
```

#### **Set Operational Expenses**
```typescript
// POST /api/admin/subscriptions/:id/expenses
interface SetExpensesRequest {
  monthlyOperationalCost: number;
  description?: string;
  effectiveFrom: string; // ISO date
}

const setSubscriptionExpenses = async (c: Context) => {
  const subscriptionId = c.req.param('id');
  const { monthlyOperationalCost, description, effectiveFrom } = await c.req.json();

  // Deactivate existing expenses
  await db.update(subscriptionExpenses)
    .set({ isActive: false, updatedAt: new Date() })
    .where(and(
      eq(subscriptionExpenses.subscriptionId, subscriptionId),
      eq(subscriptionExpenses.isActive, true)
    ));

  // Create new expense record
  const [expense] = await db.insert(subscriptionExpenses)
    .values({
      subscriptionId,
      monthlyOperationalCost,
      description,
      effectiveFrom: new Date(effectiveFrom),
      isActive: true,
      createdBy: c.get('user').userId
    })
    .returning();

  return c.json({ success: true, data: expense });
};
```

### **Enhanced School Billing APIs**

#### **Get Comprehensive Billing Dashboard**
```typescript
// GET /api/school/billing/dashboard
interface BillingDashboardResponse {
  success: boolean;
  data: {
    currentBill: {
      originalAmount: number;
      discountAmount: number;
      finalAmount: number;
      dueDate: string;
      daysUntilDue: number;
      penaltyAmount: number;
      status: string;
    };
    activeDiscount: {
      percentage: number;
      remainingMonths: number;
      endDate: string;
      monthlySavings: number;
      totalSavingsToDate: number;
    } | null;
    advancePayment: {
      balance: number;
      monthsRemaining: number;
      nextDeductionDate: string;
    } | null;
    paymentHistory: BillingTransaction[];
    upcomingBills: FutureBill[];
  };
}

const getSchoolBillingDashboard = async (c: Context) => {
  const clientId = c.get('user').clientId;

  // Get active subscription
  const [subscription] = await db.select()
    .from(billingSubscriptions)
    .where(eq(billingSubscriptions.clientId, clientId))
    .limit(1);

  if (!subscription) {
    return c.json({ success: false, error: 'No active subscription found' }, 404);
  }

  // Get current bill
  const currentBill = await getCurrentBillDetails(subscription.id);

  // Get active discount
  const activeDiscount = await getActiveDiscount(subscription.id);

  // Get advance payment balance
  const advancePayment = await getAdvancePaymentBalance(subscription.id);

  // Get payment history
  const paymentHistory = await getPaymentHistory(subscription.id);

  // Get upcoming bills
  const upcomingBills = await getUpcomingBills(subscription.id);

  return c.json({
    success: true,
    data: {
      currentBill,
      activeDiscount,
      advancePayment,
      paymentHistory,
      upcomingBills
    }
  });
};
```

#### **Create Advance Payment Order**
```typescript
// POST /api/school/billing/advance-payment
interface AdvancePaymentRequest {
  months: number; // 1-12
  paymentMethod?: string;
}

const createAdvancePaymentOrder = async (c: Context) => {
  const clientId = c.get('user').clientId;
  const { months } = await c.req.json();

  // Get subscription details
  const [subscription] = await db.select()
    .from(billingSubscriptions)
    .where(eq(billingSubscriptions.clientId, clientId))
    .limit(1);

  // Calculate advance payment amount
  const monthlyAmount = subscription.monthlyAmount;
  const discountAmount = subscription.hasActiveDiscount
    ? (monthlyAmount * subscription.currentDiscountPercentage) / 100
    : 0;
  const finalMonthlyAmount = monthlyAmount - discountAmount;
  const totalAmount = finalMonthlyAmount * months;

  // Create Razorpay order
  const razorpayOrder = await razorpayService.createOrder({
    amount: Math.round(totalAmount * 100), // Convert to paise
    currency: 'INR',
    receipt: `adv_${subscription.id}_${Date.now()}`,
    notes: {
      type: 'advance_payment',
      subscription_id: subscription.id,
      months: months.toString(),
      client_id: clientId
    }
  });

  return c.json({
    success: true,
    data: {
      orderId: razorpayOrder.id,
      amount: totalAmount,
      months,
      monthlyAmount: finalMonthlyAmount,
      discountApplied: discountAmount,
      currency: 'INR',
      keyId: process.env.RAZORPAY_KEY_ID,
      description: `Advance payment for ${months} months - ${subscription.schoolName}`,
      prefill: {
        name: subscription.schoolName,
        email: subscription.email,
        contact: subscription.phone
      }
    }
  });
};
```

### **Partner Commission Management APIs**

#### **Get Commission Dashboard**
```typescript
// GET /api/partner/commissions/dashboard
interface PartnerCommissionDashboard {
  overview: {
    totalEarningsThisMonth: number;
    totalEarningsAllTime: number;
    pendingAmount: number;
    eligibleForPayout: number;
    averageCommissionPerSchool: number;
    activeSchoolsCount: number;
  };
  recentCommissions: CommissionTransaction[];
  overdueSchools: OverdueSchoolAlert[];
  payoutHistory: PayoutRecord[];
}

const getPartnerCommissionDashboard = async (c: Context) => {
  const partnerId = c.get('user').partnerId;

  // Get overview statistics
  const overview = await getPartnerCommissionOverview(partnerId);

  // Get recent commission transactions
  const recentCommissions = await db.select({
    id: partnerCommissionTransactions.id,
    schoolName: clients.schoolName,
    commissionAmount: partnerCommissionTransactions.commissionAmount,
    status: partnerCommissionTransactions.status,
    createdAt: partnerCommissionTransactions.createdAt,
    eligibleDate: partnerCommissionTransactions.eligibleDate
  })
  .from(partnerCommissionTransactions)
  .innerJoin(billingSubscriptions, eq(partnerCommissionTransactions.subscriptionId, billingSubscriptions.id))
  .innerJoin(clients, eq(billingSubscriptions.clientId, clients.id))
  .where(eq(partnerCommissionTransactions.partnerId, partnerId))
  .orderBy(desc(partnerCommissionTransactions.createdAt))
  .limit(10);

  // Get overdue schools
  const overdueSchools = await getOverdueSchoolsForPartner(partnerId);

  // Get payout history
  const payoutHistory = await getPartnerPayoutHistory(partnerId);

  return c.json({
    success: true,
    data: {
      overview,
      recentCommissions,
      overdueSchools,
      payoutHistory
    }
  });
};
```

## 🔄 **SERVICE IMPLEMENTATIONS**

### **Discount Management Service**
```typescript
class DiscountManagementService {
  async applyMonthlyDiscount(subscriptionId: string): Promise<DiscountApplication> {
    // Get active discount
    const activeDiscount = await this.getActiveDiscount(subscriptionId);
    if (!activeDiscount) {
      return { hasDiscount: false, originalAmount: 0, discountAmount: 0, finalAmount: 0 };
    }

    // Get subscription details
    const subscription = await this.getSubscription(subscriptionId);
    const originalAmount = subscription.monthlyAmount;

    // Calculate discount
    const discountAmount = (originalAmount * activeDiscount.discountPercentage) / 100;
    const finalAmount = originalAmount - discountAmount;

    // Update remaining months
    await this.updateDiscountRemainingMonths(activeDiscount.id);

    return {
      hasDiscount: true,
      originalAmount,
      discountAmount,
      finalAmount,
      discountPercentage: activeDiscount.discountPercentage,
      remainingMonths: activeDiscount.remainingMonths - 1
    };
  }

  async expireDiscounts(): Promise<void> {
    // Daily job to expire discounts
    await db.update(subscriptionDiscounts)
      .set({ isActive: false, updatedAt: new Date() })
      .where(and(
        eq(subscriptionDiscounts.isActive, true),
        lte(subscriptionDiscounts.endDate, new Date())
      ));
  }
}
```

### **Commission Calculation Service**
```typescript
class CommissionCalculationService {
  async calculateCommission(
    paymentId: string,
    partnerId: string,
    subscriptionId: string
  ): Promise<CommissionCalculation> {
    // Get payment details
    const payment = await this.getPaymentDetails(paymentId);

    // Get operational expenses
    const expenses = await this.getOperationalExpenses(subscriptionId);

    // Get commission configuration
    const commissionConfig = await this.getCommissionConfig(partnerId, subscriptionId);

    // Calculate commission (excluding penalties from partner commission)
    const basePayment = payment.totalAmount - (payment.penaltyAmount || 0);
    const profitAmount = basePayment - (payment.discountAmount || 0) - expenses.monthlyOperationalCost;
    const commissionAmount = (profitAmount * commissionConfig.commissionPercentage) / 100;

    // Determine holding period
    const holdUntilDate = new Date();
    holdUntilDate.setDate(holdUntilDate.getDate() + commissionConfig.holdingPeriodDays);

    // Create commission transaction
    const commissionTransaction = await db.insert(partnerCommissionTransactions)
      .values({
        partnerId,
        subscriptionId,
        paymentId,
        schoolPaymentAmount: basePayment,
        discountAmount: payment.discountAmount || 0,
        operationalExpenses: expenses.monthlyOperationalCost,
        profitAmount,
        commissionPercentage: commissionConfig.commissionPercentage,
        commissionAmount,
        penaltyAmount: payment.penaltyAmount || 0,
        status: 'held',
        holdUntilDate,
        eligibleDate: holdUntilDate
      })
      .returning();

    return {
      commissionId: commissionTransaction[0].id,
      commissionAmount,
      profitAmount,
      holdUntilDate,
      status: 'held'
    };
  }

  async processEligibleCommissions(): Promise<void> {
    // Daily job to mark held commissions as eligible
    await db.update(partnerCommissionTransactions)
      .set({ status: 'eligible', updatedAt: new Date() })
      .where(and(
        eq(partnerCommissionTransactions.status, 'held'),
        lte(partnerCommissionTransactions.holdUntilDate, new Date())
      ));
  }
}
```

## 📧 **EMAIL AUTOMATION & PDF GENERATION**

### **Invoice Generation Service**
```typescript
class InvoiceGenerationService {
  async generateInvoice(paymentId: string): Promise<InvoiceDetails> {
    const payment = await this.getPaymentWithDetails(paymentId);
    const subscription = await this.getSubscriptionDetails(payment.subscriptionId);
    const client = await this.getClientDetails(subscription.clientId);

    // Generate invoice number
    const invoiceNumber = this.generateInvoiceNumber(payment.createdAt);

    // Create invoice data
    const invoiceData = {
      invoiceNumber,
      invoiceDate: payment.createdAt,
      dueDate: payment.dueDate,
      client: {
        name: client.schoolName,
        email: client.email,
        phone: client.phone,
        address: client.address
      },
      subscription: {
        studentCount: subscription.studentCount,
        pricePerStudent: subscription.pricePerStudent,
        billingPeriod: this.formatBillingPeriod(payment.dueDate)
      },
      billing: {
        originalAmount: payment.originalAmount || payment.amount,
        discountPercentage: payment.discountAmount ?
          ((payment.discountAmount / payment.originalAmount) * 100) : 0,
        discountAmount: payment.discountAmount || 0,
        finalAmount: payment.finalAmount || payment.totalAmount,
        penaltyAmount: payment.penaltyAmount || 0,
        totalAmount: payment.totalAmount
      },
      paymentDetails: payment.razorpayPaymentId ? {
        paymentId: payment.razorpayPaymentId,
        paymentMethod: payment.paymentMethod,
        paymentDate: payment.paymentDate
      } : null
    };

    // Generate PDF
    const pdfBuffer = await this.generateInvoicePDF(invoiceData);
    const pdfPath = await this.savePDF(pdfBuffer, `invoice_${invoiceNumber}.pdf`);

    // Update payment record
    await db.update(billingTransactions)
      .set({
        invoiceNumber,
        invoicePdfPath: pdfPath,
        updatedAt: new Date()
      })
      .where(eq(billingTransactions.id, paymentId));

    return {
      invoiceNumber,
      pdfPath,
      invoiceData
    };
  }

  private async generateInvoicePDF(data: InvoiceData): Promise<Buffer> {
    // Use a PDF generation library like puppeteer or jsPDF
    const html = this.generateInvoiceHTML(data);
    return await this.htmlToPDF(html);
  }

  private generateInvoiceHTML(data: InvoiceData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Invoice ${data.invoiceNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          .header { text-align: center; margin-bottom: 30px; }
          .invoice-details { margin-bottom: 30px; }
          .billing-table { width: 100%; border-collapse: collapse; }
          .billing-table th, .billing-table td {
            border: 1px solid #ddd; padding: 12px; text-align: left;
          }
          .total-row { font-weight: bold; background-color: #f5f5f5; }
          .discount-row { color: #28a745; }
          .penalty-row { color: #dc3545; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>SCHOPIO</h1>
          <h2>School Management System</h2>
          <p>Invoice #${data.invoiceNumber}</p>
        </div>

        <div class="invoice-details">
          <div style="display: flex; justify-content: space-between;">
            <div>
              <h3>Bill To:</h3>
              <p><strong>${data.client.name}</strong></p>
              <p>${data.client.email}</p>
              <p>${data.client.phone}</p>
              <p>${data.client.address}</p>
            </div>
            <div>
              <p><strong>Invoice Date:</strong> ${data.invoiceDate.toLocaleDateString()}</p>
              <p><strong>Due Date:</strong> ${data.dueDate.toLocaleDateString()}</p>
              <p><strong>Billing Period:</strong> ${data.subscription.billingPeriod}</p>
            </div>
          </div>
        </div>

        <table class="billing-table">
          <thead>
            <tr>
              <th>Description</th>
              <th>Quantity</th>
              <th>Rate</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>School Management System Subscription</td>
              <td>${data.subscription.studentCount} students</td>
              <td>₹${data.subscription.pricePerStudent}</td>
              <td>₹${data.billing.originalAmount.toFixed(2)}</td>
            </tr>
            ${data.billing.discountAmount > 0 ? `
            <tr class="discount-row">
              <td>Discount (${data.billing.discountPercentage.toFixed(1)}%)</td>
              <td>-</td>
              <td>-</td>
              <td>-₹${data.billing.discountAmount.toFixed(2)}</td>
            </tr>
            ` : ''}
            ${data.billing.penaltyAmount > 0 ? `
            <tr class="penalty-row">
              <td>Late Payment Penalty</td>
              <td>-</td>
              <td>-</td>
              <td>₹${data.billing.penaltyAmount.toFixed(2)}</td>
            </tr>
            ` : ''}
            <tr class="total-row">
              <td colspan="3"><strong>Total Amount</strong></td>
              <td><strong>₹${data.billing.totalAmount.toFixed(2)}</strong></td>
            </tr>
          </tbody>
        </table>

        ${data.paymentDetails ? `
        <div style="margin-top: 30px;">
          <h3>Payment Details</h3>
          <p><strong>Payment ID:</strong> ${data.paymentDetails.paymentId}</p>
          <p><strong>Payment Method:</strong> ${data.paymentDetails.paymentMethod}</p>
          <p><strong>Payment Date:</strong> ${data.paymentDetails.paymentDate.toLocaleDateString()}</p>
          <p style="color: #28a745;"><strong>Status: PAID</strong></p>
        </div>
        ` : `
        <div style="margin-top: 30px;">
          <p style="color: #dc3545;"><strong>Status: PENDING</strong></p>
          <p>Please make payment by the due date to avoid penalties.</p>
        </div>
        `}

        <div style="margin-top: 40px; text-align: center; font-size: 12px; color: #666;">
          <p>Thank you for choosing Schopio School Management System</p>
          <p>For support, contact <NAME_EMAIL></p>
        </div>
      </body>
      </html>
    `;
  }
}
```

### **Email Automation Service**
```typescript
class EmailAutomationService {
  async sendPaymentConfirmation(paymentId: string): Promise<void> {
    const payment = await this.getPaymentWithDetails(paymentId);
    const invoice = await this.invoiceService.generateInvoice(paymentId);

    const emailData = {
      to: payment.client.email,
      subject: `Payment Confirmation - Invoice #${invoice.invoiceNumber}`,
      template: 'payment-confirmation',
      data: {
        schoolName: payment.client.schoolName,
        invoiceNumber: invoice.invoiceNumber,
        amount: payment.totalAmount,
        paymentDate: payment.paymentDate,
        paymentMethod: payment.paymentMethod,
        nextDueDate: payment.nextDueDate
      },
      attachments: [{
        filename: `invoice_${invoice.invoiceNumber}.pdf`,
        path: invoice.pdfPath
      }]
    };

    await this.resendService.sendEmail(emailData);

    // Update payment record
    await db.update(billingTransactions)
      .set({ emailSentAt: new Date() })
      .where(eq(billingTransactions.id, paymentId));
  }

  async sendOverdueNotification(subscriptionId: string): Promise<void> {
    const subscription = await this.getSubscriptionWithClient(subscriptionId);
    const overdueAmount = await this.calculateOverdueAmount(subscriptionId);
    const penaltyAmount = await this.calculateCurrentPenalty(subscriptionId);

    const emailData = {
      to: subscription.client.email,
      subject: `Payment Overdue - Action Required`,
      template: 'overdue-notification',
      data: {
        schoolName: subscription.client.schoolName,
        overdueAmount,
        penaltyAmount,
        totalAmount: overdueAmount + penaltyAmount,
        daysOverdue: this.calculateDaysOverdue(subscription.dueDate),
        paymentLink: this.generatePaymentLink(subscriptionId)
      }
    };

    await this.resendService.sendEmail(emailData);

    // Notify partner if exists
    if (subscription.partnerId) {
      await this.notifyPartnerOfOverduePayment(subscription.partnerId, subscriptionId);
    }
  }

  async sendDiscountNotification(subscriptionId: string, discountId: string): Promise<void> {
    const subscription = await this.getSubscriptionWithClient(subscriptionId);
    const discount = await this.getDiscountDetails(discountId);

    const emailData = {
      to: subscription.client.email,
      subject: `Special Discount Applied - ${discount.discountPercentage}% Off`,
      template: 'discount-notification',
      data: {
        schoolName: subscription.client.schoolName,
        discountPercentage: discount.discountPercentage,
        durationMonths: discount.durationMonths,
        monthlySavings: (subscription.monthlyAmount * discount.discountPercentage) / 100,
        totalSavings: ((subscription.monthlyAmount * discount.discountPercentage) / 100) * discount.durationMonths,
        validUntil: discount.endDate
      }
    };

    await this.resendService.sendEmail(emailData);
  }
}
```

## 🚀 **IMPLEMENTATION ROADMAP**

### **Phase 1: Database Schema & Core Services (Week 1)**
1. **Database Migration**
   - Execute schema migration script
   - Create indexes and triggers
   - Test data integrity constraints

2. **Core Service Implementation**
   - Discount Management Service
   - Commission Calculation Service
   - Enhanced Billing Calculator

3. **Basic API Endpoints**
   - Admin discount management
   - Expense tracking
   - Commission configuration

### **Phase 2: Enhanced Billing Interface (Week 2)**
1. **School Portal Enhancements**
   - Comprehensive billing dashboard
   - Advance payment interface
   - Payment history with discounts

2. **Admin Dashboard Updates**
   - Discount management interface
   - Commission overview dashboard
   - Expense tracking interface

3. **Partner Portal Enhancements**
   - Commission dashboard
   - Overdue school alerts
   - Payout history

### **Phase 3: Automation & Integration (Week 3)**
1. **Email Automation**
   - Payment confirmation emails
   - Overdue notifications
   - Discount notifications

2. **PDF Generation**
   - Invoice generation
   - Receipt creation
   - Download portal

3. **Payment Processing**
   - Enhanced Razorpay integration
   - Advance payment handling
   - Commission calculation automation

### **Phase 4: Testing & Deployment (Week 4)**
1. **End-to-End Testing**
   - Discount application workflows
   - Commission calculation accuracy
   - Payment processing flows

2. **Performance Optimization**
   - Database query optimization
   - API response time improvements
   - PDF generation optimization

3. **Production Deployment**
   - Environment configuration
   - Monitoring setup
   - Documentation completion

## 📋 **TESTING CHECKLIST**

### **Discount System Testing**
- [ ] Create discount with various percentages and durations
- [ ] Verify automatic discount application in billing
- [ ] Test discount expiration and deactivation
- [ ] Validate discount impact on commission calculations

### **Commission System Testing**
- [ ] Verify commission calculation accuracy
- [ ] Test holding period functionality
- [ ] Validate manual payout processing
- [ ] Check partner dashboard data accuracy

### **Payment Processing Testing**
- [ ] Test regular monthly payments with discounts
- [ ] Verify advance payment allocation
- [ ] Test penalty calculation and application
- [ ] Validate Razorpay integration

### **Email & PDF Testing**
- [ ] Test invoice PDF generation
- [ ] Verify email delivery for all scenarios
- [ ] Test attachment functionality
- [ ] Validate email template rendering

## 🔧 **DEPLOYMENT CONFIGURATION**

### **Environment Variables**
```bash
# Existing variables
DATABASE_URL=postgresql://...
RAZORPAY_KEY_ID=rzp_test_...
RAZORPAY_KEY_SECRET=...
RESEND_API_KEY=...

# New variables for enhanced system
PDF_STORAGE_PATH=/app/storage/pdfs
INVOICE_NUMBER_PREFIX=INV
RECEIPT_NUMBER_PREFIX=RCP
COMMISSION_HOLDING_PERIOD_DAYS=30
DEFAULT_PENALTY_RATE=2.0
DEFAULT_GRACE_PERIOD_DAYS=3
```

### **File Storage Setup**
```bash
# Create directories for PDF storage
mkdir -p /app/storage/pdfs/invoices
mkdir -p /app/storage/pdfs/receipts
chmod 755 /app/storage/pdfs
```

### **Cron Jobs Setup**
```bash
# Daily jobs for automation
0 2 * * * /app/scripts/expire-discounts.js
0 3 * * * /app/scripts/calculate-penalties.js
0 4 * * * /app/scripts/process-eligible-commissions.js
0 6 * * * /app/scripts/send-overdue-notifications.js
```

This comprehensive system design provides the foundation for implementing a sophisticated discount-based billing system with partner commission management while maintaining the flexibility of manual payments.
