import { db } from '@/src/db'
import { operationalExpenses, partnerCommissionEscrow, partners } from '@/src/db/schema'
import { eq, and, sql, desc } from 'drizzle-orm'

/**
 * Commission Calculation Engine
 * Handles automated commission calculations with operational expense deduction,
 * risk assessment, and escrow management integration
 */

export interface CommissionCalculation {
  grossAmount: number
  operationalExpenses: ExpenseBreakdown
  netAmount: number
  partnerSharePercentage: number
  finalCommissionAmount: number
  riskScore: number
  holdPeriod: number
  metadata: CommissionMetadata
}

export interface ExpenseBreakdown {
  totalExpenses: number
  expenseCategories: ExpenseCategory[]
  expensePercentage: number
}

export interface ExpenseCategory {
  categoryName: string
  amount: number
  isPercentage: boolean
  appliesTo: string
}

export interface CommissionMetadata {
  calculationDate: Date
  partnerId: string
  schoolId: string
  monthYear: string
  paymentReference: string
  calculationMethod: 'standard' | 'custom'
  riskFactors: string[]
}

export interface ReleaseConditions {
  canRelease: boolean
  holdReason?: string
  requiredConditions: string[]
  estimatedReleaseDate?: Date
  manualReviewRequired: boolean
}

export class CommissionCalculationEngine {
  
  /**
   * Calculate monthly commission for a partner-school relationship
   */
  async calculateMonthlyCommission(
    schoolId: string,
    partnerId: string,
    grossAmount: number,
    monthYear: string,
    paymentReference: string
  ): Promise<CommissionCalculation> {
    try {
      // Get partner details for commission rate
      const [partner] = await db
        .select({
          id: partners.id,
          profitSharePercentage: partners.profitSharePercentage,
          isActive: partners.isActive,
          createdAt: partners.createdAt
        })
        .from(partners)
        .where(and(
          eq(partners.id, partnerId),
          eq(partners.isActive, true)
        ))
        .limit(1)

      if (!partner) {
        throw new Error(`Partner not found or inactive: ${partnerId}`)
      }

      // Calculate operational expenses
      const expenseBreakdown = await this.processOperationalExpenses(grossAmount, partnerId, schoolId)
      
      // Calculate net amount after expenses
      const netAmount = grossAmount - expenseBreakdown.totalExpenses
      
      // Calculate partner commission
      const partnerSharePercentage = parseFloat(partner.profitSharePercentage || '20')
      const finalCommissionAmount = Math.max(0, netAmount * (partnerSharePercentage / 100))
      
      // Calculate risk score for escrow management
      const riskScore = await this.calculateRiskScore(partnerId, schoolId, grossAmount, finalCommissionAmount)
      
      // Determine hold period based on risk
      const holdPeriod = this.calculateHoldPeriod(riskScore)
      
      const metadata: CommissionMetadata = {
        calculationDate: new Date(),
        partnerId,
        schoolId,
        monthYear,
        paymentReference,
        calculationMethod: 'standard',
        riskFactors: await this.getRiskFactors(partnerId, schoolId, grossAmount)
      }

      return {
        grossAmount,
        operationalExpenses: expenseBreakdown,
        netAmount,
        partnerSharePercentage,
        finalCommissionAmount,
        riskScore,
        holdPeriod,
        metadata
      }

    } catch (error) {
      console.error('Commission calculation error:', error)
      throw new Error(`Failed to calculate commission: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Process operational expenses with category-based deduction
   */
  async processOperationalExpenses(
    baseAmount: number,
    partnerId: string,
    schoolId: string
  ): Promise<ExpenseBreakdown> {
    try {
      // Get active operational expenses
      const expenses = await db
        .select({
          categoryName: operationalExpenses.categoryName,
          amountPerSchool: operationalExpenses.amountPerSchool,
          isPercentage: operationalExpenses.isPercentage,
          percentageValue: operationalExpenses.percentageValue,
          appliesTo: operationalExpenses.appliesTo
        })
        .from(operationalExpenses)
        .where(eq(operationalExpenses.isActive, true))
        .orderBy(desc(operationalExpenses.createdAt))

      const expenseCategories: ExpenseCategory[] = []
      let totalExpenses = 0

      for (const expense of expenses) {
        // Check if expense applies to this partner/school
        if (expense.appliesTo !== 'all') {
          // TODO: Add logic for specific partner/school filtering
          continue
        }

        let expenseAmount = 0
        
        if (expense.isPercentage && expense.percentageValue) {
          expenseAmount = baseAmount * (parseFloat(expense.percentageValue) / 100)
        } else if (expense.amountPerSchool) {
          expenseAmount = parseFloat(expense.amountPerSchool)
        }

        if (expenseAmount > 0) {
          expenseCategories.push({
            categoryName: expense.categoryName,
            amount: expenseAmount,
            isPercentage: expense.isPercentage || false,
            appliesTo: expense.appliesTo
          })
          totalExpenses += expenseAmount
        }
      }

      const expensePercentage = baseAmount > 0 ? (totalExpenses / baseAmount) * 100 : 0

      return {
        totalExpenses,
        expenseCategories,
        expensePercentage
      }

    } catch (error) {
      console.error('Expense processing error:', error)
      throw new Error(`Failed to process operational expenses: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Calculate risk score for commission release (0-100)
   */
  async calculateRiskScore(
    partnerId: string,
    schoolId: string,
    grossAmount: number,
    commissionAmount: number
  ): Promise<number> {
    let riskScore = 0

    try {
      // Base risk factors
      if (grossAmount > 50000) riskScore += 15 // High value transactions
      if (grossAmount > 100000) riskScore += 25 // Very high value transactions
      if (commissionAmount > 10000) riskScore += 10 // High commission amounts

      // Partner history factors
      const [partner] = await db
        .select({
          createdAt: partners.createdAt,
          isActive: partners.isActive
        })
        .from(partners)
        .where(eq(partners.id, partnerId))
        .limit(1)

      if (partner && partner.createdAt) {
        // New partner risk (since we don't have trustScore field)
        const partnerAge = Date.now() - new Date(partner.createdAt).getTime()
        const daysOld = partnerAge / (1000 * 60 * 60 * 24)
        if (daysOld < 30) riskScore += 20 // New partners (< 30 days)
        else if (daysOld < 90) riskScore += 10 // Recent partners (< 90 days)
      }

      // Payment timing factors
      const currentDate = new Date()
      const dayOfMonth = currentDate.getDate()
      if (dayOfMonth <= 5) riskScore += 10 // End/start of month payments

      // Commission history factors
      const recentEscrows = await db
        .select({ count: sql<number>`count(*)` })
        .from(partnerCommissionEscrow)
        .where(and(
          eq(partnerCommissionEscrow.partnerId, partnerId),
          sql`${partnerCommissionEscrow.createdAt} >= NOW() - INTERVAL '30 days'`
        ))

      const recentCount = recentEscrows[0]?.count || 0
      if (recentCount > 5) riskScore += 15 // High frequency commissions

      return Math.min(100, Math.max(0, riskScore))

    } catch (error) {
      console.error('Risk calculation error:', error)
      return 50 // Default medium risk
    }
  }

  /**
   * Calculate hold period based on risk score
   */
  calculateHoldPeriod(riskScore: number): number {
    if (riskScore <= 30) return 1 // Tier 1: 0-1 days (low risk)
    if (riskScore <= 70) return 3 // Tier 2: 1-3 days (medium risk)
    return 7 // Tier 3: 3-7 days (high risk)
  }

  /**
   * Get risk factors for audit trail
   */
  async getRiskFactors(
    partnerId: string,
    schoolId: string,
    grossAmount: number
  ): Promise<string[]> {
    const factors: string[] = []

    if (grossAmount > 50000) factors.push('high_value_transaction')
    if (grossAmount > 100000) factors.push('very_high_value_transaction')

    // Check partner status (since we don't have trustScore field)
    const [partner] = await db
      .select({
        isActive: partners.isActive,
        createdAt: partners.createdAt
      })
      .from(partners)
      .where(eq(partners.id, partnerId))
      .limit(1)

    if (partner && partner.createdAt) {
      // Use partner age as trust indicator
      const partnerAge = Date.now() - new Date(partner.createdAt).getTime()
      const partnerAgeMonths = partnerAge / (1000 * 60 * 60 * 24 * 30)
      if (partnerAgeMonths < 1) factors.push('very_new_partner')
      else if (partnerAgeMonths < 3) factors.push('new_partner')
    }

    // Check timing
    const dayOfMonth = new Date().getDate()
    if (dayOfMonth <= 5) factors.push('month_end_payment')

    return factors
  }

  /**
   * Determine release conditions for escrow
   */
  async determineReleaseConditions(
    escrowId: string,
    schoolPaymentStatus: string,
    riskScore: number
  ): Promise<ReleaseConditions> {
    try {
      const requiredConditions: string[] = []
      let canRelease = true
      let holdReason: string | undefined
      let manualReviewRequired = false

      // Check school payment status
      if (schoolPaymentStatus !== 'success') {
        canRelease = false
        holdReason = 'School payment not confirmed'
        requiredConditions.push('school_payment_confirmation')
      }

      // Check risk-based conditions
      if (riskScore > 80) {
        canRelease = false
        manualReviewRequired = true
        holdReason = 'High risk transaction requires manual review'
        requiredConditions.push('manual_admin_approval')
      }

      // Check hold period
      const [escrow] = await db
        .select({
          holdUntilDate: partnerCommissionEscrow.holdUntilDate,
          createdAt: partnerCommissionEscrow.createdAt
        })
        .from(partnerCommissionEscrow)
        .where(eq(partnerCommissionEscrow.id, escrowId))
        .limit(1)

      if (escrow && escrow.holdUntilDate) {
        const now = new Date()
        const holdUntil = new Date(escrow.holdUntilDate)
        
        if (now < holdUntil) {
          canRelease = false
          holdReason = 'Hold period not yet expired'
          requiredConditions.push('hold_period_completion')
        }
      }

      return {
        canRelease,
        holdReason,
        requiredConditions,
        estimatedReleaseDate: escrow?.holdUntilDate ? new Date(escrow.holdUntilDate) : undefined,
        manualReviewRequired
      }

    } catch (error) {
      console.error('Release conditions error:', error)
      return {
        canRelease: false,
        holdReason: 'Error evaluating release conditions',
        requiredConditions: ['system_error_resolution'],
        manualReviewRequired: true
      }
    }
  }
}

// Export singleton instance
export const commissionCalculationEngine = new CommissionCalculationEngine()
