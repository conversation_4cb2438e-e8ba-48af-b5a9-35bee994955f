# Comprehensive Testing Guide for Schopio

This document provides a complete guide to testing the Schopio system, including all billing, partner management, and school portal functionality.

## Table of Contents

1. [Overview](#overview)
2. [Test Categories](#test-categories)
3. [Running Tests](#running-tests)
4. [Test Configuration](#test-configuration)
5. [Test Data Setup](#test-data-setup)
6. [Continuous Integration](#continuous-integration)
7. [Troubleshooting](#troubleshooting)

## Overview

The Schopio testing suite provides comprehensive coverage of:

- **Authentication & Authorization** - Login systems for admin, school, and partner portals
- **Billing System** - Subscription management, invoice generation, payment processing
- **Partner Management** - Commission tracking, referral systems, earnings management
- **School Portal** - Billing dashboard, payment functionality, document downloads
- **Admin Dashboard** - System management, financial oversight, user administration
- **Integration Tests** - End-to-end workflows and system integration
- **Performance Tests** - Response times, throughput, and load testing
- **Security Tests** - Authentication, authorization, and data protection

## Test Categories

### 🔐 Authentication & Authorization Tests

**Purpose**: Verify secure access control across all portals

**Coverage**:
- Admin login/logout functionality
- School user authentication
- Partner portal access
- JWT token validation
- Role-based access control
- Session management

**Key Tests**:
```bash
npm run test:comprehensive  # Includes auth tests
```

### 💳 Billing System Tests

**Purpose**: Ensure accurate billing, invoicing, and payment processing

**Coverage**:
- Subscription creation and management
- Invoice generation and PDF creation
- Payment order creation
- Payment verification
- Discount application
- Advance payment handling
- Billing cycle management

**Key Tests**:
```bash
npm run test:billing        # School billing workflow
npm run test:payment        # Payment gateway integration
```

### 🤝 Partner Management Tests

**Purpose**: Validate partner system functionality and commission calculations

**Coverage**:
- Partner registration and authentication
- Referral code generation and validation
- Commission calculation accuracy
- Earnings tracking
- Withdrawal request processing
- Partner analytics

### 🏫 School Portal Tests

**Purpose**: Test school-facing functionality and user experience

**Coverage**:
- School dashboard access
- Billing information display
- Invoice and receipt downloads
- Payment initiation
- Subscription management
- Support ticket creation

### 👨‍💼 Admin Dashboard Tests

**Purpose**: Verify administrative functionality and system oversight

**Coverage**:
- Admin dashboard data accuracy
- Financial reporting
- User management
- System configuration
- Bulk operations
- Audit trail functionality

### 🔗 Integration Tests

**Purpose**: Test complete end-to-end workflows

**Coverage**:
- Complete subscription lifecycle
- Payment processing workflows
- Email notification systems
- PDF generation and delivery
- Database consistency
- API integration points

## Running Tests

### Prerequisites

1. **Environment Setup**:
   ```bash
   # Copy environment template
   cp .env.example .env.local
   
   # Configure test database
   export TEST_DATABASE_URL="your_test_database_url"
   ```

2. **Test User Setup**:
   ```bash
   # Create test admin user
   npm run seed:admin
   
   # Create test referral codes
   node tests/create-test-referral-codes.js
   ```

### Available Test Commands

```bash
# Run all tests
npm test

# Run tests for specific environment
npm run test:dev          # Development environment
npm run test:staging      # Staging environment

# Run specific test categories
npm run test:comprehensive # Complete system test
npm run test:billing       # Billing workflow tests
npm run test:payment       # Payment integration tests
npm run test:integration   # Integration test suite

# Run single test file
npm run test:single tests/comprehensive-system-test.js

# Type checking
npm run typecheck
```

### Test Execution Modes

#### Development Mode
```bash
NODE_ENV=development npm test
```
- Uses local database
- Includes write operations
- Full test coverage
- Mock payment gateway

#### Staging Mode
```bash
NODE_ENV=staging npm test
```
- Uses staging environment
- Real API endpoints
- Limited write operations
- Real payment gateway (test mode)

#### Production Mode
```bash
NODE_ENV=production npm test
```
- Read-only tests only
- No data modification
- Health checks and monitoring
- Performance validation

## Test Configuration

### Environment Variables

```bash
# Test Environment
TEST_BASE_URL="http://localhost:3000"
NODE_ENV="development"

# Test Credentials
TEST_ADMIN_EMAIL="<EMAIL>"
TEST_ADMIN_PASSWORD="Admin@123456"
TEST_SCHOOL_EMAIL="<EMAIL>"
TEST_SCHOOL_PASSWORD="password123"
TEST_PARTNER_EMAIL="<EMAIL>"
TEST_PARTNER_PASSWORD="partner123"

# Database
TEST_DATABASE_URL="postgresql://..."

# Payment Gateway (for testing)
RAZORPAY_KEY_ID="rzp_test_..."
RAZORPAY_KEY_SECRET="..."

# Email Service (for testing)
RESEND_API_KEY="re_..."
```

### Test Data Configuration

The test suite uses configurable test data templates:

```javascript
// Subscription test data
const subscriptionData = {
  studentCount: 100,
  pricePerStudent: 50,
  billingCycle: 'monthly',
  gracePeriodDays: 3
};

// School test data
const schoolData = {
  schoolName: 'Test School',
  contactPerson: 'Test Admin',
  actualStudentCount: 150,
  averageMonthlyFee: 2500
};
```

## Test Data Setup

### 1. Admin User Creation

```bash
# Create admin user with all permissions
npm run seed:admin

# Create admin user with specific permissions
npm run seed:admin -- --permissions="billing:read,billing:write"
```

### 2. Test School Setup

```bash
# Run school setup script
node tests/setup-test-school.js
```

### 3. Partner System Setup

```bash
# Create test partners with referral codes
node tests/create-test-referral-codes.js

# Verify existing referral codes
node tests/check-existing-referral-codes.js
```

### 4. Test Database Reset

```bash
# Reset test database to clean state
node scripts/reset-test-database.js
```

## Test Reports

### Report Generation

Tests automatically generate comprehensive reports:

```bash
# Reports are saved to ./test-results/
test-report-2024-01-15T10-30-00.json
test-coverage-2024-01-15T10-30-00.html
```

### Report Contents

- **Summary**: Pass/fail rates, execution time, success rates
- **Category Breakdown**: Results by test category
- **Performance Metrics**: Response times, throughput
- **Error Details**: Failed test information and stack traces
- **Environment Info**: Node version, platform, configuration

### Viewing Reports

```bash
# Open HTML report in browser
open test-results/test-report-latest.html

# View JSON report
cat test-results/test-report-latest.json | jq
```

## Continuous Integration

### GitHub Actions Integration

```yaml
# .github/workflows/test.yml
name: Comprehensive Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install
      - run: npm run typecheck
      - run: npm test
      - uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: test-results/
```

### Test Thresholds

- **Critical Tests**: Must have 95%+ success rate
- **Normal Tests**: Must have 85%+ success rate
- **Performance**: Response times < 2 seconds
- **Coverage**: Minimum 80% code coverage

## Troubleshooting

### Common Issues

#### 1. Authentication Failures
```bash
# Check admin user exists
npm run seed:admin

# Verify credentials in .env.local
echo $TEST_ADMIN_EMAIL
```

#### 2. Database Connection Issues
```bash
# Test database connection
node -e "require('./src/db').db.execute('SELECT 1')"

# Check environment variables
echo $DATABASE_URL
```

#### 3. Payment Gateway Issues
```bash
# Verify Razorpay configuration
node tests/test-payment-gateway-integration.js
```

#### 4. Test Data Issues
```bash
# Reset test data
node scripts/reset-test-database.js
npm run seed:admin
```

### Debug Mode

Enable verbose logging:

```bash
DEBUG=true npm test
VERBOSE=true npm run test:comprehensive
```

### Test Isolation

Run tests in isolation to debug specific issues:

```bash
# Run single test file
npm run test:single tests/comprehensive-system-test.js

# Run specific test category
npm run test:billing
```

## Best Practices

1. **Test Data Isolation**: Each test should use unique test data
2. **Cleanup**: Tests should clean up after themselves
3. **Idempotency**: Tests should be repeatable and produce consistent results
4. **Error Handling**: Tests should handle and report errors gracefully
5. **Performance**: Tests should complete within reasonable time limits
6. **Documentation**: Test failures should provide clear error messages

## Support

For testing issues or questions:

1. Check this documentation
2. Review test logs in `./test-results/`
3. Run tests in debug mode
4. Contact the development team

---

**Last Updated**: January 2024
**Version**: 1.0.0
