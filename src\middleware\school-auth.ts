import { Context } from 'hono'
import jwt from 'jsonwebtoken'
import { db } from '@/src/db'
import { clientUsers, clients } from '@/src/db/schema'
import { eq, and } from 'drizzle-orm'
import { auditLogger } from '@/src/services/auditLogger'
import { securityMonitor } from '@/src/services/securityMonitor'
import { rateLimiter } from '@/src/services/rateLimiter'
import crypto from 'crypto'

// School user interface for context
export interface SchoolUser {
  id: string
  email: string
  name: string
  role: string
  clientId: string
  schoolName: string
  permissions: string[]
}

// Helper function to generate JWT token for school users with enhanced security
export function generateSchoolToken(
  userId: string, 
  email: string, 
  role: string, 
  clientId: string,
  schoolName: string,
  permissions: string[] = []
): string {
  const jti = crypto.randomUUID() // JWT ID for token tracking
  const iat = Math.floor(Date.now() / 1000) // Issued at

  return jwt.sign(
    {
      userId,
      email,
      role,
      clientId,
      schoolName,
      permissions,
      type: 'school',
      jti, // JWT ID for revocation
      iat, // Issued at timestamp
      iss: 'schopio-school', // Issuer
      aud: 'schopio-school-portal' // Audience
    },
    process.env.JWT_SECRET || 'fallback-secret-key',
    {
      expiresIn: '12h', // 12 hours for school users
      algorithm: 'HS256'
    }
  )
}

// Enhanced school authentication middleware with security monitoring
export const schoolAuthMiddleware = async (c: Context, next: () => Promise<void>) => {
  const startTime = Date.now()
  const requestId = crypto.randomUUID()
  const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
  const userAgent = c.req.header('user-agent') || 'unknown'

  // Add request ID to context for tracking
  c.set('requestId', requestId)

  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // Log failed authentication attempt
      await auditLogger.logAuth('failed_login', {
        email: 'unknown',
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Missing authorization header'
      })

      await securityMonitor.logSecurityEvent({
        type: 'unauthorized_access',
        ipAddress,
        userAgent,
        details: {
          endpoint: c.req.path,
          method: c.req.method,
          reason: 'Missing authorization header',
          requestId,
          userType: 'school'
        },
        severity: 'medium'
      })

      return c.json({
        error: 'Authorization token required',
        requestId
      }, 401)
    }

    const token = authHeader.substring(7)

    // Enhanced JWT verification
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key', {
      algorithms: ['HS256'],
      issuer: 'schopio-school',
      audience: 'schopio-school-portal'
    }) as any

    if (!decoded.userId || !decoded.email || !decoded.clientId || decoded.type !== 'school') {
      await auditLogger.logAuth('failed_login', {
        email: decoded.email || 'unknown',
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Invalid token format'
      })

      return c.json({
        error: 'Invalid school token',
        requestId
      }, 401)
    }

    // Fetch school user from database with client information
    const [userWithClient] = await db
      .select({
        user: clientUsers,
        client: clients
      })
      .from(clientUsers)
      .leftJoin(clients, eq(clientUsers.clientId, clients.id))
      .where(and(
        eq(clientUsers.id, decoded.userId),
        eq(clientUsers.clientId, decoded.clientId)
      ))
      .limit(1)

    if (!userWithClient || !userWithClient.user || !userWithClient.client) {
      await auditLogger.logAuth('failed_login', {
        email: decoded.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'School user or client not found'
      })

      return c.json({
        error: 'School user not found',
        requestId
      }, 401)
    }

    const { user, client } = userWithClient

    // Check if user is active
    if (!user.isActive) {
      await auditLogger.logAuth('failed_login', {
        userId: user.id,
        email: user.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'School user account deactivated'
      })

      await securityMonitor.logSecurityEvent({
        type: 'unauthorized_access',
        userId: user.id,
        ipAddress,
        userAgent,
        details: {
          reason: 'Deactivated school account access attempt',
          userEmail: user.email,
          schoolName: client.schoolName,
          requestId
        },
        severity: 'high'
      })

      return c.json({
        error: 'School account is deactivated',
        requestId
      }, 403)
    }

    // Check if email is verified
    if (!user.emailVerified) {
      return c.json({
        error: 'Email not verified. Please verify your email to access the school portal.',
        requestId
      }, 403)
    }

    // Check if client is active
    if (client.status !== 'active') {
      await auditLogger.logAuth('failed_login', {
        userId: user.id,
        email: user.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: `School client status: ${client.status}`
      })

      return c.json({
        error: `School access restricted. Status: ${client.status}`,
        requestId
      }, 403)
    }

    // Create school user object for context
    const schoolUser: SchoolUser = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role || 'admin', // Default to admin if role is null
      clientId: user.clientId!,
      schoolName: client.schoolName,
      permissions: [] // School users don't have permissions field in DB yet
    }

    // Add school user to context
    c.set('schoolUser', schoolUser)
    c.set('client', client)

    // Log successful authentication
    await auditLogger.logAuth('login', {
      userId: user.id,
      email: user.email,
      ipAddress,
      userAgent,
      success: true
    })

    // Log data access for audit trail
    await auditLogger.logDataAccess('school_api_access', {
      userId: user.id,
      resource: c.req.path,
      ipAddress,
      userAgent,
      success: true
    })

    // Update last login timestamp
    await db
      .update(clientUsers)
      .set({ lastLogin: new Date() })
      .where(eq(clientUsers.id, user.id))

    await next()

  } catch (error) {
    let errorMessage = 'Authentication failed'
    let statusCode = 500

    if (error instanceof jwt.JsonWebTokenError) {
      errorMessage = 'Invalid token'
      statusCode = 401
    } else if (error instanceof jwt.TokenExpiredError) {
      errorMessage = 'Token expired'
      statusCode = 401
    } else if (error instanceof jwt.NotBeforeError) {
      errorMessage = 'Token not active'
      statusCode = 401
    }

    // Log authentication error
    await auditLogger.logAuth('failed_login', {
      email: 'unknown',
      ipAddress,
      userAgent,
      success: false,
      errorMessage
    })

    await securityMonitor.logSecurityEvent({
      type: 'unauthorized_access',
      ipAddress,
      userAgent,
      details: {
        error: errorMessage,
        endpoint: c.req.path,
        method: c.req.method,
        requestId,
        userType: 'school'
      },
      severity: 'medium'
    })

    console.error('School auth middleware error:', error)
    return c.json({
      error: errorMessage,
      requestId
    }, statusCode as any)
  }
}

// Role-based permission middleware for school users
export const requireSchoolRole = (allowedRoles: string[]) => {
  return async (c: Context, next: () => Promise<void>) => {
    const schoolUser = c.get('schoolUser') as SchoolUser

    if (!schoolUser) {
      return c.json({ error: 'School authentication required' }, 401)
    }

    // School admin and admin roles have access to most functions
    if (schoolUser.role === 'school_admin' || schoolUser.role === 'admin') {
      await next()
      return
    }

    if (!allowedRoles.includes(schoolUser.role)) {
      return c.json({
        error: 'Insufficient permissions',
        required: allowedRoles,
        current: schoolUser.role
      }, 403)
    }

    await next()
  }
}

// Permission-based middleware for school users
export const requireSchoolPermission = (permission: string) => {
  return async (c: Context, next: () => Promise<void>) => {
    const schoolUser = c.get('schoolUser') as SchoolUser
    
    if (!schoolUser) {
      return c.json({ error: 'School authentication required' }, 401)
    }

    // School admin has all permissions
    if (schoolUser.permissions?.includes('*') || schoolUser.role === 'school_admin') {
      await next()
      return
    }

    // Check specific permission
    if (!schoolUser.permissions?.includes(permission)) {
      return c.json({ 
        error: 'Insufficient permissions',
        required: permission,
        current: schoolUser.permissions
      }, 403)
    }

    await next()
  }
}

// Multi-tenant data isolation middleware
export const ensureSchoolDataAccess = async (c: Context, next: () => Promise<void>) => {
  const schoolUser = c.get('schoolUser') as SchoolUser
  
  if (!schoolUser) {
    return c.json({ error: 'School authentication required' }, 401)
  }

  // Add client ID to context for data filtering
  c.set('clientId', schoolUser.clientId)
  
  await next()
}

// Enhanced rate limiting middleware for school routes
export const schoolRateLimit = rateLimiter.createMiddleware('school:api', {
  keyGenerator: (c: Context) => {
    const ip = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const schoolUser = c.get('schoolUser') as SchoolUser | undefined

    // Use school user ID if authenticated, otherwise use IP
    return schoolUser ? `school:${schoolUser.id}` : `ip:${ip}`
  }
})

// Helper function to get current school user from context
export const getCurrentSchoolUser = (c: Context): SchoolUser | null => {
  return c.get('schoolUser') || null
}

// Helper function to require school authentication
export const requireSchoolAuth = (c: Context): SchoolUser => {
  const schoolUser = getCurrentSchoolUser(c)
  if (!schoolUser) {
    throw new Error('School authentication required')
  }
  return schoolUser
}

// Helper function to check if school user has specific permission
export const hasSchoolPermission = (schoolUser: SchoolUser, permission: string): boolean => {
  return schoolUser.role === 'school_admin' || 
         schoolUser.permissions?.includes('*') || 
         schoolUser.permissions?.includes(permission)
}

// Security headers middleware for school routes
export const schoolSecurityHeaders = async (c: Context, next: () => Promise<void>) => {
  // Security headers for school portal
  c.header('X-Content-Type-Options', 'nosniff')
  c.header('X-Frame-Options', 'SAMEORIGIN') // Allow framing within same origin for school portal
  c.header('X-XSS-Protection', '1; mode=block')
  c.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  c.header('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  await next()
}
