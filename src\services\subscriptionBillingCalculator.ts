import { db } from '@/src/db'
import { billingSubscriptions, billingPayments, clients } from '@/src/db/schema'
import { eq, and, desc, sql } from 'drizzle-orm'

export interface SubscriptionBillingStatus {
  id: string
  clientId: string
  currentMonthAmount: number
  outstandingAmount: number
  isPaid: boolean
  isOverdue: boolean
  isInGracePeriod: boolean
  daysOverdue: number
  penaltyAmount: number
  nextBillingDate: string
  gracePeriodEnd: string | null
  paymentStatus: 'current' | 'due' | 'grace_period' | 'overdue_with_penalty'
  canMakePayment: boolean
}

export class SubscriptionBillingCalculator {
  
  /**
   * Calculate outstanding amount and billing status for a subscription (Manual Billing)
   */
  static async calculateBillingStatus(clientId: string): Promise<SubscriptionBillingStatus | null> {
    try {
      console.log(`🔍 [Billing Calculator] Calculating billing status for client: ${clientId}`)

      // Get active subscription for the client with manual billing fields
      const [subscription] = await db
        .select({
          id: billingSubscriptions.id,
          clientId: billingSubscriptions.clientId,
          monthlyAmount: billingSubscriptions.monthlyAmount,
          studentCount: billingSubscriptions.studentCount,
          status: billingSubscriptions.status,
          dueDate: billingSubscriptions.dueDate,
          nextBillingDate: billingSubscriptions.nextBillingDate,
          paymentStatus: billingSubscriptions.paymentStatus,
          currentPenaltyAmount: billingSubscriptions.currentPenaltyAmount,
          gracePeriodDays: billingSubscriptions.gracePeriodDays,
          penaltyRate: billingSubscriptions.penaltyRate,
          lastPaymentDate: billingSubscriptions.lastPaymentDate
        })
        .from(billingSubscriptions)
        .where(and(
          eq(billingSubscriptions.clientId, clientId),
          eq(billingSubscriptions.status, 'active')
        ))
        .orderBy(desc(billingSubscriptions.createdAt))
        .limit(1)

      if (!subscription) {
        console.log(`❌ [Billing Calculator] No active subscription found for client: ${clientId}`)
        return null
      }

      console.log(`✅ [Billing Calculator] Found subscription:`, {
        id: subscription.id,
        monthlyAmount: subscription.monthlyAmount,
        dueDate: subscription.dueDate,
        nextBillingDate: subscription.nextBillingDate,
        paymentStatus: subscription.paymentStatus,
        currentPenaltyAmount: subscription.currentPenaltyAmount,
        studentCount: subscription.studentCount
      })

      const currentDate = new Date()
      const dueDate = subscription.dueDate ? new Date(subscription.dueDate) : new Date(subscription.nextBillingDate)
      const nextBillingDate = new Date(subscription.nextBillingDate)
      const currentMonthAmount = parseFloat(subscription.monthlyAmount)
      const currentPenaltyAmount = parseFloat(subscription.currentPenaltyAmount || '0')

      console.log(`📅 [Billing Calculator] Date comparison:`, {
        currentDate: currentDate.toISOString().split('T')[0],
        dueDate: subscription.dueDate,
        nextBillingDate: subscription.nextBillingDate,
        currentMonthAmount,
        paymentStatus: subscription.paymentStatus,
        currentPenaltyAmount,
        isOverdue: currentDate > dueDate
      })

      // Use the stored payment status from manual billing system
      const isPaid = subscription.paymentStatus === 'paid'
      const isOverdue = subscription.paymentStatus === 'overdue' ||
                       (subscription.paymentStatus === 'pending' && currentDate > dueDate)

      console.log(`💳 [Billing Calculator] Payment status:`, {
        storedPaymentStatus: subscription.paymentStatus,
        lastPaymentDate: subscription.lastPaymentDate,
        isPaid,
        isOverdue
      })
      
      // Calculate grace period
      const gracePeriodDays = subscription.gracePeriodDays || 3
      const gracePeriodEnd = new Date(dueDate)
      gracePeriodEnd.setDate(gracePeriodEnd.getDate() + gracePeriodDays)

      const isInGracePeriod = subscription.paymentStatus === 'grace_period' ||
                             (isOverdue && currentDate <= gracePeriodEnd)
      const daysOverdue = isOverdue ? Math.floor((currentDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0

      // Calculate penalty amount for manual billing system
      let penaltyAmount = currentPenaltyAmount

      // If payment is overdue and past grace period, calculate daily penalty
      if (isOverdue && !isInGracePeriod && subscription.paymentStatus !== 'paid') {
        const penaltyRate = parseFloat(subscription.penaltyRate?.toString() || '0.02') // 2% daily default
        const daysAfterGrace = Math.max(0, daysOverdue - gracePeriodDays)

        if (daysAfterGrace > 0) {
          // Calculate compound daily penalty: Principal × (1 + rate)^days - Principal
          const compoundPenalty = currentMonthAmount * (Math.pow(1 + penaltyRate, daysAfterGrace) - 1)
          penaltyAmount = Math.max(currentPenaltyAmount, compoundPenalty)

          console.log(`💰 [Penalty Calculator] Calculated penalty:`, {
            daysAfterGrace,
            penaltyRate,
            currentMonthAmount,
            calculatedPenalty: compoundPenalty,
            storedPenalty: currentPenaltyAmount,
            finalPenalty: penaltyAmount
          })
        }
      }

      // Enhanced manual payment logic for advance payment model
      let outstandingAmount = 0
      let canMakePayment = false

      if (!isPaid) {
        if (currentDate >= dueDate) {
          // Payment is due or overdue - include penalties
          outstandingAmount = currentMonthAmount + penaltyAmount
          canMakePayment = true
        } else {
          // Advance payment allowed - schools can pay early for next month
          outstandingAmount = currentMonthAmount
          canMakePayment = true
        }
      } else {
        // Already paid for current period, allow advance payment for next month
        const nextMonthStart = new Date(nextBillingDate)
        const nextMonthEnd = new Date(nextMonthStart)
        nextMonthEnd.setMonth(nextMonthEnd.getMonth() + 1)

        // Allow advance payment 7 days before next billing cycle
        const advancePaymentDate = new Date(nextBillingDate)
        advancePaymentDate.setDate(advancePaymentDate.getDate() - 7)

        if (currentDate >= advancePaymentDate) {
          outstandingAmount = currentMonthAmount
          canMakePayment = true
        }
      }

      console.log(`💰 [Billing Calculator] Outstanding calculation:`, {
        isPaid,
        currentDateGteDueDate: currentDate >= dueDate,
        shouldCharge: !isPaid,
        outstandingAmount,
        penaltyAmount,
        canMakePayment,
        storedPaymentStatus: subscription.paymentStatus
      })

      // Enhanced payment status logic for manual payment system
      let paymentStatus: SubscriptionBillingStatus['paymentStatus']

      if (subscription.paymentStatus === 'paid') {
        // Check if advance payment window is open
        const advancePaymentDate = new Date(nextBillingDate)
        advancePaymentDate.setDate(advancePaymentDate.getDate() - 7)

        if (currentDate >= advancePaymentDate) {
          paymentStatus = 'due' // Allow advance payment for next month
        } else {
          paymentStatus = 'current' // Still in current paid period
        }
      } else if (subscription.paymentStatus === 'grace_period') {
        paymentStatus = 'grace_period'
      } else if (subscription.paymentStatus === 'overdue') {
        paymentStatus = penaltyAmount > 0 ? 'overdue_with_penalty' : 'grace_period'
      } else if (currentDate < dueDate) {
        // Due date hasn't arrived yet - subscription is current
        paymentStatus = 'current'
      } else if (isInGracePeriod) {
        paymentStatus = 'grace_period'
      } else {
        paymentStatus = 'overdue_with_penalty'
      }

      console.log(`🎯 [Billing Calculator] Final result:`, {
        paymentStatus,
        outstandingAmount,
        canMakePayment
      })

      return {
        id: subscription.id,
        clientId: clientId,
        currentMonthAmount,
        outstandingAmount,
        isPaid,
        isOverdue,
        isInGracePeriod,
        daysOverdue,
        penaltyAmount,
        nextBillingDate: subscription.nextBillingDate,
        gracePeriodEnd: isOverdue ? gracePeriodEnd.toISOString() : null,
        paymentStatus,
        canMakePayment
      }

    } catch (error) {
      console.error('Error calculating subscription billing status:', error)
      return null
    }
  }

  /**
   * Get billing summary for school dashboard
   */
  static async getBillingSummary(clientId: string) {
    try {
      console.log(`📊 [Billing Summary] Getting billing summary for client: ${clientId}`)
      const billingStatus = await this.calculateBillingStatus(clientId)

      if (!billingStatus) {
        console.log(`❌ [Billing Summary] No billing status found - returning no_subscription`)
        return {
          currentMonthAmount: 0,
          outstandingAmount: 0,
          paymentStatus: 'no_subscription' as const,
          nextBillingDate: null,
          gracePeriodEnd: null
        }
      }

      console.log(`✅ [Billing Summary] Returning billing summary:`, {
        currentMonthAmount: billingStatus.currentMonthAmount,
        outstandingAmount: billingStatus.outstandingAmount,
        paymentStatus: billingStatus.paymentStatus,
        canMakePayment: billingStatus.canMakePayment
      })

      return {
        currentMonthAmount: billingStatus.currentMonthAmount,
        outstandingAmount: billingStatus.outstandingAmount,
        paymentStatus: billingStatus.paymentStatus,
        nextBillingDate: billingStatus.nextBillingDate,
        gracePeriodEnd: billingStatus.gracePeriodEnd,
        daysOverdue: billingStatus.daysOverdue,
        isInGracePeriod: billingStatus.isInGracePeriod,
        canMakePayment: billingStatus.canMakePayment
      }

    } catch (error) {
      console.error('Error getting billing summary:', error)
      throw error
    }
  }

  /**
   * Format outstanding amount for display
   */
  static formatOutstandingDisplay(billingStatus: SubscriptionBillingStatus): {
    monthlyAmount: number
    displayText: string
    urgency: 'none' | 'low' | 'medium' | 'high'
    actionRequired: boolean
  } {
    if (billingStatus.isPaid) {
      return {
        monthlyAmount: 0,
        displayText: 'Current - No outstanding amount',
        urgency: 'none',
        actionRequired: false
      }
    }

    if (billingStatus.paymentStatus === 'due') {
      return {
        monthlyAmount: billingStatus.outstandingAmount,
        displayText: `Due: ₹${billingStatus.currentMonthAmount.toLocaleString()}`,
        urgency: 'low',
        actionRequired: true
      }
    }

    if (billingStatus.paymentStatus === 'grace_period') {
      return {
        monthlyAmount: billingStatus.outstandingAmount,
        displayText: `Grace Period: ₹${billingStatus.currentMonthAmount.toLocaleString()} (${billingStatus.daysOverdue} days overdue)`,
        urgency: 'medium',
        actionRequired: true
      }
    }

    if (billingStatus.paymentStatus === 'overdue_with_penalty') {
      return {
        monthlyAmount: billingStatus.outstandingAmount,
        displayText: `Overdue: ₹${billingStatus.currentMonthAmount.toLocaleString()} + ₹${billingStatus.penaltyAmount.toLocaleString()} penalty`,
        urgency: 'high',
        actionRequired: true
      }
    }

    return {
      monthlyAmount: billingStatus.outstandingAmount,
      displayText: `Outstanding: ₹${billingStatus.outstandingAmount.toLocaleString()}`,
      urgency: 'medium',
      actionRequired: true
    }
  }
}
