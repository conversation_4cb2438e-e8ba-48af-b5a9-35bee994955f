import { db } from '@/src/db'
import {
  billingSubscriptions,
  billingInvoices,
  billingTransactions,
  clients
} from '@/src/db/schema'
import { eq, and, desc, gte, lte, sql } from 'drizzle-orm'

export interface PaymentStatus {
  hasCurrentMonthPayment: boolean
  currentPeriodStart: string
  currentPeriodEnd: string
  nextPeriodStart: string
  earliestDiscountStartDate: string
  paymentScenario: 'payment_pending' | 'payment_made'
  canApplyToCurrentPeriod: boolean
  currentMonthInvoiceId?: string
  currentMonthInvoiceStatus?: string
}

export interface DiscountStartDateValidation {
  isValid: boolean
  error?: string
  recommendedStartDate?: string
  paymentScenario: 'payment_pending' | 'payment_made'
}

class PaymentBasedDiscountService {
  /**
   * Check payment status for current subscription period
   */
  async getPaymentStatus(subscriptionId: string): Promise<PaymentStatus> {
    try {
      // Get subscription details
      const [subscription] = await db.select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        currentPeriodStart: billingSubscriptions.currentPeriodStart,
        currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
        nextBillingDate: billingSubscriptions.nextBillingDate,
        status: billingSubscriptions.status
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      // Calculate current period dates
      const today = new Date()
      const currentPeriodStart = subscription.currentPeriodStart || 
        new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0]
      const currentPeriodEnd = subscription.currentPeriodEnd || 
        new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split('T')[0]
      
      // Calculate next period start
      const nextPeriodStart = new Date(currentPeriodEnd)
      nextPeriodStart.setDate(nextPeriodStart.getDate() + 1)
      const nextPeriodStartStr = nextPeriodStart.toISOString().split('T')[0]

      // Check for current month invoice and payment
      const [currentInvoice] = await db.select({
        id: billingInvoices.id,
        status: billingInvoices.status,
        totalAmount: billingInvoices.totalAmount,
        issuedDate: billingInvoices.issuedDate,
        dueDate: billingInvoices.dueDate
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.subscriptionId, subscriptionId),
        gte(billingInvoices.periodStart, currentPeriodStart),
        lte(billingInvoices.periodEnd, currentPeriodEnd)
      ))
      .orderBy(desc(billingInvoices.issuedDate))
      .limit(1)

      // Check if current month payment has been made
      let hasCurrentMonthPayment = false
      if (currentInvoice) {
        // Check for successful payment transaction
        const [payment] = await db.select({
          id: billingTransactions.id,
          status: billingTransactions.status,
          amount: billingTransactions.amount
        })
        .from(billingTransactions)
        .where(and(
          eq(billingTransactions.invoiceId, currentInvoice.id),
          eq(billingTransactions.status, 'completed')
        ))
        .limit(1)

        hasCurrentMonthPayment = !!payment || currentInvoice.status === 'paid'
      }

      // Determine payment scenario and earliest discount start date
      const paymentScenario: 'payment_pending' | 'payment_made' = hasCurrentMonthPayment ? 'payment_made' : 'payment_pending'
      const canApplyToCurrentPeriod = !hasCurrentMonthPayment
      const earliestDiscountStartDate = canApplyToCurrentPeriod ? currentPeriodStart : nextPeriodStartStr

      return {
        hasCurrentMonthPayment,
        currentPeriodStart,
        currentPeriodEnd,
        nextPeriodStart: nextPeriodStartStr,
        earliestDiscountStartDate,
        paymentScenario,
        canApplyToCurrentPeriod,
        currentMonthInvoiceId: currentInvoice?.id,
        currentMonthInvoiceStatus: currentInvoice?.status
      }

    } catch (error) {
      console.error('Error checking payment status:', error)
      throw error
    }
  }

  /**
   * Validate discount start date based on payment status
   */
  async validateDiscountStartDate(
    subscriptionId: string, 
    proposedStartDate: string
  ): Promise<DiscountStartDateValidation> {
    try {
      const paymentStatus = await this.getPaymentStatus(subscriptionId)
      const proposedDate = new Date(proposedStartDate)
      const earliestAllowedDate = new Date(paymentStatus.earliestDiscountStartDate)

      // Check if proposed date is too early
      if (proposedDate < earliestAllowedDate) {
        const scenario = paymentStatus.paymentScenario
        const errorMessage = scenario === 'payment_made' 
          ? `Current month payment has been made. Discount can only start from next billing period (${paymentStatus.nextPeriodStart})`
          : `Invalid start date. Earliest allowed date is ${paymentStatus.earliestDiscountStartDate}`

        return {
          isValid: false,
          error: errorMessage,
          recommendedStartDate: paymentStatus.earliestDiscountStartDate,
          paymentScenario: scenario
        }
      }

      return {
        isValid: true,
        paymentScenario: paymentStatus.paymentScenario
      }

    } catch (error) {
      console.error('Error validating discount start date:', error)
      return {
        isValid: false,
        error: 'Unable to validate start date due to system error',
        paymentScenario: 'payment_pending'
      }
    }
  }

  /**
   * Get discount start date options for admin UI
   */
  async getDiscountStartDateOptions(subscriptionId: string): Promise<{
    earliestDate: string
    currentPeriodOption?: {
      date: string
      label: string
      description: string
    }
    nextPeriodOption: {
      date: string
      label: string
      description: string
    }
    paymentScenario: 'payment_pending' | 'payment_made'
  }> {
    try {
      const paymentStatus = await this.getPaymentStatus(subscriptionId)

      const result = {
        earliestDate: paymentStatus.earliestDiscountStartDate,
        nextPeriodOption: {
          date: paymentStatus.nextPeriodStart,
          label: 'Next Billing Period',
          description: `Discount starts from next billing cycle (${new Date(paymentStatus.nextPeriodStart).toLocaleDateString()})`
        },
        paymentScenario: paymentStatus.paymentScenario
      }

      // Add current period option if payment hasn't been made
      if (paymentStatus.canApplyToCurrentPeriod) {
        return {
          ...result,
          currentPeriodOption: {
            date: paymentStatus.currentPeriodStart,
            label: 'Current Billing Period',
            description: `Apply discount immediately to current period (${new Date(paymentStatus.currentPeriodStart).toLocaleDateString()})`
          }
        }
      }

      return result

    } catch (error) {
      console.error('Error getting discount start date options:', error)
      throw error
    }
  }

  /**
   * Apply discount with payment-based logic
   */
  async applyDiscountWithPaymentLogic(
    subscriptionId: string,
    discountData: {
      discountPercentage: number
      durationMonths: number
      startDate: string
      reason: string
      adminId?: string
      adminName?: string
    }
  ): Promise<{
    success: boolean
    message: string
    appliedToCurrentInvoice?: boolean
    currentInvoiceId?: string
  }> {
    try {
      // Validate start date
      const validation = await this.validateDiscountStartDate(subscriptionId, discountData.startDate)
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.error || 'Invalid start date'
        }
      }

      const paymentStatus = await this.getPaymentStatus(subscriptionId)
      
      // Apply discount to subscription
      const { discountManagementService } = await import('./discountManagementService')
      await discountManagementService.applyDiscount(subscriptionId, {
        ...discountData,
        startDate: new Date(discountData.startDate)
      })

      // If applying to current period and there's an unpaid invoice, update it
      let appliedToCurrentInvoice = false
      let currentInvoiceId: string | undefined

      if (paymentStatus.canApplyToCurrentPeriod && 
          discountData.startDate === paymentStatus.currentPeriodStart &&
          paymentStatus.currentMonthInvoiceId &&
          paymentStatus.currentMonthInvoiceStatus !== 'paid') {
        
        await this.updateCurrentInvoiceWithDiscount(
          paymentStatus.currentMonthInvoiceId,
          discountData.discountPercentage
        )
        
        appliedToCurrentInvoice = true
        currentInvoiceId = paymentStatus.currentMonthInvoiceId
      }

      const message = appliedToCurrentInvoice 
        ? `Discount applied successfully. Current month invoice updated with discounted amount.`
        : `Discount applied successfully. Will take effect from ${new Date(discountData.startDate).toLocaleDateString()}.`

      return {
        success: true,
        message,
        appliedToCurrentInvoice,
        currentInvoiceId
      }

    } catch (error) {
      console.error('Error applying discount with payment logic:', error)
      return {
        success: false,
        message: 'Failed to apply discount due to system error'
      }
    }
  }

  /**
   * Update current month invoice with discount
   */
  private async updateCurrentInvoiceWithDiscount(
    invoiceId: string,
    discountPercentage: number
  ): Promise<void> {
    try {
      // Get current invoice details
      const [invoice] = await db.select({
        id: billingInvoices.id,
        subtotal: billingInvoices.subtotal,
        taxAmount: billingInvoices.taxAmount,
        discountAmount: billingInvoices.discountAmount
      })
      .from(billingInvoices)
      .where(eq(billingInvoices.id, invoiceId))
      .limit(1)

      if (!invoice) {
        throw new Error('Invoice not found')
      }

      // Calculate new amounts
      const originalSubtotal = parseFloat(invoice.subtotal)
      const discountAmount = (originalSubtotal * discountPercentage) / 100
      const newSubtotal = originalSubtotal - discountAmount
      const taxAmount = parseFloat(invoice.taxAmount || '0')
      const newTotal = newSubtotal + taxAmount

      // Update invoice
      await db.update(billingInvoices)
        .set({
          discountAmount: discountAmount.toString(),
          totalAmount: newTotal.toString(),
          notes: `Discount applied: ${discountPercentage}% (₹${discountAmount.toFixed(2)} savings)`,
          updatedAt: new Date()
        })
        .where(eq(billingInvoices.id, invoiceId))

      console.log(`✅ Updated invoice ${invoiceId} with ${discountPercentage}% discount`)

    } catch (error) {
      console.error('Error updating invoice with discount:', error)
      throw error
    }
  }
}

export const paymentBasedDiscountService = new PaymentBasedDiscountService()
export default paymentBasedDiscountService
