import { Hono } from "hono"
import { zValidator } from "@hono/zod-validator"
import { z } from "zod"
import { db } from "@/db"
import { demoBookings, leads } from "@/db/schema"
import { eq, desc, and, gte, lte, sql } from "drizzle-orm"

// Validation schemas
const createDemoBookingSchema = z.object({
  leadId: z.string().uuid("Invalid lead ID"),
  scheduledDate: z.string().datetime("Invalid date format"),
  demoType: z.enum(["online", "onsite"]).default("online"),
  notes: z.string().optional(),
})

const createDemoRequestSchema = z.object({
  contactPerson: z.string().min(2, "Contact person name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits").optional(),
  schoolName: z.string().min(2, "School name is required"),
  estimatedStudents: z.number().min(1, "Student count must be at least 1").max(10000, "Student count too large").optional(),
  scheduledDate: z.string().datetime("Invalid date format"),
  demoType: z.enum(["online", "onsite"]).default("online"),
  notes: z.string().optional(),
})

const updateDemoBookingSchema = z.object({
  status: z.enum(["scheduled", "completed", "cancelled", "rescheduled"]).optional(),
  scheduledDate: z.string().datetime().optional(),
  meetingLink: z.string().url().optional(),
  notes: z.string().optional(),
})

const demoQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1).optional(),
  limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)).optional(),
  status: z.string().optional(),
  demoType: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.enum(["scheduledDate", "createdAt", "status"]).default("scheduledDate"),
  sortOrder: z.enum(["asc", "desc"]).default("asc"),
})

// Create Hono app for demo routes
const app = new Hono()

// Create demo request (creates lead + demo booking in one call)
app.post(
  "/request",
  zValidator("json", createDemoRequestSchema),
  async (c) => {
    try {
      const validatedData = c.req.valid("json")

      // First, create or find existing lead
      let leadId: string

      // Check if lead with this email already exists
      const [existingLead] = await db
        .select()
        .from(leads)
        .where(eq(leads.email, validatedData.email))

      if (existingLead) {
        // Update existing lead
        const [updatedLead] = await db
          .update(leads)
          .set({
            contactPerson: validatedData.contactPerson,
            phone: validatedData.phone,
            schoolName: validatedData.schoolName,
            estimatedStudents: validatedData.estimatedStudents,
            status: "demo_scheduled",
            notes: validatedData.notes,
            updatedAt: new Date()
          })
          .where(eq(leads.id, existingLead.id))
          .returning()

        leadId = updatedLead.id
      } else {
        // Create new lead
        const [newLead] = await db.insert(leads).values({
          email: validatedData.email,
          schoolName: validatedData.schoolName,
          contactPerson: validatedData.contactPerson,
          phone: validatedData.phone,
          estimatedStudents: validatedData.estimatedStudents,
          source: "demo_request",
          status: "demo_scheduled",
          notes: validatedData.notes,
        }).returning()

        leadId = newLead.id
      }

      // Create demo booking
      const [newDemo] = await db.insert(demoBookings).values({
        leadId: leadId,
        scheduledDate: new Date(validatedData.scheduledDate),
        demoType: validatedData.demoType,
        status: "scheduled",
        notes: validatedData.notes,
      }).returning()

      // TODO: Send confirmation email to lead
      // TODO: Send calendar invite
      // TODO: Create meeting link for online demos
      // TODO: Notify sales team

      return c.json({
        success: true,
        data: {
          leadId: leadId,
          demoId: newDemo.id,
          message: "Demo scheduled successfully! You'll receive a confirmation email shortly."
        }
      }, 201)

    } catch (error) {
      console.error("Error creating demo request:", error)
      return c.json({
        success: false,
        error: "Failed to schedule demo. Please try again."
      }, 500)
    }
  }
)

// Create new demo booking
app.post(
  "/",
  zValidator("json", createDemoBookingSchema),
  async (c) => {
    try {
      const validatedData = c.req.valid("json")
      
      // Check if lead exists
      const [existingLead] = await db
        .select()
        .from(leads)
        .where(eq(leads.id, validatedData.leadId))
      
      if (!existingLead) {
        return c.json({
          success: false,
          error: "Lead not found"
        }, 404)
      }
      
      // Create demo booking
      const [newDemo] = await db.insert(demoBookings).values({
        leadId: validatedData.leadId,
        scheduledDate: new Date(validatedData.scheduledDate),
        demoType: validatedData.demoType,
        status: "scheduled",
        notes: validatedData.notes,
      }).returning()

      // Update lead status to demo_scheduled
      await db
        .update(leads)
        .set({ 
          status: "demo_scheduled",
          updatedAt: new Date()
        })
        .where(eq(leads.id, validatedData.leadId))

      // TODO: Send confirmation email to lead
      // TODO: Send calendar invite
      // TODO: Create meeting link for online demos
      // TODO: Notify sales team

      return c.json({
        success: true,
        data: newDemo,
        message: "Demo booking created successfully"
      }, 201)
      
    } catch (error) {
      console.error("Error creating demo booking:", error)
      return c.json({
        success: false,
        error: "Failed to create demo booking"
      }, 500)
    }
  }
)

// Get all demo bookings with filtering and pagination
app.get(
  "/",
  zValidator("query", demoQuerySchema),
  async (c) => {
    try {
      const query = c.req.valid("query")
      const { page = 1, limit = 20, status, demoType, dateFrom, dateTo, sortBy, sortOrder } = query
      
      // Build where conditions
      const conditions = []
      
      if (status) {
        conditions.push(eq(demoBookings.status, status as any))
      }
      
      if (demoType) {
        conditions.push(eq(demoBookings.demoType, demoType as any))
      }
      
      if (dateFrom) {
        conditions.push(gte(demoBookings.scheduledDate, new Date(dateFrom)))
      }
      
      if (dateTo) {
        conditions.push(lte(demoBookings.scheduledDate, new Date(dateTo)))
      }
      
      // Calculate offset
      const offset = (page - 1) * limit
      
      // Get demo bookings with lead information
      const demos = await db
        .select({
          id: demoBookings.id,
          leadId: demoBookings.leadId,
          scheduledDate: demoBookings.scheduledDate,
          demoType: demoBookings.demoType,
          status: demoBookings.status,
          meetingLink: demoBookings.meetingLink,
          notes: demoBookings.notes,
          createdAt: demoBookings.createdAt,
          // Lead information
          leadEmail: leads.email,
          leadSchoolName: leads.schoolName,
          leadContactPerson: leads.contactPerson,
          leadPhone: leads.phone,
        })
        .from(demoBookings)
        .leftJoin(leads, eq(demoBookings.leadId, leads.id))
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(sortOrder === "desc" ? desc(demoBookings[sortBy]) : demoBookings[sortBy])
        .limit(limit)
        .offset(offset)
      
      // Get total count for pagination
      const [{ count }] = await db
        .select({ count: sql<number>`count(*)` })
        .from(demoBookings)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
      
      const totalPages = Math.ceil(count / limit)
      
      return c.json({
        success: true,
        data: demos,
        pagination: {
          page,
          limit,
          total: count,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        }
      })
      
    } catch (error) {
      console.error("Error fetching demo bookings:", error)
      return c.json({
        success: false,
        error: "Failed to fetch demo bookings"
      }, 500)
    }
  }
)

// Get single demo booking by ID
app.get(
  "/:id",
  async (c) => {
    try {
      const demoId = c.req.param("id")
      
      const [demo] = await db
        .select({
          id: demoBookings.id,
          leadId: demoBookings.leadId,
          scheduledDate: demoBookings.scheduledDate,
          demoType: demoBookings.demoType,
          status: demoBookings.status,
          meetingLink: demoBookings.meetingLink,
          notes: demoBookings.notes,
          createdAt: demoBookings.createdAt,
          // Lead information
          leadEmail: leads.email,
          leadSchoolName: leads.schoolName,
          leadContactPerson: leads.contactPerson,
          leadPhone: leads.phone,
          leadEstimatedStudents: leads.estimatedStudents,
        })
        .from(demoBookings)
        .leftJoin(leads, eq(demoBookings.leadId, leads.id))
        .where(eq(demoBookings.id, demoId))
      
      if (!demo) {
        return c.json({
          success: false,
          error: "Demo booking not found"
        }, 404)
      }
      
      return c.json({
        success: true,
        data: demo
      })
      
    } catch (error) {
      console.error("Error fetching demo booking:", error)
      return c.json({
        success: false,
        error: "Failed to fetch demo booking"
      }, 500)
    }
  }
)

// Update demo booking
app.put(
  "/:id",
  zValidator("json", updateDemoBookingSchema),
  async (c) => {
    try {
      const demoId = c.req.param("id")
      const updates = c.req.valid("json")
      
      // Check if demo booking exists
      const [existingDemo] = await db
        .select()
        .from(demoBookings)
        .where(eq(demoBookings.id, demoId))
      
      if (!existingDemo) {
        return c.json({
          success: false,
          error: "Demo booking not found"
        }, 404)
      }
      
      // Prepare update data
      const updateData: any = {}
      
      if (updates.status) updateData.status = updates.status
      if (updates.scheduledDate) updateData.scheduledDate = new Date(updates.scheduledDate)
      if (updates.meetingLink) updateData.meetingLink = updates.meetingLink
      if (updates.notes) updateData.notes = updates.notes
      
      // Update demo booking
      const [updatedDemo] = await db
        .update(demoBookings)
        .set(updateData)
        .where(eq(demoBookings.id, demoId))
        .returning()
      
      // TODO: Send notification emails for status changes
      // TODO: Update calendar invites for rescheduled demos
      
      return c.json({
        success: true,
        data: updatedDemo,
        message: "Demo booking updated successfully"
      })
      
    } catch (error) {
      console.error("Error updating demo booking:", error)
      return c.json({
        success: false,
        error: "Failed to update demo booking"
      }, 500)
    }
  }
)

// Cancel demo booking
app.delete(
  "/:id",
  async (c) => {
    try {
      const demoId = c.req.param("id")
      
      // Check if demo booking exists
      const [existingDemo] = await db
        .select()
        .from(demoBookings)
        .where(eq(demoBookings.id, demoId))
      
      if (!existingDemo) {
        return c.json({
          success: false,
          error: "Demo booking not found"
        }, 404)
      }
      
      // Update status to cancelled instead of deleting
      const [cancelledDemo] = await db
        .update(demoBookings)
        .set({ status: "cancelled" })
        .where(eq(demoBookings.id, demoId))
        .returning()
      
      // TODO: Send cancellation notification
      // TODO: Cancel calendar invite
      
      return c.json({
        success: true,
        data: cancelledDemo,
        message: "Demo booking cancelled successfully"
      })
      
    } catch (error) {
      console.error("Error cancelling demo booking:", error)
      return c.json({
        success: false,
        error: "Failed to cancel demo booking"
      }, 500)
    }
  }
)

export default app
