/**
 * Seed Test School User Script
 * Creates a test school and user for testing the billing system
 */

import { config } from 'dotenv'
import { resolve } from 'path'

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') })

import bcrypt from 'bcryptjs'
import { db } from '../src/db'
import { clients, clientUsers } from '../src/db/schema'
import { eq } from 'drizzle-orm'

// Test school configuration
const TEST_SCHOOL = {
  schoolName: 'Test High School',
  schoolCode: 'TEST001',
  email: '<EMAIL>',
  phone: '+91-9876543210',
  address: '123 Test Street, Test City, Test State 123456',
  contactPerson: 'Test Principal',
  actualStudentCount: 2200,
  estimatedStudentCount: 2200,
  averageMonthlyFee: '75.00'
}

const TEST_USER = {
  email: '<EMAIL>',
  password: 'password123',
  name: 'Test School Admin',
  role: 'admin'
}

async function seedTestSchool() {
  try {
    console.log('🏫 Creating test school...')
    
    // Check if school already exists
    const [existingClient] = await db
      .select()
      .from(clients)
      .where(eq(clients.email, TEST_SCHOOL.email))
      .limit(1)
    
    let clientId: string
    
    if (existingClient) {
      console.log(`⚠️  School already exists: ${TEST_SCHOOL.schoolName}`)
      clientId = existingClient.id
    } else {
      // Create the school/client
      const [newClient] = await db.insert(clients).values({
        schoolName: TEST_SCHOOL.schoolName,
        schoolCode: TEST_SCHOOL.schoolCode,
        email: TEST_SCHOOL.email,
        phone: TEST_SCHOOL.phone,
        address: TEST_SCHOOL.address,
        contactPerson: TEST_SCHOOL.contactPerson,
        actualStudentCount: TEST_SCHOOL.actualStudentCount,
        estimatedStudentCount: TEST_SCHOOL.estimatedStudentCount,
        averageMonthlyFee: TEST_SCHOOL.averageMonthlyFee,
        status: 'active'
      }).returning()
      
      clientId = newClient.id
      console.log(`✅ Created school: ${TEST_SCHOOL.schoolName} (ID: ${clientId})`)
    }
    
    // Check if user already exists
    const [existingUser] = await db
      .select()
      .from(clientUsers)
      .where(eq(clientUsers.email, TEST_USER.email))
      .limit(1)
    
    if (existingUser) {
      console.log(`⚠️  School user already exists: ${TEST_USER.email}`)
      
      // Update user to ensure it's active and email verified
      await db.update(clientUsers)
        .set({
          isActive: true,
          emailVerified: true,
          clientId: clientId
        })
        .where(eq(clientUsers.id, existingUser.id))
      
      console.log(`✅ Updated existing user: ${TEST_USER.email}`)
    } else {
      // Hash password
      console.log('🔐 Hashing password...')
      const passwordHash = await bcrypt.hash(TEST_USER.password, 12)
      
      // Create the school user
      console.log('👤 Creating school user...')
      const [newUser] = await db.insert(clientUsers).values({
        clientId: clientId,
        email: TEST_USER.email,
        passwordHash,
        name: TEST_USER.name,
        role: TEST_USER.role,
        isActive: true,
        emailVerified: true
      }).returning()
      
      console.log(`✅ Created school user: ${TEST_USER.email} (ID: ${newUser.id})`)
    }
    
    console.log('')
    console.log('🎉 Test school seeding completed!')
    console.log('')
    console.log('📋 Test Credentials:')
    console.log(`   School: ${TEST_SCHOOL.schoolName}`)
    console.log(`   Email: ${TEST_USER.email}`)
    console.log(`   Password: ${TEST_USER.password}`)
    console.log(`   Login URL: http://localhost:3000/auth`)
    console.log('')
    
  } catch (error) {
    console.error('❌ Error seeding test school:', error)
    process.exit(1)
  }
}

async function main() {
  console.log('🚀 Schopio Test School Seeding Script')
  console.log('=====================================')
  console.log('')
  
  await seedTestSchool()
  
  process.exit(0)
}

// Run the script
if (require.main === module) {
  main()
}

export { seedTestSchool }
