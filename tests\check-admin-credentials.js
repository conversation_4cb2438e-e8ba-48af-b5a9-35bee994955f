/**
 * Check Admin Credentials Test
 * Verifies what admin accounts exist in the database
 */

const BASE_URL = 'http://localhost:3000'

async function checkAdminCredentials() {
  console.log('🔍 Checking Admin Credentials...\n')

  // Test different admin credential combinations
  const adminCredentials = [
    { email: '<EMAIL>', password: 'admin123' },
    { email: '<EMAIL>', password: 'admin123' },
    { email: '<EMAIL>', password: 'admin123' },
    { email: '<EMAIL>', password: 'password' },
    { email: '<EMAIL>', password: 'admin' }
  ]

  for (const credentials of adminCredentials) {
    try {
      console.log(`Testing: ${credentials.email} / ${credentials.password}`)
      
      const response = await fetch(`${BASE_URL}/api/admin/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      })

      if (response.ok) {
        const data = await response.json()
        console.log(`✅ SUCCESS: ${credentials.email} / ${credentials.password}`)
        console.log(`🎯 Admin Token: ${data.token?.substring(0, 20)}...`)
        return credentials
      } else {
        const error = await response.json()
        console.log(`❌ FAILED: ${credentials.email} - ${error.error || response.status}`)
      }
    } catch (error) {
      console.log(`❌ ERROR: ${credentials.email} - ${error.message}`)
    }
  }

  console.log('\n⚠️ No valid admin credentials found')
  console.log('💡 You may need to create an admin user first')
  return null
}

// Run the test
checkAdminCredentials()
