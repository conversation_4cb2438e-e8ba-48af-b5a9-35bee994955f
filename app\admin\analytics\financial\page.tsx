'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  BarChart3, 
  PieChart,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  AlertTriangle,
  Download,
  RefreshCw,
  Calendar
} from 'lucide-react'

interface AdvancedFinancialMetrics {
  revenueMetrics: {
    mrr: number
    arr: number
    mrrGrowthRate: number
    arpu: number
    customerLifetimeValue: number
    revenueChurnRate: number
  }
  profitabilityMetrics: {
    grossMargin: number
    operatingMargin: number
    netProfitMargin: number
    ebitda: number
    costOfRevenue: number
    operatingExpenseRatio: number
  }
  operationalMetrics: {
    cashFlowFromOperations: number
    daysInAccountsReceivable: number
    paymentCollectionRate: number
    averagePaymentTime: number
    expenseGrowthRate: number
    burnRate: number
  }
  partnerMetrics: {
    totalPartnerROI: number
    averageCommissionRate: number
    partnerRetentionRate: number
    topPerformingPartners: any[]
    commissionEfficiency: number
    partnerConcentrationRisk: number
  }
  riskMetrics: {
    concentrationRisk: any[]
    paymentDefaultRisk: number
    customerChurnRisk: number
    revenueVolatility: number
  }
}

export default function AdvancedFinancialAnalytics() {
  const [analytics, setAnalytics] = useState<AdvancedFinancialMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<string>('')
  const [selectedPeriod, setSelectedPeriod] = useState('12months')

  useEffect(() => {
    fetchAdvancedAnalytics()
  }, [selectedPeriod])

  const fetchAdvancedAnalytics = async () => {
    try {
      setLoading(true)
      const adminToken = localStorage.getItem('adminToken')
      
      const endDate = new Date()
      const startDate = new Date()
      
      switch (selectedPeriod) {
        case '3months':
          startDate.setMonth(endDate.getMonth() - 3)
          break
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6)
          break
        case '12months':
          startDate.setMonth(endDate.getMonth() - 12)
          break
        case 'ytd':
          startDate.setMonth(0, 1)
          break
      }

      const response = await fetch(
        `/api/admin/analytics/financial/advanced?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
        {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        }
      )

      if (response.ok) {
        const data = await response.json()
        setAnalytics(data.analytics)
        setLastUpdated(data.metadata.generatedAt)
      }
    } catch (error) {
      console.error('Error fetching advanced analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getGrowthIcon = (value: number) => {
    return value >= 0 ? (
      <ArrowUpRight className="h-4 w-4 text-green-500" />
    ) : (
      <ArrowDownRight className="h-4 w-4 text-red-500" />
    )
  }

  const getGrowthColor = (value: number) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600'
  }

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-slate-600">Loading advanced financial analytics...</p>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <p className="text-slate-600">Failed to load financial analytics</p>
        <Button onClick={fetchAdvancedAnalytics} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Advanced Financial Analytics</h1>
            <p className="text-slate-600 mt-1">
              Comprehensive business intelligence and financial insights
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {/* Period Selector */}
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="3months">Last 3 Months</option>
              <option value="6months">Last 6 Months</option>
              <option value="12months">Last 12 Months</option>
              <option value="ytd">Year to Date</option>
            </select>
            
            <Button onClick={fetchAdvancedAnalytics} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Last Updated */}
        <div className="text-sm text-slate-500">
          Last updated: {new Date(lastUpdated).toLocaleString()}
        </div>

        {/* Revenue Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Monthly Recurring Revenue</p>
                  <p className="text-3xl font-bold text-slate-900">
                    {formatCurrency(analytics.revenueMetrics.mrr)}
                  </p>
                  <div className="flex items-center mt-2">
                    {getGrowthIcon(analytics.revenueMetrics.mrrGrowthRate)}
                    <span className={`text-sm font-medium ml-1 ${getGrowthColor(analytics.revenueMetrics.mrrGrowthRate)}`}>
                      {formatPercentage(analytics.revenueMetrics.mrrGrowthRate)} MoM
                    </span>
                  </div>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Annual Recurring Revenue</p>
                  <p className="text-3xl font-bold text-slate-900">
                    {formatCurrency(analytics.revenueMetrics.arr)}
                  </p>
                  <p className="text-sm text-slate-500 mt-2">
                    {formatCurrency(analytics.revenueMetrics.mrr)} × 12
                  </p>
                </div>
                <Target className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Average Revenue Per User</p>
                  <p className="text-3xl font-bold text-slate-900">
                    {formatCurrency(analytics.revenueMetrics.arpu)}
                  </p>
                  <p className="text-sm text-slate-500 mt-2">Per month</p>
                </div>
                <Users className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Customer Lifetime Value</p>
                  <p className="text-3xl font-bold text-slate-900">
                    {formatCurrency(analytics.revenueMetrics.customerLifetimeValue)}
                  </p>
                  <p className="text-sm text-slate-500 mt-2">
                    Churn: {formatPercentage(analytics.revenueMetrics.revenueChurnRate)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Profitability Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Profitability Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">Gross Margin</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatPercentage(analytics.profitabilityMetrics.grossMargin)}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">Operating Margin</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatPercentage(analytics.profitabilityMetrics.operatingMargin)}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">Net Profit Margin</p>
                <p className="text-2xl font-bold text-purple-600">
                  {formatPercentage(analytics.profitabilityMetrics.netProfitMargin)}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">EBITDA</p>
                <p className="text-2xl font-bold text-indigo-600">
                  {formatCurrency(analytics.profitabilityMetrics.ebitda)}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">Cost of Revenue</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(analytics.profitabilityMetrics.costOfRevenue)}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">OpEx Ratio</p>
                <p className="text-2xl font-bold text-orange-600">
                  {formatPercentage(analytics.profitabilityMetrics.operatingExpenseRatio)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Operational & Partner Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Operational Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-slate-600">Cash Flow from Operations</span>
                <span className="font-semibold">
                  {formatCurrency(analytics.operationalMetrics.cashFlowFromOperations)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-600">Payment Collection Rate</span>
                <span className="font-semibold">
                  {formatPercentage(analytics.operationalMetrics.paymentCollectionRate)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-600">Average Payment Time</span>
                <span className="font-semibold">
                  {analytics.operationalMetrics.averagePaymentTime ?
                    `${analytics.operationalMetrics.averagePaymentTime.toFixed(1)} days` :
                    'N/A'
                  }
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-600">Monthly Burn Rate</span>
                <span className="font-semibold">
                  {formatCurrency(analytics.operationalMetrics.burnRate)}
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Partner Performance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-slate-600">Total Partner ROI</span>
                <span className="font-semibold">
                  {formatPercentage(analytics.partnerMetrics.totalPartnerROI)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-600">Average Commission Rate</span>
                <span className="font-semibold">
                  {formatPercentage(analytics.partnerMetrics.averageCommissionRate)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-600">Partner Retention Rate</span>
                <span className="font-semibold">
                  {formatPercentage(analytics.partnerMetrics.partnerRetentionRate)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-600">Commission Efficiency</span>
                <span className="font-semibold">
                  {formatPercentage(analytics.partnerMetrics.commissionEfficiency)}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Risk Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Risk Assessment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">Payment Default Risk</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatPercentage(analytics.riskMetrics.paymentDefaultRisk)}
                </p>
                <Badge variant="outline" className="mt-2">
                  {analytics.riskMetrics.paymentDefaultRisk < 5 ? 'Low' : 'Medium'}
                </Badge>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">Customer Churn Risk</p>
                <p className="text-2xl font-bold text-orange-600">
                  {formatPercentage(analytics.riskMetrics.customerChurnRisk)}
                </p>
                <Badge variant="outline" className="mt-2">
                  {analytics.riskMetrics.customerChurnRisk < 10 ? 'Low' : 'Medium'}
                </Badge>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">Revenue Volatility</p>
                <p className="text-2xl font-bold text-purple-600">
                  {formatPercentage(analytics.riskMetrics.revenueVolatility)}
                </p>
                <Badge variant="outline" className="mt-2">
                  {analytics.riskMetrics.revenueVolatility < 15 ? 'Low' : 'Medium'}
                </Badge>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-slate-600">Concentration Risk</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatPercentage(analytics.partnerMetrics.partnerConcentrationRisk)}
                </p>
                <Badge variant="outline" className="mt-2">
                  {analytics.partnerMetrics.partnerConcentrationRisk < 30 ? 'Low' : 'Medium'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
