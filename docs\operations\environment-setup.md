# 🔧 Schopio Environment Setup Guide

## 🎯 **ENVIRONMENT OVERVIEW**

### **Environment Types**
- **Development**: Local development environment
- **Staging**: Pre-production testing environment  
- **Production**: Live production environment

### **Technology Stack**
- **Frontend**: Next.js 14 with App Router
- **Backend**: Hono.js API with Edge Runtime
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Package Manager**: npm/bun
- **Deployment**: Vercel

---

## 💻 **DEVELOPMENT ENVIRONMENT SETUP**

### **1. Prerequisites**

#### **Required Software**
```bash
# Node.js (v18 or higher)
node --version  # Should be v18+

# npm (comes with Node.js)
npm --version

# Git
git --version

# Optional: Bun (faster package manager)
curl -fsSL https://bun.sh/install | bash
```

#### **Development Tools**
```bash
# VS Code (recommended)
# Extensions:
- TypeScript and JavaScript Language Features
- Tailwind CSS IntelliSense
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint
```

### **2. Project Setup**

#### **Clone Repository**
```bash
# Clone the project
git clone [repository-url]
cd landing-page

# Install dependencies
npm install
# OR
bun install
```

#### **Environment Variables**
```bash
# Create .env.local file
cp .env.example .env.local

# Configure development environment variables
DATABASE_URL="postgresql://[neon-dev-connection]"
JWT_SECRET="dev-jwt-secret-32-characters-long"
RAZORPAY_KEY_ID="rzp_test_[test-key]"
RAZORPAY_KEY_SECRET="[test-secret]"
RESEND_API_KEY="re_[resend-api-key]"
FROM_EMAIL="<EMAIL>"
FROM_NAME="Schopio Dev"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
NODE_ENV="development"
```

### **3. Database Setup**

#### **Neon Development Database**
```bash
# Create development database on Neon
# 1. Login to Neon Console
# 2. Create project: "schopio-development"
# 3. Copy connection string to .env.local

# Apply database schema
bunx drizzle-kit push

# Optional: Open database studio
bunx drizzle-kit studio
```

#### **Local Database (Alternative)**
```bash
# If using local PostgreSQL
# Install PostgreSQL locally
# Create database
createdb schopio_dev

# Update DATABASE_URL in .env.local
DATABASE_URL="postgresql://username:password@localhost:5432/schopio_dev"
```

### **4. Development Server**

#### **Start Development Server**
```bash
# Start the development server
npm run dev
# OR
bun dev

# Server will start at http://localhost:3000
```

#### **Development URLs**
```
Frontend: http://localhost:3000
API: http://localhost:3000/api
Admin: http://localhost:3000/admin
School Portal: http://localhost:3000/profile
```

---

## 🧪 **STAGING ENVIRONMENT SETUP**

### **1. Staging Infrastructure**

#### **Vercel Staging Deployment**
```bash
# Create staging branch
git checkout -b staging
git push origin staging

# Deploy to Vercel staging
vercel --target staging
```

#### **Staging Environment Variables**
```bash
# Staging environment variables
DATABASE_URL="postgresql://[neon-staging-connection]"
JWT_SECRET="staging-jwt-secret-32-characters"
RAZORPAY_KEY_ID="rzp_test_[staging-test-key]"
RAZORPAY_KEY_SECRET="[staging-test-secret]"
RESEND_API_KEY="re_[staging-resend-key]"
FROM_EMAIL="<EMAIL>"
FROM_NAME="Schopio Staging"
NEXT_PUBLIC_API_URL="https://schopio-staging.vercel.app/api"
NODE_ENV="staging"
```

### **2. Staging Database**

#### **Neon Staging Database**
```bash
# Create staging database
# 1. Create project: "schopio-staging"
# 2. Import production schema
# 3. Use test data for staging

# Deploy schema to staging
DATABASE_URL=[staging-url] bunx drizzle-kit push
```

### **3. Staging Testing**

#### **Testing Checklist**
```bash
# Functional testing
- User authentication flow
- Payment processing (test mode)
- Email notifications
- Admin dashboard functionality
- School portal features

# Performance testing
- Page load times
- API response times
- Database query performance
```

---

## 🌐 **PRODUCTION ENVIRONMENT**

### **1. Production Infrastructure**

#### **Vercel Production Setup**
```bash
# Production deployment
git checkout main
git tag v1.0.0
git push origin main --tags

# Automatic deployment to production
```

#### **Production Environment Variables**
```bash
# Production environment variables (secure)
DATABASE_URL="postgresql://[neon-prod-connection]"
JWT_SECRET="[secure-32-char-production-secret]"
RAZORPAY_KEY_ID="rzp_live_[live-key]"
RAZORPAY_KEY_SECRET="[live-secret]"
RESEND_API_KEY="re_[production-resend-key]"
FROM_EMAIL="<EMAIL>"
FROM_NAME="Schopio"
NEXT_PUBLIC_API_URL="https://schopio.com/api"
NODE_ENV="production"
```

### **2. Production Database**

#### **Neon Production Database**
```bash
# Production database configuration
Project: schopio-production
Region: Asia Pacific (Mumbai)
Compute: Auto-scaling
Storage: Auto-scaling
Backups: Daily automated backups
```

### **3. Production Security**

#### **Security Configuration**
```bash
# Security headers
- HTTPS only (HSTS)
- Content Security Policy
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff

# API Security
- Rate limiting
- CORS configuration
- JWT token validation
- Input sanitization
```

---

## 🔧 **CONFIGURATION MANAGEMENT**

### **1. Environment Variable Management**

#### **Variable Categories**
```typescript
// Database Configuration
DATABASE_URL: string;

// Authentication
JWT_SECRET: string;

// Payment Gateway
RAZORPAY_KEY_ID: string;
RAZORPAY_KEY_SECRET: string;

// Email Service
RESEND_API_KEY: string;
FROM_EMAIL: string;
FROM_NAME: string;

// Application
NEXT_PUBLIC_API_URL: string;
NODE_ENV: 'development' | 'staging' | 'production';
```

#### **Security Best Practices**
```bash
# Environment variable security
- Never commit .env files to git
- Use different secrets for each environment
- Rotate secrets regularly (quarterly)
- Use strong, random secrets (32+ characters)
- Limit access to production secrets
```

### **2. Configuration Validation**

#### **Environment Validation**
```typescript
// Environment validation schema
const envSchema = {
  DATABASE_URL: z.string().url(),
  JWT_SECRET: z.string().min(32),
  RAZORPAY_KEY_ID: z.string().startsWith('rzp_'),
  RAZORPAY_KEY_SECRET: z.string().min(20),
  RESEND_API_KEY: z.string().startsWith('re_'),
  FROM_EMAIL: z.string().email(),
  FROM_NAME: z.string().min(1),
  NEXT_PUBLIC_API_URL: z.string().url(),
  NODE_ENV: z.enum(['development', 'staging', 'production'])
};
```

### **3. Configuration Documentation**

#### **Environment Variable Documentation**
```markdown
| Variable | Description | Example | Required |
|----------|-------------|---------|----------|
| DATABASE_URL | PostgreSQL connection string | postgresql://... | Yes |
| JWT_SECRET | JWT signing secret | 32-char-random-string | Yes |
| RAZORPAY_KEY_ID | Razorpay API key | rzp_test_... | Yes |
| RAZORPAY_KEY_SECRET | Razorpay secret | secret-string | Yes |
| RESEND_API_KEY | Resend email API key | re_... | Yes |
| FROM_EMAIL | Sender email address | <EMAIL> | Yes |
| FROM_NAME | Sender name | Schopio | Yes |
| NEXT_PUBLIC_API_URL | Public API URL | https://... | Yes |
| NODE_ENV | Environment type | production | Yes |
```

---

## 🛠️ **DEVELOPMENT TOOLS & SCRIPTS**

### **1. Package Scripts**

#### **Available Scripts**
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "db:push": "bunx drizzle-kit push",
    "db:studio": "bunx drizzle-kit studio",
    "db:generate": "bunx drizzle-kit generate",
    "format": "prettier --write .",
    "format:check": "prettier --check ."
  }
}
```

### **2. Development Workflow**

#### **Daily Development Workflow**
```bash
# 1. Start development
git pull origin main
npm install  # if package.json changed
npm run dev

# 2. Make changes
# Edit code, test locally

# 3. Type checking
npm run type-check

# 4. Code formatting
npm run format

# 5. Commit changes
git add .
git commit -m "feat: description"
git push origin feature-branch
```

### **3. Database Development**

#### **Database Workflow**
```bash
# 1. Schema changes
# Edit src/db/schema.ts

# 2. Generate migration
bunx drizzle-kit generate

# 3. Apply to development database
bunx drizzle-kit push

# 4. Verify changes
bunx drizzle-kit studio

# 5. Test application
npm run dev
```

---

## 🔍 **TROUBLESHOOTING**

### **1. Common Issues**

#### **Database Connection Issues**
```bash
# Check database URL format
echo $DATABASE_URL

# Test database connectivity
bunx drizzle-kit studio

# Common fixes
- Verify connection string format
- Check network connectivity
- Verify database exists
- Check authentication credentials
```

#### **Build Issues**
```bash
# TypeScript errors
npm run type-check

# Dependency issues
rm -rf node_modules package-lock.json
npm install

# Environment variable issues
cp .env.example .env.local
# Fill in required variables
```

### **2. Performance Issues**

#### **Development Performance**
```bash
# Slow development server
- Check for large files in project
- Verify Node.js version (v18+)
- Clear Next.js cache: rm -rf .next
- Use bun instead of npm for faster installs
```

### **3. Environment-Specific Issues**

#### **Environment Debugging**
```bash
# Check environment variables
console.log(process.env.NODE_ENV);

# Verify API endpoints
curl http://localhost:3000/api/health

# Database connectivity
bunx drizzle-kit studio
```
