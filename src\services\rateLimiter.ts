import { Context } from 'hono'
import { db } from '@/src/db'
import { rateLimits, securityEvents } from '@/src/db/schema'
import { eq, and, gte, lte } from 'drizzle-orm'
import { auditLogger } from './auditLogger'

export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  blockDurationMs: number // How long to block after limit exceeded
  skipSuccessfulRequests?: boolean // Don't count successful requests
  skipFailedRequests?: boolean // Don't count failed requests
  keyGenerator?: (c: Context) => string // Custom key generator
}

export interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: Date
  retryAfter?: number // Seconds to wait before retry
}

class RateLimiter {
  private static instance: RateLimiter
  private configs: Map<string, RateLimitConfig> = new Map()

  private constructor() {
    // Set up default rate limit configurations
    this.setupDefaultConfigs()
    
    // Clean up old rate limit records every hour
    setInterval(() => {
      this.cleanupOldRecords()
    }, 60 * 60 * 1000)
  }

  static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter()
    }
    return RateLimiter.instance
  }

  private setupDefaultConfigs(): void {
    // Admin login rate limiting - stricter
    this.configs.set('admin:login', {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5, // 5 attempts per 15 minutes
      blockDurationMs: 30 * 60 * 1000, // Block for 30 minutes
    })

    // Client login rate limiting
    this.configs.set('client:login', {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 10, // 10 attempts per 15 minutes
      blockDurationMs: 15 * 60 * 1000, // Block for 15 minutes
    })

    // Payment endpoints - very strict
    this.configs.set('payment:create', {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 3, // 3 payment attempts per minute
      blockDurationMs: 5 * 60 * 1000, // Block for 5 minutes
    })

    // Admin API general rate limiting
    this.configs.set('admin:api', {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 requests per minute
      blockDurationMs: 60 * 1000, // Block for 1 minute
    })

    // Client API general rate limiting
    this.configs.set('client:api', {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 60, // 60 requests per minute
      blockDurationMs: 60 * 1000, // Block for 1 minute
    })

    // Public API rate limiting
    this.configs.set('public:api', {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 requests per minute
      blockDurationMs: 2 * 60 * 1000, // Block for 2 minutes
    })

    // AI Chat rate limiting
    this.configs.set('ai:chat', {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10, // 10 chat messages per minute
      blockDurationMs: 60 * 1000, // Block for 1 minute
    })
  }

  /**
   * Check rate limit for a request
   */
  async checkRateLimit(
    endpoint: string,
    identifier: string,
    configKey?: string
  ): Promise<RateLimitResult> {
    try {
      const config = this.configs.get(configKey || 'public:api')
      if (!config) {
        return {
          allowed: true,
          remaining: 999,
          resetTime: new Date(Date.now() + 60000)
        }
      }

      const now = new Date()
      const windowStart = new Date(now.getTime() - config.windowMs)

      // Check if currently blocked
      const [existingRecord] = await db
        .select()
        .from(rateLimits)
        .where(
          and(
            eq(rateLimits.identifier, identifier),
            eq(rateLimits.endpoint, endpoint),
            eq(rateLimits.blocked, true),
            gte(rateLimits.blockedUntil, now)
          )
        )
        .limit(1)

      if (existingRecord) {
        const retryAfter = Math.ceil((existingRecord.blockedUntil!.getTime() - now.getTime()) / 1000)
        
        // Log security event for blocked request
        await auditLogger.logSecurity({
          type: 'rate_limit_exceeded',
          ipAddress: identifier,
          details: {
            endpoint,
            blockedUntil: existingRecord.blockedUntil,
            retryAfter
          },
          severity: 'medium'
        })

        return {
          allowed: false,
          remaining: 0,
          resetTime: existingRecord.blockedUntil!,
          retryAfter
        }
      }

      // Get or create rate limit record for current window
      const [currentRecord] = await db
        .select()
        .from(rateLimits)
        .where(
          and(
            eq(rateLimits.identifier, identifier),
            eq(rateLimits.endpoint, endpoint),
            gte(rateLimits.windowStart, windowStart)
          )
        )
        .limit(1)

      if (currentRecord) {
        // Update existing record
        const newCount = (currentRecord.requestCount || 0) + 1

        if (newCount > config.maxRequests) {
          // Block the identifier
          const blockedUntil = new Date(now.getTime() + config.blockDurationMs)
          
          await db
            .update(rateLimits)
            .set({
              requestCount: newCount,
              lastRequest: now,
              blocked: true,
              blockedUntil
            })
            .where(eq(rateLimits.id, currentRecord.id))

          // Log security event
          await auditLogger.logSecurity({
            type: 'rate_limit_exceeded',
            ipAddress: identifier,
            details: {
              endpoint,
              requestCount: newCount,
              maxRequests: config.maxRequests,
              blockedUntil
            },
            severity: 'medium'
          })

          const retryAfter = Math.ceil(config.blockDurationMs / 1000)
          return {
            allowed: false,
            remaining: 0,
            resetTime: blockedUntil,
            retryAfter
          }
        } else {
          // Update count
          await db
            .update(rateLimits)
            .set({
              requestCount: newCount,
              lastRequest: now
            })
            .where(eq(rateLimits.id, currentRecord.id))

          const resetTime = new Date(currentRecord.windowStart.getTime() + config.windowMs)
          return {
            allowed: true,
            remaining: config.maxRequests - newCount,
            resetTime
          }
        }
      } else {
        // Create new record
        const [newRecord] = await db
          .insert(rateLimits)
          .values({
            identifier,
            endpoint,
            requestCount: 1,
            windowStart: now,
            lastRequest: now
          })
          .returning()

        const resetTime = new Date(now.getTime() + config.windowMs)
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime
        }
      }

    } catch (error) {
      console.error('Rate limit check error:', error)
      // On error, allow the request but log the issue
      await auditLogger.logSecurity({
        type: 'rate_limit_exceeded',
        ipAddress: identifier,
        details: {
          endpoint,
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        severity: 'high'
      })

      return {
        allowed: true,
        remaining: 999,
        resetTime: new Date(Date.now() + 60000)
      }
    }
  }

  /**
   * Create rate limiting middleware
   */
  createMiddleware(configKey: string, options?: {
    keyGenerator?: (c: Context) => string
    onLimitReached?: (c: Context, result: RateLimitResult) => Response | Promise<Response>
  }) {
    return async (c: Context, next: () => Promise<void>) => {
      const identifier = options?.keyGenerator 
        ? options.keyGenerator(c)
        : this.getDefaultIdentifier(c)
      
      const endpoint = c.req.path
      const result = await this.checkRateLimit(endpoint, identifier, configKey)

      // Add rate limit headers
      c.header('X-RateLimit-Limit', this.configs.get(configKey)?.maxRequests.toString() || '60')
      c.header('X-RateLimit-Remaining', result.remaining.toString())
      c.header('X-RateLimit-Reset', Math.ceil(result.resetTime.getTime() / 1000).toString())

      if (!result.allowed) {
        if (result.retryAfter) {
          c.header('Retry-After', result.retryAfter.toString())
        }

        if (options?.onLimitReached) {
          return options.onLimitReached(c, result)
        }

        return c.json({
          error: 'Rate limit exceeded',
          message: `Too many requests. Try again ${result.retryAfter ? `in ${result.retryAfter} seconds` : 'later'}.`,
          retryAfter: result.retryAfter
        }, 429)
      }

      await next()
    }
  }

  /**
   * Get default identifier (IP address)
   */
  private getDefaultIdentifier(c: Context): string {
    return c.req.header('x-forwarded-for') || 
           c.req.header('x-real-ip') || 
           c.req.header('cf-connecting-ip') || 
           'unknown'
  }

  /**
   * Clean up old rate limit records
   */
  private async cleanupOldRecords(): Promise<void> {
    try {
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
      
      await db
        .delete(rateLimits)
        .where(lte(rateLimits.lastRequest, cutoffTime))

      console.log('Cleaned up old rate limit records')
    } catch (error) {
      console.error('Failed to cleanup rate limit records:', error)
    }
  }

  /**
   * Reset rate limit for identifier
   */
  async resetRateLimit(identifier: string, endpoint?: string): Promise<void> {
    try {
      const whereCondition = endpoint
        ? and(eq(rateLimits.identifier, identifier), eq(rateLimits.endpoint, endpoint))
        : eq(rateLimits.identifier, identifier)

      await db
        .delete(rateLimits)
        .where(whereCondition)

      console.log(`Reset rate limit for ${identifier}${endpoint ? ` on ${endpoint}` : ''}`)
    } catch (error) {
      console.error('Failed to reset rate limit:', error)
    }
  }
}

export const rateLimiter = RateLimiter.getInstance()
