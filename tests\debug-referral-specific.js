// Specific debug test for referral code application
// This simulates the exact frontend request

const BASE_URL = 'http://localhost:3000'

async function testSpecificReferralRequest() {
  console.log('🔍 Testing Specific Referral Code Request...\n')

  try {
    // Test the exact request structure from frontend
    console.log('Testing exact frontend request structure...')
    
    const requestBody = { code: 'TEST123' }
    console.log('Request body:', JSON.stringify(requestBody))
    
    const response = await fetch(`${BASE_URL}/api/auth/referral/apply`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })
    
    console.log(`Status: ${response.status}`)
    console.log(`Status Text: ${response.statusText}`)
    console.log(`Content-Type: ${response.headers.get('content-type')}`)
    
    const responseText = await response.text()
    console.log(`Raw Response: ${responseText}`)
    
    try {
      const responseJson = JSON.parse(responseText)
      console.log('Parsed Response:', responseJson)
    } catch (parseError) {
      console.log('Failed to parse response as JSON:', parseError.message)
    }
    
    // Test with different code formats
    console.log('\n--- Testing different code formats ---')
    
    const testCodes = [
      'ABC123',
      'abc123',
      'ABC12345',
      '123ABC',
      'TEST',
      '',
      null,
      undefined
    ]
    
    for (const testCode of testCodes) {
      console.log(`\nTesting code: "${testCode}"`)
      
      const testBody = testCode !== undefined ? { code: testCode } : {}
      
      const testResponse = await fetch(`${BASE_URL}/api/auth/referral/apply`, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testBody)
      })
      
      const testData = await testResponse.text()
      console.log(`  Status: ${testResponse.status}, Response: ${testData.substring(0, 100)}`)
    }
    
    // Test validation endpoint with same codes
    console.log('\n--- Testing validation endpoint ---')
    
    for (const testCode of ['ABC123', 'TEST123', '']) {
      console.log(`\nValidating code: "${testCode}"`)
      
      const validateResponse = await fetch(`${BASE_URL}/api/auth/referral/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: testCode })
      })
      
      const validateData = await validateResponse.text()
      console.log(`  Status: ${validateResponse.status}, Response: ${validateData.substring(0, 100)}`)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('Stack:', error.stack)
  }
}

// Run the test
testSpecificReferralRequest()
