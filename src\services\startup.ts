import { billingScheduler } from './billingScheduler'
import { billingHealthMonitor } from './billingHealthMonitor'
import { missedBillDetector } from './missedBillDetector'

interface StartupConfig {
  enableBillingScheduler: boolean
  enableHealthMonitoring: boolean
  enableMissedBillDetection: boolean
  environment: 'development' | 'production' | 'staging'
}

class StartupService {
  private config: StartupConfig
  private isInitialized = false
  private services: Map<string, { name: string; status: 'starting' | 'running' | 'failed' | 'stopped' }> = new Map()

  constructor() {
    this.config = {
      enableBillingScheduler: process.env.BILLING_SCHEDULER_ENABLED !== 'false', // Default true
      enableHealthMonitoring: process.env.HEALTH_MONITORING_ENABLED !== 'false', // Default true
      enableMissedBillDetection: process.env.MISSED_BILL_DETECTION_ENABLED !== 'false', // Default true
      environment: (process.env.NODE_ENV as any) || 'development'
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('🔄 Services already initialized, skipping...')
      return
    }

    console.log('🚀 Starting Schopio Production Services...')
    console.log(`📊 Environment: ${this.config.environment}`)
    console.log(`⚙️ Configuration:`, this.config)

    try {
      // Initialize services in order of dependency
      await this.initializeBillingScheduler()
      await this.initializeHealthMonitoring()
      await this.initializeMissedBillDetection()

      this.isInitialized = true
      console.log('✅ All services initialized successfully!')

      // Log service status
      this.logServiceStatus()

    } catch (error) {
      console.error('❌ Failed to initialize services:', error)
      throw error
    }
  }

  private async initializeBillingScheduler(): Promise<void> {
    if (!this.config.enableBillingScheduler) {
      console.log('⏭️ Billing scheduler disabled by configuration')
      return
    }

    try {
      this.services.set('billing-scheduler', { name: 'Billing Scheduler', status: 'starting' })
      console.log('🔄 Initializing billing scheduler...')

      billingScheduler.init()

      this.services.set('billing-scheduler', { name: 'Billing Scheduler', status: 'running' })
      console.log('✅ Billing scheduler initialized and running')

    } catch (error) {
      this.services.set('billing-scheduler', { name: 'Billing Scheduler', status: 'failed' })
      console.error('❌ Failed to initialize billing scheduler:', error)
      throw error
    }
  }

  private async initializeHealthMonitoring(): Promise<void> {
    if (!this.config.enableHealthMonitoring) {
      console.log('⏭️ Health monitoring disabled by configuration')
      return
    }

    try {
      this.services.set('health-monitor', { name: 'Health Monitor', status: 'starting' })
      console.log('🔄 Initializing health monitoring...')

      await billingHealthMonitor.init()

      this.services.set('health-monitor', { name: 'Health Monitor', status: 'running' })
      console.log('✅ Health monitoring initialized and running')

    } catch (error) {
      this.services.set('health-monitor', { name: 'Health Monitor', status: 'failed' })
      console.error('❌ Failed to initialize health monitoring:', error)
      // Don't throw - health monitoring failure shouldn't stop the app
    }
  }

  private async initializeMissedBillDetection(): Promise<void> {
    if (!this.config.enableMissedBillDetection) {
      console.log('⏭️ Missed bill detection disabled by configuration')
      return
    }

    try {
      this.services.set('missed-bill-detector', { name: 'Missed Bill Detector', status: 'starting' })
      console.log('🔄 Initializing missed bill detection...')

      await missedBillDetector.init()

      this.services.set('missed-bill-detector', { name: 'Missed Bill Detector', status: 'running' })
      console.log('✅ Missed bill detection initialized and running')

    } catch (error) {
      this.services.set('missed-bill-detector', { name: 'Missed Bill Detector', status: 'failed' })
      console.error('❌ Failed to initialize missed bill detection:', error)
      // Don't throw - this is a safety net, not critical for startup
    }
  }

  private logServiceStatus(): void {
    console.log('\n📊 Service Status Summary:')
    console.log('=' .repeat(50))

    for (const [key, service] of this.services) {
      const statusIcon = this.getStatusIcon(service.status)
      console.log(`${statusIcon} ${service.name}: ${service.status.toUpperCase()}`)
    }

    console.log('=' .repeat(50))
    console.log('🎉 Schopio is ready for production!')
  }

  private getStatusIcon(status: string): string {
    switch (status) {
      case 'running': return '✅'
      case 'starting': return '🔄'
      case 'failed': return '❌'
      case 'stopped': return '⏹️'
      default: return '❓'
    }
  }

  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down services...')

    try {
      // Shutdown in reverse order
      if (this.services.has('missed-bill-detector')) {
        await missedBillDetector.shutdown()
        this.services.set('missed-bill-detector', { name: 'Missed Bill Detector', status: 'stopped' })
      }

      if (this.services.has('health-monitor')) {
        await billingHealthMonitor.shutdown()
        this.services.set('health-monitor', { name: 'Health Monitor', status: 'stopped' })
      }

      if (this.services.has('billing-scheduler')) {
        billingScheduler.stop()
        this.services.set('billing-scheduler', { name: 'Billing Scheduler', status: 'stopped' })
      }

      console.log('✅ All services shut down gracefully')

    } catch (error) {
      console.error('❌ Error during service shutdown:', error)
    }
  }

  getServiceStatus(): Record<string, any> {
    const status: Record<string, any> = {
      initialized: this.isInitialized,
      environment: this.config.environment,
      config: this.config,
      services: {}
    }

    for (const [key, service] of this.services) {
      status.services[key] = {
        name: service.name,
        status: service.status,
        healthy: service.status === 'running'
      }
    }

    return status
  }

  isHealthy(): boolean {
    if (!this.isInitialized) return false

    // Check if critical services are running
    const criticalServices = ['billing-scheduler']

    for (const serviceKey of criticalServices) {
      const service = this.services.get(serviceKey)
      if (!service || service.status !== 'running') {
        return false
      }
    }

    return true
  }
}

// Create singleton instance
const startupService = new StartupService()

/**
 * Initialize all background services
 */
export async function initializeServices(): Promise<void> {
  return startupService.initialize()
}

/**
 * Gracefully shutdown all services
 */
export async function shutdownServices(): Promise<void> {
  return startupService.shutdown()
}

/**
 * Get service status for health checks
 */
export function getServiceStatus(): Record<string, any> {
  return startupService.getServiceStatus()
}

/**
 * Check if all critical services are healthy
 */
export function isHealthy(): boolean {
  return startupService.isHealthy()
}

// Export the service instance for advanced usage
export { startupService }

// Graceful shutdown handlers
process.on('SIGTERM', async () => {
  console.log('📡 Received SIGTERM, shutting down gracefully...')
  await startupService.shutdown()
  process.exit(0)
})

process.on('SIGINT', async () => {
  console.log('📡 Received SIGINT, shutting down gracefully...')
  await startupService.shutdown()
  process.exit(0)
})

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
  console.error('💥 Uncaught Exception:', error)
  await startupService.shutdown()
  process.exit(1)
})

process.on('unhandledRejection', async (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason)
  await startupService.shutdown()
  process.exit(1)
})

// Auto-initialize services when module is loaded (production only)
if (process.env.NODE_ENV === 'production' || process.env.AUTO_START_SERVICES === 'true') {
  // Delay initialization slightly to ensure all modules are loaded
  setTimeout(async () => {
    try {
      console.log('🚀 Auto-starting Schopio services...')
      await initializeServices()
      console.log('✅ Auto-start complete')
    } catch (error) {
      console.error('❌ Auto-start failed:', error)
    }
  }, 2000) // 2 second delay to ensure all modules are loaded
}
