# Stakeholder-Specific Financial Workflows

## 🏫 School Portal Financial Workflows

### School Payment Journey
```typescript
interface SchoolFinancialWorkflow {
  // 1. Subscription Setup
  subscriptionCreation: {
    studentCount: number;
    monthlyAmount: number; // ₹80 × studentCount
    billingDate: Date; // 1st of every month
    autoRenewal: boolean;
    paymentMethod: 'manual' | 'auto_debit';
  };
  
  // 2. Monthly Billing Process
  monthlyBilling: {
    invoiceGeneration: Date; // 25th of previous month
    paymentDueDate: Date; // 5th of current month
    gracePeriod: number; // 3 days after due date
    lateFeeApplicable: boolean; // 2% daily after grace period
  };
  
  // 3. Payment Processing
  paymentFlow: {
    paymentMethods: ['credit_card', 'debit_card', 'netbanking', 'upi', 'wallet'];
    paymentGateway: 'razorpay';
    confirmationTime: 'instant';
    receiptGeneration: 'automatic';
  };
}
```

### School Portal Payment Features

#### 1. Dashboard Payment Overview
```typescript
interface SchoolPaymentDashboard {
  currentSubscription: {
    planName: string;
    studentCount: number;
    monthlyAmount: number;
    nextBillingDate: Date;
    status: 'active' | 'suspended' | 'cancelled';
  };
  
  paymentHistory: {
    invoiceNumber: string;
    amount: number;
    dueDate: Date;
    paidDate?: Date;
    status: 'paid' | 'pending' | 'overdue' | 'failed';
    downloadLink: string;
  }[];
  
  upcomingPayments: {
    amount: number;
    dueDate: Date;
    description: string;
  }[];
  
  paymentMethods: {
    savedCards: SavedCard[];
    bankAccounts: BankAccount[];
    defaultMethod: string;
  };
}
```

#### 2. Payment Processing Workflow
```typescript
const schoolPaymentProcess = {
  // Step 1: Invoice Notification
  invoiceNotification: async (schoolId: string) => {
    const invoice = await generateMonthlyInvoice(schoolId);
    await sendInvoiceEmail(schoolId, invoice);
    await sendInvoiceSMS(schoolId, invoice);
    return invoice;
  },
  
  // Step 2: Payment Initiation
  paymentInitiation: async (invoiceId: string, paymentMethod: PaymentMethod) => {
    const razorpayOrder = await createRazorpayOrder(invoiceId);
    return {
      orderId: razorpayOrder.id,
      amount: razorpayOrder.amount,
      currency: 'INR',
      paymentUrl: generatePaymentUrl(razorpayOrder)
    };
  },
  
  // Step 3: Payment Confirmation
  paymentConfirmation: async (paymentData: PaymentResponse) => {
    await verifyPaymentSignature(paymentData);
    await updateInvoiceStatus(paymentData.orderId, 'paid');
    await sendPaymentConfirmation(paymentData.clientId);
    await triggerPartnerCommissionCalculation(paymentData);
  }
};
```

#### 3. Grace Period & Late Fee Management
```typescript
interface GracePeriodManagement {
  gracePeriodDays: 3; // 3 days after due date
  lateFeePercentage: 2; // 2% per day after grace period
  maxLateFeePercentage: 25; // Maximum 25% of invoice amount
  suspensionThreshold: 15; // Suspend after 15 days overdue
  cancellationThreshold: 30; // Cancel after 30 days overdue
}

const handleOverduePayments = async (schoolId: string) => {
  const overdueInvoices = await getOverdueInvoices(schoolId);
  
  for (const invoice of overdueInvoices) {
    const daysOverdue = calculateDaysOverdue(invoice.dueDate);
    
    if (daysOverdue <= 3) {
      // Within grace period - send reminder
      await sendGracePeriodReminder(schoolId, invoice);
    } else if (daysOverdue <= 15) {
      // Apply late fee
      const lateFee = calculateLateFee(invoice.amount, daysOverdue);
      await applyLateFee(invoice.id, lateFee);
      await sendLateFeeNotification(schoolId, invoice, lateFee);
    } else if (daysOverdue <= 30) {
      // Suspend subscription
      await suspendSubscription(schoolId, 'payment_overdue');
      await sendSuspensionNotice(schoolId);
    } else {
      // Cancel subscription
      await cancelSubscription(schoolId, 'payment_default');
      await sendCancellationNotice(schoolId);
    }
  }
};
```

## 🤝 Partner Portal Financial Workflows

### Partner Commission Journey
```typescript
interface PartnerFinancialWorkflow {
  // 1. Commission Earning
  commissionEarning: {
    referralTracking: 'automatic'; // Via referral code
    commissionRate: '35-50%'; // Configurable per partner
    calculationBasis: 'net_revenue'; // After operational expenses
    earningTrigger: 'school_payment_confirmed';
  };
  
  // 2. Escrow Management
  escrowProcess: {
    initialStatus: 'pending'; // All commissions start in escrow
    holdPeriod: '3-7_days'; // Based on risk assessment
    releaseConditions: ReleaseConditions;
    manualReviewRequired: boolean;
  };
  
  // 3. Payout Processing
  payoutFlow: {
    method: 'razorpay_route'; // Automated via Razorpay Route
    frequency: 'automatic'; // Released when conditions met
    minimumAmount: 1000; // ₹1000 minimum payout
    processingTime: '24_hours'; // After release approval
  };
}
```

### Partner Portal Commission Features

#### 1. Earnings Dashboard
```typescript
interface PartnerEarningsDashboard {
  earningsSummary: {
    totalEarned: number; // All-time earnings
    currentMonthEarnings: number;
    pendingCommissions: number; // In escrow
    availableForWithdrawal: number; // Released commissions
  };
  
  commissionBreakdown: {
    schoolId: string;
    schoolName: string;
    monthYear: string;
    baseAmount: number; // School payment
    commissionPercentage: number;
    grossCommission: number;
    operationalExpenses: number;
    netCommission: number;
    status: 'pending' | 'held' | 'released' | 'paid';
    expectedReleaseDate?: Date;
  }[];
  
  payoutHistory: {
    payoutId: string;
    amount: number;
    processedDate: Date;
    bankAccount: string;
    status: 'processing' | 'completed' | 'failed';
    razorpayTransferId: string;
  }[];
}
```

#### 2. Commission Calculation Transparency
```typescript
const partnerCommissionCalculation = {
  // Step 1: Base Commission Calculation
  calculateBaseCommission: (schoolPayment: number, commissionRate: number) => {
    return schoolPayment * (commissionRate / 100);
  },
  
  // Step 2: Operational Expense Deduction
  calculateOperationalExpenses: (baseAmount: number) => {
    const expenses = {
      razorpayFees: baseAmount * 0.02, // 2% payment gateway fees
      platformCosts: baseAmount * 0.05, // 5% platform operational costs
      gstOnCommission: 0, // Calculated separately
    };
    return expenses;
  },
  
  // Step 3: Net Commission Calculation
  calculateNetCommission: (grossCommission: number, operationalExpenses: number) => {
    return grossCommission - operationalExpenses.razorpayFees - operationalExpenses.platformCosts;
  },
  
  // Step 4: Tax Calculation (if applicable)
  calculateTaxDeduction: (netCommission: number, partnerType: 'individual' | 'business') => {
    if (partnerType === 'business' && netCommission > 30000) {
      return netCommission * 0.01; // 1% TDS for business partners
    }
    return 0;
  }
};
```

#### 3. Escrow Status Tracking
```typescript
interface EscrowStatusTracking {
  escrowStages: {
    stage: 'payment_received' | 'verification_pending' | 'hold_period' | 'ready_for_release' | 'released';
    description: string;
    estimatedDuration: string;
    currentStatus: 'completed' | 'in_progress' | 'pending';
  }[];
  
  holdReasons: {
    reason: 'school_payment_pending' | 'risk_assessment' | 'manual_review' | 'dispute_investigation';
    description: string;
    expectedResolution: Date;
    actionRequired?: string;
  }[];
  
  releaseConditions: {
    condition: string;
    status: 'met' | 'pending' | 'failed';
    lastChecked: Date;
  }[];
}
```

#### 4. Bank Account Management
```typescript
interface PartnerBankAccountManagement {
  fundAccountValidation: {
    accountNumber: string;
    ifscCode: string;
    accountHolderName: string;
    validationStatus: 'pending' | 'verified' | 'failed';
    validationDate?: Date;
    razorpayFundAccountId?: string;
  };
  
  payoutPreferences: {
    minimumPayoutAmount: number; // Default ₹1000
    autoPayoutEnabled: boolean; // Automatic vs manual withdrawal
    payoutFrequency: 'immediate' | 'weekly' | 'monthly';
    notificationPreferences: {
      email: boolean;
      sms: boolean;
      whatsapp: boolean;
    };
  };
}
```

## 👨‍💼 Admin Portal Financial Workflows

### Admin Financial Management
```typescript
interface AdminFinancialWorkflow {
  // 1. Revenue Oversight
  revenueManagement: {
    totalRevenue: number; // All school payments
    monthlyRecurringRevenue: number; // MRR tracking
    revenueGrowthRate: number; // Month-over-month growth
    churnRate: number; // Subscription cancellation rate
  };
  
  // 2. Commission Management
  commissionOversight: {
    totalCommissionsPaid: number;
    commissionsInEscrow: number;
    averageReleaseTime: number; // Days
    manualInterventionRate: number; // Percentage requiring manual review
  };
  
  // 3. Financial Controls
  financialControls: {
    emergencyStopCapability: boolean; // Stop all payouts
    manualApprovalThresholds: number[]; // Amount-based approval requirements
    riskAssessmentRules: RiskRule[];
    auditTrailMaintenance: boolean;
  };
}
```

### Admin Portal Financial Features

#### 1. Financial Dashboard
```typescript
interface AdminFinancialDashboard {
  revenueMetrics: {
    totalRevenue: number;
    monthlyRevenue: number;
    projectedRevenue: number;
    revenueByPartner: PartnerRevenue[];
  };
  
  commissionMetrics: {
    totalCommissionsPaid: number;
    pendingCommissions: number;
    heldCommissions: number;
    averageCommissionPercentage: number;
  };
  
  operationalMetrics: {
    totalOperationalExpenses: number;
    razorpayFees: number;
    platformCosts: number;
    netProfitMargin: number;
  };
  
  riskMetrics: {
    chargebackRate: number;
    disputeRate: number;
    fraudDetectionRate: number;
    manualReviewRate: number;
  };
}
```

#### 2. Commission Approval Workflow
```typescript
const adminCommissionApproval = {
  // High-value commission approval
  reviewHighValueCommissions: async (threshold: number = 50000) => {
    const highValueCommissions = await getCommissionsAboveThreshold(threshold);
    
    for (const commission of highValueCommissions) {
      const riskAssessment = await performRiskAssessment(commission);
      
      if (riskAssessment.requiresManualReview) {
        await flagForManualReview(commission.id, riskAssessment.riskFactors);
      } else {
        await approveCommissionRelease(commission.id);
      }
    }
  },
  
  // Manual intervention for disputed commissions
  handleDisputedCommissions: async () => {
    const disputedCommissions = await getDisputedCommissions();
    
    for (const commission of disputedCommissions) {
      const investigation = await investigateDispute(commission);
      
      if (investigation.resolution === 'approve') {
        await releaseCommission(commission.id);
      } else if (investigation.resolution === 'reject') {
        await reverseCommission(commission.id, investigation.reason);
      } else {
        await escalateToSeniorAdmin(commission.id);
      }
    }
  }
};
```

#### 3. Financial Reconciliation
```typescript
interface FinancialReconciliation {
  dailyReconciliation: {
    schoolPaymentsReceived: number;
    partnerCommissionsPaid: number;
    operationalExpensesIncurred: number;
    netCashFlow: number;
    discrepancies: Discrepancy[];
  };
  
  monthlyReconciliation: {
    totalRevenue: number;
    totalCommissions: number;
    totalExpenses: number;
    netProfit: number;
    profitMargin: number;
    taxLiability: number;
  };
  
  auditTrail: {
    transactionId: string;
    type: 'revenue' | 'commission' | 'expense';
    amount: number;
    timestamp: Date;
    initiatedBy: string;
    approvedBy?: string;
    status: 'pending' | 'completed' | 'failed';
  }[];
}
```

## 🔄 Cross-Portal Integration Points

### Real-time Financial Updates
```typescript
interface CrossPortalFinancialSync {
  // School payment triggers partner commission calculation
  schoolPaymentToPartnerCommission: {
    trigger: 'payment.captured';
    action: 'calculate_partner_commission';
    delay: 'immediate';
  };
  
  // Partner commission release triggers notifications
  commissionReleaseToNotifications: {
    trigger: 'commission.released';
    action: 'send_partner_notification';
    delay: 'immediate';
  };
  
  // Admin actions trigger stakeholder notifications
  adminActionToStakeholderNotification: {
    trigger: 'admin.manual_intervention';
    action: 'notify_affected_parties';
    delay: 'immediate';
  };
}
```

This comprehensive stakeholder-specific workflow documentation ensures each portal has clear financial processes while maintaining system-wide consistency and risk management.
