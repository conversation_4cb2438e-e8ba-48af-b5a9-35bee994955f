/**
 * Email Integration Verification Test
 *
 * This test verifies that the Resend email service is properly configured
 * and can send billing-related emails to schools.
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Import Resend directly for testing
const { Resend } = require('resend');

// Create a simple email service for testing
class TestEmailService {
  constructor() {
    // Check if API key is available
    if (!process.env.RESEND_API_KEY) {
      console.log('⚠️ RESEND_API_KEY not found in environment variables');
      this.resend = null;
    } else {
      this.resend = new Resend(process.env.RESEND_API_KEY);
    }
    this.defaultFrom = `${process.env.FROM_NAME || 'Schopio'} <${process.env.FROM_EMAIL || '<EMAIL>'}>`;
  }

  async sendEmail(options) {
    if (!this.resend) {
      return { success: false, error: 'Resend API key not configured' };
    }

    try {
      const result = await this.resend.emails.send({
        from: options.from || this.defaultFrom,
        to: options.to,
        subject: options.subject,
        html: options.html,
        replyTo: options.replyTo || process.env.FROM_EMAIL,
        attachments: options.attachments
      });

      console.log('Email sent successfully:', result.data?.id);
      return { success: true, id: result.data?.id };
    } catch (error) {
      console.error('Failed to send email:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async sendPaymentConfirmation(data) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' };
    }

    const html = this.getPaymentConfirmationTemplate(data);

    return this.sendEmail({
      to: data.email,
      subject: `Payment Received - Invoice ${data.invoiceNumber}`,
      html
    });
  }

  async sendInvoiceGenerated(data) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' };
    }

    const html = this.getInvoiceGeneratedTemplate(data);

    return this.sendEmail({
      to: data.email,
      subject: `New Invoice Generated - ${data.invoiceNumber}`,
      html
    });
  }

  async sendPaymentReminder(data) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' };
    }

    const html = this.getPaymentReminderTemplate(data);

    return this.sendEmail({
      to: data.email,
      subject: `Payment Reminder - Invoice ${data.invoiceNumber} Due Soon`,
      html
    });
  }

  async sendOverdueNotice(data) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' };
    }

    const html = this.getOverdueNoticeTemplate(data);

    return this.sendEmail({
      to: data.email,
      subject: `URGENT: Overdue Payment - Invoice ${data.invoiceNumber}`,
      html
    });
  }

  getBaseTemplate(content) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schopio</title>
    <style>
        .btn { background: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block; }
        .btn-success { background: #10b981; }
        .btn-warning { background: #f59e0b; }
        .btn-danger { background: #ef4444; }
        .amount { font-size: 18px; font-weight: bold; color: #2563eb; }
        .invoice-details { background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #2563eb; padding-bottom: 20px;">
        <h1 style="color: #2563eb; margin: 0; font-size: 28px;">Schopio</h1>
        <p style="color: #64748b; margin: 5px 0;">School Management Platform</p>
    </div>

    ${content}

    <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 14px;">
        <p>© 2024 Schopio. All rights reserved.</p>
        <p>This is an automated email. Please do not reply to this email.</p>
    </div>
</body>
</html>`;
  }

  getPaymentConfirmationTemplate(data) {
    const content = `
<h2 style="color: #10b981; margin-bottom: 20px;">Payment Received Successfully</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>We have successfully received your payment for <strong>${data.schoolName || 'your school'}</strong>.</p>

<div class="invoice-details">
    <h3 style="color: #2563eb; margin-top: 0;">Payment Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount Paid:</strong> <span class="amount">₹${data.amount}</span></p>
    <p><strong>Payment Date:</strong> ${new Date().toLocaleDateString('en-IN')}</p>
</div>

<p>Your subscription remains active and all services will continue uninterrupted.</p>

${data.receiptUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.receiptUrl}" class="btn btn-success">Download Receipt</a>
</div>
` : ''}

<p>Thank you for your continued trust in Schopio.</p>

<p>Best regards,<br>
The Schopio Team</p>`;

    return this.getBaseTemplate(content);
  }

  getInvoiceGeneratedTemplate(data) {
    const content = `
<h2 style="color: #2563eb; margin-bottom: 20px;">New Invoice Generated</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>Your monthly invoice for <strong>${data.schoolName || 'your school'}</strong> has been generated and is ready for payment.</p>

<div class="invoice-details">
    <h3 style="color: #2563eb; margin-top: 0;">Invoice Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount:</strong> <span class="amount">₹${data.amount}</span></p>
    <p><strong>Due Date:</strong> ${data.dueDate}</p>
</div>

<p>Please make the payment by the due date to ensure uninterrupted service.</p>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn">Pay Now</a>
</div>
` : ''}

<p>Thank you for choosing Schopio for your school management needs.</p>

<p>Best regards,<br>
The Schopio Team</p>`;

    return this.getBaseTemplate(content);
  }

  getPaymentReminderTemplate(data) {
    const content = `
<h2 style="color: #f59e0b; margin-bottom: 20px;">Payment Due in 3 Days</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>This is a friendly reminder that your monthly subscription payment for <strong>${data.schoolName || 'your school'}</strong> is due in 3 days.</p>

<div class="invoice-details">
    <h3 style="color: #2563eb; margin-top: 0;">Invoice Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount:</strong> <span class="amount">₹${data.amount}</span></p>
    <p><strong>Due Date:</strong> ${data.dueDate}</p>
</div>

<p>To avoid any service interruption, please make your payment before the due date.</p>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn btn-warning">Pay Now</a>
</div>
` : ''}

<p>If you have already made the payment, please ignore this reminder.</p>

<p>Best regards,<br>
The Schopio Team</p>`;

    return this.getBaseTemplate(content);
  }

  getOverdueNoticeTemplate(data) {
    const content = `
<h2 style="color: #ef4444; margin-bottom: 20px;">Payment Overdue</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>Your payment for <strong>${data.schoolName || 'your school'}</strong> is now overdue by ${data.daysOverdue || 0} days.</p>

<div class="invoice-details">
    <h3 style="color: #2563eb; margin-top: 0;">Overdue Invoice Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount:</strong> <span class="amount">₹${data.amount}</span></p>
    <p><strong>Original Due Date:</strong> ${data.dueDate}</p>
    <p><strong>Days Overdue:</strong> ${data.daysOverdue || 0} days</p>
</div>

<p><strong>Important:</strong> Your account will be suspended if payment is not received within 15 days of the due date.</p>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn btn-danger">Pay Now</a>
</div>
` : ''}

<p>If you have any questions or need assistance, please contact our support team immediately.</p>

<p>Best regards,<br>
The Schopio Team</p>`;

    return this.getBaseTemplate(content);
  }
}

const emailService = new TestEmailService();

// Test configuration
const TEST_CONFIG = {
  testEmail: process.env.TEST_EMAIL || '<EMAIL>',
  schoolName: 'Test School',
  contactPerson: 'Test Admin',
  invoiceNumber: 'INV-TEST-001',
  amount: '5000',
  dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('en-IN'),
  paymentUrl: 'https://schopio.orionixtech.com/billing/pay/test'
};

/**
 * Verify environment variables are configured
 */
function verifyEnvironmentConfig() {
  console.log('\n🔍 Verifying Environment Configuration...');

  const requiredVars = [
    'RESEND_API_KEY',
    'FROM_EMAIL',
    'FROM_NAME'
  ];

  const missingVars = [];

  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missingVars.push(varName);
    } else {
      console.log(`✅ ${varName}: ${varName === 'RESEND_API_KEY' ? '***' : process.env[varName]}`);
    }
  }

  if (missingVars.length > 0) {
    console.error(`❌ Missing environment variables: ${missingVars.join(', ')}`);
    console.log('\n📝 To test email integration, please set the following environment variables:');
    console.log('   RESEND_API_KEY=your_resend_api_key');
    console.log('   FROM_EMAIL=<EMAIL>');
    console.log('   FROM_NAME=Schopio');
    console.log('\n💡 You can create a .env.local file with these variables for testing.');
    return false;
  }

  console.log('✅ All required environment variables are configured');
  return true;
}

/**
 * Test basic email sending functionality
 */
async function testBasicEmailSending() {
  console.log('\n📧 Testing Basic Email Sending...');
  
  try {
    const result = await emailService.sendEmail({
      to: TEST_CONFIG.testEmail,
      subject: 'Schopio Email Integration Test',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb;">Email Integration Test</h2>
          <p>This is a test email to verify that the Schopio email service is working correctly.</p>
          <p><strong>Test Time:</strong> ${new Date().toISOString()}</p>
          <p><strong>Service:</strong> Resend API</p>
          <p>If you receive this email, the integration is working properly.</p>
        </div>
      `
    });
    
    if (result.success) {
      console.log(`✅ Basic email sent successfully (ID: ${result.id})`);
      return true;
    } else {
      console.error(`❌ Basic email failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Basic email test error: ${error.message}`);
    return false;
  }
}

/**
 * Test payment confirmation email
 */
async function testPaymentConfirmationEmail() {
  console.log('\n💰 Testing Payment Confirmation Email...');
  
  try {
    const result = await emailService.sendPaymentConfirmation({
      schoolName: TEST_CONFIG.schoolName,
      contactPerson: TEST_CONFIG.contactPerson,
      email: TEST_CONFIG.testEmail,
      invoiceNumber: TEST_CONFIG.invoiceNumber,
      amount: TEST_CONFIG.amount,
      dueDate: TEST_CONFIG.dueDate,
      receiptUrl: 'https://schopio.com/receipts/test-receipt.pdf'
    });
    
    if (result.success) {
      console.log(`✅ Payment confirmation email sent successfully (ID: ${result.id})`);
      return true;
    } else {
      console.error(`❌ Payment confirmation email failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Payment confirmation email test error: ${error.message}`);
    return false;
  }
}

/**
 * Test invoice generated email
 */
async function testInvoiceGeneratedEmail() {
  console.log('\n📄 Testing Invoice Generated Email...');
  
  try {
    const result = await emailService.sendInvoiceGenerated({
      schoolName: TEST_CONFIG.schoolName,
      contactPerson: TEST_CONFIG.contactPerson,
      email: TEST_CONFIG.testEmail,
      invoiceNumber: TEST_CONFIG.invoiceNumber,
      amount: TEST_CONFIG.amount,
      dueDate: TEST_CONFIG.dueDate,
      paymentUrl: TEST_CONFIG.paymentUrl
    });
    
    if (result.success) {
      console.log(`✅ Invoice generated email sent successfully (ID: ${result.id})`);
      return true;
    } else {
      console.error(`❌ Invoice generated email failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Invoice generated email test error: ${error.message}`);
    return false;
  }
}

/**
 * Test payment reminder email
 */
async function testPaymentReminderEmail() {
  console.log('\n⏰ Testing Payment Reminder Email...');
  
  try {
    const result = await emailService.sendPaymentReminder({
      schoolName: TEST_CONFIG.schoolName,
      contactPerson: TEST_CONFIG.contactPerson,
      email: TEST_CONFIG.testEmail,
      invoiceNumber: TEST_CONFIG.invoiceNumber,
      amount: TEST_CONFIG.amount,
      dueDate: TEST_CONFIG.dueDate,
      paymentUrl: TEST_CONFIG.paymentUrl
    });
    
    if (result.success) {
      console.log(`✅ Payment reminder email sent successfully (ID: ${result.id})`);
      return true;
    } else {
      console.error(`❌ Payment reminder email failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Payment reminder email test error: ${error.message}`);
    return false;
  }
}

/**
 * Test overdue notice email
 */
async function testOverdueNoticeEmail() {
  console.log('\n🚨 Testing Overdue Notice Email...');
  
  try {
    const result = await emailService.sendOverdueNotice({
      schoolName: TEST_CONFIG.schoolName,
      contactPerson: TEST_CONFIG.contactPerson,
      email: TEST_CONFIG.testEmail,
      invoiceNumber: TEST_CONFIG.invoiceNumber,
      amount: TEST_CONFIG.amount,
      dueDate: TEST_CONFIG.dueDate,
      daysOverdue: 5,
      paymentUrl: TEST_CONFIG.paymentUrl
    });
    
    if (result.success) {
      console.log(`✅ Overdue notice email sent successfully (ID: ${result.id})`);
      return true;
    } else {
      console.error(`❌ Overdue notice email failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Overdue notice email test error: ${error.message}`);
    return false;
  }
}

/**
 * Run all email integration tests
 */
async function runEmailIntegrationTests() {
  console.log('🚀 Starting Email Integration Verification Tests');
  console.log('='.repeat(60));
  
  // Check environment configuration
  if (!verifyEnvironmentConfig()) {
    console.log('\n❌ Email integration tests failed - missing environment configuration');
    process.exit(1);
  }
  
  const tests = [
    { name: 'Basic Email Sending', fn: testBasicEmailSending },
    { name: 'Payment Confirmation Email', fn: testPaymentConfirmationEmail },
    { name: 'Invoice Generated Email', fn: testInvoiceGeneratedEmail },
    { name: 'Payment Reminder Email', fn: testPaymentReminderEmail },
    { name: 'Overdue Notice Email', fn: testOverdueNoticeEmail }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passedTests++;
      }
      
      // Wait 2 seconds between tests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`❌ Test "${test.name}" failed with error: ${error.message}`);
    }
  }
  
  // Print summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 Email Integration Test Summary');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests} tests`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All email integration tests passed! Email service is working correctly.');
    console.log(`📧 Test emails sent to: ${TEST_CONFIG.testEmail}`);
  } else {
    console.log('\n⚠️ Some email integration tests failed. Please check the configuration and try again.');
  }
  
  return passedTests === totalTests;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runEmailIntegrationTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Email integration test suite failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runEmailIntegrationTests,
  verifyEnvironmentConfig,
  testBasicEmailSending,
  testPaymentConfirmationEmail,
  testInvoiceGeneratedEmail,
  testPaymentReminderEmail,
  testOverdueNoticeEmail
};
