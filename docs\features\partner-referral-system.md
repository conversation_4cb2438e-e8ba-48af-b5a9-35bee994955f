# 🤝 Schopio Partner Referral System Documentation

## 📋 **SYSTEM OVERVIEW**

The Partner Referral System is a comprehensive revenue-sharing platform that allows external partners to refer schools to Schopio and earn commissions based on the schools' subscription payments. The system ensures complete transparency, security, and accurate financial tracking.

## 🎯 **CORE FEATURES**

### 1. **PARTNER MANAGEMENT**
- **Admin-Only Registration**: Only admin users can create partner accounts to prevent unauthorized registrations
- **Separate Authentication**: Partners have dedicated login credentials independent from school users
- **Unique Referral Codes**: Each partner receives a unique, non-transferable 8-character alphanumeric code
- **Complete Profile Management**: Contact details, bank account information, and business details

### 2. **REFERRAL TRACKING SYSTEM**
- **Multi-Point Integration**: Referral codes can be applied during school registration or post-registration
- **Permanent Attribution**: Once a referral code is applied, the school-partner relationship is permanent
- **Validation System**: Real-time validation ensures only active, valid referral codes are accepted
- **Fraud Prevention**: Prevents self-referrals and duplicate referral attempts

### 3. **REVENUE CALCULATION ENGINE**
- **Configurable Profit Sharing**: Admin can set profit sharing percentage (35-50%) per partner or globally
- **Transparent Expense Deduction**: Detailed breakdown of operational expenses per school
- **Real-Time Calculations**: Automatic calculation of partner earnings based on school payments
- **Historical Tracking**: Complete audit trail of all financial calculations

## 💰 **FINANCIAL WORKFLOW**

### **Revenue Calculation Formula**
```
Monthly School Payment: ₹50,000
Less: Operational Expenses
  - Database Hosting: ₹2,000
  - Website Maintenance: ₹3,000
  - Support Costs: ₹3,000
  - Infrastructure: ₹2,000
  Total Expenses: ₹10,000

Net Profit: ₹50,000 - ₹10,000 = ₹40,000
Partner Share (50%): ₹40,000 × 50% = ₹20,000
```

### **Expense Categories**
1. **Database Hosting Charges**: Per-school database hosting costs
2. **Website Maintenance**: Platform maintenance and updates
3. **Support Infrastructure**: Customer support and technical assistance
4. **Operational Overhead**: General business operational costs
5. **Custom Expenses**: Admin-configurable additional expense categories

### **Payment Dependency Model**
- Partner earnings are calculated when schools make payments
- Withdrawals are only processed after corresponding school payments are received
- Pending amounts remain in "pending" status until school payments are confirmed

## 🏦 **WALLET SYSTEM**

### **Wallet Components**
1. **Available Balance**: Ready for withdrawal (from paid school invoices)
2. **Pending Amount**: Earnings from schools with unpaid invoices
3. **Total Lifetime Earnings**: Cumulative earnings since partnership began
4. **Monthly Breakdown**: Detailed monthly earnings from each referred school

### **Transaction Categories**
- **EARNING**: Partner commission from school payment
- **WITHDRAWAL**: Partner withdrawal request
- **ADJUSTMENT**: Admin adjustments (positive/negative)
- **BONUS**: Special bonuses or incentives
- **PENALTY**: Deductions for policy violations

## 🔄 **WITHDRAWAL WORKFLOW**

### **Monthly Withdrawal Process**
1. **Request Submission**: Partners can request withdrawal once per month
2. **Balance Validation**: System verifies available balance vs. requested amount
3. **Payment Dependency Check**: Ensures all corresponding school payments are received
4. **Admin Review**: Admin reviews and approves withdrawal requests
5. **Manual Processing**: Admin processes NEFT/bank transfer manually
6. **Status Update**: Admin updates withdrawal status with transaction details

### **Withdrawal Restrictions**
- Maximum one withdrawal request per month
- Minimum withdrawal amount: ₹1,000
- Maximum withdrawal: Available balance only
- Processing time: 5-7 business days after approval

## 🗄️ **DATABASE SCHEMA DESIGN**

### **Core Tables Structure**

#### **1. partners**
```sql
- id (UUID, Primary Key)
- partner_code (VARCHAR(8), Unique) -- Auto-generated referral code
- email (VARCHAR(255), Unique)
- password_hash (VARCHAR(255))
- name (VARCHAR(255))
- company_name (VARCHAR(255))
- phone (VARCHAR(20))
- address (TEXT)
- bank_account_number (VARCHAR(50), Encrypted)
- bank_ifsc_code (VARCHAR(11))
- bank_account_holder_name (VARCHAR(255))
- profit_share_percentage (DECIMAL(5,2)) -- Individual or NULL for global
- is_active (BOOLEAN, Default: true)
- created_by (UUID, FK: admin_users.id)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### **2. referral_codes**
```sql
- id (UUID, Primary Key)
- partner_id (UUID, FK: partners.id)
- code (VARCHAR(8), Unique)
- is_active (BOOLEAN, Default: true)
- usage_count (INTEGER, Default: 0)
- created_at (TIMESTAMP)
- deactivated_at (TIMESTAMP)
```

#### **3. school_referrals**
```sql
- id (UUID, Primary Key)
- client_id (UUID, FK: clients.id)
- partner_id (UUID, FK: partners.id)
- referral_code_id (UUID, FK: referral_codes.id)
- referred_at (TIMESTAMP)
- referral_source (VARCHAR(20)) -- 'registration', 'profile_update'
- ip_address (VARCHAR(45))
- user_agent (TEXT)
- is_active (BOOLEAN, Default: true)
```

#### **4. operational_expenses**
```sql
- id (UUID, Primary Key)
- category_name (VARCHAR(100))
- description (TEXT)
- amount_per_school (DECIMAL(10,2))
- is_percentage (BOOLEAN, Default: false)
- percentage_value (DECIMAL(5,2))
- is_active (BOOLEAN, Default: true)
- created_by (UUID, FK: admin_users.id)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### **5. partner_earnings**
```sql
- id (UUID, Primary Key)
- partner_id (UUID, FK: partners.id)
- client_id (UUID, FK: clients.id)
- invoice_id (UUID, FK: invoices.id)
- payment_id (UUID, FK: payments.id)
- gross_amount (DECIMAL(10,2)) -- School payment amount
- total_expenses (DECIMAL(10,2)) -- Sum of all expenses
- net_profit (DECIMAL(10,2)) -- Gross - Expenses
- partner_share_percentage (DECIMAL(5,2))
- partner_earning (DECIMAL(10,2)) -- Net profit × percentage
- status (VARCHAR(20)) -- 'pending', 'available', 'withdrawn'
- calculated_at (TIMESTAMP)
- available_at (TIMESTAMP) -- When school payment confirmed
```

#### **6. withdrawal_requests**
```sql
- id (UUID, Primary Key)
- partner_id (UUID, FK: partners.id)
- requested_amount (DECIMAL(10,2))
- available_balance (DECIMAL(10,2)) -- Balance at time of request
- status (VARCHAR(20)) -- 'pending', 'approved', 'processed', 'rejected'
- request_month (DATE) -- YYYY-MM-01 format
- requested_at (TIMESTAMP)
- reviewed_by (UUID, FK: admin_users.id)
- reviewed_at (TIMESTAMP)
- processed_at (TIMESTAMP)
- transaction_reference (VARCHAR(100))
- bank_details_snapshot (JSONB) -- Bank details at time of request
- rejection_reason (TEXT)
```

#### **7. partner_transactions**
```sql
- id (UUID, Primary Key)
- partner_id (UUID, FK: partners.id)
- transaction_type (VARCHAR(20)) -- 'EARNING', 'WITHDRAWAL', 'ADJUSTMENT', 'BONUS', 'PENALTY'
- amount (DECIMAL(10,2))
- description (TEXT)
- reference_id (UUID) -- Links to earnings, withdrawals, etc.
- reference_type (VARCHAR(50))
- balance_before (DECIMAL(10,2))
- balance_after (DECIMAL(10,2))
- created_by (UUID) -- admin_user_id for manual transactions
- created_at (TIMESTAMP)
- metadata (JSONB) -- Additional transaction details
```

## 🔐 **SECURITY MEASURES**

### **Authentication & Authorization**
- Separate JWT tokens for partners with different claims
- Role-based access control (partner vs admin)
- Session management with secure token refresh
- Password encryption using bcrypt with 12 salt rounds

### **Fraud Prevention**
- IP tracking for referral code applications
- Duplicate referral prevention (one partner per school)
- Self-referral detection and blocking
- Suspicious activity monitoring and alerts

### **Data Privacy**
- Partners can only access their own referred schools' data
- Encrypted storage of sensitive bank account information
- Audit trails for all financial transactions
- GDPR compliance for data handling and deletion

### **Financial Security**
- Double-entry bookkeeping for all transactions
- Immutable transaction records with cryptographic hashing
- Real-time balance validation and reconciliation
- Automated fraud detection algorithms

## 📊 **REPORTING & ANALYTICS**

### **Partner Dashboard Metrics**
- Total referred schools count
- Monthly/yearly earnings breakdown
- Conversion rates and performance metrics
- Wallet balance and transaction history
- Expense transparency reports

### **Admin Analytics**
- Partner performance rankings
- Revenue attribution analysis
- Expense optimization insights
- Withdrawal processing metrics
- System-wide financial health reports

## 🚨 **ERROR HANDLING & EDGE CASES**

### **Referral Code Edge Cases**
- Expired or deactivated codes
- Invalid code format validation
- Code usage after partner deactivation
- Duplicate code application attempts

### **Financial Edge Cases**
- School payment failures or refunds
- Partner account deactivation with pending earnings
- Expense configuration changes affecting existing earnings
- Withdrawal requests exceeding available balance

### **System Edge Cases**
- Database transaction failures
- Payment gateway timeouts
- Email notification failures
- Concurrent withdrawal request handling

---

*This documentation serves as the complete reference for the Schopio Partner Referral System implementation.*
