#!/usr/bin/env node

/**
 * COMPREHENSIVE END-TO-END SYSTEM AUDIT
 * Tests the complete payment and discount system across all components
 */

import { Pool } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

class ComprehensiveSystemAuditor {
  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: { rejectUnauthorized: false }
    });
    
    this.auditResults = {
      schoolPortal: { passed: 0, failed: 0, tests: [] },
      adminPortal: { passed: 0, failed: 0, tests: [] },
      emailInvoice: { passed: 0, failed: 0, tests: [] },
      databaseIntegrity: { passed: 0, failed: 0, tests: [] },
      integrationPerformance: { passed: 0, failed: 0, tests: [] },
      overall: { passed: 0, failed: 0, total: 0 }
    };
  }

  async runTest(category, testName, testFunction) {
    console.log(`🧪 [${category.toUpperCase()}] ${testName}`);
    
    try {
      const result = await testFunction();
      this.auditResults[category].passed++;
      this.auditResults[category].tests.push({ name: testName, status: 'PASSED', details: result });
      console.log(`✅ PASSED: ${testName}`);
      return true;
    } catch (error) {
      this.auditResults[category].failed++;
      this.auditResults[category].tests.push({ name: testName, status: 'FAILED', error: error.message });
      console.log(`❌ FAILED: ${testName} - ${error.message}`);
      return false;
    }
  }

  // ===== 1. SCHOOL PORTAL PAYMENT FLOW TESTING =====
  
  async testSchoolPortalPaymentFlow() {
    console.log('\n🏫 === SCHOOL PORTAL PAYMENT FLOW TESTING ===\n');

    await this.runTest('schoolPortal', 'School Billing Data Structure', async () => {
      const result = await this.pool.query(`
        SELECT 
          bs.id,
          bs.monthly_amount,
          bs.has_active_discount,
          bs.current_discount_percentage,
          bs.discount_end_date,
          bs.advance_payment_balance,
          bs.advance_months_remaining,
          c.school_name,
          c.email
        FROM billing_subscriptions bs
        JOIN clients c ON bs.client_id = c.id
        WHERE bs.status = 'active'
        LIMIT 5
      `);
      
      if (result.rows.length === 0) {
        throw new Error('No active subscriptions found for testing');
      }

      const subscription = result.rows[0];
      const requiredFields = ['id', 'monthly_amount', 'school_name', 'email'];
      
      for (const field of requiredFields) {
        if (!subscription[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      return `Found ${result.rows.length} active subscriptions with complete billing data`;
    });

    await this.runTest('schoolPortal', 'Invoice Generation and Status', async () => {
      const result = await this.pool.query(`
        SELECT 
          bi.id,
          bi.invoice_number,
          bi.total_amount,
          bi.status,
          bi.due_date,
          bi.issued_date,
          c.school_name
        FROM billing_invoices bi
        JOIN clients c ON bi.client_id = c.id
        ORDER BY bi.created_at DESC
        LIMIT 10
      `);

      if (result.rows.length === 0) {
        throw new Error('No invoices found in system');
      }

      const invoiceStatuses = [...new Set(result.rows.map(r => r.status))];
      const hasValidStatuses = invoiceStatuses.every(status => 
        ['pending', 'sent', 'paid', 'overdue', 'cancelled'].includes(status)
      );

      if (!hasValidStatuses) {
        throw new Error(`Invalid invoice statuses found: ${invoiceStatuses.join(', ')}`);
      }

      return `Found ${result.rows.length} invoices with valid statuses: ${invoiceStatuses.join(', ')}`;
    });

    await this.runTest('schoolPortal', 'Payment Transaction Recording', async () => {
      const result = await this.pool.query(`
        SELECT 
          bp.id,
          bp.amount,
          bp.status,
          bp.payment_method,
          bp.razorpay_payment_id,
          bi.invoice_number,
          c.school_name
        FROM billing_payments bp
        JOIN billing_invoices bi ON bp.invoice_id = bi.id
        JOIN clients c ON bp.client_id = c.id
        WHERE bp.status = 'succeeded'
        ORDER BY bp.created_at DESC
        LIMIT 5
      `);

      if (result.rows.length === 0) {
        throw new Error('No successful payments found');
      }

      const payment = result.rows[0];
      if (!payment.razorpay_payment_id || !payment.amount) {
        throw new Error('Payment record missing critical data');
      }

      return `Found ${result.rows.length} successful payments with complete transaction data`;
    });

    await this.runTest('schoolPortal', 'Discount Application Display', async () => {
      const result = await this.pool.query(`
        SELECT 
          sd.id,
          sd.discount_percentage,
          sd.remaining_months,
          sd.end_date,
          sd.is_active,
          bs.has_active_discount,
          bs.current_discount_percentage,
          c.school_name
        FROM subscription_discounts sd
        JOIN billing_subscriptions bs ON sd.subscription_id = bs.id
        JOIN clients c ON bs.client_id = c.id
        WHERE sd.is_active = true
        LIMIT 5
      `);

      if (result.rows.length > 0) {
        const discount = result.rows[0];
        
        // Verify discount data consistency
        if (parseFloat(discount.discount_percentage) !== parseFloat(discount.current_discount_percentage)) {
          throw new Error('Discount percentage mismatch between tables');
        }

        if (!discount.has_active_discount) {
          throw new Error('Subscription not marked as having active discount');
        }

        return `Found ${result.rows.length} active discounts with consistent data`;
      } else {
        return 'No active discounts found (this is acceptable)';
      }
    });
  }

  // ===== 2. ADMIN PORTAL DISCOUNT & PAYMENT MANAGEMENT TESTING =====
  
  async testAdminPortalManagement() {
    console.log('\n👨‍💼 === ADMIN PORTAL MANAGEMENT TESTING ===\n');

    await this.runTest('adminPortal', 'Discount Management Schema', async () => {
      const result = await this.pool.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'subscription_discounts'
        ORDER BY ordinal_position
      `);

      const requiredColumns = [
        'id', 'subscription_id', 'discount_percentage', 'discount_duration_months',
        'start_date', 'end_date', 'remaining_months', 'is_active', 'created_by'
      ];

      const existingColumns = result.rows.map(r => r.column_name);
      const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));

      if (missingColumns.length > 0) {
        throw new Error(`Missing required columns: ${missingColumns.join(', ')}`);
      }

      return `All ${requiredColumns.length} required discount management columns present`;
    });

    await this.runTest('adminPortal', 'Commission Configuration Schema', async () => {
      const result = await this.pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'partner_commission_config'
        ORDER BY ordinal_position
      `);

      const requiredColumns = [
        'id', 'partner_id', 'subscription_id', 'commission_percentage', 
        'holding_period_days', 'is_active'
      ];

      const existingColumns = result.rows.map(r => r.column_name);
      const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));

      if (missingColumns.length > 0) {
        throw new Error(`Missing required columns: ${missingColumns.join(', ')}`);
      }

      return `All ${requiredColumns.length} required commission config columns present`;
    });

    await this.runTest('adminPortal', 'Operational Expense Tracking', async () => {
      const result = await this.pool.query(`
        SELECT 
          se.id,
          se.subscription_id,
          se.monthly_operational_cost,
          se.description,
          se.category,
          se.effective_from,
          se.is_active,
          c.school_name
        FROM subscription_expenses se
        JOIN billing_subscriptions bs ON se.subscription_id = bs.id
        JOIN clients c ON bs.client_id = c.id
        WHERE se.is_active = true
        LIMIT 5
      `);

      if (result.rows.length > 0) {
        const expense = result.rows[0];
        if (!expense.monthly_operational_cost || parseFloat(expense.monthly_operational_cost) < 0) {
          throw new Error('Invalid operational cost data');
        }
        return `Found ${result.rows.length} active expense records with valid data`;
      } else {
        return 'No active expense records found (this is acceptable)';
      }
    });

    await this.runTest('adminPortal', 'Commission Transaction Tracking', async () => {
      const result = await this.pool.query(`
        SELECT 
          pct.id,
          pct.partner_id,
          pct.subscription_id,
          pct.school_payment_amount,
          pct.discount_amount,
          pct.operational_expenses,
          pct.commission_amount,
          pct.status,
          p.name as partner_name
        FROM partner_commission_transactions pct
        LEFT JOIN partners p ON pct.partner_id = p.id
        ORDER BY pct.created_at DESC
        LIMIT 5
      `);

      if (result.rows.length > 0) {
        const transaction = result.rows[0];
        
        // Verify commission calculation logic
        const schoolPayment = parseFloat(transaction.school_payment_amount);
        const discountAmount = parseFloat(transaction.discount_amount || '0');
        const operationalExpenses = parseFloat(transaction.operational_expenses || '0');
        const commissionAmount = parseFloat(transaction.commission_amount);

        if (commissionAmount > schoolPayment) {
          throw new Error('Commission amount exceeds school payment');
        }

        return `Found ${result.rows.length} commission transactions with valid calculations`;
      } else {
        return 'No commission transactions found (this is acceptable for new system)';
      }
    });
  }

  // ===== 3. EMAIL & INVOICE SYSTEM VERIFICATION =====
  
  async testEmailInvoiceSystem() {
    console.log('\n📧 === EMAIL & INVOICE SYSTEM VERIFICATION ===\n');

    await this.runTest('emailInvoice', 'Email Service Configuration', async () => {
      const requiredEnvVars = ['RESEND_API_KEY', 'FROM_EMAIL', 'FROM_NAME'];
      const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

      if (missingVars.length > 0) {
        throw new Error(`Missing email environment variables: ${missingVars.join(', ')}`);
      }

      return `All ${requiredEnvVars.length} email configuration variables present`;
    });

    await this.runTest('emailInvoice', 'Invoice Number Generation', async () => {
      const result = await this.pool.query(`
        SELECT 
          invoice_number,
          COUNT(*) as count
        FROM billing_invoices
        WHERE invoice_number IS NOT NULL
        GROUP BY invoice_number
        HAVING COUNT(*) > 1
      `);

      if (result.rows.length > 0) {
        throw new Error(`Found ${result.rows.length} duplicate invoice numbers`);
      }

      const invoiceCount = await this.pool.query(`
        SELECT COUNT(*) as total FROM billing_invoices WHERE invoice_number IS NOT NULL
      `);

      return `All ${invoiceCount.rows[0].total} invoices have unique invoice numbers`;
    });

    await this.runTest('emailInvoice', 'PDF Generation Capability', async () => {
      // Test if we can access the PDF service
      const result = await this.pool.query(`
        SELECT id FROM billing_invoices 
        WHERE status IN ('sent', 'paid') 
        LIMIT 1
      `);

      if (result.rows.length === 0) {
        throw new Error('No invoices available for PDF testing');
      }

      // Check if PDF service files exist
      const fs = await import('fs');
      const pdfServicePath = path.join(__dirname, '..', 'src', 'services', 'pdfInvoiceService.ts');
      
      if (!fs.existsSync(pdfServicePath)) {
        throw new Error('PDF invoice service file not found');
      }

      return 'PDF invoice service is properly configured and accessible';
    });

    await this.runTest('emailInvoice', 'Email Template Structure', async () => {
      const emailServicePath = path.join(__dirname, '..', 'src', 'services', 'emailService.ts');
      const fs = await import('fs');
      
      if (!fs.existsSync(emailServicePath)) {
        throw new Error('Email service file not found');
      }

      const emailServiceContent = fs.readFileSync(emailServicePath, 'utf8');
      
      const requiredMethods = [
        'sendPaymentConfirmation',
        'sendInvoiceGenerated',
        'sendPaymentReminder',
        'sendOverdueNotice'
      ];

      const missingMethods = requiredMethods.filter(method => 
        !emailServiceContent.includes(method)
      );

      if (missingMethods.length > 0) {
        throw new Error(`Missing email methods: ${missingMethods.join(', ')}`);
      }

      return `All ${requiredMethods.length} required email methods are implemented`;
    });
  }

  // ===== 4. DATABASE INTEGRITY & COMMISSION ACCURACY TESTING =====
  
  async testDatabaseIntegrity() {
    console.log('\n🗄️ === DATABASE INTEGRITY & COMMISSION ACCURACY ===\n');

    await this.runTest('databaseIntegrity', 'Foreign Key Constraints', async () => {
      const result = await this.pool.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_name IN (
            'subscription_discounts', 'subscription_expenses', 
            'partner_commission_config', 'partner_commission_transactions',
            'advance_payments', 'billing_transactions'
          )
      `);

      if (result.rows.length < 5) {
        throw new Error('Insufficient foreign key constraints found');
      }

      return `Found ${result.rows.length} foreign key constraints ensuring data integrity`;
    });

    await this.runTest('databaseIntegrity', 'Discount Data Consistency', async () => {
      const result = await this.pool.query(`
        SELECT 
          sd.id,
          sd.discount_percentage as sd_percentage,
          bs.current_discount_percentage as bs_percentage,
          sd.is_active as sd_active,
          bs.has_active_discount as bs_active
        FROM subscription_discounts sd
        JOIN billing_subscriptions bs ON sd.subscription_id = bs.id
        WHERE sd.is_active = true
          AND (
            ABS(CAST(sd.discount_percentage AS DECIMAL) - CAST(bs.current_discount_percentage AS DECIMAL)) > 0.01
            OR bs.has_active_discount != true
          )
      `);

      if (result.rows.length > 0) {
        throw new Error(`Found ${result.rows.length} discount data inconsistencies`);
      }

      return 'All active discounts have consistent data across tables';
    });

    await this.runTest('databaseIntegrity', 'Commission Calculation Accuracy', async () => {
      const result = await this.pool.query(`
        SELECT 
          pct.id,
          pct.school_payment_amount,
          pct.discount_amount,
          pct.operational_expenses,
          pct.commission_percentage,
          pct.commission_amount,
          (
            (CAST(pct.school_payment_amount AS DECIMAL) - 
             CAST(COALESCE(pct.discount_amount, '0') AS DECIMAL) - 
             CAST(COALESCE(pct.operational_expenses, '0') AS DECIMAL)) * 
            CAST(pct.commission_percentage AS DECIMAL) / 100
          ) as calculated_commission
        FROM partner_commission_transactions pct
        WHERE pct.commission_amount IS NOT NULL
        LIMIT 10
      `);

      for (const row of result.rows) {
        const storedCommission = parseFloat(row.commission_amount);
        const calculatedCommission = parseFloat(row.calculated_commission);
        const difference = Math.abs(storedCommission - calculatedCommission);

        if (difference > 0.01) {
          throw new Error(`Commission calculation mismatch: stored=${storedCommission}, calculated=${calculatedCommission}`);
        }
      }

      return `Verified ${result.rows.length} commission calculations are accurate`;
    });

    await this.runTest('databaseIntegrity', 'Advance Payment Tracking', async () => {
      const result = await this.pool.query(`
        SELECT
          ap.id,
          ap.subscription_id,
          ap.months_paid,
          ap.remaining_months,
          ap.amount_per_month,
          bs.advance_months_remaining
        FROM advance_payments ap
        JOIN billing_subscriptions bs ON ap.subscription_id = bs.id
        WHERE ap.remaining_months > 0
          AND ap.remaining_months != bs.advance_months_remaining
      `);

      if (result.rows.length > 0) {
        throw new Error(`Found ${result.rows.length} advance payment inconsistencies`);
      }

      return 'All advance payments have consistent tracking data';
    });
  }

  // ===== 5. INTEGRATION & PERFORMANCE TESTING =====
  
  async testIntegrationPerformance() {
    console.log('\n⚡ === INTEGRATION & PERFORMANCE TESTING ===\n');

    await this.runTest('integrationPerformance', 'Performance Index Effectiveness', async () => {
      const testQueries = [
        {
          name: 'Active Discounts Query',
          query: `
            EXPLAIN (ANALYZE, BUFFERS) 
            SELECT * FROM subscription_discounts 
            WHERE subscription_id = (SELECT id FROM billing_subscriptions LIMIT 1)
              AND is_active = true
          `
        },
        {
          name: 'Commission Status Query',
          query: `
            EXPLAIN (ANALYZE, BUFFERS)
            SELECT * FROM partner_commission_transactions 
            WHERE status = 'eligible' 
            ORDER BY eligible_date
            LIMIT 10
          `
        }
      ];

      let totalExecutionTime = 0;
      let indexUsageCount = 0;

      for (const testQuery of testQueries) {
        try {
          const result = await this.pool.query(testQuery.query);
          const planText = result.rows.map(r => r['QUERY PLAN']).join('\n');
          
          // Check for index usage
          if (planText.includes('Index Scan') || planText.includes('Bitmap Index Scan')) {
            indexUsageCount++;
          }

          // Extract execution time
          const timeMatch = planText.match(/actual time=[\d.]+\.\.[\d.]+/);
          if (timeMatch) {
            const timeStr = timeMatch[0];
            const endTime = parseFloat(timeStr.split('..')[1]);
            totalExecutionTime += endTime;
          }
        } catch (error) {
          console.warn(`Query analysis failed for ${testQuery.name}: ${error.message}`);
        }
      }

      return `${indexUsageCount}/${testQueries.length} queries using indexes, avg execution time: ${(totalExecutionTime / testQueries.length).toFixed(2)}ms`;
    });

    await this.runTest('integrationPerformance', 'Razorpay Integration Configuration', async () => {
      const requiredEnvVars = ['RAZORPAY_KEY_ID', 'RAZORPAY_KEY_SECRET'];
      const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

      if (missingVars.length > 0) {
        throw new Error(`Missing Razorpay environment variables: ${missingVars.join(', ')}`);
      }

      // Check if we have payment records with Razorpay IDs
      const result = await this.pool.query(`
        SELECT COUNT(*) as count 
        FROM billing_payments 
        WHERE razorpay_payment_id IS NOT NULL
      `);

      return `Razorpay integration configured, ${result.rows[0].count} payments processed`;
    });

    await this.runTest('integrationPerformance', 'TypeScript Service Compilation', async () => {
      try {
        const { execSync } = await import('child_process');
        const output = execSync('bunx tsc --noEmit --skipLibCheck', { 
          cwd: path.join(__dirname, '..'),
          stdio: 'pipe',
          encoding: 'utf8'
        });
        
        return 'All TypeScript services compile without errors';
      } catch (error) {
        if (error.stdout && error.stdout.includes('error TS')) {
          const errorCount = (error.stdout.match(/error TS/g) || []).length;
          throw new Error(`${errorCount} TypeScript compilation errors found`);
        }
        throw new Error(`TypeScript compilation failed: ${error.message}`);
      }
    });

    await this.runTest('integrationPerformance', 'Database Connection Pool Health', async () => {
      const startTime = Date.now();
      
      // Test multiple concurrent connections
      const promises = Array.from({ length: 5 }, async (_, i) => {
        const result = await this.pool.query('SELECT $1 as test_id', [i]);
        return result.rows[0].test_id;
      });

      const results = await Promise.all(promises);
      const endTime = Date.now();

      if (results.length !== 5 || !results.every((r, i) => parseInt(r) === i)) {
        throw new Error('Connection pool test failed');
      }

      return `Connection pool healthy, 5 concurrent queries completed in ${endTime - startTime}ms`;
    });
  }

  async generateFinalReport() {
    console.log('\n📊 === COMPREHENSIVE AUDIT REPORT ===\n');

    // Calculate overall statistics
    Object.keys(this.auditResults).forEach(category => {
      if (category !== 'overall') {
        this.auditResults.overall.passed += this.auditResults[category].passed;
        this.auditResults.overall.failed += this.auditResults[category].failed;
      }
    });
    this.auditResults.overall.total = this.auditResults.overall.passed + this.auditResults.overall.failed;

    const successRate = ((this.auditResults.overall.passed / this.auditResults.overall.total) * 100).toFixed(1);

    console.log('🎯 OVERALL SYSTEM HEALTH:');
    console.log(`✅ Passed Tests: ${this.auditResults.overall.passed}`);
    console.log(`❌ Failed Tests: ${this.auditResults.overall.failed}`);
    console.log(`📈 Success Rate: ${successRate}%`);
    console.log(`📊 Total Tests: ${this.auditResults.overall.total}`);

    console.log('\n📋 CATEGORY BREAKDOWN:');
    Object.keys(this.auditResults).forEach(category => {
      if (category !== 'overall') {
        const categoryData = this.auditResults[category];
        const categoryRate = categoryData.passed + categoryData.failed > 0 
          ? ((categoryData.passed / (categoryData.passed + categoryData.failed)) * 100).toFixed(1)
          : '0.0';
        
        console.log(`${category.toUpperCase()}: ${categoryData.passed}/${categoryData.passed + categoryData.failed} (${categoryRate}%)`);
      }
    });

    if (this.auditResults.overall.failed > 0) {
      console.log('\n❌ FAILED TESTS SUMMARY:');
      Object.keys(this.auditResults).forEach(category => {
        if (category !== 'overall') {
          const failedTests = this.auditResults[category].tests.filter(t => t.status === 'FAILED');
          if (failedTests.length > 0) {
            console.log(`\n${category.toUpperCase()}:`);
            failedTests.forEach(test => {
              console.log(`  • ${test.name}: ${test.error}`);
            });
          }
        }
      });
    }

    console.log('\n🎉 AUDIT COMPLETE!');
    
    if (parseFloat(successRate) >= 90) {
      console.log('🟢 SYSTEM STATUS: EXCELLENT - Ready for production deployment');
    } else if (parseFloat(successRate) >= 75) {
      console.log('🟡 SYSTEM STATUS: GOOD - Minor issues to address before production');
    } else {
      console.log('🔴 SYSTEM STATUS: NEEDS ATTENTION - Critical issues must be resolved');
    }

    return parseFloat(successRate) >= 75;
  }

  async runCompleteAudit() {
    console.log('🚀 STARTING COMPREHENSIVE END-TO-END SYSTEM AUDIT');
    console.log('📅 Date:', new Date().toISOString());
    console.log('🎯 Scope: Payment & Discount System Complete Verification\n');

    try {
      // Test database connection
      await this.pool.query('SELECT 1');
      console.log('✅ Database connection established\n');

      // Run all test suites
      await this.testSchoolPortalPaymentFlow();
      await this.testAdminPortalManagement();
      await this.testEmailInvoiceSystem();
      await this.testDatabaseIntegrity();
      await this.testIntegrationPerformance();

      // Generate final report
      const success = await this.generateFinalReport();
      
      await this.pool.end();
      return success;

    } catch (error) {
      console.error('\n💥 AUDIT FAILED:', error);
      await this.pool.end();
      return false;
    }
  }
}

// Run the comprehensive audit
const auditor = new ComprehensiveSystemAuditor();
auditor.runCompleteAudit()
  .then((success) => {
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 Audit crashed:', error);
    process.exit(1);
  });
