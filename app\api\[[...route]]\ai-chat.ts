import { Hono } from 'hono'
import { GoogleGenAI } from '@google/genai'
import { streamText } from 'hono/streaming'

const app = new Hono()

// Initialize Google AI
const ai = new GoogleGenAI({
  apiKey: process.env.GOOGLE_AI_API_KEY
})

// System prompt for Schopio AI assistant
const SYSTEM_PROMPT = `You are an AI assistant for Schopio, a comprehensive school management system. Your role is to help potential customers understand our platform and naturally collect their contact information for lead qualification.

IMPORTANT INSTRUCTIONS:
1. Be conversational, helpful, and professional
2. Focus on understanding their school management needs
3. Naturally collect contact information during conversation:
   - Full name
   - Phone number (with country code)
   - Email address  
   - School name (optional)

4. When you have collected contact information, generate an internal action tag EXACTLY in this format:
   <Action type="contact_info" content="name=<PERSON>, phone=+91-9876543210, email=<EMAIL>, school=ABC School"/>

5. CRITICAL: The action tag is ONLY for internal processing and will be automatically removed from your response before showing to the user
6. After generating the action tag, continue the conversation naturally
7. <PERSON>VER mention the action tag or reference it in your visible response to the user

ABOUT SCHOPIO - COMPREHENSIVE SCHOOL MANAGEMENT PLATFORM:

🏫 COMPLETE SOFTWARE MODULES (11 Integrated Systems):
1. Student Information System (SIS) - Complete student profiles, enrollment, academic records, attendance tracking
2. Academic Management - Curriculum planning, class scheduling, exam management, grade books, report cards
3. Teacher Portal - Lesson planning, attendance marking, grade entry, parent communication, resource sharing
4. Parent Portal - Real-time student progress, attendance monitoring, fee status, direct teacher communication
5. Fee Management - Online fee collection, payment tracking, receipts, financial reporting, scholarship management
6. Library Management - Book cataloging, issue/return tracking, digital library, reading analytics
7. Transport Management - Route planning, vehicle tracking, driver management, student pickup/drop notifications
8. Hostel Management - Room allocation, mess management, visitor tracking, student welfare monitoring
9. Examination System - Online exams, question banks, automated grading, result analytics, certificate generation
10. HR & Payroll - Staff management, attendance, salary processing, leave management, performance tracking
11. Inventory Management - Asset tracking, procurement, maintenance schedules, vendor management

👥 ROLE-BASED ACCESS (11 User Types):
- School Administrators: Complete system control, analytics, reporting, user management
- Principal/Head: Academic oversight, performance analytics, strategic planning tools
- Teachers: Classroom management, student assessment, parent communication
- Students: Learning portal, assignment submission, grade viewing, peer collaboration
- Parents: Child monitoring, communication tools, fee payments, progress tracking
- Accountant: Financial management, fee collection, expense tracking, audit reports
- Librarian: Library operations, book management, reading analytics
- Transport Manager: Route optimization, vehicle maintenance, safety monitoring
- Admission Officer: Enrollment management, application processing, document verification
- Hostel Manager: Accommodation management, student welfare, facility maintenance
- IT Administrator: System configuration, security management, technical support

🤖 AI-POWERED FEATURES (Using Gemma-3.27B Model):
- Smart Analytics: Predictive insights for student performance and institutional growth
- Automated Reporting: AI-generated reports and recommendations
- Intelligent Scheduling: Optimized timetables and resource allocation
- Student Success Prediction: Early intervention alerts for at-risk students
- Personalized Learning Paths: AI-driven curriculum recommendations
- Communication Intelligence: Smart notifications and priority messaging

💻 TECHNICAL SPECIFICATIONS:
- Platform: 100% Web-based application (no mobile app or IoT devices by default)
- Architecture: Modern 3-portal system (Landing Page, Admin Dashboard, School Portal)
- Technology: Built with latest web technologies for speed and reliability
- Security: Enterprise-grade security with role-based access control
- Scalability: Handles institutions from 100 to 10,000+ students
- Integration: API-ready for third-party integrations
- Biometric Support: Available on demand for attendance and access control

📊 IMPLEMENTATION & SUPPORT:
- Quick Setup: Maximum 3 weeks implementation timeline
- Training: Comprehensive staff training and guidance phase
- Support: Dedicated technical support and customer success team
- Data Migration: Seamless transfer from existing systems
- Customization: Tailored features based on institutional needs

💰 VALUE PROPOSITION:
- All-in-One Solution: Complete functionality in basic plan (all 11 modules included)
- Cost Effective: Eliminates need for multiple software subscriptions
- Time Savings: Reduces administrative workload by 70%
- Efficiency Gains: Streamlines all school operations in one platform
- Digital Transformation: Modernizes traditional school management
- Scalable Growth: Grows with your institution's needs

🎯 CONVERSATION STRATEGY:
1. Greet warmly and ask about their current school management challenges
2. Identify pain points: manual processes, communication gaps, time wastage, scattered data
3. Naturally ask for their name to personalize the conversation
4. Ask for contact details to send relevant information and schedule demo
5. Collect school name and size for tailored recommendations
6. Generate action tag when you have sufficient contact info (name + phone/email)
7. Continue helping with specific questions about modules and features

🧠 PSYCHOLOGICAL TRIGGERS TO USE:
- Problem-Solution Focus: "How much time do you spend on manual attendance every day?"
- Social Proof: "Schools like yours typically save 15+ hours per week with our system"
- Urgency: "Many schools are implementing digital solutions this academic year"
- Authority: "Our AI-powered analytics help principals make data-driven decisions"
- Scarcity: "We're currently onboarding select schools for our premium support program"

💬 CONVERSATION GUIDELINES:
- Be consultative, not salesy - focus on solving their problems
- Ask one specific question at a time to understand their needs
- Keep responses short (2-3 sentences max) and benefit-focused
- Use "you" and "your school" to make it personal
- Address specific pain points with relevant Schopio modules
- Create urgency around digital transformation and efficiency gains
- Always end with a question to keep the conversation flowing

📋 COMMON PAIN POINTS & SCHOPIO SOLUTIONS:
- Manual Attendance → Automated attendance with real-time parent notifications
- Paper-based Fee Collection → Online payment gateway with automated receipts
- Poor Parent Communication → Real-time updates and direct messaging portal
- Scattered Student Data → Centralized student information system with analytics
- Time-consuming Report Cards → Automated grade calculation and report generation
- Library Management Issues → Digital cataloging with barcode scanning and analytics
- Transport Safety Concerns → GPS tracking with pickup/drop notifications to parents
- Exam Management Chaos → Online examination system with automated grading
- HR & Payroll Complexity → Integrated staff management with automated salary processing
- Inventory Tracking Problems → Asset management with maintenance scheduling

🎯 KEY SELLING POINTS TO EMPHASIZE:
- "All 11 modules included in basic plan - no hidden costs or module restrictions"
- "70% reduction in administrative workload within first month"
- "Real-time parent engagement increases satisfaction by 85%"
- "AI-powered insights help identify at-risk students early"
- "Complete implementation in just 3 weeks with full training"
- "Web-based platform works on any device - no app downloads needed"
- "Enterprise-grade security with role-based access for all users"
- "Seamless data migration from your current system"

Remember: Focus on their specific challenges and how Schopio transforms their daily operations. Be helpful, not pushy!
📝 RESPONSE FORMATTING GUIDELINES:
- Use clear paragraphs with line breaks for readability
- Use bullet points (•) for listing features or benefits
- Use numbered lists (1., 2., 3.) for step-by-step processes
- Keep sentences short and conversational
- Use emojis sparingly for emphasis (📚 for education, 💡 for insights, ⚡ for efficiency)
- Break up long responses into digestible chunks
- Use bold text for important points when needed
- Always end with a clear question to continue the conversation

EXAMPLE FORMATTING:
"Hi! I'd love to help you streamline your school operations.

Here's how Schopio can transform your daily workflow:

• **Student Management** - Centralized profiles with real-time attendance
• **Parent Communication** - Instant notifications and progress updates
• **Fee Collection** - Automated online payments with receipts

Most schools see a 70% reduction in administrative time within the first month.

What's your biggest challenge with your current system - is it manual attendance, parent communication, or something else?"

Remember: Format responses to be scannable and easy to read, like a professional conversation.
### keep the response short, clean and understandable.
### always try to dig out the contact info from the user, that you can atlest send some info to admin.
### Use psychological tricks while conversations that they build a trust and share info with us.
`

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Simple conversation logging for monitoring (in production, use proper logging service)
function logConversation(clientIP: string, message: string, response: string) {
  if (process.env.NODE_ENV === 'production') {
    console.log(`[CHAT LOG] ${new Date().toISOString()} | IP: ${clientIP} | User: ${message.substring(0, 100)} | Bot: ${response.substring(0, 100)}`)
  }
}

// Rate limiting function
function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const windowMs = 60000 // 1 minute
  const maxRequests = 10 // 10 requests per minute
  
  const record = rateLimitStore.get(ip)
  
  if (!record || now > record.resetTime) {
    rateLimitStore.set(ip, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (record.count >= maxRequests) {
    return false
  }
  
  record.count++
  return true
}

// Input validation and sanitization
function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/[<>]/g, '') // Remove < and > characters
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .substring(0, 1000) // Limit length
}

// Validate conversation history
function validateConversationHistory(history: any[]): boolean {
  if (!Array.isArray(history)) return false
  if (history.length > 50) return false // Limit conversation length

  return history.every(msg =>
    msg &&
    typeof msg === 'object' &&
    typeof msg.type === 'string' &&
    typeof msg.content === 'string' &&
    ['user', 'bot'].includes(msg.type) &&
    msg.content.length <= 2000
  )
}

// Content filtering for inappropriate content
function containsInappropriateContent(text: string): boolean {
  const inappropriatePatterns = [
    /\b(hack|exploit|vulnerability|injection|xss|csrf)\b/i,
    /\b(password|token|secret|key)\s*[:=]/i,
    /<\s*script/i,
    /javascript\s*:/i
  ]

  return inappropriatePatterns.some(pattern => pattern.test(text))
}

// Action tag parser for extracting contact information
interface ContactInfo {
  name?: string
  phone?: string
  email?: string
  school?: string
}

function parseActionTags(text: string): { cleanText: string; contactInfo: ContactInfo | null } {
  // Multiple regex patterns to catch different action tag formats
  const actionTagPatterns = [
    /<Action\s+type="contact_info"\s+content="([^"]+)"\s*\/>/gi,
    /<Action\s+type="contact_info"\s+content="([^"]*)">/gi,
    /<Action[^>]*type="contact_info"[^>]*content="([^"]*)"[^>]*\/?>/gi,
    /<Action[^>]*>/gi // Catch any remaining action tags
  ]

  let contactInfo: ContactInfo | null = null
  let cleanText = text

  // Try each pattern
  for (const pattern of actionTagPatterns) {
    let match
    while ((match = pattern.exec(text)) !== null) {
      if (match[1]) { // If we have content to parse
        const content = match[1]
        const parsedInfo: ContactInfo = {}

        // Parse the content string (format: "name=John Doe, phone=+91-9876543210, email=<EMAIL>, school=ABC School")
        const pairs = content.split(',').map(pair => pair.trim())

        for (const pair of pairs) {
          const [key, value] = pair.split('=').map(s => s.trim())
          if (key && value) {
            switch (key.toLowerCase()) {
              case 'name':
                parsedInfo.name = value
                break
              case 'phone':
                parsedInfo.phone = value
                break
              case 'email':
                parsedInfo.email = value
                break
              case 'school':
                parsedInfo.school = value
                break
            }
          }
        }

        // Only set contactInfo if we have at least name and either phone or email
        if (parsedInfo.name && (parsedInfo.phone || parsedInfo.email)) {
          contactInfo = parsedInfo
        }
      }

      // Remove the action tag from the text (so it's not visible to user)
      cleanText = cleanText.replace(match[0], '')
    }
  }

  return { cleanText: cleanText.trim(), contactInfo }
}

// Function to submit lead to existing API
async function submitLead(contactInfo: ContactInfo): Promise<boolean> {
  try {
    const leadData = {
      contactPerson: contactInfo.name || '',
      email: contactInfo.email || '',
      phone: contactInfo.phone || '',
      schoolName: contactInfo.school || '',
      source: 'ai_chat',
      notes: 'Lead collected through AI chatbot conversation'
    }

    // Submit to existing leads API (using internal API call)
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const response = await fetch(`${baseUrl}/api/leads`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(leadData)
    })

    return response.ok
  } catch (error) {
    console.error('Error submitting lead:', error)
    return false
  }
}

// AI Chat endpoint with streaming
app.post('/chat', async (c) => {
  try {
    // Security headers
    c.header('X-Content-Type-Options', 'nosniff')
    c.header('X-Frame-Options', 'DENY')
    c.header('X-XSS-Protection', '1; mode=block')

    // Rate limiting
    const clientIP = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    if (!checkRateLimit(clientIP)) {
      return c.json({ error: 'Rate limit exceeded. Please try again later.' }, 429)
    }

    const body = await c.req.json()
    const { message, conversationHistory = [] } = body

    // Input validation
    if (!message || typeof message !== 'string') {
      return c.json({ error: 'Invalid message format' }, 400)
    }

    // Validate conversation history
    if (!validateConversationHistory(conversationHistory)) {
      return c.json({ error: 'Invalid conversation history format' }, 400)
    }

    // Sanitize input
    const sanitizedMessage = sanitizeInput(message)
    if (sanitizedMessage.length === 0) {
      return c.json({ error: 'Message cannot be empty' }, 400)
    }

    // Content filtering
    if (containsInappropriateContent(sanitizedMessage)) {
      return c.json({ error: 'Message contains inappropriate content' }, 400)
    }

    // Build conversation context
    const conversationContext = conversationHistory
      .map((msg: any) => `${msg.type === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
      .join('\n')

    const fullPrompt = `${SYSTEM_PROMPT}

Previous conversation:
${conversationContext}

User: ${sanitizedMessage}`

    // Generate streaming response using Google GenAI
    const response = await ai.models.generateContentStream({
      model: 'gemma-3-27b-it',
      contents: fullPrompt,
      config: {
        temperature: 0.6,
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 2048,
      }
    })

    // Stream the response with real-time character-by-character streaming
    return streamText(c, async (stream) => {
      try {
        let fullResponse = ''
        let streamedText = ''
        let actionTagBuffer = ''
        let insideActionTag = false

        // Stream chunks in real-time
        for await (const chunk of response) {
          if (chunk.text) {
            fullResponse += chunk.text

            // Process each character for real-time streaming
            for (const char of chunk.text) {
              if (char === '<' && !insideActionTag) {
                // Potential start of action tag
                actionTagBuffer = '<'
                insideActionTag = true
              } else if (insideActionTag) {
                actionTagBuffer += char

                // Check if action tag is complete
                if (char === '>' && actionTagBuffer.includes('Action')) {
                  // Action tag complete, don't stream it
                  insideActionTag = false
                  actionTagBuffer = ''
                } else if (char === '>' && !actionTagBuffer.includes('Action')) {
                  // Not an action tag, stream the buffer
                  streamedText += actionTagBuffer
                  await stream.write(actionTagBuffer)
                  insideActionTag = false
                  actionTagBuffer = ''
                }
              } else {
                // Regular character, stream immediately
                streamedText += char
                await stream.write(char)

                // Add small delay for typing effect (optional)
                await new Promise(resolve => setTimeout(resolve, 10))
              }
            }
          }
        }

        // Parse action tags from the complete response for lead submission
        const { cleanText, contactInfo } = parseActionTags(fullResponse)

        // Submit lead if contact info was extracted
        if (contactInfo) {
          const leadSubmitted = await submitLead(contactInfo)
          if (leadSubmitted) {
            console.log('Lead submitted successfully:', contactInfo)
            // Add success message
            const successMsg = '\n\nThank you! I\'ve sent your details to our team. Someone will contact you within 24 hours.'
            await stream.write(successMsg)
          } else {
            console.error('Failed to submit lead:', contactInfo)
          }
        }

        // Log conversation for monitoring
        const clientIP = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
        logConversation(clientIP, sanitizedMessage, cleanText)

      } catch (error) {
        console.error('Streaming error:', error)
        await stream.write('Sorry, I encountered an error. Please try again.')
      }
    })

  } catch (error) {
    // Log error with timestamp and client info for monitoring
    const clientIP = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    console.error(`[${new Date().toISOString()}] AI Chat error from ${clientIP}:`, error)

    // Return generic error message to avoid information leakage
    return c.json({
      error: 'Internal server error. Please try again later.'
    }, 500)
  }
})

export default app
