import { db } from '@/src/db'
import {
  partnerCommissionConfig,
  partnerCommissionTransactions,
  subscriptionExpenses,
  billingTransactions,
  billingSubscriptions,
  partners,
  clients
} from '@/src/db/schema'
import { eq, and, lte, sql, desc } from 'drizzle-orm'
import { discountManagementService } from './discountManagementService'

export interface CommissionCalculation {
  commissionId: string
  schoolPayment: number
  discountAmount: number
  operationalExpenses: number
  profitAmount: number
  commissionPercentage: number
  commissionAmount: number
  penaltyAmount: number
  holdUntilDate: Date
  eligibleDate: Date
  status: string
}

export interface CommissionOverview {
  totalEarningsThisMonth: number
  totalEarningsAllTime: number
  pendingAmount: number
  eligibleForPayout: number
  averageCommissionPerSchool: number
  activeSchoolsCount: number
}

export interface EligiblePayout {
  partnerId: string
  partnerName: string
  totalEligibleAmount: number
  oldestEligibleDate: Date
  transactionCount: number
  schoolsCount: number
}

class CommissionCalculationService {
  /**
   * Calculate commission for a payment
   */
  async calculateCommission(
    paymentId: string,
    partnerId: string,
    subscriptionId: string
  ): Promise<CommissionCalculation> {
    try {
      // Get payment details
      const [payment] = await db.select({
        id: billingTransactions.id,
        originalAmount: billingTransactions.originalAmount,
        discountAmount: billingTransactions.discountAmount,
        amount: billingTransactions.amount,
        totalAmount: billingTransactions.totalAmount,
        penaltyAmount: billingTransactions.penaltyAmount
      })
      .from(billingTransactions)
      .where(eq(billingTransactions.id, paymentId))
      .limit(1)

      if (!payment) {
        throw new Error('Payment not found')
      }

      // Get operational expenses
      const expenses = await discountManagementService.getOperationalExpenses(subscriptionId)
      const operationalExpenses = expenses?.monthlyOperationalCost || 0

      // Get commission configuration
      const [commissionConfig] = await db.select({
        commissionPercentage: partnerCommissionConfig.commissionPercentage,
        holdingPeriodDays: partnerCommissionConfig.holdingPeriodDays
      })
      .from(partnerCommissionConfig)
      .where(and(
        eq(partnerCommissionConfig.partnerId, partnerId),
        eq(partnerCommissionConfig.subscriptionId, subscriptionId),
        eq(partnerCommissionConfig.isActive, true)
      ))
      .limit(1)

      if (!commissionConfig) {
        throw new Error('Commission configuration not found')
      }

      // Calculate commission (excluding penalties from partner commission)
      const basePayment = parseFloat(payment.totalAmount) - (parseFloat(payment.penaltyAmount || '0'))
      const discountAmount = parseFloat(payment.discountAmount || '0')
      const profitAmount = basePayment - discountAmount - operationalExpenses
      const commissionAmount = Math.max(0, (profitAmount * parseFloat(commissionConfig.commissionPercentage)) / 100)

      // Determine holding period
      const holdUntilDate = new Date()
      const holdingDays = commissionConfig.holdingPeriodDays || 0
      holdUntilDate.setDate(holdUntilDate.getDate() + holdingDays)

      // Create commission transaction
      const [commissionTransaction] = await db.insert(partnerCommissionTransactions)
        .values({
          partnerId,
          subscriptionId,
          paymentId,
          schoolPaymentAmount: basePayment.toString(),
          discountAmount: discountAmount.toString(),
          operationalExpenses: operationalExpenses.toString(),
          profitAmount: profitAmount.toString(),
          commissionPercentage: commissionConfig.commissionPercentage,
          commissionAmount: commissionAmount.toString(),
          penaltyAmount: (parseFloat(payment.penaltyAmount || '0')).toString(),
          status: holdingDays > 0 ? 'held' : 'eligible',
          holdUntilDate: holdingDays > 0 ? holdUntilDate.toISOString().split('T')[0] : null,
          eligibleDate: holdingDays > 0 ? holdUntilDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
        })
        .returning()

      return {
        commissionId: commissionTransaction.id,
        schoolPayment: basePayment,
        discountAmount,
        operationalExpenses,
        profitAmount,
        commissionPercentage: parseFloat(commissionConfig.commissionPercentage),
        commissionAmount,
        penaltyAmount: parseFloat(payment.penaltyAmount || '0'),
        holdUntilDate,
        eligibleDate: holdingDays > 0 ? holdUntilDate : new Date(),
        status: commissionTransaction.status || 'pending'
      }

    } catch (error) {
      console.error('Error calculating commission:', error)
      throw error
    }
  }

  /**
   * Process eligible commissions (daily job)
   */
  async processEligibleCommissions(): Promise<number> {
    try {
      const today = new Date().toISOString().split('T')[0]

      const result = await db.update(partnerCommissionTransactions)
        .set({
          status: 'eligible',
          updatedAt: new Date()
        })
        .where(and(
          eq(partnerCommissionTransactions.status, 'held'),
          lte(partnerCommissionTransactions.holdUntilDate, today)
        ))

      return result.rowCount || 0

    } catch (error) {
      console.error('Error processing eligible commissions:', error)
      throw error
    }
  }

  /**
   * Get partner commission overview
   */
  async getPartnerCommissionOverview(partnerId: string): Promise<CommissionOverview> {
    try {
      // Get this month's earnings
      const currentMonth = new Date()
      currentMonth.setDate(1)
      const [thisMonthStats] = await db.select({
        totalEarningsThisMonth: sql<number>`COALESCE(SUM(${partnerCommissionTransactions.commissionAmount}), 0)`
      })
      .from(partnerCommissionTransactions)
      .where(and(
        eq(partnerCommissionTransactions.partnerId, partnerId),
        sql`${partnerCommissionTransactions.createdAt} >= ${currentMonth}`
      ))

      // Get all-time earnings
      const [allTimeStats] = await db.select({
        totalEarningsAllTime: sql<number>`COALESCE(SUM(${partnerCommissionTransactions.commissionAmount}), 0)`
      })
      .from(partnerCommissionTransactions)
      .where(eq(partnerCommissionTransactions.partnerId, partnerId))

      // Get pending amount (held + eligible)
      const [pendingStats] = await db.select({
        pendingAmount: sql<number>`COALESCE(SUM(${partnerCommissionTransactions.commissionAmount}), 0)`
      })
      .from(partnerCommissionTransactions)
      .where(and(
        eq(partnerCommissionTransactions.partnerId, partnerId),
        sql`${partnerCommissionTransactions.status} IN ('held', 'eligible')`
      ))

      // Get eligible for payout
      const [eligibleStats] = await db.select({
        eligibleForPayout: sql<number>`COALESCE(SUM(${partnerCommissionTransactions.commissionAmount}), 0)`
      })
      .from(partnerCommissionTransactions)
      .where(and(
        eq(partnerCommissionTransactions.partnerId, partnerId),
        eq(partnerCommissionTransactions.status, 'eligible')
      ))

      // Get active schools count and average commission
      const [schoolStats] = await db.select({
        activeSchoolsCount: sql<number>`COUNT(DISTINCT ${partnerCommissionTransactions.subscriptionId})`,
        averageCommissionPerSchool: sql<number>`COALESCE(AVG(${partnerCommissionTransactions.commissionAmount}), 0)`
      })
      .from(partnerCommissionTransactions)
      .where(eq(partnerCommissionTransactions.partnerId, partnerId))

      return {
        totalEarningsThisMonth: thisMonthStats.totalEarningsThisMonth || 0,
        totalEarningsAllTime: allTimeStats.totalEarningsAllTime || 0,
        pendingAmount: pendingStats.pendingAmount || 0,
        eligibleForPayout: eligibleStats.eligibleForPayout || 0,
        averageCommissionPerSchool: schoolStats.averageCommissionPerSchool || 0,
        activeSchoolsCount: schoolStats.activeSchoolsCount || 0
      }

    } catch (error) {
      console.error('Error getting partner commission overview:', error)
      return {
        totalEarningsThisMonth: 0,
        totalEarningsAllTime: 0,
        pendingAmount: 0,
        eligibleForPayout: 0,
        averageCommissionPerSchool: 0,
        activeSchoolsCount: 0
      }
    }
  }

  /**
   * Get eligible payouts for admin
   */
  async getEligiblePayouts(): Promise<EligiblePayout[]> {
    try {
      const eligiblePayouts = await db.select({
        partnerId: partnerCommissionTransactions.partnerId,
        partnerName: partners.name,
        totalEligibleAmount: sql<number>`SUM(CAST(${partnerCommissionTransactions.commissionAmount} AS DECIMAL))`,
        oldestEligibleDate: sql<Date>`MIN(${partnerCommissionTransactions.eligibleDate})`,
        transactionCount: sql<number>`COUNT(*)`,
        schoolsCount: sql<number>`COUNT(DISTINCT ${partnerCommissionTransactions.subscriptionId})`
      })
      .from(partnerCommissionTransactions)
      .innerJoin(partners, eq(partnerCommissionTransactions.partnerId, partners.id))
      .where(eq(partnerCommissionTransactions.status, 'eligible'))
      .groupBy(partnerCommissionTransactions.partnerId, partners.name)
      .orderBy(sql`SUM(CAST(${partnerCommissionTransactions.commissionAmount} AS DECIMAL)) DESC`)

      return eligiblePayouts.map(payout => ({
        partnerId: payout.partnerId!,
        partnerName: payout.partnerName,
        totalEligibleAmount: payout.totalEligibleAmount,
        oldestEligibleDate: payout.oldestEligibleDate,
        transactionCount: payout.transactionCount,
        schoolsCount: payout.schoolsCount
      }))

    } catch (error) {
      console.error('Error getting eligible payouts:', error)
      return []
    }
  }

  /**
   * Process manual payout
   */
  async processManualPayout(
    partnerId: string,
    payoutAmount: number,
    payoutMethod: string,
    transactionReference: string,
    payoutNotes?: string
  ): Promise<{ processedTransactions: number; totalAmount: number }> {
    try {
      // Get eligible transactions for this partner
      const eligibleTransactions = await db.select({
        id: partnerCommissionTransactions.id,
        commissionAmount: partnerCommissionTransactions.commissionAmount
      })
      .from(partnerCommissionTransactions)
      .where(and(
        eq(partnerCommissionTransactions.partnerId, partnerId),
        eq(partnerCommissionTransactions.status, 'eligible')
      ))
      .orderBy(partnerCommissionTransactions.eligibleDate)

      if (eligibleTransactions.length === 0) {
        throw new Error('No eligible transactions found for payout')
      }

      // Calculate total eligible amount
      const totalEligibleAmount = eligibleTransactions.reduce(
        (sum, transaction) => sum + parseFloat(transaction.commissionAmount),
        0
      )

      if (payoutAmount > totalEligibleAmount) {
        throw new Error('Payout amount exceeds eligible amount')
      }

      // Process payout for transactions up to the payout amount
      let remainingAmount = payoutAmount
      const transactionIds: string[] = []

      for (const transaction of eligibleTransactions) {
        if (remainingAmount <= 0) break

        const transactionAmount = parseFloat(transaction.commissionAmount)
        const amountToPay = Math.min(remainingAmount, transactionAmount)
        transactionIds.push(transaction.id)
        remainingAmount -= amountToPay

        if (amountToPay === transactionAmount) {
          // Full payment
          await db.update(partnerCommissionTransactions)
            .set({
              status: 'paid',
              paidDate: new Date().toISOString().split('T')[0],
              payoutMethod,
              transactionReference,
              payoutAmount: amountToPay.toString(),
              payoutNotes,
              updatedAt: new Date()
            })
            .where(eq(partnerCommissionTransactions.id, transaction.id))
        }
      }

      return {
        processedTransactions: transactionIds.length,
        totalAmount: payoutAmount - remainingAmount
      }

    } catch (error) {
      console.error('Error processing manual payout:', error)
      throw error
    }
  }

  /**
   * Get overdue schools for a partner
   */
  async getOverdueSchoolsForPartner(partnerId: string): Promise<Array<{
    schoolName: string
    subscriptionId: string
    daysOverdue: number
    overdueAmount: number
    affectedCommission: number
    lastPaymentDate: Date | null
  }>> {
    try {
      const overdueSchools = await db.select({
        schoolName: clients.schoolName,
        subscriptionId: billingSubscriptions.id,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        dueDate: billingSubscriptions.dueDate,
        lastPaymentDate: billingSubscriptions.lastPaymentDate,
        commissionPercentage: partnerCommissionConfig.commissionPercentage
      })
      .from(billingSubscriptions)
      .innerJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .innerJoin(partnerCommissionConfig, and(
        eq(partnerCommissionConfig.subscriptionId, billingSubscriptions.id),
        eq(partnerCommissionConfig.partnerId, partnerId),
        eq(partnerCommissionConfig.isActive, true)
      ))
      .where(and(
        eq(billingSubscriptions.paymentStatus, 'overdue'),
        sql`${billingSubscriptions.dueDate} < CURRENT_DATE`
      ))

      return overdueSchools.map(school => {
        const daysOverdue = school.dueDate ? Math.floor(
          (new Date().getTime() - new Date(school.dueDate).getTime()) / (1000 * 60 * 60 * 24)
        ) : 0
        const affectedCommission = (parseFloat(school.monthlyAmount) * parseFloat(school.commissionPercentage)) / 100

        return {
          schoolName: school.schoolName,
          subscriptionId: school.subscriptionId,
          daysOverdue,
          overdueAmount: parseFloat(school.monthlyAmount),
          affectedCommission,
          lastPaymentDate: school.lastPaymentDate ? new Date(school.lastPaymentDate) : null
        }
      })

    } catch (error) {
      console.error('Error getting overdue schools for partner:', error)
      return []
    }
  }
}

export const commissionCalculationService = new CommissionCalculationService()
