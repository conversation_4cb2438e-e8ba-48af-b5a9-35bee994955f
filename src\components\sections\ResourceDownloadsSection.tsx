'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Download, 
  FileText, 
  Calculator, 
  CheckSquare, 
  Shield, 
  BookOpen,
  Users,
  TrendingUp,
  ArrowRight,
  Star
} from 'lucide-react'

const ResourceDownloadsSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const resources = [
    {
      icon: FileText,
      title: "Complete Feature Guide",
      description: "Comprehensive 24-page guide covering all 8+ modules with screenshots and use cases",
      format: "PDF • 2.4 MB",
      downloadCount: "5,200+ downloads",
      benefits: [
        "Detailed module breakdowns",
        "Real school examples", 
        "Implementation roadmap",
        "Feature comparison charts"
      ],
      color: "from-blue-500 to-blue-600",
      popular: true
    },
    {
      icon: Calculator,
      title: "ROI Calculator Spreadsheet",
      description: "Interactive Excel calculator to determine your school&apos;s potential savings and ROI",
      format: "Excel • 850 KB",
      downloadCount: "3,800+ downloads",
      benefits: [
        "Customizable for your school size",
        "Cost-benefit analysis",
        "5-year projection models",
        "Comparison with competitors"
      ],
      color: "from-emerald-500 to-emerald-600",
      popular: false
    },
    {
      icon: CheckSquare,
      title: "Implementation Checklist",
      description: "Step-by-step 14-day implementation checklist with timelines and responsibilities",
      format: "PDF • 1.2 MB",
      downloadCount: "4,100+ downloads",
      benefits: [
        "Day-by-day action items",
        "Staff responsibility matrix",
        "Data migration checklist",
        "Go-live verification steps"
      ],
      color: "from-purple-500 to-purple-600",
      popular: false
    },
    {
      icon: Shield,
      title: "Security & Compliance Whitepaper",
      description: "Detailed security architecture, compliance certifications, and data protection measures",
      format: "PDF • 3.1 MB",
      downloadCount: "2,900+ downloads",
      benefits: [
        "ISO 27001 certification details",
        "GDPR compliance framework",
        "Data encryption standards",
        "Security audit reports"
      ],
      color: "from-red-500 to-red-600",
      popular: false
    }
  ]

  const additionalResources = [
    {
      icon: BookOpen,
      title: "School Management Best Practices Guide",
      description: "Industry best practices for modern school administration"
    },
    {
      icon: Users,
      title: "Staff Training Templates",
      description: "Ready-to-use training materials for your team"
    },
    {
      icon: TrendingUp,
      title: "Performance Metrics Dashboard",
      description: "KPI templates for measuring school efficiency"
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Download className="w-4 h-4" />
            Free Resources
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Download Free 
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Implementation Resources</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Get everything you need to evaluate, plan, and implement Schopio successfully. No registration required - instant downloads.
          </p>
        </motion.div>

        {/* Main Resources */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 gap-8 mb-16"
        >
          {resources.map((resource, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative"
            >
              {resource.popular && (
                <div className="absolute -top-3 left-6 z-10">
                  <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-slate-900 px-3 py-1 rounded-full text-xs font-bold shadow-lg flex items-center gap-1">
                    <Star className="w-3 h-3 fill-current" />
                    MOST POPULAR
                  </div>
                </div>
              )}
              
              <Card className="h-full bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <CardContent padding="lg">
                  <div className="flex items-start gap-4 mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${resource.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <resource.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-slate-900 mb-2">{resource.title}</h3>
                      <p className="text-slate-600 text-sm mb-3">{resource.description}</p>
                      <div className="flex items-center gap-4 text-xs text-slate-500">
                        <span className="font-medium">{resource.format}</span>
                        <span>•</span>
                        <span>{resource.downloadCount}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2 mb-6">
                    <h4 className="text-sm font-semibold text-slate-700">What&apos;s Included:</h4>
                    {resource.benefits.map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-center gap-2 text-sm text-slate-600">
                        <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full flex-shrink-0" />
                        <span>{benefit}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    size="lg"
                    icon={Download}
                    iconPosition="right"
                    className={`w-full bg-gradient-to-r ${resource.color} hover:opacity-90 text-white font-bold py-3`}
                  >
                    Download Free
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Resources */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-slate-900 text-center mb-8">Additional Resources</h3>
          <div className="grid md:grid-cols-3 gap-6">
            {additionalResources.map((resource, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-white border border-slate-200 shadow-md hover:shadow-lg transition-all duration-300 group cursor-pointer">
                  <CardContent padding="lg">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-gradient-to-br from-slate-100 to-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <resource.icon className="w-6 h-6 text-blue-600" />
                      </div>
                      <h4 className="text-lg font-bold text-slate-900 mb-2">{resource.title}</h4>
                      <p className="text-sm text-slate-600 mb-4">{resource.description}</p>
                      <div className="text-blue-600 text-sm font-medium flex items-center justify-center gap-1 group-hover:gap-2 transition-all">
                        <span>Coming Soon</span>
                        <ArrowRight className="w-4 h-4" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="text-center"
        >
          <Card className="bg-gradient-to-r from-blue-600 to-emerald-600 border-0 max-w-2xl mx-auto">
            <CardContent padding="xl">
              <div className="text-white">
                <h3 className="text-2xl font-bold mb-4">Need Personalized Resources?</h3>
                <p className="text-blue-100 mb-6">
                  Get custom implementation guides, ROI reports, and training materials tailored specifically for your school.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    size="lg"
                    icon={ArrowRight}
                    iconPosition="right"
                    className="bg-white text-blue-600 hover:bg-blue-50 font-bold px-8 py-4"
                  >
                    Request Custom Resources
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4"
                  >
                    Talk to Implementation Expert
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default ResourceDownloadsSection
