# Complete Financial System Overview - Schopio SaaS Platform

## 💰 Financial System Architecture

### Core Financial Entities
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     SCHOOLS     │    │    PARTNERS     │    │     ADMIN       │
│   (Customers)   │    │  (Referrers)    │    │  (Platform)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ Pay Subscription      │ Earn Commission       │ Manage Platform
         │ ₹80/student/month     │ 35-50% of revenue     │ Operational Costs
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────────────┐
                    │   FINANCIAL WORKFLOWS   │
                    │                         │
                    │ • Subscription Billing  │
                    │ • Commission Calculation│
                    │ • Escrow Management     │
                    │ • Payment Processing    │
                    │ • Risk Management       │
                    └─────────────────────────┘
```

## 🔄 Money Flow Architecture

### Primary Revenue Flow: School → Platform
```typescript
interface SchoolPaymentFlow {
  // Step 1: Subscription Creation
  subscription: {
    schoolId: string;
    studentCount: number;
    monthlyAmount: number; // ₹80 × studentCount
    billingDate: Date; // 1st of every month
    partnerId?: string; // Referral partner if exists
  };
  
  // Step 2: Invoice Generation
  invoice: {
    baseAmount: number; // ₹80 × studentCount
    discountAmount: number; // Any promotional discounts
    taxAmount: number; // 18% GST
    totalAmount: number; // Final amount to collect
    dueDate: Date; // Invoice due date
  };
  
  // Step 3: Payment Processing
  payment: {
    razorpayOrderId: string;
    paymentMethod: 'card' | 'netbanking' | 'upi' | 'wallet';
    status: 'pending' | 'success' | 'failed';
    processedAt: Date;
  };
}
```

### Secondary Flow: Platform → Partner Commission
```typescript
interface PartnerCommissionFlow {
  // Step 1: Commission Calculation (After School Payment)
  commission: {
    partnerId: string;
    schoolId: string;
    baseAmount: number; // School payment amount
    commissionPercentage: number; // 35-50%
    grossCommission: number; // baseAmount × percentage
    operationalExpenses: number; // Platform costs
    netCommission: number; // Final partner payout
  };
  
  // Step 2: Escrow Management (Risk Control)
  escrow: {
    status: 'pending' | 'held' | 'released' | 'reversed';
    holdReason?: string;
    releaseConditions: ReleaseConditions;
    holdUntilDate?: Date;
  };
  
  // Step 3: Automated Payout (Via Razorpay Route)
  payout: {
    razorpayTransferId: string;
    partnerBankAccount: string;
    amount: number;
    status: 'processing' | 'processed' | 'failed';
    processedAt: Date;
  };
}
```

## 🏦 Database Schema Overview

### Core Financial Tables
```sql
-- Subscription Management
billing_subscriptions (
  id, client_id, plan_id, student_count, monthly_amount,
  status, current_period_start, current_period_end,
  razorpay_subscription_id, created_at, updated_at
)

-- Invoice Management  
billing_invoices (
  id, subscription_id, invoice_number, base_amount,
  discount_amount, tax_amount, total_amount,
  due_date, status, razorpay_order_id, created_at
)

-- Payment Tracking
billing_payments (
  id, invoice_id, razorpay_payment_id, amount,
  payment_method, status, processed_at,
  partner_commission_processed, created_at
)

-- Partner Commission Escrow (NEW)
partner_commission_escrow (
  id, partner_id, school_id, subscription_id,
  month_year, commission_amount, net_commission,
  escrow_status, school_payment_status,
  razorpay_transfer_id, created_at, updated_at
)

-- Partner Fund Accounts (NEW)
partner_fund_accounts (
  id, partner_id, account_number, ifsc_code,
  account_holder_name, validation_status,
  razorpay_fund_account_id, created_at
)
```

## 💳 Payment Processing Workflow

### School Payment Journey
```typescript
// 1. Monthly Billing Cycle Trigger
const processMonthleBilling = async () => {
  const activeSubscriptions = await getActiveSubscriptions();
  
  for (const subscription of activeSubscriptions) {
    // Generate invoice for current month
    const invoice = await generateMonthlyInvoice(subscription);
    
    // Create Razorpay payment order
    const paymentOrder = await createRazorpayOrder(invoice);
    
    // Send payment link to school
    await sendPaymentNotification(subscription.clientId, paymentOrder);
  }
};

// 2. Payment Success Handler
const handlePaymentSuccess = async (paymentData: PaymentWebhook) => {
  // Verify payment signature
  const isValid = verifyRazorpaySignature(paymentData);
  if (!isValid) throw new Error('Invalid payment signature');
  
  // Update payment record
  await updatePaymentStatus(paymentData.paymentId, 'success');
  
  // Update invoice status
  await markInvoiceAsPaid(paymentData.orderId);
  
  // Trigger partner commission calculation
  await processPartnerCommission(paymentData);
  
  // Send confirmation to school
  await sendPaymentConfirmation(paymentData.clientId);
};
```

### Partner Commission Processing
```typescript
// 3. Commission Calculation & Escrow
const processPartnerCommission = async (paymentData: PaymentWebhook) => {
  const subscription = await getSubscriptionByPayment(paymentData.paymentId);
  
  if (subscription.partnerId) {
    // Calculate commission
    const commission = await calculatePartnerCommission(
      subscription.partnerId,
      subscription.clientId,
      paymentData.amount
    );
    
    // Create escrow record
    const escrow = await createCommissionEscrow({
      partnerId: subscription.partnerId,
      schoolId: subscription.clientId,
      commissionAmount: commission.netAmount,
      schoolPaymentId: paymentData.paymentId,
      status: 'pending'
    });
    
    // Evaluate release conditions
    const canRelease = await evaluateReleaseConditions(escrow);
    
    if (canRelease) {
      await releaseCommissionToPartner(escrow.id);
    } else {
      await holdCommissionInEscrow(escrow.id, canRelease.holdReason);
    }
  }
};
```

## 🔒 Risk Management & Escrow Logic

### Payment Dependency Management
```typescript
interface ReleaseConditions {
  // Primary Dependencies
  schoolPaymentReceived: boolean; // Payment webhook confirmed
  schoolPaymentCleared: boolean; // 3-day bank clearing period
  
  // Business Rules
  subscriptionActive: boolean; // School subscription not cancelled
  noActiveDisputes: boolean; // No chargebacks or disputes
  partnerKycValid: boolean; // Partner KYC documents valid
  
  // Risk Factors
  fraudCheckPassed: boolean; // Anti-fraud validation
  chargebackRiskLow: boolean; // Historical chargeback analysis
  partnerAccountValid: boolean; // Bank account verified
  
  // Manual Controls
  adminApprovalRequired: boolean; // High-risk transactions
  emergencyStop: boolean; // System-wide hold
}

const evaluateReleaseConditions = async (escrow: CommissionEscrow): Promise<ReleaseDecision> => {
  const conditions = await checkAllConditions(escrow);
  
  // All conditions must be true for automatic release
  const canAutoRelease = Object.values(conditions).every(Boolean);
  
  if (canAutoRelease) {
    return { canRelease: true, method: 'automatic' };
  }
  
  // Determine hold reason and duration
  const holdReason = determineHoldReason(conditions);
  const holdDuration = calculateHoldDuration(escrow, conditions);
  
  return {
    canRelease: false,
    holdReason,
    holdUntilDate: new Date(Date.now() + holdDuration),
    requiresManualReview: conditions.adminApprovalRequired
  };
};
```

## 📊 Financial Reporting & Analytics

### Revenue Tracking
```typescript
interface FinancialMetrics {
  // School Revenue
  totalRevenue: number; // All school payments
  monthlyRecurringRevenue: number; // MRR
  averageRevenuePerUser: number; // ARPU
  
  // Partner Commissions
  totalCommissionsPaid: number; // All partner payouts
  commissionsHeldInEscrow: number; // Pending releases
  averageCommissionPerPartner: number;
  
  // Platform Metrics
  operationalExpenses: number; // Platform costs
  netRevenue: number; // Revenue - Commissions - Expenses
  profitMargin: number; // Net revenue percentage
}

const generateFinancialReport = async (period: DateRange): Promise<FinancialMetrics> => {
  const schoolPayments = await getSchoolPayments(period);
  const partnerCommissions = await getPartnerCommissions(period);
  const operationalCosts = await getOperationalExpenses(period);
  
  return {
    totalRevenue: schoolPayments.reduce((sum, p) => sum + p.amount, 0),
    totalCommissionsPaid: partnerCommissions.filter(c => c.status === 'paid').reduce((sum, c) => sum + c.amount, 0),
    commissionsHeldInEscrow: partnerCommissions.filter(c => c.status === 'held').reduce((sum, c) => sum + c.amount, 0),
    operationalExpenses: operationalCosts.reduce((sum, e) => sum + e.amount, 0),
    // ... calculate other metrics
  };
};
```

## 🎯 Key Financial Principles

### 1. Payment Dependency Enforcement
- **No partner commission without confirmed school payment**
- **Multi-tier verification before release**
- **Automatic hold for failed/disputed payments**

### 2. Risk-Based Release Management
- **Low-risk partners**: 3-day automatic release
- **Medium-risk partners**: 7-day hold with conditions
- **High-risk partners**: Manual approval required

### 3. Comprehensive Audit Trail
- **Every financial transaction logged**
- **Commission calculation transparency**
- **Dispute resolution documentation**

### 4. Automated Reconciliation
- **Daily financial reconciliation**
- **Weekly partner commission reports**
- **Monthly financial statements**

## 🔄 Integration Points

### Razorpay Integration
- **Payment Gateway**: School subscription payments
- **Razorpay Route**: Automated partner payouts
- **Webhooks**: Real-time payment status updates
- **Fund Account Validation**: Partner bank verification

### Internal Systems
- **School Portal**: Payment processing and billing
- **Partner Portal**: Commission tracking and payouts
- **Admin Portal**: Financial oversight and controls
- **Email/SMS**: Payment notifications and alerts

This financial system ensures **zero financial risk** to Schopio while providing **transparent, automated commission distribution** to partners based on confirmed school payments.
