import { Hono } from 'hono'
import { partnerCommissionMicroservice } from '@/src/services/partnerCommissionMicroservice'
import { db } from '@/src/db'
import { partners, referralCodes, schoolReferrals, billingInvoices, clients, adminUsers, billingSubscriptions } from '@/src/db/schema'
import { eq, and } from 'drizzle-orm'
import bcrypt from 'bcryptjs'

const app = new Hono()

// Test endpoint to run partner commission audit
app.get('/test-commission-audit', async (c) => {
  try {
    console.log('🔍 Running partner commission audit test...')
    
    const results = await partnerCommissionMicroservice.checkAndUpdatePartnerCommissions()
    
    return c.json({
      success: true,
      message: 'Partner commission audit completed',
      results: {
        totalInvoicesChecked: results.totalChecked,
        newCommissionsCreated: results.commissionsCreated,
        errors: results.errors,
        details: results.details
      }
    })

  } catch (error) {
    console.error('❌ Commission audit test error:', error)
    return c.json({ 
      error: 'Failed to run commission audit test',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Test endpoint to verify specific school payment commission
app.get('/test-school-commission/:clientId', async (c) => {
  try {
    const clientId = c.req.param('clientId')
    
    const verification = await partnerCommissionMicroservice.verifySchoolPaymentCommission(clientId)
    
    return c.json({
      success: true,
      data: verification
    })

  } catch (error) {
    console.error('❌ School commission verification error:', error)
    return c.json({ 
      error: 'Failed to verify school commission',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Setup test partner and referral for commission testing
app.post('/setup-test-partner', async (c) => {
  try {
    console.log('🔧 Setting up test partner and referral...')

    // Get the client ID from the paid invoice
    const [invoice] = await db
      .select({
        clientId: billingInvoices.clientId,
        schoolName: clients.schoolName
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .where(eq(billingInvoices.status, 'paid'))
      .limit(1)

    if (!invoice) {
      return c.json({ error: 'No paid invoice found to test with' }, 404)
    }

    console.log(`📋 Found paid invoice for client: ${invoice.clientId} (${invoice.schoolName})`)

    // Get a real admin user ID for foreign key constraint
    const [adminUser] = await db
      .select({ id: adminUsers.id })
      .from(adminUsers)
      .limit(1)

    if (!adminUser) {
      return c.json({ error: 'No admin user found in system' }, 500)
    }

    // Check if partner already exists
    const [existingPartner] = await db
      .select({ id: partners.id, name: partners.name })
      .from(partners)
      .where(eq(partners.email, '<EMAIL>'))
      .limit(1)

    let partnerId = existingPartner?.id

    if (!existingPartner) {
      // Create test partner
      const passwordHash = await bcrypt.hash('testpassword123', 12)

      const [newPartner] = await db.insert(partners).values({
        partnerCode: 'TEST001',
        email: '<EMAIL>',
        passwordHash,
        name: 'Test Partner',
        companyName: 'Test Partner Company',
        phone: '+91-9876543210',
        address: 'Test Address, Test City',
        profitSharePercentage: '30.00', // 30% commission
        isActive: true,
        createdBy: adminUser.id
      }).returning({ id: partners.id })

      partnerId = newPartner.id
      console.log(`✅ Created test partner: ${partnerId}`)
    } else {
      console.log(`ℹ️ Using existing test partner: ${partnerId}`)
    }

    // Create referral code if not exists
    const [existingCode] = await db
      .select({ id: referralCodes.id })
      .from(referralCodes)
      .where(eq(referralCodes.code, 'TEST001'))
      .limit(1)

    let referralCodeId = existingCode?.id

    if (!existingCode) {
      const [newCode] = await db.insert(referralCodes).values({
        partnerId,
        code: 'TEST001',
        isActive: true
      }).returning({ id: referralCodes.id })

      referralCodeId = newCode.id
      console.log(`✅ Created test referral code: ${referralCodeId}`)
    } else {
      console.log(`ℹ️ Using existing referral code: ${referralCodeId}`)
    }

    // Check if school referral already exists
    if (!invoice.clientId) {
      throw new Error('Invoice clientId is required')
    }

    const [existingReferral] = await db
      .select({ id: schoolReferrals.id })
      .from(schoolReferrals)
      .where(and(
        eq(schoolReferrals.clientId, invoice.clientId),
        eq(schoolReferrals.partnerId, partnerId)
      ))
      .limit(1)

    if (!existingReferral) {
      // Create school referral
      await db.insert(schoolReferrals).values({
        clientId: invoice.clientId,
        partnerId,
        referralCodeId,
        referralSource: 'test_setup',
        isActive: true,
        verifiedAt: new Date(),
        verifiedBy: adminUser.id
      })

      console.log(`✅ Created school referral for client: ${invoice.clientId}`)
    } else {
      console.log(`ℹ️ School referral already exists`)
    }

    return c.json({
      success: true,
      message: 'Test partner and referral setup completed',
      data: {
        partnerId,
        referralCodeId,
        clientId: invoice.clientId,
        schoolName: invoice.schoolName
      }
    })

  } catch (error) {
    console.error('❌ Setup test partner error:', error)
    return c.json({
      error: 'Failed to setup test partner',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Test subscription creation with partner validation
app.post('/test-subscription-creation', async (c) => {
  try {
    console.log('🧪 Testing subscription creation with partner validation...')

    // Get the client ID that has a partner referral
    const [clientWithPartner] = await db
      .select({
        clientId: schoolReferrals.clientId,
        schoolName: clients.schoolName,
        partnerId: schoolReferrals.partnerId,
        partnerName: partners.name
      })
      .from(schoolReferrals)
      .leftJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .where(and(
        eq(schoolReferrals.isActive, true),
        eq(partners.isActive, true)
      ))
      .limit(1)

    if (!clientWithPartner) {
      return c.json({ error: 'No client with active partner referral found for testing' }, 404)
    }

    console.log(`📋 Testing with client: ${clientWithPartner.schoolName} (Partner: ${clientWithPartner.partnerName})`)

    // Test subscription data
    const testSubscriptionData = {
      clientId: clientWithPartner.clientId,
      studentCount: 100,
      pricePerStudent: 25,
      billingCycle: 'monthly',
      startDate: new Date().toISOString().split('T')[0],
      gracePeriodDays: 3,
      setupFee: 0,
      discountPercentage: 0,
      notes: 'Test subscription with partner commission',
      operationalExpenses: {
        databaseCosts: 1000,
        websiteMaintenance: 800,
        supportCosts: 2000,
        infrastructureCosts: 1400
      }
    }

    return c.json({
      success: true,
      message: 'Test data prepared for subscription creation',
      testData: {
        client: {
          id: clientWithPartner.clientId,
          schoolName: clientWithPartner.schoolName
        },
        partner: {
          id: clientWithPartner.partnerId,
          name: clientWithPartner.partnerName
        },
        subscriptionData: testSubscriptionData,
        expectedCalculations: {
          grossAmount: testSubscriptionData.studentCount * testSubscriptionData.pricePerStudent,
          totalExpenses: Object.values(testSubscriptionData.operationalExpenses).reduce((sum, cost) => sum + cost, 0),
          netAmount: (testSubscriptionData.studentCount * testSubscriptionData.pricePerStudent) - Object.values(testSubscriptionData.operationalExpenses).reduce((sum, cost) => sum + cost, 0),
          expectedCommission: '30% of net amount (based on test partner settings)'
        }
      },
      instructions: 'Use this data to test subscription creation via admin panel'
    })

  } catch (error) {
    console.error('❌ Test subscription creation error:', error)
    return c.json({
      error: 'Failed to prepare test subscription creation',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Update existing subscription with operational expenses for testing
app.post('/update-subscription-expenses', async (c) => {
  try {
    console.log('🔧 Updating existing subscription with operational expenses...')

    // Get the existing subscription
    const [subscription] = await db
      .select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        schoolName: clients.schoolName,
        monthlyAmount: billingSubscriptions.monthlyAmount
      })
      .from(billingSubscriptions)
      .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .limit(1)

    if (!subscription) {
      return c.json({ error: 'No subscription found to update' }, 404)
    }

    // Test operational expenses
    const testExpenses = {
      databaseCosts: 1000,
      websiteMaintenance: 800,
      supportCosts: 2000,
      infrastructureCosts: 1400
    }

    const totalExpenses = Object.values(testExpenses).reduce((sum, cost) => sum + cost, 0)

    // Update the subscription with operational expenses
    await db.update(billingSubscriptions)
      .set({
        operationalExpenses: testExpenses,
        databaseCosts: testExpenses.databaseCosts.toString(),
        websiteMaintenance: testExpenses.websiteMaintenance.toString(),
        supportCosts: testExpenses.supportCosts.toString(),
        infrastructureCosts: testExpenses.infrastructureCosts.toString(),
        totalOperationalExpenses: totalExpenses.toString(),
        notes: 'Updated with test operational expenses for form testing',
        updatedAt: new Date()
      })
      .where(eq(billingSubscriptions.id, subscription.id))

    console.log(`✅ Updated subscription ${subscription.id} with operational expenses: ₹${totalExpenses}`)

    return c.json({
      success: true,
      message: 'Subscription updated with operational expenses',
      data: {
        subscriptionId: subscription.id,
        schoolName: subscription.schoolName,
        monthlyAmount: subscription.monthlyAmount,
        operationalExpenses: testExpenses,
        totalExpenses,
        instructions: 'Now test the manage subscription form to see if expenses are displayed correctly'
      }
    })

  } catch (error) {
    console.error('❌ Update subscription expenses error:', error)
    return c.json({
      error: 'Failed to update subscription expenses',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Test subscription data retrieval (no auth required)
app.get('/test-subscription-data/:subscriptionId', async (c) => {
  try {
    const subscriptionId = c.req.param('subscriptionId')
    console.log(`🔍 Testing subscription data retrieval for: ${subscriptionId}`)

    // Get subscription data the same way the admin API does
    const [subscription] = await db.select({
      id: billingSubscriptions.id,
      clientId: billingSubscriptions.clientId,
      studentCount: billingSubscriptions.studentCount,
      pricePerStudent: billingSubscriptions.pricePerStudent,
      monthlyAmount: billingSubscriptions.monthlyAmount,
      status: billingSubscriptions.status,
      billingCycle: billingSubscriptions.billingCycle,
      gracePeriodDays: billingSubscriptions.gracePeriodDays,
      // Operational expenses fields
      operationalExpenses: billingSubscriptions.operationalExpenses,
      databaseCosts: billingSubscriptions.databaseCosts,
      websiteMaintenance: billingSubscriptions.websiteMaintenance,
      supportCosts: billingSubscriptions.supportCosts,
      infrastructureCosts: billingSubscriptions.infrastructureCosts,
      totalOperationalExpenses: billingSubscriptions.totalOperationalExpenses,
      notes: billingSubscriptions.notes,
      setupFee: billingSubscriptions.setupFee,
      // Client info
      schoolName: clients.schoolName,
      email: clients.email
    })
    .from(billingSubscriptions)
    .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))
    .where(eq(billingSubscriptions.id, subscriptionId))
    .limit(1)

    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    // Prepare operational expenses data the same way the admin API does
    const operationalExpensesData = {
      operationalExpenses: subscription.operationalExpenses || {
        databaseCosts: 0,
        websiteMaintenance: 0,
        supportCosts: 0,
        infrastructureCosts: 0
      },
      databaseCosts: parseFloat(subscription.databaseCosts || '0'),
      websiteMaintenance: parseFloat(subscription.websiteMaintenance || '0'),
      supportCosts: parseFloat(subscription.supportCosts || '0'),
      infrastructureCosts: parseFloat(subscription.infrastructureCosts || '0'),
      totalOperationalExpenses: parseFloat(subscription.totalOperationalExpenses || '0'),
      notes: subscription.notes || '',
      setupFee: parseFloat(subscription.setupFee || '0'),
      billingCycle: subscription.billingCycle || 'monthly'
    }

    return c.json({
      success: true,
      message: 'Subscription data retrieved successfully',
      data: {
        subscription: {
          ...subscription,
          ...operationalExpensesData
        },
        rawData: {
          databaseCosts: subscription.databaseCosts,
          websiteMaintenance: subscription.websiteMaintenance,
          supportCosts: subscription.supportCosts,
          infrastructureCosts: subscription.infrastructureCosts,
          totalOperationalExpenses: subscription.totalOperationalExpenses,
          operationalExpenses: subscription.operationalExpenses
        },
        testResults: {
          hasOperationalExpenses: !!subscription.operationalExpenses,
          hasIndividualCosts: !!(subscription.databaseCosts || subscription.websiteMaintenance || subscription.supportCosts || subscription.infrastructureCosts),
          totalExpensesCalculated: operationalExpensesData.totalOperationalExpenses,
          formShouldShow: operationalExpensesData.totalOperationalExpenses > 0 ? 'YES' : 'NO'
        }
      }
    })

  } catch (error) {
    console.error('❌ Test subscription data retrieval error:', error)
    return c.json({
      error: 'Failed to retrieve subscription data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Check invoice and client relationship
app.get('/check-invoice-client/:invoiceId', async (c) => {
  try {
    const invoiceId = c.req.param('invoiceId')

    const [invoice] = await db
      .select({
        invoiceId: billingInvoices.id,
        clientId: billingInvoices.clientId,
        totalAmount: billingInvoices.totalAmount,
        status: billingInvoices.status,
        schoolName: clients.schoolName
      })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .where(eq(billingInvoices.id, invoiceId))
      .limit(1)

    if (!invoice) {
      return c.json({ error: 'Invoice not found' }, 404)
    }

    // Check if this client has a partner referral
    if (!invoice.clientId) {
      return c.json({ error: 'Invoice clientId is required' }, 400)
    }

    const [referral] = await db
      .select({
        id: schoolReferrals.id,
        partnerId: schoolReferrals.partnerId,
        partnerName: partners.name,
        isActive: schoolReferrals.isActive
      })
      .from(schoolReferrals)
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .where(and(
        eq(schoolReferrals.clientId, invoice.clientId),
        eq(schoolReferrals.isActive, true)
      ))
      .limit(1)

    return c.json({
      success: true,
      data: {
        invoice,
        hasPartnerReferral: !!referral,
        partnerReferral: referral,
        issue: !referral ? 'This client does not have an active partner referral' : null,
        solution: !referral ? 'Create a partner referral for this client or use a different invoice for testing' : null
      }
    })

  } catch (error) {
    console.error('❌ Check invoice client error:', error)
    return c.json({
      error: 'Failed to check invoice client relationship',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

export default app
