#!/usr/bin/env node

/**
 * SMART DATABASE CLEANUP - PRESERVE ADMIN ONLY
 * Intelligently cleans only existing tables, preserves admin login
 */

import { Pool } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

class SmartDatabaseCleaner {
  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: { rejectUnauthorized: false }
    });
  }

  async getExistingTables() {
    const result = await this.pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);
    return result.rows.map(row => row.table_name);
  }

  async tableExists(tableName) {
    const result = await this.pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = $1
      )
    `, [tableName]);
    return result.rows[0].exists;
  }

  async cleanTableIfExists(tableName, description, preserveCondition = null) {
    try {
      const exists = await this.tableExists(tableName);
      if (!exists) {
        console.log(`⏭️  Skipping ${tableName}: Table does not exist`);
        return;
      }

      let query = `DELETE FROM ${tableName}`;
      if (preserveCondition) {
        query += ` WHERE NOT (${preserveCondition})`;
      }

      const result = await this.pool.query(query);
      const deletedCount = result.rowCount || 0;
      
      if (deletedCount > 0) {
        console.log(`🗑️  Cleaned ${tableName}: ${deletedCount} records deleted`);
      } else {
        console.log(`✨ ${tableName}: Already empty or preserved`);
      }
    } catch (error) {
      console.error(`❌ Error cleaning ${tableName}:`, error.message);
      // Don't throw - continue with other tables
    }
  }

  async cleanDatabase() {
    console.log('🧹 STARTING SMART DATABASE CLEANUP');
    console.log('📅 Date:', new Date().toISOString());
    console.log('🎯 Scope: Clean all data except admin login\n');

    try {
      // Test connection
      await this.pool.query('SELECT 1');
      console.log('✅ Database connection established\n');

      // Get existing tables
      const existingTables = await this.getExistingTables();
      console.log(`📋 Found ${existingTables.length} tables in database:`);
      existingTables.forEach(table => console.log(`   📄 ${table}`));
      console.log('');

      // Get current admin users before cleanup
      if (await this.tableExists('admin_users')) {
        const adminUsers = await this.pool.query(`
          SELECT id, email, name, role 
          FROM admin_users 
          WHERE role IN ('super_admin', 'admin')
        `);
        
        console.log(`🔐 Found ${adminUsers.rows.length} admin users to preserve:`);
        adminUsers.rows.forEach(admin => {
          console.log(`   👤 ${admin.name} (${admin.email}) - ${admin.role}`);
        });
        console.log('');
      }

      // Start cleanup transaction
      await this.pool.query('BEGIN');

      console.log('🗑️  CLEANING BUSINESS DATA TABLES...\n');

      // Clean tables in dependency order (children first, then parents)
      
      // 1. Clean transaction and detail tables first
      await this.cleanTableIfExists('partner_commission_transactions', 'Partner commission transactions');
      await this.cleanTableIfExists('billing_payments', 'Billing payments');
      await this.cleanTableIfExists('billing_transactions', 'Billing transactions');
      await this.cleanTableIfExists('ticket_messages', 'Ticket messages');
      
      // 2. Clean discount and configuration tables
      await this.cleanTableIfExists('subscription_discounts', 'Subscription discounts');
      await this.cleanTableIfExists('subscription_expenses', 'Subscription expenses');
      await this.cleanTableIfExists('partner_commission_config', 'Partner commission configurations');
      await this.cleanTableIfExists('advance_payments', 'Advance payments');
      
      // 3. Clean billing and invoice tables
      await this.cleanTableIfExists('billing_invoices', 'Billing invoices');
      await this.cleanTableIfExists('billing_subscriptions', 'Billing subscriptions');
      
      // 4. Clean support and communication tables
      await this.cleanTableIfExists('support_tickets', 'Support tickets');
      await this.cleanTableIfExists('email_logs', 'Email logs');
      await this.cleanTableIfExists('notifications', 'Notifications');
      
      // 5. Clean partner and referral tables
      await this.cleanTableIfExists('partner_earnings', 'Partner earnings');
      await this.cleanTableIfExists('partner_withdrawals', 'Partner withdrawals');
      await this.cleanTableIfExists('school_referrals', 'School referrals');
      await this.cleanTableIfExists('partners', 'Partners');
      
      // 6. Clean client and user tables (preserve admin users)
      await this.cleanTableIfExists('client_users', 'Client users');
      await this.cleanTableIfExists('subscriptions', 'Legacy subscriptions');
      await this.cleanTableIfExists('clients', 'Clients');
      
      // 7. Clean lead and demo tables
      await this.cleanTableIfExists('demo_requests', 'Demo requests');
      await this.cleanTableIfExists('leads', 'Leads');
      
      // 8. Clean any other business tables but preserve admin_users
      const businessTables = existingTables.filter(table => 
        !['admin_users', 'schema_migrations', 'drizzle_migrations'].includes(table)
      );
      
      for (const table of businessTables) {
        if (!['partner_commission_transactions', 'billing_payments', 'billing_transactions', 
              'ticket_messages', 'subscription_discounts', 'subscription_expenses', 
              'partner_commission_config', 'advance_payments', 'billing_invoices', 
              'billing_subscriptions', 'support_tickets', 'email_logs', 'notifications',
              'partner_earnings', 'partner_withdrawals', 'school_referrals', 'partners',
              'client_users', 'subscriptions', 'clients', 'demo_requests', 'leads'].includes(table)) {
          await this.cleanTableIfExists(table, `Additional table: ${table}`);
        }
      }

      // 9. Reset sequences
      await this.resetSequences();

      // Commit the cleanup
      await this.pool.query('COMMIT');

      console.log('\n✅ DATABASE CLEANUP COMPLETED SUCCESSFULLY!\n');

      // Verify admin users are still there
      if (await this.tableExists('admin_users')) {
        const remainingAdmins = await this.pool.query(`
          SELECT id, email, name, role 
          FROM admin_users 
          WHERE role IN ('super_admin', 'admin')
        `);

        console.log(`🔐 PRESERVED ADMIN USERS (${remainingAdmins.rows.length}):`);
        remainingAdmins.rows.forEach(admin => {
          console.log(`   ✅ ${admin.name} (${admin.email}) - ${admin.role}`);
        });
      }

      // Show final database state
      await this.showDatabaseState();

      console.log('\n🎯 DATABASE IS NOW READY FOR MANUAL TESTING!');
      console.log('\n📋 MANUAL TESTING INSTRUCTIONS:');
      console.log('');
      console.log('1. 👨‍💼 ADMIN TESTING:');
      console.log('   - Login: http://localhost:3000/admin/login');
      console.log('   - Use preserved admin credentials');
      console.log('   - Test: Create clients, manage discounts, configure commissions');
      console.log('');
      console.log('2. 🏫 SCHOOL CLIENT TESTING:');
      console.log('   - Create new school through admin panel first');
      console.log('   - Or register at: http://localhost:3000/register');
      console.log('   - Test: Billing dashboard, payments, discount display');
      console.log('');
      console.log('3. 🤝 PARTNER TESTING:');
      console.log('   - Create partner through admin panel');
      console.log('   - Test: Commission dashboard, earnings, withdrawals');
      console.log('');
      console.log('4. 💰 DISCOUNT SYSTEM TESTING:');
      console.log('   - Create discounts via admin panel');
      console.log('   - Verify discount application in school portal');
      console.log('   - Test commission calculations exclude discounts');

    } catch (error) {
      await this.pool.query('ROLLBACK');
      console.error('💥 Database cleanup failed:', error);
      throw error;
    } finally {
      await this.pool.end();
      console.log('\n🔌 Database connection closed');
    }
  }

  async resetSequences() {
    console.log('\n🔄 RESETTING SEQUENCES...');
    
    try {
      // Get all sequences in the database
      const sequences = await this.pool.query(`
        SELECT sequence_name 
        FROM information_schema.sequences 
        WHERE sequence_schema = 'public'
      `);

      for (const seq of sequences.rows) {
        try {
          await this.pool.query(`ALTER SEQUENCE ${seq.sequence_name} RESTART WITH 1`);
          console.log(`🔄 Reset sequence: ${seq.sequence_name}`);
        } catch (error) {
          console.log(`⚠️  Could not reset ${seq.sequence_name}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('❌ Error resetting sequences:', error.message);
    }
  }

  async showDatabaseState() {
    console.log('\n📊 FINAL DATABASE STATE:');
    
    const importantTables = [
      'admin_users',
      'clients', 
      'client_users',
      'billing_subscriptions',
      'billing_invoices',
      'billing_payments',
      'subscription_discounts',
      'partner_commission_transactions',
      'partners',
      'leads',
      'demo_requests'
    ];

    for (const table of importantTables) {
      try {
        const exists = await this.tableExists(table);
        if (exists) {
          const result = await this.pool.query(`SELECT COUNT(*) as count FROM ${table}`);
          const count = result.rows[0].count;
          const status = count > 0 ? `${count} records` : 'Empty';
          console.log(`   📋 ${table}: ${status}`);
        } else {
          console.log(`   ❌ ${table}: Table does not exist`);
        }
      } catch (error) {
        console.log(`   ❌ ${table}: Error checking (${error.message})`);
      }
    }
  }
}

// Run the cleanup
const cleaner = new SmartDatabaseCleaner();
cleaner.cleanDatabase()
  .then(() => {
    console.log('\n🎉 CLEANUP COMPLETE - READY FOR MANUAL TESTING!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Cleanup failed:', error);
    process.exit(1);
  });
