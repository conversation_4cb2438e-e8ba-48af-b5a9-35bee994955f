import ResourceDownloadsSection from '@/components/sections/ResourceDownloadsSection'
import ImplementationTimelineSection from '@/components/sections/ImplementationTimelineSection'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Free Implementation Resources | Schopio School Management',
  description: 'Download free implementation guides, checklists, and resources to help your school successfully adopt Schopio school management system.',
  keywords: 'school management resources, implementation guide, educational software resources, school digital transformation',
  openGraph: {
    title: 'Free Implementation Resources | Schopio',
    description: 'Download free implementation guides, checklists, and resources to help your school successfully adopt Schopio.',
    type: 'website',
  },
}

export default function ResourcesPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center gap-2 bg-slate-100 text-slate-700 border border-slate-200 px-4 py-2 rounded-full text-sm font-bold mb-6">
              <span className="w-2 h-2 bg-slate-600 rounded-full"></span>
              Free Resources
            </div>
            <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 mb-6">
              Download Free 
              <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Implementation Resources</span>
            </h1>
            <p className="text-xl text-slate-600 leading-relaxed">
              Get everything you need to successfully implement Schopio at your school. 
              Our comprehensive guides, checklists, and templates will help you navigate 
              the digital transformation journey with confidence.
            </p>
          </div>
        </div>
      </section>

      {/* Resource Downloads Section */}
      <ResourceDownloadsSection />

      {/* Implementation Timeline Section */}
      <ImplementationTimelineSection />

      {/* Additional Support Section */}
      <section className="py-16 bg-gradient-to-br from-blue-50 to-emerald-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-6">
              Need Additional Support?
            </h2>
            <p className="text-lg text-slate-600 mb-8">
              Our implementation team is here to help you every step of the way. 
              From planning to go-live, we ensure your school&apos;s digital transformation is successful.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/demo" 
                className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-emerald-600 text-white font-bold rounded-lg hover:from-blue-700 hover:to-emerald-700 transition-all duration-300"
              >
                Schedule Implementation Call
              </a>
              <a 
                href="/solutions" 
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-slate-300 text-slate-700 font-bold rounded-lg hover:border-blue-500 hover:text-blue-600 transition-all duration-300"
              >
                View All Solutions
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
