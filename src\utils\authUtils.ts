import jwt from 'jsonwebtoken'

// Token storage keys
const TOKEN_KEYS = {
  school: 'schoolToken',
  admin: 'adminToken',
  partner: 'partnerToken'
} as const

export type UserType = keyof typeof TOKEN_KEYS

// Token validation interface
interface TokenPayload {
  userId: string
  email: string
  type: UserType
  exp: number
  iat: number
  [key: string]: any
}

// Authentication utilities class
export class AuthUtils {
  
  // Set authentication token
  static setToken(userType: UserType, token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(TOKEN_KEYS[userType], token)
      
      // Also set as cookie for middleware access
      const isProduction = window.location.protocol === 'https:'
      const secureFlag = isProduction ? '; secure' : ''
      document.cookie = `${TOKEN_KEYS[userType]}=${token}; path=/; max-age=${7 * 24 * 60 * 60}${secureFlag}; samesite=strict`
    }
  }
  
  // Get authentication token
  static getToken(userType: UserType): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(TOKEN_KEYS[userType])
    }
    return null
  }
  
  // Remove authentication token
  static removeToken(userType: UserType): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(TOKEN_KEYS[userType])
      
      // Remove cookie
      document.cookie = `${TOKEN_KEYS[userType]}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`
    }
  }
  
  // Validate token structure and expiration
  static validateToken(token: string, expectedType: UserType): TokenPayload | null {
    try {
      // Basic token structure check
      if (!token || typeof token !== 'string' || token.split('.').length !== 3) {
        return null
      }

      const decoded = jwt.decode(token) as TokenPayload

      if (!decoded || typeof decoded !== 'object') {
        return null
      }

      // Check required fields
      if (!decoded.userId || !decoded.email || !decoded.type) {
        return null
      }

      // Check token type
      if (decoded.type !== expectedType) {
        return null
      }

      // Check expiration
      const now = Math.floor(Date.now() / 1000)
      if (decoded.exp && decoded.exp < now) {
        return null
      }

      return decoded
    } catch (error) {
      console.error(`Token validation error:`, error)
      return null
    }
  }
  
  // Check if user is authenticated
  static isAuthenticated(userType: UserType): boolean {
    try {
      const token = this.getToken(userType)
      if (!token) {
        return false
      }

      const payload = this.validateToken(token, userType)

      // If token is invalid, remove it to prevent issues
      if (payload === null) {
        this.removeToken(userType)
        return false
      }

      return payload !== null
    } catch (error) {
      console.error(`Authentication check error for ${userType}:`, error)
      // Clear invalid token
      this.removeToken(userType)
      return false
    }
  }
  
  // Get current user data from token
  static getCurrentUser(userType: UserType): TokenPayload | null {
    const token = this.getToken(userType)
    if (!token) {
      return null
    }
    
    return this.validateToken(token, userType)
  }
  
  // Check if token is about to expire (within 1 hour)
  static isTokenExpiringSoon(userType: UserType): boolean {
    const token = this.getToken(userType)
    if (!token) {
      return true
    }
    
    const payload = this.validateToken(token, userType)
    if (!payload) {
      return true
    }
    
    const now = Math.floor(Date.now() / 1000)
    const oneHour = 60 * 60
    
    return payload.exp - now < oneHour
  }
  
  // Logout user
  static logout(userType: UserType): void {
    this.removeToken(userType)
    
    // Redirect to appropriate login page
    if (typeof window !== 'undefined') {
      const loginPaths = {
        school: '/school/login',
        admin: '/admin/login',
        partner: '/partner/login'
      }
      
      window.location.href = loginPaths[userType]
    }
  }
  
  // Redirect to dashboard if authenticated
  static redirectToDashboardIfAuthenticated(userType: UserType): void {
    if (this.isAuthenticated(userType)) {
      const dashboardPaths = {
        school: '/school/dashboard',
        admin: '/admin/dashboard',
        partner: '/partner/dashboard'
      }
      
      if (typeof window !== 'undefined') {
        window.location.href = dashboardPaths[userType]
      }
    }
  }
  
  // Get authorization header for API calls
  static getAuthHeader(userType: UserType): { Authorization: string } | {} {
    const token = this.getToken(userType)
    if (!token) {
      return {}
    }
    
    return {
      Authorization: `Bearer ${token}`
    }
  }
  
  // Refresh token if needed (placeholder for future implementation)
  static async refreshTokenIfNeeded(userType: UserType): Promise<boolean> {
    // TODO: Implement token refresh logic
    // For now, just check if token is valid
    return this.isAuthenticated(userType)
  }
  
  // Handle authentication errors
  static handleAuthError(userType: UserType, error: any): void {
    console.error(`Authentication error for ${userType}:`, error)
    
    // If it's a token-related error, logout the user
    if (
      error?.response?.status === 401 ||
      error?.message?.includes('token') ||
      error?.message?.includes('unauthorized')
    ) {
      this.logout(userType)
    }
  }
}

export { TOKEN_KEYS }
export type { TokenPayload }
