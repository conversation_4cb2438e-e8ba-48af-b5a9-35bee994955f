/**
 * Production Error Handler
 * Comprehensive error handling, retry mechanisms, and failure recovery for billing operations
 */

import { auditLogger } from './auditLogger'
import { emailService } from './emailService'

interface RetryConfig {
  maxAttempts: number
  baseDelayMs: number
  maxDelayMs: number
  backoffMultiplier: number
  retryableErrors: string[]
}

interface ErrorContext {
  operation: string
  data?: any
  userId?: string
  clientId?: string
  subscriptionId?: string
  invoiceId?: string
}

interface ErrorHandlingResult {
  success: boolean
  attempts: number
  finalError?: Error
  recoveryAction?: string
}

class ProductionErrorHandler {
  private defaultRetryConfig: RetryConfig = {
    maxAttempts: 3,
    baseDelayMs: 1000,
    maxDelayMs: 30000,
    backoffMultiplier: 2,
    retryableErrors: [
      'ECONNRESET',
      'ENOTFOUND',
      'ETIMEDOUT',
      'ECONNREFUSED',
      'NETWORK_ERROR',
      'TEMPORARY_FAILURE',
      'RATE_LIMIT_EXCEEDED',
      'DATABASE_TIMEOUT'
    ]
  }

  /**
   * Execute operation with automatic retry and error handling
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    customConfig?: Partial<RetryConfig>
  ): Promise<T> {
    const config = { ...this.defaultRetryConfig, ...customConfig }
    let lastError: Error
    let attempt = 0

    while (attempt < config.maxAttempts) {
      attempt++
      
      try {
        console.log(`🔄 Executing ${context.operation} (attempt ${attempt}/${config.maxAttempts})`)
        
        const result = await operation()
        
        if (attempt > 1) {
          console.log(`✅ ${context.operation} succeeded after ${attempt} attempts`)
          
          // Log successful recovery
          await this.logRecovery(context, attempt, null)
        }
        
        return result

      } catch (error) {
        lastError = error as Error
        
        console.error(`❌ ${context.operation} failed (attempt ${attempt}/${config.maxAttempts}):`, error)

        // Check if error is retryable
        if (!this.isRetryableError(lastError, config) || attempt >= config.maxAttempts) {
          break
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          config.baseDelayMs * Math.pow(config.backoffMultiplier, attempt - 1),
          config.maxDelayMs
        )

        console.log(`⏳ Retrying ${context.operation} in ${delay}ms...`)
        await this.delay(delay)
      }
    }

    // All attempts failed
    console.error(`💥 ${context.operation} failed after ${attempt} attempts`)
    
    // Log failure and attempt recovery
    const recoveryResult = await this.handleFailure(lastError!, context, attempt)
    
    if (recoveryResult.success) {
      console.log(`🔧 Recovery successful for ${context.operation}`)
      return recoveryResult as any // Recovery succeeded, return placeholder
    }

    // Recovery failed, throw the original error
    throw lastError!
  }

  /**
   * Handle operation failure with recovery mechanisms
   */
  private async handleFailure(
    error: Error,
    context: ErrorContext,
    attempts: number
  ): Promise<ErrorHandlingResult> {
    try {
      // Log the failure
      await auditLogger.logAdmin('operation_failure', {
        adminId: 'system',
        resource: 'error_handler',
        details: {
          operation: context.operation,
          error: error.message,
          attempts,
          context: {
            userId: context.userId,
            clientId: context.clientId,
            subscriptionId: context.subscriptionId,
            invoiceId: context.invoiceId
          }
        },
        ipAddress: 'system',
        userAgent: 'error-handler',
        success: false
      })

      // Attempt specific recovery based on operation type
      const recoveryAction = await this.attemptRecovery(error, context)

      // Send alert for critical failures
      if (this.isCriticalFailure(error, context)) {
        await this.sendCriticalAlert(error, context, attempts)
      }

      return {
        success: false,
        attempts,
        finalError: error,
        recoveryAction
      }

    } catch (handlingError) {
      console.error('Error in failure handling:', handlingError)
      return {
        success: false,
        attempts,
        finalError: error
      }
    }
  }

  /**
   * Attempt operation-specific recovery
   */
  private async attemptRecovery(error: Error, context: ErrorContext): Promise<string> {
    try {
      switch (context.operation) {
        case 'generate_monthly_billing':
          return await this.recoverBillingGeneration(error, context)
        
        case 'process_payment':
          return await this.recoverPaymentProcessing(error, context)
        
        case 'send_invoice_email':
          return await this.recoverEmailSending(error, context)
        
        case 'update_subscription':
          return await this.recoverSubscriptionUpdate(error, context)
        
        default:
          return 'No specific recovery available'
      }
    } catch (recoveryError) {
      console.error('Recovery attempt failed:', recoveryError)
      return 'Recovery failed'
    }
  }

  private async recoverBillingGeneration(error: Error, context: ErrorContext): Promise<string> {
    // For billing generation failures, queue for manual review
    console.log('🔧 Attempting billing generation recovery...')
    
    // TODO: Add to failed billing queue for manual processing
    // TODO: Create admin notification for manual intervention
    
    return 'Queued for manual billing generation'
  }

  private async recoverPaymentProcessing(error: Error, context: ErrorContext): Promise<string> {
    // For payment failures, ensure payment status is correctly recorded
    console.log('🔧 Attempting payment processing recovery...')
    
    // TODO: Verify payment status with Razorpay
    // TODO: Update payment record if needed
    
    return 'Payment status verification queued'
  }

  private async recoverEmailSending(error: Error, context: ErrorContext): Promise<string> {
    // For email failures, queue for retry with different service
    console.log('🔧 Attempting email sending recovery...')
    
    // TODO: Queue email for retry
    // TODO: Try alternative email service if available
    
    return 'Email queued for retry'
  }

  private async recoverSubscriptionUpdate(error: Error, context: ErrorContext): Promise<string> {
    // For subscription update failures, ensure data consistency
    console.log('🔧 Attempting subscription update recovery...')
    
    // TODO: Verify subscription state
    // TODO: Rollback partial updates if needed
    
    return 'Subscription state verification queued'
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: Error, config: RetryConfig): boolean {
    const errorMessage = error.message.toLowerCase()
    const errorCode = (error as any).code

    // Check error codes
    if (errorCode && config.retryableErrors.includes(errorCode)) {
      return true
    }

    // Check error messages
    return config.retryableErrors.some(retryableError => 
      errorMessage.includes(retryableError.toLowerCase())
    )
  }

  /**
   * Check if failure is critical and requires immediate attention
   */
  private isCriticalFailure(error: Error, context: ErrorContext): boolean {
    const criticalOperations = [
      'generate_monthly_billing',
      'process_payment',
      'update_subscription_status'
    ]

    const criticalErrors = [
      'database_corruption',
      'payment_gateway_down',
      'security_breach',
      'data_loss'
    ]

    return criticalOperations.includes(context.operation) ||
           criticalErrors.some(criticalError => 
             error.message.toLowerCase().includes(criticalError)
           )
  }

  /**
   * Send critical failure alert
   */
  private async sendCriticalAlert(error: Error, context: ErrorContext, attempts: number): Promise<void> {
    try {
      const alertMessage = `
🚨 CRITICAL BILLING SYSTEM FAILURE

Operation: ${context.operation}
Error: ${error.message}
Attempts: ${attempts}

Context:
- User ID: ${context.userId || 'N/A'}
- Client ID: ${context.clientId || 'N/A'}
- Subscription ID: ${context.subscriptionId || 'N/A'}
- Invoice ID: ${context.invoiceId || 'N/A'}

Stack Trace:
${error.stack}

IMMEDIATE ACTION REQUIRED
      `

      await emailService.sendAlert({
        to: '<EMAIL>',
        subject: `🚨 CRITICAL: ${context.operation} Failed`,
        message: alertMessage
      })

      console.log('📧 Critical failure alert sent')

    } catch (alertError) {
      console.error('Failed to send critical alert:', alertError)
    }
  }

  /**
   * Log successful recovery
   */
  private async logRecovery(context: ErrorContext, attempts: number, recoveryAction: string | null): Promise<void> {
    try {
      await auditLogger.logAdmin('operation_recovery', {
        adminId: 'system',
        resource: 'error_handler',
        details: {
          operation: context.operation,
          attempts,
          recoveryAction,
          context: {
            userId: context.userId,
            clientId: context.clientId,
            subscriptionId: context.subscriptionId,
            invoiceId: context.invoiceId
          }
        },
        ipAddress: 'system',
        userAgent: 'error-handler',
        success: true
      })
    } catch (logError) {
      console.error('Failed to log recovery:', logError)
    }
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Get error handling statistics
   */
  async getErrorStats(timeframe: 'hour' | 'day' | 'week' = 'day'): Promise<{
    totalErrors: number
    recoveredErrors: number
    criticalErrors: number
    topErrors: Array<{ operation: string; count: number }>
  }> {
    // TODO: Implement error statistics from audit logs
    return {
      totalErrors: 0,
      recoveredErrors: 0,
      criticalErrors: 0,
      topErrors: []
    }
  }
}

// Export singleton instance
export const productionErrorHandler = new ProductionErrorHandler()
