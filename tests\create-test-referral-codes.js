// Create test referral codes through API
// Run with: node tests/create-test-referral-codes.js

const BASE_URL = 'http://localhost:3000'

async function createTestReferralCodes() {
  console.log('🔧 Creating test referral codes through API...\n')

  try {
    // Step 1: Login as admin to get token
    console.log('1. Attempting admin login...')
    const loginResponse = await fetch(`${BASE_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    })

    if (!loginResponse.ok) {
      console.log(`   ❌ Admin login failed: ${loginResponse.status}`)
      const loginError = await loginResponse.text()
      console.log(`   Error: ${loginError}`)
      
      // Try to create admin user first
      console.log('\n2. Admin user might not exist. Please create admin user first.')
      console.log('   Run: npx tsx scripts/seed-admin.ts')
      console.log('   Or manually create admin user through database.')
      return
    }

    const loginData = await loginResponse.json()
    const adminToken = loginData.token
    console.log('   ✅ Admin login successful')

    // Step 2: Create test partners
    console.log('\n3. Creating test partners...')
    
    const testPartners = [
      {
        name: 'John Smith',
        email: '<EMAIL>',
        password: 'Partner@123',
        companyName: 'EduTech Solutions',
        phone: '+91-9876543210',
        address: '123 Business Park, Mumbai, Maharashtra 400001',
        profitSharePercentage: 40.00
      },
      {
        name: 'Sarah Johnson', 
        email: '<EMAIL>',
        password: 'Partner@456',
        companyName: 'School Connect India',
        phone: '+91-9876543211',
        address: '456 Tech Hub, Bangalore, Karnataka 560001',
        profitSharePercentage: 45.00
      }
    ]

    const createdPartners = []

    for (const partner of testPartners) {
      console.log(`   📝 Creating partner: ${partner.name}`)
      
      const partnerResponse = await fetch(`${BASE_URL}/api/admin/partners`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(partner)
      })

      if (partnerResponse.ok) {
        const partnerData = await partnerResponse.json()
        console.log(`      ✅ Partner created: ${partnerData.partner?.partnerCode || 'Unknown'}`)
        console.log(`      🎫 Referral code: ${partnerData.partner?.referralCode || 'Auto-generated'}`)
        createdPartners.push(partnerData.partner)
      } else {
        const error = await partnerResponse.text()
        console.log(`      ❌ Failed to create partner: ${error}`)
      }
    }

    // Step 3: Partners already have auto-generated referral codes
    console.log('\n4. Partners created with auto-generated referral codes!')
    console.log('   Note: Each partner automatically gets a referral code when created.')

    // Step 4: Get all available referral codes
    console.log('\n5. Getting all available referral codes...')

    const partnersResponse = await fetch(`${BASE_URL}/api/admin/partners`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    })

    if (partnersResponse.ok) {
      const partnersData = await partnersResponse.json()
      console.log('\n📋 Available referral codes for testing:')

      for (const partner of partnersData.partners || []) {
        // Get partner details to find referral codes
        const partnerDetailResponse = await fetch(`${BASE_URL}/api/admin/partners/${partner.id}`, {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        })

        if (partnerDetailResponse.ok) {
          const partnerDetail = await partnerDetailResponse.json()
          if (partnerDetail.referralCodes && partnerDetail.referralCodes.length > 0) {
            partnerDetail.referralCodes.forEach(code => {
              console.log(`   🎫 ${code.code} - ${partner.name} (${partner.companyName || 'No Company'})`)
            })
          }
        }
      }
    }

    console.log('\n🎉 Test referral codes creation completed!')

    console.log('\n💡 You can now test referral code functionality in the school portal!')

  } catch (error) {
    console.error('❌ Error creating test referral codes:', error.message)
  }
}

// Run the creation
createTestReferralCodes()
