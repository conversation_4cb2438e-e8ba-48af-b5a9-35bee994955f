# Schopio Multi-Role System Documentation

## 🏗️ System Architecture Overview

Schopio implements a comprehensive 3-portal architecture with distinct roles and access levels:

### **1. School Portal (Client Role)**
- **Primary Users**: School administrators, principals, staff
- **Access Level**: Limited to own school data and billing
- **Authentication**: Email OTP verification via Resend

### **2. Admin Portal (Administrative Roles)**
- **Primary Users**: Schopio administrators and billing staff
- **Access Level**: Full system access with role-based permissions
- **Authentication**: Secure admin login (not publicly accessible)

### **3. Partner Portal (Partner Role)**
- **Primary Users**: Referral partners and sales agents
- **Access Level**: Partner-specific data and revenue tracking
- **Authentication**: Partner-specific login system

---

## 👥 Role Definitions & Permissions

### **SCHOOL ROLE**
**Database Table**: `clients` and `clientUsers`

**Core Capabilities**:
- View own school profile and subscription details
- Access billing dashboard and payment history
- Make payments for invoices via Razorpay integration
- Update school profile information
- View software request status and history

**Billing Access**:
- View pending and paid invoices
- Download invoice PDFs (when implemented)
- Make payments through secure Razorpay gateway
- View payment history and transaction details
- Access billing cycle information

**API Endpoints**:
```typescript
// School Authentication
POST /api/auth/register          // School registration
POST /api/auth/login            // School login
POST /api/auth/verify-otp       // OTP verification
POST /api/auth/resend-otp       // Resend OTP

// School Profile Management
GET  /api/school/profile        // Get school profile
PUT  /api/school/profile        // Update school profile
POST /api/school/software-request // Create software request

// School Billing (CURRENTLY PLACEHOLDER)
GET  /api/school/billing        // Get billing information
GET  /api/school/invoices       // Get school invoices
POST /api/school/payments       // Process payments
```

**Current Implementation Status**:
- ✅ Authentication system fully functional
- ✅ Profile management working
- ❌ School billing API endpoints are placeholders (501 responses)
- ✅ Frontend billing dashboard exists but relies on payment API

---

### **ADMIN ROLE**
**Database Table**: `adminUsers`

**Role Hierarchy**:
- `super_admin`: Full system access
- `billing`: Billing and subscription management
- `support`: Customer support and lead management
- `partner_manager`: Partner and referral management

**Core Capabilities**:
- Complete client management and oversight
- Subscription creation and billing management
- Lead conversion and software request approval
- Partner management and revenue tracking
- System administration and user management

**Billing Management**:
- Create and manage client subscriptions
- Generate invoices manually or automatically
- Process payments and record transactions
- Monitor billing health and system metrics
- Handle payment failures and dunning management
- Manage operational expenses and partner revenue

**API Endpoints**:
```typescript
// Admin Authentication
POST /api/admin/auth/login      // Admin login
POST /api/admin/auth/refresh    // Token refresh

// Client Management
GET  /api/admin/clients         // List all clients
GET  /api/admin/clients/:id     // Get client details
PUT  /api/admin/clients/:id     // Update client
POST /api/admin/leads/:id/convert // Convert lead to client

// Subscription Management
POST /api/admin/subscriptions   // Create subscription
GET  /api/admin/subscriptions   // List subscriptions
PUT  /api/admin/subscriptions/:id // Update subscription
GET  /api/admin/billing/:clientId // Get billing details

// Invoice & Payment Management
POST /api/admin/invoices/generate // Generate invoice
GET  /api/admin/invoices        // List invoices
POST /api/admin/payments/record // Record payment
POST /api/admin/payments/create-order // Create Razorpay order

// Partner Management
GET  /api/admin/partners        // List partners
POST /api/admin/partners        // Create partner
PUT  /api/admin/partners/:id    // Update partner
GET  /api/admin/partner-earnings // Partner revenue data

// System Management
GET  /api/admin/dashboard       // Admin dashboard data
POST /api/scheduler/generate-billing // Manual billing generation
GET  /api/admin/reports         // System reports
```

**Current Implementation Status**:
- ✅ Complete admin authentication system
- ✅ Comprehensive client management
- ✅ Full subscription and billing management
- ✅ Invoice generation and payment processing
- ✅ Partner management and revenue tracking
- ✅ System monitoring and reporting

---

### **PARTNER ROLE**
**Database Table**: `partners`

**Core Capabilities**:
- View referred schools and conversion status
- Track revenue earnings and commission calculations
- Request monthly withdrawals via NEFT
- Monitor referral performance metrics
- Access partner-specific dashboard

**Revenue System**:
- Configurable profit sharing (35-50% of net profit)
- Automatic earnings calculation on school payments
- Expense deduction from gross revenue
- Monthly withdrawal request system
- Complete transaction history and audit trail

**API Endpoints**:
```typescript
// Partner Authentication
POST /api/partner/auth/login    // Partner login
GET  /api/partner/profile       // Partner profile

// Referral Management
GET  /api/partner/referrals     // List referred schools
GET  /api/partner/conversions   // Conversion metrics
POST /api/partner/referral-code // Generate referral codes

// Revenue Management
GET  /api/partner/earnings      // Earnings summary
GET  /api/partner/transactions  // Transaction history
POST /api/partner/withdrawal    // Request withdrawal
GET  /api/partner/wallet        // Wallet balance
```

**Current Implementation Status**:
- ✅ Partner database schema complete
- ✅ Revenue calculation system functional
- ✅ Referral tracking implemented
- ❌ Partner portal frontend not implemented
- ✅ Admin partner management functional

---

## 💰 Complete Billing Architecture

### **Billing Flow Overview**:
```
1. School Registration → Software Request → Admin Approval
2. Admin Creates Subscription → Billing Automation Starts
3. Monthly Billing Cycle → Invoice Generation → Payment Request
4. School Payment → Webhook Processing → Partner Revenue Calculation
5. Next Billing Cycle Scheduled → Process Repeats
```

### **Database Schema Relationships**:
```sql
clients (schools) 
  ↓ 1:1
subscriptions (billing setup)
  ↓ 1:many
billing_cycles (monthly cycles)
  ↓ 1:many
invoices (payment requests)
  ↓ 1:many
payments (transactions)
  ↓ triggers
partner_earnings (revenue sharing)
```

### **Automated Systems**:
1. **Billing Scheduler**: Monthly billing cycle generation
2. **Due Date Manager**: Penalty calculation and status updates
3. **Dunning Manager**: 7-step payment reminder escalation
4. **Payment Failure Handler**: Intelligent retry with exponential backoff
5. **Billing Monitor**: Health monitoring and alert system

### **Integration Points**:
- **Razorpay**: Payment processing and webhook handling
- **Resend**: Email notifications and OTP delivery
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT with role-based access control

---

## 🔐 Security & Access Control

### **Authentication Methods**:
- **Schools**: Email OTP verification (no passwords)
- **Admins**: Username/password with JWT tokens
- **Partners**: Secure login with referral code validation

### **Authorization Levels**:
- **Public**: Landing page and lead generation
- **School**: Own data access only
- **Admin**: Role-based system access
- **Partner**: Partner-specific data access

### **Data Protection**:
- Encrypted sensitive data at rest
- Secure API endpoints with proper validation
- Audit logging for all financial transactions
- Role-based data access restrictions

---

## 📊 Current Implementation Status

### **PRODUCTION READY**:
- ✅ Admin portal with complete functionality
- ✅ School authentication and profile management
- ✅ Billing automation and payment processing
- ✅ Partner revenue calculation system
- ✅ Razorpay integration with webhook handling

### **NEEDS IMPLEMENTATION**:
- ❌ School billing API endpoints (currently 501 placeholders)
- ❌ Partner portal frontend interface
- ❌ Email automation for billing notifications
- ❌ PDF invoice generation and delivery

### **NEXT STEPS**:
1. Implement functional school billing API endpoints
2. Create partner portal frontend interface
3. Add email automation for invoice delivery
4. Implement PDF generation for invoices
5. Complete end-to-end testing of all user flows

This multi-role system provides a comprehensive foundation for managing schools, administrators, and partners with proper security, billing automation, and revenue tracking capabilities.

---

## 🎯 User Journey Documentation

### **SCHOOL USER JOURNEY**

#### **1. Registration & Onboarding**
```
Landing Page → Lead Form → Email OTP → Profile Setup → Software Request
```

**Detailed Flow**:
1. **Lead Generation**: School visits landing page, fills contact form
2. **Email Verification**: Receives OTP via Resend, verifies email
3. **Profile Creation**: Completes school profile with basic information
4. **Software Request**: Submits demo or production software request
5. **Admin Review**: Waits for admin approval and subscription setup

#### **2. Billing & Payment Journey**
```
Subscription Active → Monthly Invoice → Payment Portal → Razorpay Gateway → Confirmation
```

**Detailed Flow**:
1. **Invoice Notification**: Receives email notification (when implemented)
2. **Billing Dashboard**: Logs into school portal, views pending invoices
3. **Payment Initiation**: Clicks "Pay Now" button for invoice
4. **Razorpay Integration**: Redirected to secure payment gateway
5. **Payment Confirmation**: Webhook updates status, school receives confirmation

#### **3. Ongoing Management**
```
Dashboard Access → Profile Updates → Billing History → Support Requests
```

---

### **ADMIN USER JOURNEY**

#### **1. Lead Management**
```
Lead Review → Client Conversion → Subscription Setup → Billing Activation
```

**Detailed Flow**:
1. **Lead Dashboard**: Reviews incoming leads and software requests
2. **Client Conversion**: Converts approved leads to active clients
3. **Subscription Creation**: Sets up per-student pricing and billing cycle
4. **Billing Activation**: Automated billing scheduler takes over

#### **2. Client Management**
```
Client Overview → Subscription Management → Billing Oversight → Support Resolution
```

**Detailed Flow**:
1. **Client Dashboard**: Comprehensive view of all active clients
2. **Subscription Updates**: Modify pricing, student counts, billing cycles
3. **Invoice Management**: Generate, review, and track invoice payments
4. **Issue Resolution**: Handle payment failures and client support requests

#### **3. Partner Management**
```
Partner Creation → Revenue Tracking → Withdrawal Processing → Performance Analysis
```

---

### **PARTNER USER JOURNEY**

#### **1. Referral Process**
```
Referral Code Generation → School Referral → Conversion Tracking → Revenue Calculation
```

**Detailed Flow**:
1. **Code Assignment**: Admin assigns unique referral code to partner
2. **School Referral**: Partner refers schools using referral code
3. **Conversion Tracking**: System tracks lead-to-client conversions
4. **Revenue Calculation**: Automatic earnings calculation on school payments

#### **2. Revenue Management**
```
Earnings Dashboard → Withdrawal Request → Admin Approval → NEFT Processing
```

**Detailed Flow**:
1. **Earnings Tracking**: Monitor monthly earnings and commission rates
2. **Withdrawal Request**: Submit monthly withdrawal request
3. **Admin Processing**: Admin reviews and approves withdrawal
4. **Payment Transfer**: NEFT transfer to partner bank account

---

## 🔧 API Architecture Details

### **Authentication Flow**
```typescript
// School Authentication
POST /api/auth/register
  → Email OTP sent via Resend
  → OTP verification
  → JWT token issued
  → School profile access granted

// Admin Authentication
POST /api/admin/auth/login
  → Username/password validation
  → Role-based JWT token issued
  → Admin dashboard access granted

// Partner Authentication
POST /api/partner/auth/login
  → Partner credentials validation
  → Partner-specific JWT token issued
  → Partner dashboard access granted
```

### **Data Access Patterns**
```typescript
// School Data Access (Restricted)
- Own profile data only
- Own billing and payment history
- Own software request status
- No access to other schools' data

// Admin Data Access (Role-Based)
- super_admin: Full system access
- billing: Client billing and subscription data
- support: Lead and client support data
- partner_manager: Partner and referral data

// Partner Data Access (Filtered)
- Own referral data only
- Own earnings and transaction history
- Referred schools' conversion status
- No access to other partners' data
```

### **Security Implementation**
```typescript
// JWT Token Structure
{
  userId: string,
  role: 'school' | 'admin' | 'partner',
  permissions: string[],
  schoolId?: string,    // For school users
  adminRole?: string,   // For admin users
  partnerId?: string    // For partner users
}

// Middleware Protection
- authMiddleware: Validates JWT tokens
- roleMiddleware: Checks role permissions
- schoolMiddleware: Restricts to own school data
- adminMiddleware: Validates admin role access
- partnerMiddleware: Restricts to own partner data
```

---

## 📈 System Metrics & Monitoring

### **Key Performance Indicators**
- **School Metrics**: Active subscriptions, payment success rate, churn rate
- **Admin Metrics**: Lead conversion rate, billing efficiency, support resolution time
- **Partner Metrics**: Referral conversion rate, earnings per partner, withdrawal frequency

### **Automated Monitoring**
- **Billing Health**: Monthly billing cycle success rate
- **Payment Processing**: Razorpay webhook response times
- **System Performance**: API response times and error rates
- **Security Monitoring**: Failed login attempts and suspicious activity

### **Reporting Capabilities**
- **Financial Reports**: Revenue, expenses, partner commissions
- **Operational Reports**: Client activity, subscription status, payment trends
- **Partner Reports**: Referral performance, earnings distribution, conversion metrics

This comprehensive multi-role system ensures secure, scalable, and efficient management of all stakeholders in the Schopio ecosystem.
