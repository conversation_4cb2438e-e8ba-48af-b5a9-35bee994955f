'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { AuthUtils } from '@/src/utils/authUtils'

interface PartnerAuthWrapperProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
}

export default function PartnerAuthWrapper({ 
  children, 
  requireAuth = true, 
  redirectTo 
}: PartnerAuthWrapperProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authenticated = AuthUtils.isAuthenticated('partner')
        setIsAuthenticated(authenticated)

        // Handle authentication logic
        if (requireAuth && !authenticated) {
          // Partner needs to be authenticated but isn't
          const loginUrl = `/partner/login?redirect=${encodeURIComponent(pathname)}`
          router.replace(loginUrl)
          return
        }

        if (!requireAuth && authenticated) {
          // Partner is authenticated but on a public page (like login)
          const dashboardUrl = redirectTo || '/partner/dashboard'
          router.replace(dashboardUrl)
          return
        }

        // Check if token is expiring soon
        if (authenticated && AuthUtils.isTokenExpiringSoon('partner')) {
          console.warn('Partner token expiring soon')
          // TODO: Implement token refresh or show warning
        }

      } catch (error) {
        console.error('Partner auth check failed:', error)
        AuthUtils.handleAuthError('partner', error)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()

    // Listen for storage changes (logout from another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'partnerToken') {
        checkAuth()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [requireAuth, redirectTo, router, pathname])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying partner authentication...</p>
        </div>
      </div>
    )
  }

  // Don't render children if auth state doesn't match requirements
  if (requireAuth && !isAuthenticated) {
    return null // Will redirect
  }

  if (!requireAuth && isAuthenticated) {
    return null // Will redirect
  }

  return <>{children}</>
}

// Higher-order component for protected partner routes
export function withPartnerAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: { requireAuth?: boolean; redirectTo?: string } = {}
) {
  return function AuthenticatedPartnerComponent(props: P) {
    return (
      <PartnerAuthWrapper 
        requireAuth={options.requireAuth ?? true}
        redirectTo={options.redirectTo}
      >
        <Component {...props} />
      </PartnerAuthWrapper>
    )
  }
}

// Hook for partner authentication state
export function usePartnerAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)
  const [partner, setPartner] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkAuth = () => {
      const authenticated = AuthUtils.isAuthenticated('partner')
      const partnerData = AuthUtils.getCurrentUser('partner')
      
      setIsAuthenticated(authenticated)
      setPartner(partnerData)
      setLoading(false)
    }

    checkAuth()

    // Listen for auth changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'partnerToken') {
        checkAuth()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  const login = (token: string) => {
    AuthUtils.setToken('partner', token)
    setIsAuthenticated(true)
    setPartner(AuthUtils.getCurrentUser('partner'))
  }

  const logout = () => {
    AuthUtils.logout('partner')
    setIsAuthenticated(false)
    setPartner(null)
  }

  return {
    isAuthenticated,
    partner,
    loading,
    login,
    logout
  }
}

// Partner permission checking hook
export function usePartnerPermission(requiredPermission: string) {
  const { partner, isAuthenticated, loading } = usePartnerAuth()
  
  const hasPermission = () => {
    if (!partner || !isAuthenticated) return false
    
    // Check if partner has the specific permission or 'all' permission
    return partner.permissions?.includes('all') || partner.permissions?.includes(requiredPermission)
  }

  return {
    hasPermission: hasPermission(),
    partner,
    isAuthenticated,
    loading
  }
}

// Partner status checking hook
export function usePartnerStatus() {
  const { partner, isAuthenticated, loading } = usePartnerAuth()
  
  const getStatus = () => {
    if (!partner || !isAuthenticated) return 'unauthenticated'
    
    if (!partner.isActive) return 'inactive'
    
    return 'active'
  }

  return {
    status: getStatus(),
    isActive: partner?.isActive || false,
    partner,
    isAuthenticated,
    loading
  }
}
