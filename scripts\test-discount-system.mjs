#!/usr/bin/env node

/**
 * Comprehensive Discount System Testing Suite
 * Tests all discount system APIs and service layer functionality
 */

import { Pool } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

class DiscountSystemTester {
  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: { rejectUnauthorized: false }
    });
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  async runTest(testName, testFunction) {
    this.testResults.total++;
    console.log(`🧪 Running: ${testName}`);
    
    try {
      await testFunction();
      this.testResults.passed++;
      console.log(`✅ PASSED: ${testName}`);
      this.testResults.details.push({ name: testName, status: 'PASSED', error: null });
    } catch (error) {
      this.testResults.failed++;
      console.log(`❌ FAILED: ${testName} - ${error.message}`);
      this.testResults.details.push({ name: testName, status: 'FAILED', error: error.message });
    }
  }

  async testDatabaseConnection() {
    const result = await this.pool.query('SELECT 1 as test');
    if (result.rows[0].test !== 1) {
      throw new Error('Database connection test failed');
    }
  }

  async testSchemaExists() {
    const tables = [
      'subscription_discounts',
      'subscription_expenses',
      'partner_commission_config',
      'partner_commission_transactions',
      'advance_payments'
    ];

    for (const table of tables) {
      const result = await this.pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [table]);
      
      if (!result.rows[0].exists) {
        throw new Error(`Table ${table} does not exist`);
      }
    }
  }

  async testIndexesExist() {
    const expectedIndexes = [
      'idx_subscription_discounts_active',
      'idx_commission_transactions_status',
      'idx_advance_payments_remaining',
      'idx_billing_subscriptions_discount'
    ];

    for (const indexName of expectedIndexes) {
      const result = await this.pool.query(`
        SELECT EXISTS (
          SELECT FROM pg_indexes 
          WHERE indexname = $1
        )
      `, [indexName]);
      
      if (!result.rows[0].exists) {
        throw new Error(`Index ${indexName} does not exist`);
      }
    }
  }

  async testBillingSubscriptionEnhancements() {
    const result = await this.pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'billing_subscriptions' 
      AND column_name IN ('has_active_discount', 'current_discount_percentage', 'discount_end_date')
    `);
    
    if (result.rows.length !== 3) {
      throw new Error('Billing subscriptions table missing discount enhancement fields');
    }
  }

  async testDiscountSystemConstraints() {
    // Test subscription_discounts constraints
    const discountConstraints = await this.pool.query(`
      SELECT constraint_name, constraint_type 
      FROM information_schema.table_constraints 
      WHERE table_name = 'subscription_discounts'
    `);
    
    const hasCheckConstraints = discountConstraints.rows.some(row => 
      row.constraint_type === 'CHECK'
    );
    
    if (!hasCheckConstraints) {
      console.log('⚠️  Warning: No CHECK constraints found on subscription_discounts');
    }
  }

  async testSampleDataOperations() {
    // Test if we can insert and query discount data (using a transaction that rolls back)
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Try to insert a test discount (this will be rolled back)
      const testSubscriptionId = '00000000-0000-0000-0000-000000000000';
      
      await client.query(`
        INSERT INTO subscription_discounts (
          subscription_id, discount_percentage, discount_duration_months,
          start_date, end_date, remaining_months, is_active, reason
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        testSubscriptionId, '20.00', 6, 
        '2025-07-01', '2025-12-31', 6, true, 'Test discount'
      ]);
      
      // Query the test data
      const result = await client.query(`
        SELECT * FROM subscription_discounts 
        WHERE subscription_id = $1
      `, [testSubscriptionId]);
      
      if (result.rows.length !== 1) {
        throw new Error('Failed to insert or query test discount data');
      }
      
      // Test decimal field parsing
      const discountPercentage = parseFloat(result.rows[0].discount_percentage);
      if (discountPercentage !== 20.00) {
        throw new Error('Decimal field parsing failed');
      }
      
      await client.query('ROLLBACK');
      
    } finally {
      client.release();
    }
  }

  async testTypeScriptCompilation() {
    // This test verifies that our service files compile without errors
    try {
      const { execSync } = await import('child_process');
      execSync('bunx tsc --noEmit --skipLibCheck', { 
        cwd: path.join(__dirname, '..'),
        stdio: 'pipe'
      });
    } catch (error) {
      throw new Error(`TypeScript compilation failed: ${error.message}`);
    }
  }

  async testPerformanceIndexes() {
    // Test that our performance indexes are actually being used
    const testQueries = [
      {
        name: 'Active discounts query',
        query: `
          EXPLAIN (FORMAT JSON) 
          SELECT * FROM subscription_discounts 
          WHERE subscription_id = '00000000-0000-0000-0000-000000000000' 
          AND is_active = true
        `
      },
      {
        name: 'Commission status query',
        query: `
          EXPLAIN (FORMAT JSON)
          SELECT * FROM partner_commission_transactions 
          WHERE status = 'eligible' 
          ORDER BY eligible_date
        `
      }
    ];

    for (const testQuery of testQueries) {
      const result = await this.pool.query(testQuery.query);
      const plan = result.rows[0]['QUERY PLAN'][0];
      
      // Check if index scan is being used (indicates our indexes are working)
      const planStr = JSON.stringify(plan);
      const hasIndexScan = planStr.includes('Index Scan') || planStr.includes('Bitmap Index Scan');
      
      if (!hasIndexScan) {
        console.log(`⚠️  Warning: ${testQuery.name} may not be using indexes optimally`);
      }
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Discount System Testing...');
    console.log('📅 Date:', new Date().toISOString());
    console.log('🎯 Testing: Database Schema, Indexes, TypeScript, and Performance\n');

    // Database Tests
    await this.runTest('Database Connection', () => this.testDatabaseConnection());
    await this.runTest('Schema Tables Exist', () => this.testSchemaExists());
    await this.runTest('Performance Indexes Exist', () => this.testIndexesExist());
    await this.runTest('Billing Subscription Enhancements', () => this.testBillingSubscriptionEnhancements());
    await this.runTest('Database Constraints', () => this.testDiscountSystemConstraints());
    
    // Data Operation Tests
    await this.runTest('Sample Data Operations', () => this.testSampleDataOperations());
    
    // Code Quality Tests
    await this.runTest('TypeScript Compilation', () => this.testTypeScriptCompilation());
    
    // Performance Tests
    await this.runTest('Performance Index Usage', () => this.testPerformanceIndexes());

    // Print Results
    console.log('\n📊 TEST RESULTS SUMMARY:');
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`📈 Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.testResults.details
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.error}`);
        });
    }

    if (this.testResults.failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('✨ Discount system is ready for production use');
    } else {
      console.log('\n⚠️  Some tests failed. Please review and fix issues before production deployment.');
    }

    await this.pool.end();
    return this.testResults.failed === 0;
  }
}

// Run the tests
const tester = new DiscountSystemTester();
tester.runAllTests()
  .then((success) => {
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('\n💥 Testing suite crashed:', error);
    process.exit(1);
  });
