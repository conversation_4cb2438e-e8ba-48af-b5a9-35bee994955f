import HeroSection from '@/components/sections/HeroSection'
import TrustIndicatorsSection from '@/components/sections/TrustIndicatorsSection'
import FeatureComparisonSection from '@/components/sections/FeatureComparisonSection'


import ImplementationSupportSection from '@/components/sections/ImplementationSupportSection'
import FAQSection from '@/components/sections/FAQSection'
import SimpleDemoRedirect from '@/components/sections/SimpleDemoRedirect'
import ExitIntentPopup from '@/components/ui/ExitIntentPopup'
import GoToTopButton from '@/components/ui/GoToTopButton'
import LazySection from '@/components/ui/LazySection'

export default function Home() {
  return (
    <main className="min-h-screen">
      {/* KEEP on homepage (in this order): */}

      {/* 1. Hero Section */}
      <HeroSection />

      {/* 2. System Capabilities (trust indicators) */}
      <TrustIndicatorsSection />

      {/* 3. MOVE UP: "Complete System vs. Partial Solutions" comparison section (for immediate impact) */}
      <LazySection loadingText="Loading feature comparison...">
        <FeatureComparisonSection />
      </LazySection>

      {/* 4. Implementation & Support section */}
      <LazySection loadingText="Loading implementation details...">
        <ImplementationSupportSection />
      </LazySection>

      {/* 5. FAQ section */}
      <LazySection loadingText="Loading FAQ section...">
        <FAQSection />
      </LazySection>

      {/* 6. Simple demo request component that redirects to /demo page */}
      <SimpleDemoRedirect />

      {/* Keep essential UI components */}
      <ExitIntentPopup />
      <GoToTopButton />
    </main>
  )
}
