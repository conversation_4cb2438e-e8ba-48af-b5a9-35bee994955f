// Debug test for referral code application issue
// Run with: node tests/debug-referral-apply.js

const BASE_URL = 'http://localhost:3000'

async function testReferralCodeApplication() {
  console.log('🔍 Testing Referral Code Application...\n')

  try {
    // Test 1: Check if the endpoint exists
    console.log('1. Testing endpoint availability...')
    const testResponse = await fetch(`${BASE_URL}/api/auth/referral/apply`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ code: 'TEST123' })
    })
    
    console.log(`   Status: ${testResponse.status}`)
    console.log(`   Status Text: ${testResponse.statusText}`)
    
    const testData = await testResponse.text()
    console.log(`   Response: ${testData.substring(0, 200)}...`)
    
    // Test 2: Check with proper authorization (you'll need a valid token)
    console.log('\n2. Testing with authorization header...')
    const authResponse = await fetch(`${BASE_URL}/api/auth/referral/apply`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer invalid-token-for-testing',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ code: 'TEST123' })
    })
    
    console.log(`   Status: ${authResponse.status}`)
    console.log(`   Status Text: ${authResponse.statusText}`)
    
    const authData = await authResponse.text()
    console.log(`   Response: ${authData.substring(0, 200)}...`)
    
    // Test 3: Check validation endpoint
    console.log('\n3. Testing validation endpoint...')
    const validateResponse = await fetch(`${BASE_URL}/api/auth/referral/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ code: 'TEST123' })
    })
    
    console.log(`   Status: ${validateResponse.status}`)
    console.log(`   Status Text: ${validateResponse.statusText}`)
    
    const validateData = await validateResponse.text()
    console.log(`   Response: ${validateData.substring(0, 200)}...`)
    
    // Test 4: Check with empty body
    console.log('\n4. Testing with empty body...')
    const emptyResponse = await fetch(`${BASE_URL}/api/auth/referral/apply`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    })
    
    console.log(`   Status: ${emptyResponse.status}`)
    console.log(`   Status Text: ${emptyResponse.statusText}`)
    
    const emptyData = await emptyResponse.text()
    console.log(`   Response: ${emptyData.substring(0, 200)}...`)
    
    // Test 5: Check with malformed JSON
    console.log('\n5. Testing with malformed JSON...')
    const malformedResponse = await fetch(`${BASE_URL}/api/auth/referral/apply`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: 'invalid-json'
    })
    
    console.log(`   Status: ${malformedResponse.status}`)
    console.log(`   Status Text: ${malformedResponse.statusText}`)
    
    const malformedData = await malformedResponse.text()
    console.log(`   Response: ${malformedData.substring(0, 200)}...`)
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
testReferralCodeApplication()
