import { db } from '@/src/db'
import { billingSubscriptions, clients } from '@/src/db/schema'
import { eq } from 'drizzle-orm'

export interface DiscountAuditLog {
  action: 'DISCOUNT_APPLIED' | 'DISCOUNT_EXPIRED' | 'DISCOUNT_MODIFIED' | 'DISCOUNT_CANCELLED'
  subscriptionId: string
  schoolName: string
  adminId?: string
  adminName?: string
  discountDetails: {
    percentage: number
    originalAmount: number
    discountedAmount: number
    startDate: Date
    endDate: Date
    durationMonths: number
    reason?: string
    monthlySavings: number
    totalSavings: number
  }
  timestamp: Date
  success: boolean
  errorMessage?: string
}

export interface NotificationData {
  type: 'DISCOUNT_APPLIED' | 'DISCOUNT_EXPIRED' | 'DISCOUNT_EXPIRING_SOON' | 'DISCOUNT_ERROR'
  recipients: string[]
  subject: string
  data: any
}

class DiscountAuditService {
  /**
   * Log discount application
   */
  async logDiscountApplication(
    subscriptionId: string,
    adminId: string,
    adminName: string,
    discountData: {
      percentage: number
      durationMonths: number
      startDate: Date
      reason?: string
    }
  ): Promise<void> {
    try {
      // Get subscription and school details
      const [subscription] = await db.select({
        originalAmount: billingSubscriptions.originalMonthlyAmount,
        discountedAmount: billingSubscriptions.monthlyAmount,
        schoolName: clients.schoolName
      })
      .from(billingSubscriptions)
      .innerJoin(clients, eq(billingSubscriptions.clientId, clients.id))
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        throw new Error('Subscription not found for audit logging')
      }

      const originalAmount = parseFloat(subscription.originalAmount || '0')
      const discountedAmount = parseFloat(subscription.discountedAmount || '0')
      const monthlySavings = originalAmount - discountedAmount
      const totalSavings = monthlySavings * discountData.durationMonths

      const endDate = new Date(discountData.startDate)
      endDate.setMonth(endDate.getMonth() + discountData.durationMonths)

      const auditLog: DiscountAuditLog = {
        action: 'DISCOUNT_APPLIED',
        subscriptionId,
        schoolName: subscription.schoolName,
        adminId,
        adminName,
        discountDetails: {
          percentage: discountData.percentage,
          originalAmount,
          discountedAmount,
          startDate: discountData.startDate,
          endDate,
          durationMonths: discountData.durationMonths,
          reason: discountData.reason,
          monthlySavings,
          totalSavings
        },
        timestamp: new Date(),
        success: true
      }

      await this.writeAuditLog(auditLog)
      await this.sendDiscountNotification({
        type: 'DISCOUNT_APPLIED',
        recipients: ['<EMAIL>'],
        subject: `Discount Applied: ${discountData.percentage}% for ${subscription.schoolName}`,
        data: auditLog
      })

      console.log(`📝 [Audit] Discount application logged for ${subscription.schoolName}`)

    } catch (error) {
      console.error('Error logging discount application:', error)
      // Don't throw - audit logging failure shouldn't stop the process
    }
  }

  /**
   * Log discount expiration
   */
  async logDiscountExpiration(
    subscriptionId: string,
    discountData: {
      percentage: number
      originalAmount: number
      discountedAmount: number
      schoolName: string
      monthlySavings: number
    }
  ): Promise<void> {
    try {
      const auditLog: DiscountAuditLog = {
        action: 'DISCOUNT_EXPIRED',
        subscriptionId,
        schoolName: discountData.schoolName,
        discountDetails: {
          percentage: discountData.percentage,
          originalAmount: discountData.originalAmount,
          discountedAmount: discountData.discountedAmount,
          startDate: new Date(), // Will be updated with actual data
          endDate: new Date(),
          durationMonths: 0, // Will be calculated
          monthlySavings: discountData.monthlySavings,
          totalSavings: 0 // Will be calculated
        },
        timestamp: new Date(),
        success: true
      }

      await this.writeAuditLog(auditLog)
      console.log(`📝 [Audit] Discount expiration logged for ${discountData.schoolName}`)

    } catch (error) {
      console.error('Error logging discount expiration:', error)
      // Don't throw - audit logging failure shouldn't stop the process
    }
  }

  /**
   * Log discount errors
   */
  async logDiscountError(
    subscriptionId: string,
    action: DiscountAuditLog['action'],
    errorMessage: string,
    schoolName?: string
  ): Promise<void> {
    try {
      const auditLog: DiscountAuditLog = {
        action,
        subscriptionId,
        schoolName: schoolName || 'Unknown',
        discountDetails: {
          percentage: 0,
          originalAmount: 0,
          discountedAmount: 0,
          startDate: new Date(),
          endDate: new Date(),
          durationMonths: 0,
          monthlySavings: 0,
          totalSavings: 0
        },
        timestamp: new Date(),
        success: false,
        errorMessage
      }

      await this.writeAuditLog(auditLog)
      
      // Send error notification to admin team
      await this.sendDiscountNotification({
        type: 'DISCOUNT_ERROR',
        recipients: ['<EMAIL>'],
        subject: `Discount System Error: ${action}`,
        data: { auditLog, errorMessage }
      })

      console.log(`📝 [Audit] Discount error logged for subscription ${subscriptionId}`)

    } catch (error) {
      console.error('Error logging discount error:', error)
      // Don't throw - audit logging failure shouldn't stop the process
    }
  }

  /**
   * Write audit log to storage
   */
  private async writeAuditLog(auditLog: DiscountAuditLog): Promise<void> {
    try {
      // For now, log to console with structured format
      console.log(`📊 [DISCOUNT AUDIT LOG] ${JSON.stringify({
        timestamp: auditLog.timestamp.toISOString(),
        action: auditLog.action,
        subscriptionId: auditLog.subscriptionId,
        schoolName: auditLog.schoolName,
        adminId: auditLog.adminId,
        adminName: auditLog.adminName,
        success: auditLog.success,
        discountPercentage: auditLog.discountDetails.percentage,
        originalAmount: auditLog.discountDetails.originalAmount,
        discountedAmount: auditLog.discountDetails.discountedAmount,
        monthlySavings: auditLog.discountDetails.monthlySavings,
        totalSavings: auditLog.discountDetails.totalSavings,
        durationMonths: auditLog.discountDetails.durationMonths,
        reason: auditLog.discountDetails.reason,
        errorMessage: auditLog.errorMessage
      }, null, 2)}`)

      // TODO: Implement database audit table
      // await db.insert(auditLogs).values({
      //   action: auditLog.action,
      //   entityType: 'subscription_discount',
      //   entityId: auditLog.subscriptionId,
      //   details: JSON.stringify(auditLog.discountDetails),
      //   performedBy: auditLog.adminId || 'SYSTEM',
      //   performedAt: auditLog.timestamp,
      //   success: auditLog.success,
      //   errorMessage: auditLog.errorMessage
      // })

    } catch (error) {
      console.error('Error writing audit log:', error)
      // Don't throw - logging failure shouldn't stop the process
    }
  }

  /**
   * Send discount-related notifications
   */
  private async sendDiscountNotification(notification: NotificationData): Promise<void> {
    try {
      console.log(`📧 [DISCOUNT NOTIFICATION] ${notification.type}:
        Subject: ${notification.subject}
        Recipients: ${notification.recipients.join(', ')}
        Data: ${JSON.stringify(notification.data, null, 2)}
      `)

      // TODO: Implement email service integration
      // await emailService.sendDiscountNotification({
      //   to: notification.recipients,
      //   subject: notification.subject,
      //   template: notification.type,
      //   data: notification.data
      // })

    } catch (error) {
      console.error('Error sending discount notification:', error)
      // Don't throw - notification failure shouldn't stop the process
    }
  }

  /**
   * Get discount audit history for a subscription
   */
  async getDiscountAuditHistory(subscriptionId: string): Promise<DiscountAuditLog[]> {
    try {
      // TODO: Implement database query for audit history
      // const auditLogs = await db.select()
      //   .from(auditLogs)
      //   .where(and(
      //     eq(auditLogs.entityType, 'subscription_discount'),
      //     eq(auditLogs.entityId, subscriptionId)
      //   ))
      //   .orderBy(desc(auditLogs.performedAt))

      // For now, return empty array
      console.log(`📋 [Audit History] Requested for subscription ${subscriptionId}`)
      return []

    } catch (error) {
      console.error('Error getting discount audit history:', error)
      return []
    }
  }

  /**
   * Generate discount analytics report
   */
  async generateDiscountAnalyticsReport(): Promise<{
    totalDiscountsApplied: number
    totalSavingsProvided: number
    averageDiscountPercentage: number
    mostCommonDuration: number
    topReasons: { reason: string; count: number }[]
  }> {
    try {
      // TODO: Implement analytics queries
      console.log(`📊 [Analytics] Generating discount analytics report`)
      
      return {
        totalDiscountsApplied: 0,
        totalSavingsProvided: 0,
        averageDiscountPercentage: 0,
        mostCommonDuration: 0,
        topReasons: []
      }

    } catch (error) {
      console.error('Error generating discount analytics report:', error)
      return {
        totalDiscountsApplied: 0,
        totalSavingsProvided: 0,
        averageDiscountPercentage: 0,
        mostCommonDuration: 0,
        topReasons: []
      }
    }
  }
}

export const discountAuditService = new DiscountAuditService()
