import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../globals.css";
import { PartnerLayoutClient } from "./PartnerLayoutClient";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Partner Portal | Schopio",
  description: "Schopio Partner Portal - Manage your referrals and track earnings",
};

export default function PartnerPortalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased`}>
        <PartnerLayoutClient>
          {children}
        </PartnerLayoutClient>
      </body>
    </html>
  );
}
