# School Management System SaaS - System Architecture

## 🏗️ System Overview

Our School Management System SaaS platform consists of three main components designed to handle the complete client lifecycle from lead generation to subscription management.

### Core Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Landing Page  │    │ Admin Dashboard │    │  School Portal  │
│                 │    │                 │    │                 │
│ • Lead Gen      │    │ • Lead Mgmt     │    │ • Self-Service  │
│ • Demo Booking  │    │ • Client Onboard│    │ • Payment Mgmt  │
│ • Feature Comp  │    │ • Billing Mgmt  │    │ • Support       │
│ • Trust Build   │    │ • Analytics     │    │ • Subscription  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Shared Backend │
                    │                 │
                    │ • Hono.js API   │
                    │ • Neon Postgres │
                    │ • Drizzle ORM   │
                    │ • Razorpay      │
                    └─────────────────┘
```

## 🎯 Component Responsibilities

### 1. Landing Page (Public)
**Purpose**: Lead generation and conversion
- **Target Users**: Prospective school administrators
- **Key Features**:
  - Hero section with competitive advantages
  - Interactive feature comparison table
  - Demo booking system with calendar integration
  - Trust-building elements (testimonials, case studies)
  - Contact forms with lead capture
  - Pricing calculator based on student count

### 2. Admin Dashboard (Internal)
**Purpose**: Business operations and client management
- **Target Users**: Our internal team (sales, support, billing)
- **Key Features**:
  - Lead management pipeline (inquiry → demo → proposal → client)
  - Client onboarding workflow with checklists
  - Dynamic pricing management (₹80/student/month base)
  - Subscription plan configuration (monthly/yearly discounts)
  - Billing cycle management with pro-rated calculations
  - Payment tracking and invoice generation
  - Client communication history and notes
  - Analytics dashboard with KPIs and metrics

### 3. School Portal (Client Self-Service)
**Purpose**: Client subscription and support management
- **Target Users**: School administrators (our clients)
- **Key Features**:
  - Secure school login system
  - Subscription status and billing history
  - Payment management through Razorpay
  - Student count updates and verification
  - Support ticket system
  - Invoice downloads and payment receipts
  - Plan upgrade/downgrade requests

## 🔄 Data Flow Architecture

### Lead to Client Journey
```
Landing Page → Admin Dashboard → School Portal
     │              │               │
     ▼              ▼               ▼
Lead Capture → Lead Management → Client Management
Contact Form → Demo Scheduling → Subscription Setup
Feature Demo → Proposal Creation → Payment Processing
Trust Building → Contract Signing → Ongoing Support
```

### Billing Data Flow
```
School Portal (Student Count) → Admin Dashboard (Billing Calc) → Razorpay (Payment)
        │                              │                           │
        ▼                              ▼                           ▼
Student Updates → Pro-rated Billing → Payment Processing → Invoice Generation
Verification   → Subscription Mgmt  → Payment Tracking  → Receipt Delivery
```

## 🛠️ Technical Architecture

### Backend Stack
- **API Framework**: Hono.js with method chaining
- **Database**: Neon PostgreSQL (serverless)
- **ORM**: Drizzle with TypeScript schemas
- **Payment Gateway**: Razorpay (test mode initially)
- **Authentication**: JWT with role-based access

### Frontend Stack
- **Framework**: React 18 with Vite
- **State Management**: TanStack Query for server state
- **Styling**: Tailwind CSS with design system
- **Animations**: Framer Motion for smooth interactions

### Multi-Tenant Strategy
- **Database**: Single database with tenant isolation
- **Schema**: `tenant_id` field in all relevant tables
- **Security**: Row-level security policies
- **Data Isolation**: Middleware-enforced tenant filtering

## 🔐 Security Architecture

### Authentication Layers
1. **Public Access**: Landing page (no auth required)
2. **Admin Access**: Internal dashboard (admin JWT tokens)
3. **Client Access**: School portal (client JWT tokens)

### Data Protection
- **Encryption**: All sensitive data encrypted at rest
- **API Security**: Rate limiting and input validation
- **Payment Security**: PCI compliance through Razorpay
- **Access Control**: Role-based permissions (admin, client, viewer)

## 📊 Database Architecture

### Core Entities
```
Leads → Clients → Subscriptions → Billing → Payments
  │       │           │            │         │
  ▼       ▼           ▼            ▼         ▼
Demo    School     Student      Invoice   Transaction
Booking Portal     Count        History   Records
```

### Key Relationships
- **One Lead** → **One Client** (conversion)
- **One Client** → **One Subscription** (active plan)
- **One Subscription** → **Many Billing Cycles** (monthly/yearly)
- **One Billing Cycle** → **Many Payment Attempts** (retry logic)

## 🚀 Scalability Considerations

### Performance Optimization
- **Database**: Connection pooling and query optimization
- **API**: Response caching and pagination
- **Frontend**: Code splitting and lazy loading
- **CDN**: Static asset delivery optimization

### Growth Planning
- **Horizontal Scaling**: Stateless API design
- **Database Scaling**: Read replicas for analytics
- **Monitoring**: Application performance monitoring
- **Backup Strategy**: Automated daily backups

## 🔄 Integration Points

### External Services
- **Razorpay**: Payment processing and webhooks
- **Email Service**: Transactional emails (invoices, reminders)
- **SMS Service**: Payment reminders and notifications
- **Analytics**: Usage tracking and business metrics

### Internal APIs
- **Lead API**: Capture and management endpoints
- **Billing API**: Subscription and payment processing
- **Client API**: School portal functionality
- **Admin API**: Dashboard and management features

## 📈 Business Logic Flow

### Subscription Lifecycle
1. **Lead Generation**: Landing page captures interest
2. **Demo Booking**: Prospect schedules product demo
3. **Proposal**: Admin creates custom pricing proposal
4. **Contract**: Client signs agreement and provides details
5. **Onboarding**: Admin sets up school portal access
6. **Billing Setup**: Pro-rated first payment calculation
7. **Ongoing Billing**: Monthly/yearly recurring payments
8. **Support**: Ticket system for client assistance

### Billing Calculation Logic
```javascript
// Pro-rated billing example
const calculateProRatedAmount = (startDate, monthlyRate, studentCount) => {
  const daysInMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0).getDate();
  const remainingDays = daysInMonth - startDate.getDate() + 1;
  const dailyRate = monthlyRate / daysInMonth;
  return dailyRate * remainingDays * studentCount;
};
```

This architecture ensures scalability, security, and maintainability while providing a seamless experience across all three system components.
