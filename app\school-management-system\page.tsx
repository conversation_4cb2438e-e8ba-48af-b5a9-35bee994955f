import { Metadata } from 'next'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Breadcrumb, BreadcrumbStructuredData } from '@/components/ui/Breadcrumb'
import {
  Users,
  BookOpen,
  Calendar,
  CreditCard,
  BarChart3,
  Shield,
  Clock,
  CheckCircle,
  ArrowRight,
  Star,
  Award,
  Zap
} from 'lucide-react'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Best School Management System in India | Complete Educational Software | Schopio',
  description: 'Schopio is the leading school management system in India. Complete educational software with student information system, attendance tracking, fee management, and academic tools. Trusted by 500+ schools.',
  keywords: 'school management system, school management system India, best school management system, educational software, student information system, school administration software, academic management system, school management platform',
  openGraph: {
    title: 'Best School Management System in India | Complete Educational Software | Schopio',
    description: 'Schopio is the leading school management system in India. Complete educational software with student information system, attendance tracking, fee management, and academic tools. Trusted by 500+ schools.',
    type: 'website',
  },
}

export default function SchoolManagementSystemPage() {
  const breadcrumbItems = [
    { label: 'School Management System' }
  ]

  const features = [
    {
      icon: Users,
      title: "Student Information System",
      description: "Complete student database with academic records, attendance, and performance tracking",
      benefits: ["Digital student profiles", "Academic history tracking", "Parent communication portal"]
    },
    {
      icon: BookOpen,
      title: "Academic Management",
      description: "Comprehensive curriculum planning, timetable management, and examination system",
      benefits: ["Curriculum planning", "Exam management", "Grade book system"]
    },
    {
      icon: Calendar,
      title: "Attendance Management",
      description: "Real-time attendance tracking with automated notifications and reports",
      benefits: ["Digital attendance", "Automated alerts", "Attendance analytics"]
    },
    {
      icon: CreditCard,
      title: "Fee Management",
      description: "Complete billing system with online payments and financial reporting",
      benefits: ["Online fee collection", "Payment tracking", "Financial reports"]
    },
    {
      icon: BarChart3,
      title: "Analytics & Reports",
      description: "Comprehensive reporting with AI-powered insights and performance analytics",
      benefits: ["Performance analytics", "Custom reports", "Data insights"]
    },
    {
      icon: Shield,
      title: "Security & Privacy",
      description: "Enterprise-grade security with role-based access and data protection",
      benefits: ["Data encryption", "Role-based access", "Privacy compliance"]
    }
  ]

  const benefits = [
    "Complete school management in one platform",
    "Reduce administrative workload by 60%",
    "Improve parent-teacher communication",
    "Real-time academic performance tracking",
    "Automated fee collection and billing",
    "Comprehensive reporting and analytics",
    "Mobile-friendly interface for all users",
    "24/7 customer support and training"
  ]

  const testimonials = [
    {
      name: "Dr. Priya Sharma",
      role: "Principal, Delhi Public School",
      content: "Schopio has transformed our school administration. The best school management system we've used.",
      rating: 5
    },
    {
      name: "Rajesh Kumar",
      role: "Administrator, St. Mary's School",
      content: "Complete educational software solution that covers all our needs. Highly recommended!",
      rating: 5
    }
  ]

  return (
    <main className="min-h-screen bg-white">
      <BreadcrumbStructuredData items={breadcrumbItems} />

      {/* Breadcrumb Navigation */}
      <section className="py-4 bg-white border-b">
        <div className="container mx-auto px-4">
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-emerald-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-6">
              <Star className="w-4 h-4" />
              #1 School Management System in India
            </div>
            <h1 className="text-5xl lg:text-7xl font-bold text-slate-900 mb-6">
              Best School Management 
              <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> System</span>
            </h1>
            <p className="text-xl text-slate-600 leading-relaxed mb-8">
              Schopio is India's leading school management system trusted by 500+ schools. 
              Complete educational software with student information system, academic management, 
              fee collection, and comprehensive school administration tools.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                  Get Free Demo <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/packages">
                <Button variant="outline" size="lg">
                  View Pricing
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-4">
              Complete School Management Features
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Everything you need to manage your school efficiently in one comprehensive platform
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-2 hover:border-blue-200 transition-colors">
                <CardHeader>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900">{feature.title}</h3>
                  <p className="text-slate-600">{feature.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center gap-2 text-sm text-slate-600">
                        <CheckCircle className="w-4 h-4 text-emerald-500" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-slate-900 mb-4">
                Why Choose Schopio School Management System?
              </h2>
              <p className="text-xl text-slate-600">
                Join 500+ schools that trust Schopio for their educational management needs
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-6 h-6 text-emerald-500 flex-shrink-0" />
                  <span className="text-slate-700">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-900 mb-4">
              What Schools Say About Our Management System
            </h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-2">
                <CardContent className="p-6">
                  <div className="flex gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-slate-700 mb-4">"{testimonial.content}"</p>
                  <div>
                    <p className="font-semibold text-slate-900">{testimonial.name}</p>
                    <p className="text-sm text-slate-600">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-emerald-600">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Transform Your School Management?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join 500+ schools using Schopio's comprehensive school management system. 
              Get started with a free demo today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                  Get Free Demo <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/packages">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-blue-600">
                  View Pricing Plans
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
