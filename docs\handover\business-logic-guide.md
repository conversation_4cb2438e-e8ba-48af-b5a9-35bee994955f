# 🏢 Schopio Business Logic & Operational Guide

## 📋 **Business Overview**

Schopio is a SaaS educational platform offering school management solutions with a 3-portal architecture and AI-powered insights. The platform operates on a partner referral model with profit-sharing commissions.

## 💰 **Financial Business Model**

### **Revenue Structure**
- **Primary Revenue:** Monthly subscription fees from schools
- **Pricing Model:** Dynamic per-student pricing (₹20-250/student/month)
- **Billing Frequency:** Monthly with yearly discount option (2 months free)
- **Payment Method:** Manual monthly payments (no auto-subscriptions due to large amounts)

### **Cost Structure**
- **Operational Expenses:** Database, website maintenance, support, infrastructure
- **Partner Commissions:** 20-50% of net profit (after expenses)
- **Admin Profit:** Remaining amount after expenses and partner commissions

### **Profit Distribution Formula**
```
School Payment: ₹24,000
- Operational Expenses: ₹5,200
= Net Profit: ₹18,800
- Partner Commission (50%): ₹9,400
= Admin Final Earnings: ₹9,400
```

## 🤝 **Partner Referral System**

### **Partner Onboarding**
- Partners are created by admin (not self-registration)
- Each partner gets a unique, non-transferable referral code
- Commission percentage is set during partner creation (20-50%)

### **Referral Process**
1. Partner shares referral code with potential schools
2. School uses code during software request submission
3. System creates referral link in database
4. When school makes payments, partner earns commission automatically

### **Commission Calculation**
- **Trigger:** School payment success
- **Base Amount:** School payment minus operational expenses and discounts
- **Commission:** Net profit × Partner commission percentage
- **Payment:** Monthly withdrawal requests processed by admin

### **Commission Holding & Release**
- **Immediate:** Commission calculated upon school payment
- **Holding Period:** Currently manual release (auto-release system incomplete)
- **Withdrawal:** Partners submit monthly withdrawal requests
- **Processing:** Admin approves and processes payouts

## 🏫 **School Subscription Lifecycle**

### **Lead to Client Conversion**
1. **Demo Request:** School requests demo access
2. **Demo Period:** Limited access to test features
3. **Production Upgrade:** School requests full production access
4. **Subscription Creation:** Admin creates paid subscription
5. **Payment Processing:** School makes monthly payments

### **Subscription Management**
- **Billing Cycle:** 30-day cycles starting from subscription start date
- **Due Date:** Configurable (default: 1st of each month)
- **Grace Period:** 3 days after due date
- **Penalties:** 2% daily penalty after grace period
- **Status Tracking:** Active, Overdue, Suspended, Cancelled

### **Payment Processing**
- **Method:** Razorpay integration for online payments
- **Manual Payments:** Admin can record offline payments
- **Invoice Generation:** Automatic PDF generation and email delivery
- **Receipt System:** Automatic receipt generation upon payment

## 🎛️ **Admin Operations**

### **Subscription Management**
- **Creation:** Admin creates subscriptions with pricing and terms
- **Modification:** Edit student count, pricing, billing cycle
- **Discount Application:** Percentage-based discounts with duration
- **Expense Tracking:** Operational expenses per subscription

### **Financial Management**
- **Revenue Tracking:** Monthly and yearly revenue analytics
- **Expense Management:** Track operational costs per subscription
- **Partner Payouts:** Process commission withdrawals
- **Profit Analysis:** Net earnings after all deductions

### **Client Support**
- **Software Requests:** Manage demo and production requests
- **Billing Support:** Handle payment issues and disputes
- **Technical Support:** Route tickets to appropriate partners
- **Account Management:** Subscription modifications and cancellations

## 📊 **Key Performance Indicators (KPIs)**

### **Revenue Metrics**
- **Monthly Recurring Revenue (MRR):** Total active subscription revenue
- **Annual Recurring Revenue (ARR):** MRR × 12
- **Average Revenue Per User (ARPU):** Total revenue ÷ active clients
- **Revenue Growth Rate:** Month-over-month revenue increase

### **Partner Metrics**
- **Total Partners:** Number of active referral partners
- **Conversion Rate:** Referred leads that become paying clients
- **Commission Payout:** Total commissions paid to partners
- **Partner Retention:** Partners with active referrals

### **Client Metrics**
- **Client Acquisition Cost (CAC):** Cost to acquire new client
- **Client Lifetime Value (CLV):** Total revenue from client relationship
- **Churn Rate:** Percentage of clients cancelling subscriptions
- **Payment Success Rate:** Successful payments vs failed attempts

## 🔄 **Operational Workflows**

### **Monthly Billing Process**
1. **Invoice Generation:** System generates invoices for all active subscriptions
2. **Email Delivery:** Invoices sent to school administrators
3. **Payment Processing:** Schools make payments via Razorpay
4. **Commission Calculation:** Partner commissions calculated automatically
5. **Financial Reporting:** Monthly revenue and expense reports generated

### **Partner Commission Workflow**
1. **Payment Trigger:** School makes successful payment
2. **Commission Calculation:** System calculates partner share
3. **Escrow Creation:** Commission held in escrow system
4. **Release Conditions:** Manual or automatic release based on criteria
5. **Withdrawal Processing:** Partner requests withdrawal, admin processes

### **Support Ticket Routing**
1. **Ticket Creation:** School creates support ticket
2. **Partner Check:** System checks if school has referral partner
3. **Routing Decision:** Ticket routed to partner or admin support
4. **Resolution Tracking:** Status updates and resolution monitoring
5. **Satisfaction Survey:** Post-resolution feedback collection

## 🚨 **Critical Business Rules**

### **Payment & Billing**
- **No GST Registration:** Invoices do not include GST numbers
- **Manual Payments Only:** No automatic subscription charges
- **Grace Period:** 3-day grace period for all payments
- **Penalty Calculation:** 2% daily penalty after grace period
- **Discount Limitations:** Maximum discount percentage and duration limits

### **Partner Commissions**
- **Profit-Based Only:** Commissions calculated on net profit, not gross revenue
- **Non-Transferable:** Referral codes cannot be transferred between partners
- **Monthly Withdrawals:** Partners can only withdraw once per month
- **Minimum Threshold:** Minimum withdrawal amount requirements
- **Holding Period:** Commission holding for risk management

### **Subscription Rules**
- **Student Count Validation:** Actual student count must match subscription
- **Billing Cycle Consistency:** Cannot change billing cycle mid-cycle
- **Upgrade/Downgrade:** Changes take effect next billing cycle
- **Cancellation Policy:** 30-day notice required for cancellations
- **Refund Policy:** Pro-rated refunds for mid-cycle cancellations

## 📈 **Growth Strategy**

### **Partner Network Expansion**
- **Target Partners:** Educational consultants, technology integrators
- **Commission Structure:** Competitive rates to attract quality partners
- **Training Program:** Partner onboarding and product training
- **Performance Incentives:** Bonus commissions for high-performing partners

### **Market Penetration**
- **Geographic Expansion:** Target tier-2 and tier-3 cities
- **Product Differentiation:** AI-powered insights and analytics
- **Competitive Pricing:** Flexible pricing based on school size
- **Customer Success:** Focus on retention and expansion

### **Technology Enhancement**
- **AI Integration:** Enhanced analytics and predictive insights
- **Mobile Applications:** iOS and Android apps for stakeholders
- **API Ecosystem:** Third-party integrations and marketplace
- **Automation:** Reduce manual processes and improve efficiency

## 🎯 **Success Factors**

### **Operational Excellence**
- **Reliable Platform:** 99.9% uptime and performance
- **Customer Support:** Responsive and knowledgeable support team
- **Financial Transparency:** Clear billing and commission structures
- **Partner Satisfaction:** Fair and timely commission payments

### **Market Position**
- **Value Proposition:** Cost-effective solution with AI insights
- **Customer Retention:** High satisfaction and low churn rates
- **Partner Loyalty:** Strong referral network with motivated partners
- **Competitive Advantage:** Unique features and superior service

---

**Document Version:** 1.0  
**Last Updated:** July 8, 2025  
**Next Review:** August 8, 2025
