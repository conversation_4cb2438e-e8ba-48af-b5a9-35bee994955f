import { Resend } from 'resend'
import { pdfInvoiceService } from './pdfInvoiceService'

interface EmailOptions {
  to: string | string[]
  subject: string
  html: string
  from?: string
  replyTo?: string
  attachments?: Array<{
    filename: string
    content: Buffer | string
    contentType?: string
  }>
}

interface BillingEmailData {
  schoolName: string | null
  contactPerson: string | null
  email: string | null
  invoiceNumber: string
  amount: string
  dueDate: string
  paymentUrl?: string
  receiptUrl?: string
  daysOverdue?: number
  penaltyAmount?: string
}

interface AdminAlertData {
  alertType: 'billing_failure' | 'critical_error' | 'system_health' | 'payment_failure'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  details?: any
  affectedClients?: string[]
  timestamp: string
  systemMetrics?: {
    failedBillings?: number
    overdueInvoices?: number
    systemLoad?: string
    errorRate?: string
  }
}

class EmailService {
  private resend: Resend
  private defaultFrom: string

  constructor() {
    this.resend = new Resend(process.env.RESEND_API_KEY)
    this.defaultFrom = `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`
  }

  /**
   * Send a basic email
   */
  async sendEmail(options: EmailOptions) {
    try {
      const result = await this.resend.emails.send({
        from: options.from || this.defaultFrom,
        to: options.to,
        subject: options.subject,
        html: options.html,
        replyTo: options.replyTo || process.env.FROM_EMAIL,
        attachments: options.attachments
      })

      console.log('Email sent successfully:', result.data?.id)
      return { success: true, id: result.data?.id }
    } catch (error) {
      console.error('Failed to send email:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Send payment reminder email (3 days before due date)
   */
  async sendPaymentReminder(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getPaymentReminderTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `Payment Reminder - Invoice ${data.invoiceNumber} Due Soon`,
      html
    })
  }

  /**
   * Send overdue payment notice
   */
  async sendOverdueNotice(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getOverdueNoticeTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `URGENT: Overdue Payment - Invoice ${data.invoiceNumber}`,
      html
    })
  }

  /**
   * Send invoice generated notification
   */
  async sendInvoiceGenerated(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getInvoiceGeneratedTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `New Invoice Generated - ${data.invoiceNumber}`,
      html
    })
  }

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmation(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getPaymentConfirmationTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `Payment Received - Invoice ${data.invoiceNumber}`,
      html
    })
  }

  /**
   * Send final notice before suspension
   */
  async sendFinalNotice(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getFinalNoticeTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `FINAL NOTICE: Account Suspension Warning - Invoice ${data.invoiceNumber}`,
      html
    })
  }

  /**
   * Send invoice email with PDF attachment
   */
  async sendInvoiceWithPDF(data: BillingEmailData & { invoiceId: string }) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    try {
      // Generate PDF invoice
      const pdfResult = await pdfInvoiceService.generateInvoicePDF(data.invoiceId)

      if (!pdfResult.success) {
        console.error('Failed to generate PDF for email attachment:', pdfResult.error)
        // Fall back to sending email without attachment
        return this.sendInvoiceGenerated(data)
      }

      const html = this.getInvoiceGeneratedTemplate(data)

      return this.sendEmail({
        to: data.email,
        subject: `Invoice ${data.invoiceNumber} - ${data.schoolName}`,
        html,
        attachments: [{
          filename: pdfResult.fileName || `invoice-${data.invoiceNumber}.pdf`,
          content: pdfResult.pdfBuffer!,
          contentType: 'application/pdf'
        }]
      })
    } catch (error) {
      console.error('Error sending invoice with PDF:', error)
      // Fall back to sending email without attachment
      return this.sendInvoiceGenerated(data)
    }
  }

  /**
   * Send administrator alert email for billing system issues
   */
  async sendAdminAlert(data: AdminAlertData) {
    const adminEmails = process.env.ADMIN_ALERT_EMAILS?.split(',') || ['<EMAIL>']

    if (!adminEmails.length) {
      return { success: false, error: 'No admin email addresses configured' }
    }

    const html = this.getAdminAlertTemplate(data)
    const subject = `🚨 ${data.severity.toUpperCase()} ALERT: ${data.title}`

    return this.sendEmail({
      to: adminEmails,
      subject,
      html
    })
  }

  /**
   * Send billing failure notification to administrators
   */
  async sendBillingFailureAlert(details: {
    error: string
    affectedClients: string[]
    failedOperations: number
    timestamp: string
  }) {
    const alertData: AdminAlertData = {
      alertType: 'billing_failure',
      severity: 'high',
      title: 'Billing System Failure Detected',
      description: `Billing operations have failed for ${details.affectedClients.length} clients`,
      details: {
        error: details.error,
        failedOperations: details.failedOperations,
        affectedClients: details.affectedClients
      },
      affectedClients: details.affectedClients,
      timestamp: details.timestamp
    }

    return this.sendAdminAlert(alertData)
  }

  /**
   * Send critical system alert to administrators
   */
  async sendCriticalSystemAlert(details: {
    title: string
    description: string
    systemMetrics?: any
    timestamp: string
  }) {
    const alertData: AdminAlertData = {
      alertType: 'critical_error',
      severity: 'critical',
      title: details.title,
      description: details.description,
      details: details.systemMetrics,
      timestamp: details.timestamp,
      systemMetrics: details.systemMetrics
    }

    return this.sendAdminAlert(alertData)
  }

  /**
   * Send payment confirmation email with invoice PDF attachment
   */
  async sendPaymentConfirmationWithPDF(data: BillingEmailData & { invoiceId: string }) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    try {
      // Generate PDF invoice (now marked as paid)
      const pdfResult = await pdfInvoiceService.generateInvoicePDF(data.invoiceId)

      if (!pdfResult.success) {
        console.error('Failed to generate PDF for payment confirmation:', pdfResult.error)
        // Fall back to sending email without attachment
        return this.sendPaymentConfirmation(data)
      }

      const html = this.getPaymentConfirmationTemplate(data)

      return this.sendEmail({
        to: data.email,
        subject: `Payment Confirmed - Invoice ${data.invoiceNumber}`,
        html,
        attachments: [{
          filename: pdfResult.fileName || `receipt-${data.invoiceNumber}.pdf`,
          content: pdfResult.pdfBuffer!,
          contentType: 'application/pdf'
        }]
      })
    } catch (error) {
      console.error('Error sending payment confirmation with PDF:', error)
      // Fall back to sending email without attachment
      return this.sendPaymentConfirmation(data)
    }
  }

  /**
   * Send overdue payment alert to admin
   */
  async sendOverduePaymentAlert(data: {
    schoolName: string
    amount: number
    daysOverdue: number
    penaltyAmount: number
    adminEmail: string
  }) {
    const alertData: AdminAlertData = {
      alertType: 'payment_failure',
      severity: data.daysOverdue > 15 ? 'critical' : data.daysOverdue > 7 ? 'high' : 'medium',
      title: `Payment ${data.daysOverdue} Days Overdue`,
      description: `${data.schoolName} has a payment that is ${data.daysOverdue} days overdue with ₹${data.penaltyAmount.toLocaleString()} penalty applied.`,
      details: {
        schoolName: data.schoolName,
        originalAmount: data.amount,
        daysOverdue: data.daysOverdue,
        penaltyAmount: data.penaltyAmount,
        totalAmount: data.amount + data.penaltyAmount
      },
      timestamp: new Date().toISOString(),
      systemMetrics: {
        overdueInvoices: 1
      }
    }

    return this.sendEmail({
      to: data.adminEmail,
      subject: `🚨 URGENT: Payment ${data.daysOverdue} Days Overdue - ${data.schoolName}`,
      html: this.getAdminAlertTemplate(alertData)
    })
  }

  /**
   * Get base email template wrapper
   */
  private getBaseTemplate(content: string): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schopio</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f9fafb; }
        .email-container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .email-header { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; padding: 30px 20px; text-align: center; }
        .email-body { padding: 30px 20px; }
        .email-footer { background: #f3f4f6; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb; }
        .btn { display: inline-block; padding: 12px 30px; background: #2563eb; color: white; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 20px 0; }
        .btn-warning { background: #f59e0b; }
        .btn-danger { background: #ef4444; }
        .btn-success { background: #10b981; }
        .invoice-details { background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb; }
        .warning-box { background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
        .danger-box { background: #fee2e2; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ef4444; }
        .amount { font-size: 24px; font-weight: bold; color: #2563eb; }
        .overdue-amount { color: #ef4444; }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1 style="margin: 0; font-size: 28px;">Schopio</h1>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">School Management Platform</p>
        </div>
        <div class="email-body">
            ${content}
        </div>
        <div class="email-footer">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
                © 2025 Schopio. All rights reserved.
            </p>
            <p style="color: #6b7280; font-size: 12px; margin: 10px 0 0 0;">
                <a href="mailto:${process.env.FROM_EMAIL}" style="color: #2563eb;">Support</a> | 
                <a href="#" style="color: #2563eb;">Privacy Policy</a>
            </p>
        </div>
    </div>
</body>
</html>`
  }

  /**
   * Payment reminder template (3 days before due)
   */
  private getPaymentReminderTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #f59e0b; margin-bottom: 20px;">Payment Due in 3 Days</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>This is a friendly reminder that your monthly subscription payment for <strong>${data.schoolName || 'your school'}</strong> is due in 3 days.</p>

<div class="invoice-details">
    <h3 style="color: #2563eb; margin-top: 0;">Invoice Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount:</strong> <span class="amount">₹${data.amount}</span></p>
    <p><strong>Due Date:</strong> ${data.dueDate}</p>
</div>

<p>To avoid any service interruption, please make your payment before the due date.</p>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn btn-warning">Pay Now</a>
</div>
` : ''}

<p>If you have already made the payment, please ignore this reminder.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Overdue notice template
   */
  private getOverdueNoticeTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #ef4444; margin-bottom: 20px;">Payment Overdue</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>Your payment for <strong>${data.schoolName || 'your school'}</strong> is now overdue. Please make the payment immediately to avoid service interruption.</p>

<div class="danger-box">
    <h3 style="color: #dc2626; margin-top: 0;">Overdue Invoice</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Original Amount:</strong> ₹${data.amount}</p>
    ${data.penaltyAmount ? `<p><strong>Penalty Amount:</strong> <span class="overdue-amount">₹${data.penaltyAmount}</span></p>` : ''}
    <p><strong>Days Overdue:</strong> ${data.daysOverdue || 0} days</p>
    <p><strong>Due Date:</strong> ${data.dueDate}</p>
</div>

<p><strong>Important:</strong> Your account will be suspended if payment is not received within 15 days of the due date.</p>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn btn-danger">Pay Now</a>
</div>
` : ''}

<p>If you have any questions or need assistance, please contact our support team immediately.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Invoice generated template
   */
  private getInvoiceGeneratedTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #2563eb; margin-bottom: 20px;">New Invoice Generated</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>Your monthly invoice for <strong>${data.schoolName || 'your school'}</strong> has been generated and is ready for payment.</p>

<div class="invoice-details">
    <h3 style="color: #2563eb; margin-top: 0;">Invoice Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount:</strong> <span class="amount">₹${data.amount}</span></p>
    <p><strong>Due Date:</strong> ${data.dueDate}</p>
</div>

<p>Please make the payment by the due date to ensure uninterrupted service.</p>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn">Pay Now</a>
</div>
` : ''}

<p>Thank you for choosing Schopio for your school management needs.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Payment confirmation template
   */
  private getPaymentConfirmationTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #10b981; margin-bottom: 20px;">Payment Received Successfully</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>Thank you! We have successfully received your payment for <strong>${data.schoolName || 'your school'}</strong>.</p>

<div class="invoice-details">
    <h3 style="color: #10b981; margin-top: 0;">Payment Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount Paid:</strong> <span class="amount" style="color: #10b981;">₹${data.amount}</span></p>
    <p><strong>Payment Date:</strong> ${new Date().toLocaleDateString('en-IN')}</p>
</div>

<p>Your subscription remains active and all services will continue uninterrupted.</p>

${data.receiptUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.receiptUrl}" class="btn btn-success">Download Receipt</a>
</div>
` : ''}

<p>Thank you for your continued trust in Schopio.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Final notice template
   */
  private getFinalNoticeTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #ef4444; margin-bottom: 20px;">FINAL NOTICE - Account Suspension Warning</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p><strong>URGENT:</strong> Your account for <strong>${data.schoolName || 'your school'}</strong> will be suspended if payment is not received immediately.</p>

<div class="danger-box">
    <h3 style="color: #dc2626; margin-top: 0;">Final Notice</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Total Amount Due:</strong> <span class="overdue-amount">₹${data.amount}</span></p>
    ${data.penaltyAmount ? `<p><strong>Penalty Amount:</strong> <span class="overdue-amount">₹${data.penaltyAmount}</span></p>` : ''}
    <p><strong>Days Overdue:</strong> ${data.daysOverdue || 0} days</p>
    <p><strong>Original Due Date:</strong> ${data.dueDate}</p>
</div>

<p><strong>Account suspension will result in:</strong></p>
<ul>
    <li>Loss of access to all Schopio services</li>
    <li>Data backup and potential data loss</li>
    <li>Additional recovery fees</li>
    <li>Service restoration delays</li>
</ul>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn btn-danger">Pay Immediately</a>
</div>
` : ''}

<p><strong>Contact us immediately</strong> if you need assistance or have questions about this notice.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Admin alert email template
   */
  private getAdminAlertTemplate(data: AdminAlertData): string {
    const severityColors = {
      low: '#10b981',
      medium: '#f59e0b',
      high: '#ef4444',
      critical: '#dc2626'
    }

    const severityColor = severityColors[data.severity]
    const severityIcon = data.severity === 'critical' ? '🚨' : data.severity === 'high' ? '⚠️' : data.severity === 'medium' ? '⚡' : 'ℹ️'

    const content = `
<div style="border-left: 4px solid ${severityColor}; padding-left: 20px; margin-bottom: 20px;">
  <h2 style="color: ${severityColor}; margin-bottom: 10px;">${severityIcon} ${data.severity.toUpperCase()} ALERT</h2>
  <h3 style="color: #1f2937; margin-bottom: 15px;">${data.title}</h3>
</div>

<div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <h4 style="color: #374151; margin-top: 0;">Alert Details</h4>
  <p><strong>Type:</strong> ${data.alertType.replace('_', ' ').toUpperCase()}</p>
  <p><strong>Severity:</strong> <span style="color: ${severityColor}; font-weight: bold;">${data.severity.toUpperCase()}</span></p>
  <p><strong>Timestamp:</strong> ${new Date(data.timestamp).toLocaleString('en-IN')}</p>
  <p><strong>Description:</strong> ${data.description}</p>
</div>

${data.affectedClients && data.affectedClients.length > 0 ? `
<div style="background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 15px 0;">
  <h4 style="color: #92400e; margin-top: 0;">Affected Clients (${data.affectedClients.length})</h4>
  <ul style="margin: 0; padding-left: 20px;">
    ${data.affectedClients.slice(0, 10).map(client => `<li>${client}</li>`).join('')}
    ${data.affectedClients.length > 10 ? `<li><em>... and ${data.affectedClients.length - 10} more</em></li>` : ''}
  </ul>
</div>
` : ''}

${data.systemMetrics ? `
<div style="background-color: #e0f2fe; padding: 15px; border-radius: 6px; margin: 15px 0;">
  <h4 style="color: #0277bd; margin-top: 0;">System Metrics</h4>
  ${data.systemMetrics.failedBillings ? `<p><strong>Failed Billings:</strong> ${data.systemMetrics.failedBillings}</p>` : ''}
  ${data.systemMetrics.overdueInvoices ? `<p><strong>Overdue Invoices:</strong> ${data.systemMetrics.overdueInvoices}</p>` : ''}
  ${data.systemMetrics.systemLoad ? `<p><strong>System Load:</strong> ${data.systemMetrics.systemLoad}</p>` : ''}
  ${data.systemMetrics.errorRate ? `<p><strong>Error Rate:</strong> ${data.systemMetrics.errorRate}</p>` : ''}
</div>
` : ''}

${data.details ? `
<div style="background-color: #f3f4f6; padding: 15px; border-radius: 6px; margin: 15px 0;">
  <h4 style="color: #374151; margin-top: 0;">Technical Details</h4>
  <pre style="background-color: #1f2937; color: #f9fafb; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${JSON.stringify(data.details, null, 2)}</pre>
</div>
` : ''}

<div style="background-color: #fee2e2; padding: 15px; border-radius: 6px; margin: 20px 0;">
  <h4 style="color: #dc2626; margin-top: 0;">Required Actions</h4>
  <ul style="margin: 0; padding-left: 20px; color: #7f1d1d;">
    <li>Review system logs for detailed error information</li>
    <li>Check affected client accounts and billing status</li>
    <li>Monitor system performance and error rates</li>
    ${data.severity === 'critical' ? '<li><strong>IMMEDIATE ATTENTION REQUIRED</strong></li>' : ''}
  </ul>
</div>

<p style="margin-top: 30px; color: #6b7280;">
  This is an automated alert from the Schopio billing system.<br>
  Alert generated at: ${new Date(data.timestamp).toLocaleString('en-IN')}
</p>

<p style="color: #374151;">
  Best regards,<br>
  Schopio Monitoring System
</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Send alert email for system monitoring
   */
  async sendAlert(alertData: { to: string; subject: string; message: string }): Promise<{ success: boolean; id?: string; error?: string }> {
    try {
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #fee2e2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
            <h2 style="color: #dc2626; margin: 0 0 10px 0;">🚨 System Alert</h2>
          </div>

          <div style="background: #f9fafb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
            <pre style="white-space: pre-wrap; font-family: monospace; font-size: 14px; line-height: 1.5; margin: 0;">${alertData.message}</pre>
          </div>

          <div style="background: #f3f4f6; border-radius: 8px; padding: 16px; text-align: center;">
            <p style="margin: 0; color: #6b7280; font-size: 14px;">
              This is an automated alert from Schopio System Monitor<br>
              Please investigate immediately if this is a critical alert.
            </p>
          </div>
        </div>
      `

      const result = await this.resend.emails.send({
        from: this.defaultFrom,
        to: alertData.to,
        subject: alertData.subject,
        html
      })

      if (result.error) {
        console.error('Alert email failed:', result.error)
        return { success: false, error: result.error.message }
      }

      console.log(`📧 Alert email sent successfully: ${result.data?.id}`)
      return { success: true, id: result.data?.id }

    } catch (error) {
      console.error('Alert email error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// Export singleton instance
export const emailService = new EmailService()
export type { BillingEmailData, EmailOptions, AdminAlertData }
