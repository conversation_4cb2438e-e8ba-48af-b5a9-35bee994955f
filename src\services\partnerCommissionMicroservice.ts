import { db } from '@/src/db'
import {
  billingInvoices,
  billingPayments,
  billingSubscriptions,
  schoolReferrals,
  partners,
  partnerEarnings,
  subscriptionExpenses,
  partnerCommissionTransactions
} from '@/src/db/schema'
import { eq, and, isNull, sql, desc } from 'drizzle-orm'

/**
 * Partner Commission Microservice
 * Checks if partner referrals have been properly compensated for paid subscriptions
 */
export class PartnerCommissionMicroservice {
  
  /**
   * Main function to check and update partner commissions
   */
  async checkAndUpdatePartnerCommissions(): Promise<{
    totalChecked: number
    commissionsCreated: number
    errors: string[]
    details: any[]
  }> {
    const results = {
      totalChecked: 0,
      commissionsCreated: 0,
      errors: [] as string[],
      details: [] as any[]
    }

    try {
      console.log('🔍 Starting Partner Commission Audit...')

      // Get all paid invoices that might need commission calculation
      const paidInvoices = await db
        .select({
          invoiceId: billingInvoices.id,
          subscriptionId: billingInvoices.subscriptionId,
          clientId: billingInvoices.clientId,
          totalAmount: billingInvoices.totalAmount,
          discountAmount: billingInvoices.discountAmount,
          paidDate: billingInvoices.paidDate,
          status: billingInvoices.status
        })
        .from(billingInvoices)
        .where(eq(billingInvoices.status, 'paid'))
        .orderBy(desc(billingInvoices.paidDate))

      console.log(`📊 Found ${paidInvoices.length} paid invoices to check`)

      for (const invoice of paidInvoices) {
        results.totalChecked++
        
        try {
          const commissionResult = await this.processInvoiceCommission(invoice)
          results.details.push(commissionResult)
          
          if (commissionResult.commissionCreated) {
            results.commissionsCreated++
          }
        } catch (error) {
          const errorMsg = `Error processing invoice ${invoice.invoiceId}: ${error}`
          results.errors.push(errorMsg)
          console.error('❌', errorMsg)
        }
      }

      console.log('✅ Partner Commission Audit Complete:', {
        totalChecked: results.totalChecked,
        commissionsCreated: results.commissionsCreated,
        errorCount: results.errors.length
      })

      return results

    } catch (error) {
      console.error('❌ Partner Commission Microservice Error:', error)
      throw error
    }
  }

  /**
   * Process commission for a specific invoice
   */
  private async processInvoiceCommission(invoice: any): Promise<{
    invoiceId: string
    hasPartnerReferral: boolean
    partnerInfo?: any
    existingCommission: boolean
    commissionCreated: boolean
    calculationDetails?: any
    error?: string
  }> {
    const result: any = {
      invoiceId: invoice.invoiceId,
      hasPartnerReferral: false,
      existingCommission: false,
      commissionCreated: false
    }

    try {
      console.log(`🔍 Processing invoice ${invoice.invoiceId} for client ${invoice.clientId}`)

      // 1. Check if this client has a partner referral
      const [referral] = await db
        .select({
          partnerId: schoolReferrals.partnerId,
          isActive: schoolReferrals.isActive,
          referralCodeId: schoolReferrals.referralCodeId
        })
        .from(schoolReferrals)
        .where(and(
          eq(schoolReferrals.clientId, invoice.clientId),
          eq(schoolReferrals.isActive, true)
        ))
        .limit(1)

      console.log(`🔍 Referral check for client ${invoice.clientId}:`, referral ? 'Found' : 'Not found')

      if (!referral) {
        console.log(`❌ No partner referral found for client ${invoice.clientId}`)
        return { ...result, hasPartnerReferral: false }
      }

      result.hasPartnerReferral = true

      // 2. Get partner details
      const [partner] = await db
        .select({
          id: partners.id,
          name: partners.name,
          profitSharePercentage: partners.profitSharePercentage,
          isActive: partners.isActive
        })
        .from(partners)
        .where(and(
          eq(partners.id, referral.partnerId),
          eq(partners.isActive, true)
        ))
        .limit(1)

      if (!partner) {
        console.log(`❌ Partner not found or inactive for partnerId: ${referral.partnerId}`)
        return { ...result, error: 'Partner not found or inactive' }
      }

      console.log(`✅ Partner found: ${partner.name} (${partner.profitSharePercentage}% commission)`)

      result.partnerInfo = {
        partnerId: partner.id,
        partnerName: partner.name,
        profitSharePercentage: partner.profitSharePercentage,
        referralCodeId: referral.referralCodeId
      }

      // 3. Check if commission already exists
      const [existingCommission] = await db
        .select({ id: partnerEarnings.id })
        .from(partnerEarnings)
        .where(and(
          eq(partnerEarnings.invoiceId, invoice.invoiceId),
          eq(partnerEarnings.partnerId, partner.id)
        ))
        .limit(1)

      if (existingCommission) {
        return { ...result, existingCommission: true }
      }

      // 4. Get subscription expenses (check both subscriptionExpenses table and billingSubscriptions table)
      let expenses = null
      let operationalExpensesAmount = 0

      if (invoice.subscriptionId) {
        // First, try to get from subscriptionExpenses table
        const [expenseResult] = await db
          .select({
            monthlyOperationalCost: subscriptionExpenses.monthlyOperationalCost,
            expenseBreakdown: subscriptionExpenses.expenseBreakdown
          })
          .from(subscriptionExpenses)
          .where(and(
            eq(subscriptionExpenses.subscriptionId, invoice.subscriptionId),
            eq(subscriptionExpenses.isActive, true)
          ))
          .limit(1)

        if (expenseResult) {
          expenses = expenseResult
          operationalExpensesAmount = parseFloat(expenseResult.monthlyOperationalCost || '0')
        } else {
          // If not found in subscriptionExpenses, check billingSubscriptions table
          const [subscriptionResult] = await db
            .select({
              totalOperationalExpenses: billingSubscriptions.totalOperationalExpenses,
              databaseCosts: billingSubscriptions.databaseCosts,
              websiteMaintenance: billingSubscriptions.websiteMaintenance,
              supportCosts: billingSubscriptions.supportCosts,
              infrastructureCosts: billingSubscriptions.infrastructureCosts,
              operationalExpenses: billingSubscriptions.operationalExpenses
            })
            .from(billingSubscriptions)
            .where(eq(billingSubscriptions.id, invoice.subscriptionId))
            .limit(1)

          if (subscriptionResult) {
            operationalExpensesAmount = parseFloat(subscriptionResult.totalOperationalExpenses || '0')
            expenses = {
              monthlyOperationalCost: subscriptionResult.totalOperationalExpenses || '0',
              expenseBreakdown: subscriptionResult.operationalExpenses || {
                databaseCosts: parseFloat(subscriptionResult.databaseCosts || '0'),
                websiteMaintenance: parseFloat(subscriptionResult.websiteMaintenance || '0'),
                supportCosts: parseFloat(subscriptionResult.supportCosts || '0'),
                infrastructureCosts: parseFloat(subscriptionResult.infrastructureCosts || '0')
              }
            }
          }
        }
      }

      // 5. Calculate commission
      const grossAmount = parseFloat(invoice.totalAmount)
      const discountAmount = parseFloat(invoice.discountAmount || '0')
      // Use the operationalExpensesAmount we calculated above
      
      // Net amount after discount and expenses
      const netAmount = grossAmount - discountAmount - operationalExpensesAmount
      const partnerSharePercentage = parseFloat(partner.profitSharePercentage || '20')
      const partnerEarning = Math.max(0, (netAmount * partnerSharePercentage) / 100)

      const calculationDetails = {
        grossAmount,
        discountAmount,
        operationalExpenses: operationalExpensesAmount,
        netAmount,
        partnerSharePercentage,
        partnerEarning,
        expenseBreakdown: expenses?.expenseBreakdown || null
      }

      result.calculationDetails = calculationDetails

      // 6. Create commission record if earning > 0
      if (partnerEarning > 0) {
        await db.insert(partnerEarnings).values({
          partnerId: partner.id,
          clientId: invoice.clientId,
          invoiceId: invoice.invoiceId,
          grossAmount: grossAmount.toString(),
          totalExpenses: operationalExpensesAmount.toString(),
          netProfit: netAmount.toString(),
          partnerSharePercentage: partnerSharePercentage.toString(),
          partnerEarning: partnerEarning.toString(),
          status: 'available',
          calculatedAt: new Date(),
          availableAt: new Date(),
          escrowStatus: 'manual',
          notes: `Auto-calculated by commission microservice - Invoice: ${invoice.invoiceId}`
        })

        result.commissionCreated = true
        
        console.log(`✅ Commission created for partner ${partner.name}: ₹${partnerEarning}`)
      }

      return result

    } catch (error) {
      return { ...result, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Get commission summary for a specific partner
   */
  async getPartnerCommissionSummary(partnerId: string): Promise<{
    totalEarnings: number
    availableEarnings: number
    paidEarnings: number
    pendingEarnings: number
    commissionCount: number
    lastCalculated?: Date
  }> {
    const [summary] = await db
      .select({
        totalEarnings: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`,
        availableEarnings: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN CAST(${partnerEarnings.partnerEarning} AS DECIMAL) ELSE 0 END), 0)`,
        paidEarnings: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'withdrawn' THEN CAST(${partnerEarnings.partnerEarning} AS DECIMAL) ELSE 0 END), 0)`,
        pendingEarnings: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'pending' THEN CAST(${partnerEarnings.partnerEarning} AS DECIMAL) ELSE 0 END), 0)`,
        commissionCount: sql<number>`COUNT(*)`,
        lastCalculated: sql<Date>`MAX(${partnerEarnings.calculatedAt})`
      })
      .from(partnerEarnings)
      .where(eq(partnerEarnings.partnerId, partnerId))

    return {
      totalEarnings: summary?.totalEarnings || 0,
      availableEarnings: summary?.availableEarnings || 0,
      paidEarnings: summary?.paidEarnings || 0,
      pendingEarnings: summary?.pendingEarnings || 0,
      commissionCount: summary?.commissionCount || 0,
      lastCalculated: summary?.lastCalculated
    }
  }

  /**
   * Verify specific school payment and partner commission
   */
  async verifySchoolPaymentCommission(clientId: string): Promise<{
    schoolInfo: any
    partnerReferral?: any
    payments: any[]
    commissions: any[]
    totalPaid: number
    totalCommissions: number
    missingCommissions: any[]
  }> {
    // Get school info
    const [school] = await db
      .select({
        id: billingSubscriptions.clientId,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        status: billingSubscriptions.status
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.clientId, clientId))
      .limit(1)

    // Get partner referral
    const [referral] = await db
      .select({
        partnerId: schoolReferrals.partnerId,
        referralCodeId: schoolReferrals.referralCodeId,
        partnerName: partners.name,
        profitSharePercentage: partners.profitSharePercentage
      })
      .from(schoolReferrals)
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .where(and(
        eq(schoolReferrals.clientId, clientId),
        eq(schoolReferrals.isActive, true)
      ))
      .limit(1)

    // Get all payments
    const payments = await db
      .select({
        invoiceId: billingInvoices.id,
        totalAmount: billingInvoices.totalAmount,
        paidDate: billingInvoices.paidDate,
        status: billingInvoices.status
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.clientId, clientId),
        eq(billingInvoices.status, 'paid')
      ))

    // Get all commissions
    const commissions = await db
      .select({
        id: partnerEarnings.id,
        invoiceId: partnerEarnings.invoiceId,
        partnerEarning: partnerEarnings.partnerEarning,
        status: partnerEarnings.status,
        calculatedAt: partnerEarnings.calculatedAt
      })
      .from(partnerEarnings)
      .where(eq(partnerEarnings.clientId, clientId))

    const totalPaid = payments.reduce((sum, p) => sum + parseFloat(p.totalAmount), 0)
    const totalCommissions = commissions.reduce((sum, c) => sum + parseFloat(c.partnerEarning), 0)

    // Find missing commissions
    const commissionInvoiceIds = new Set(commissions.map(c => c.invoiceId))
    const missingCommissions = payments.filter(p => !commissionInvoiceIds.has(p.invoiceId))

    return {
      schoolInfo: school,
      partnerReferral: referral,
      payments,
      commissions,
      totalPaid,
      totalCommissions,
      missingCommissions
    }
  }
}

// Export singleton instance
export const partnerCommissionMicroservice = new PartnerCommissionMicroservice()
