/**
 * Revenue Calculation Utilities for Schopio Admin System
 * Provides comprehensive revenue analytics and projections
 */

export interface ClientRevenueData {
  clientId: string
  schoolName: string
  actualStudentCount: number
  averageMonthlyFee: string | null
  softwareRequests: SoftwareRequestData[]
  subscription?: SubscriptionData
}

export interface SoftwareRequestData {
  id: string
  requestType: 'demo' | 'production'
  status: string
  studentCount: number
  facultyCount: number
  averageMonthlyFee: string | null
  createdAt: Date
  approvedAt?: Date
}

export interface SubscriptionData {
  id: string
  status: string
  monthlyAmount: string
  startDate: Date
  nextBillingDate: Date
}

export interface RevenueMetrics {
  monthlyRevenue: number
  annualRevenue: number
  feePerStudent: number
  totalRequests: number
  activeRequests: number
  averageRequestValue: number
  projectedAnnualGrowth: number
}

export interface RevenueAnalytics {
  totalClients: number
  totalMonthlyRevenue: number
  totalAnnualRevenue: number
  averageRevenuePerClient: number
  highValueClients: number // Clients with >50k annual revenue
  clientsByRevenueRange: {
    low: number    // 0-10k annual
    medium: number // 10k-50k annual
    high: number   // 50k+ annual
  }
  revenueGrowthTrend: {
    month: string
    revenue: number
  }[]
}

/**
 * Calculate revenue metrics for a single client
 */
export function calculateClientRevenue(clientData: ClientRevenueData): RevenueMetrics {
  const { actualStudentCount, averageMonthlyFee, softwareRequests, subscription } = clientData

  // Determine fee per student
  let feePerStudent = 0
  
  if (subscription?.monthlyAmount) {
    // Use subscription amount if available
    feePerStudent = parseFloat(subscription.monthlyAmount) / (actualStudentCount || 1)
  } else if (averageMonthlyFee) {
    // Use client's average monthly fee
    feePerStudent = parseFloat(averageMonthlyFee)
  } else {
    // Use latest production request fee
    const latestProductionRequest = softwareRequests
      .filter(req => req.requestType === 'production')
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]
    
    if (latestProductionRequest?.averageMonthlyFee) {
      feePerStudent = parseFloat(latestProductionRequest.averageMonthlyFee)
    }
  }

  // Calculate revenue
  const monthlyRevenue = feePerStudent * (actualStudentCount || 0)
  const annualRevenue = monthlyRevenue * 12

  // Calculate request metrics
  const totalRequests = softwareRequests.length
  const activeRequests = softwareRequests.filter(req => 
    ['pending', 'under_review', 'approved'].includes(req.status)
  ).length

  // Calculate projected growth (based on request activity)
  const recentRequests = softwareRequests.filter(req => {
    const requestDate = new Date(req.createdAt)
    const threeMonthsAgo = new Date()
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)
    return requestDate >= threeMonthsAgo
  })

  const projectedAnnualGrowth = recentRequests.length > 0 ? 
    (recentRequests.length / 3) * 12 * feePerStudent : 0

  return {
    monthlyRevenue,
    annualRevenue,
    feePerStudent,
    totalRequests,
    activeRequests,
    averageRequestValue: totalRequests > 0 ? monthlyRevenue : 0,
    projectedAnnualGrowth
  }
}

/**
 * Calculate comprehensive revenue analytics across all clients
 */
export function calculateRevenueAnalytics(clientsData: ClientRevenueData[]): RevenueAnalytics {
  const clientMetrics = clientsData.map(client => ({
    ...client,
    metrics: calculateClientRevenue(client)
  }))

  const totalClients = clientsData.length
  const totalMonthlyRevenue = clientMetrics.reduce((sum, client) => 
    sum + client.metrics.monthlyRevenue, 0
  )
  const totalAnnualRevenue = totalMonthlyRevenue * 12
  const averageRevenuePerClient = totalClients > 0 ? totalAnnualRevenue / totalClients : 0

  // Categorize clients by revenue
  const highValueClients = clientMetrics.filter(client => 
    client.metrics.annualRevenue > 50000
  ).length

  const clientsByRevenueRange = {
    low: clientMetrics.filter(client => 
      client.metrics.annualRevenue <= 10000
    ).length,
    medium: clientMetrics.filter(client => 
      client.metrics.annualRevenue > 10000 && client.metrics.annualRevenue <= 50000
    ).length,
    high: clientMetrics.filter(client => 
      client.metrics.annualRevenue > 50000
    ).length
  }

  // Generate revenue growth trend (mock data for now - would be calculated from historical data)
  const revenueGrowthTrend = generateRevenueGrowthTrend(totalMonthlyRevenue)

  return {
    totalClients,
    totalMonthlyRevenue,
    totalAnnualRevenue,
    averageRevenuePerClient,
    highValueClients,
    clientsByRevenueRange,
    revenueGrowthTrend
  }
}

/**
 * Generate revenue growth trend data
 */
function generateRevenueGrowthTrend(currentMonthlyRevenue: number) {
  const months = []
  const currentDate = new Date()
  
  for (let i = 11; i >= 0; i--) {
    const date = new Date(currentDate)
    date.setMonth(date.getMonth() - i)
    
    // Simulate growth trend (in real implementation, this would come from historical data)
    const growthFactor = 1 + (Math.random() * 0.2 - 0.1) // ±10% variation
    const revenue = currentMonthlyRevenue * growthFactor * (1 + (11 - i) * 0.05) // 5% monthly growth
    
    months.push({
      month: date.toISOString().slice(0, 7), // YYYY-MM format
      revenue: Math.round(revenue)
    })
  }
  
  return months
}

/**
 * Calculate ROI for partner referrals
 */
export function calculatePartnerROI(
  partnerExpenses: number,
  clientsReferred: ClientRevenueData[],
  partnerCommissionRate: number = 0.35
): {
  totalRevenue: number
  partnerCommission: number
  netProfit: number
  roi: number
} {
  const totalRevenue = clientsReferred.reduce((sum, client) => {
    const metrics = calculateClientRevenue(client)
    return sum + metrics.annualRevenue
  }, 0)

  const partnerCommission = totalRevenue * partnerCommissionRate
  const netProfit = totalRevenue - partnerCommission - partnerExpenses
  const roi = partnerExpenses > 0 ? (netProfit / partnerExpenses) * 100 : 0

  return {
    totalRevenue,
    partnerCommission,
    netProfit,
    roi
  }
}

/**
 * Generate revenue projections based on current trends
 */
export function generateRevenueProjections(
  currentMetrics: RevenueAnalytics,
  projectionMonths: number = 12
): {
  month: string
  projectedRevenue: number
  confidenceLevel: 'high' | 'medium' | 'low'
}[] {
  const projections = []
  const currentDate = new Date()
  const monthlyGrowthRate = 0.05 // 5% monthly growth assumption
  
  for (let i = 1; i <= projectionMonths; i++) {
    const date = new Date(currentDate)
    date.setMonth(date.getMonth() + i)
    
    const projectedRevenue = currentMetrics.totalMonthlyRevenue * Math.pow(1 + monthlyGrowthRate, i)
    
    // Confidence decreases over time
    let confidenceLevel: 'high' | 'medium' | 'low' = 'high'
    if (i > 3) confidenceLevel = 'medium'
    if (i > 6) confidenceLevel = 'low'
    
    projections.push({
      month: date.toISOString().slice(0, 7),
      projectedRevenue: Math.round(projectedRevenue),
      confidenceLevel
    })
  }
  
  return projections
}

/**
 * Utility to format currency values
 */
export function formatCurrency(amount: number, currency: string = 'INR'): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

/**
 * Utility to calculate percentage change
 */
export function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
}
