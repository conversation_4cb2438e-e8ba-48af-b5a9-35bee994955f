import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { db } from '@/src/db'
import {
  partners,
  supportTickets,
  ticketMessages,
  clients,
  clientUsers,
  schoolReferrals,
  partnerEarnings,
  partnerCommissionTransactions,
  billingSubscriptions,
  withdrawalRequests,
  subscriptions,
  referralCodes
} from '@/src/db/schema'
import { eq, and, desc, count, sum, sql, gte, lte } from 'drizzle-orm'
import { partnerAuthMiddleware, getCurrentPartner } from '@/src/middleware/partner-auth'
import { supportNotificationService } from '@/src/services/supportNotificationService'

const app = new Hono()

// Get partner dashboard overview with comprehensive performance metrics
app.get('/dashboard', partnerAuthMiddleware, async (c) => {
  try {
    const partner = getCurrentPartner(c)
    if (!partner) {
      return c.json({ error: 'Partner not found' }, 401)
    }

    // Get current date ranges for metrics
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
    const startOfYear = new Date(now.getFullYear(), 0, 1)

    // Get partner's referral code
    const [partnerReferralCode] = await db
      .select({
        code: referralCodes.code
      })
      .from(referralCodes)
      .where(and(
        eq(referralCodes.partnerId, partner.id),
        eq(referralCodes.isActive, true)
      ))
      .limit(1)

    // Get referred schools with subscription data
    const referredSchools = await db
      .select({
        school: {
          id: clients.id,
          name: clients.schoolName,
          email: clients.email,
          status: clients.status,
          studentCount: clients.actualStudentCount
        },
        referralDate: schoolReferrals.referredAt,
        subscription: {
          id: billingSubscriptions.id,
          status: billingSubscriptions.status,
          monthlyAmount: billingSubscriptions.monthlyAmount,
          pricePerStudent: billingSubscriptions.pricePerStudent,
          studentCount: billingSubscriptions.studentCount,
          billingCycle: billingSubscriptions.billingCycle,
          startDate: billingSubscriptions.currentPeriodStart,
          nextBillingDate: billingSubscriptions.nextBillingDate
        }
      })
      .from(schoolReferrals)
      .leftJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(billingSubscriptions, and(
        eq(clients.id, billingSubscriptions.clientId),
        eq(billingSubscriptions.status, 'active')
      ))
      .where(eq(schoolReferrals.partnerId, partner.id))
      .orderBy(desc(schoolReferrals.referredAt))

    // Calculate performance metrics
    const totalReferrals = referredSchools.length
    const activeClients = referredSchools.filter(r => r.school?.status === 'active').length
    const clientsWithSubscriptions = referredSchools.filter(r => r.subscription?.id && r.subscription?.status === 'active').length
    const conversionRate = totalReferrals > 0 ? (clientsWithSubscriptions / totalReferrals * 100) : 0

    // Monthly referrals
    const thisMonthReferrals = referredSchools.filter(r =>
      r.referralDate && new Date(r.referralDate) >= startOfMonth
    ).length
    const lastMonthReferrals = referredSchools.filter(r =>
      r.referralDate &&
      new Date(r.referralDate) >= startOfLastMonth &&
      new Date(r.referralDate) <= endOfLastMonth
    ).length

    // Calculate partner commission revenue (not full school payment)
    const totalMonthlyCommission = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerCommissionTransactions.commissionAmount} AS DECIMAL)), 0)`
      })
      .from(partnerCommissionTransactions)
      .innerJoin(billingSubscriptions, eq(partnerCommissionTransactions.subscriptionId, billingSubscriptions.id))
      .where(and(
        eq(partnerCommissionTransactions.partnerId, partner.id),
        eq(billingSubscriptions.status, 'active'),
        eq(partnerCommissionTransactions.status, 'eligible')
      ))

    const totalMonthlyRevenue = Number(totalMonthlyCommission[0]?.total || 0)

    // Get earnings summary from commission transactions (actual commission system)
    const [earningsSummary] = await db
      .select({
        totalEarnings: sum(partnerCommissionTransactions.commissionAmount),
        thisMonthEarnings: sum(sql`CASE WHEN ${partnerCommissionTransactions.createdAt} >= ${startOfMonth.toISOString()} THEN ${partnerCommissionTransactions.commissionAmount} ELSE 0 END`),
        lastMonthEarnings: sum(sql`CASE WHEN ${partnerCommissionTransactions.createdAt} >= ${startOfLastMonth.toISOString()} AND ${partnerCommissionTransactions.createdAt} <= ${endOfLastMonth.toISOString()} THEN ${partnerCommissionTransactions.commissionAmount} ELSE 0 END`),
        yearToDateEarnings: sum(sql`CASE WHEN ${partnerCommissionTransactions.createdAt} >= ${startOfYear.toISOString()} THEN ${partnerCommissionTransactions.commissionAmount} ELSE 0 END`)
      })
      .from(partnerCommissionTransactions)
      .where(eq(partnerCommissionTransactions.partnerId, partner.id))

    // Get recent support tickets for referred schools
    const recentTickets = await db
      .select({
        ticket: {
          id: supportTickets.id,
          title: supportTickets.title,
          status: supportTickets.status,
          priority: supportTickets.priority,
          createdAt: supportTickets.createdAt
        },
        school: {
          name: clients.schoolName
        }
      })
      .from(supportTickets)
      .leftJoin(clients, eq(supportTickets.clientId, clients.id))
      .leftJoin(schoolReferrals, eq(clients.id, schoolReferrals.clientId))
      .where(eq(schoolReferrals.partnerId, partner.id))
      .orderBy(desc(supportTickets.createdAt))
      .limit(10)

    // Calculate growth metrics
    const referralGrowth = lastMonthReferrals > 0
      ? ((thisMonthReferrals - lastMonthReferrals) / lastMonthReferrals * 100)
      : thisMonthReferrals > 0 ? 100 : 0

    const earningsGrowth = earningsSummary?.lastMonthEarnings && parseFloat(earningsSummary.lastMonthEarnings) > 0
      ? ((parseFloat(earningsSummary.thisMonthEarnings || '0') - parseFloat(earningsSummary.lastMonthEarnings)) / parseFloat(earningsSummary.lastMonthEarnings) * 100)
      : parseFloat(earningsSummary?.thisMonthEarnings || '0') > 0 ? 100 : 0

    return c.json({
      success: true,
      data: {
        partner: {
          name: partner.name,
          email: partner.email,
          companyName: partner.companyName,
          partnerCode: partner.partnerCode,
          referralCode: partnerReferralCode?.code || 'Not generated'
        },
        performanceMetrics: {
          totalReferrals,
          activeClients,
          clientsWithSubscriptions,
          conversionRate: Math.round(conversionRate * 100) / 100,
          totalMonthlyRevenue: (totalMonthlyRevenue || 0).toFixed(2),
          thisMonthReferrals,
          lastMonthReferrals,
          referralGrowth: Math.round(referralGrowth * 100) / 100
        },
        earnings: {
          total: earningsSummary?.totalEarnings || '0',
          thisMonth: earningsSummary?.thisMonthEarnings || '0',
          lastMonth: earningsSummary?.lastMonthEarnings || '0',
          yearToDate: earningsSummary?.yearToDateEarnings || '0',
          growth: Math.round(earningsGrowth * 100) / 100
        },
        recentActivity: {
          referrals: referredSchools.slice(0, 5),
          supportTickets: recentTickets
        }
      }
    })

  } catch (error) {
    console.error('Partner dashboard error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch dashboard data'
    }, 500)
  }
})

// Get partner clients with subscription and earnings data
app.get('/clients', partnerAuthMiddleware, async (c) => {
  try {
    const partner = getCurrentPartner(c)
    if (!partner) {
      return c.json({ error: 'Partner not found' }, 401)
    }

    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const status = c.req.query('status') // Filter by client status
    const subscription = c.req.query('subscription') // Filter by subscription status
    const search = c.req.query('search') // Search term

    const offset = (page - 1) * limit

    // Build query conditions
    let whereConditions = [eq(schoolReferrals.partnerId, partner.id)]

    if (status && status !== 'all') {
      whereConditions.push(eq(clients.status, status))
    }

    if (subscription && subscription !== 'all') {
      whereConditions.push(eq(subscriptions.status, subscription))
    }

    if (search) {
      whereConditions.push(
        sql`(${clients.schoolName} ILIKE ${`%${search}%`} OR ${clients.email} ILIKE ${`%${search}%`})`
      )
    }

    // Get clients with subscription and earnings data
    const clientsData = await db
      .select({
        client: {
          id: clients.id,
          schoolName: clients.schoolName,
          email: clients.email,
          contactPerson: clients.contactPerson,
          phone: clients.phone,
          address: clients.address,
          actualStudentCount: clients.actualStudentCount,
          status: clients.status
        },
        referralDate: schoolReferrals.referredAt,
        subscription: {
          id: subscriptions.id,
          status: subscriptions.status,
          planName: subscriptions.planName,
          monthlyAmount: subscriptions.monthlyAmount,
          pricePerStudent: subscriptions.pricePerStudent,
          studentCount: subscriptions.studentCount,
          startDate: subscriptions.startDate,
          nextBillingDate: subscriptions.nextBillingDate,
          billingCycle: subscriptions.billingCycle
        }
      })
      .from(schoolReferrals)
      .leftJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(subscriptions, and(
        eq(clients.id, subscriptions.clientId),
        eq(subscriptions.status, 'active')
      ))
      .where(and(...whereConditions))
      .orderBy(desc(schoolReferrals.referredAt))
      .limit(limit)
      .offset(offset)

    // Get total count for pagination
    const [totalCount] = await db
      .select({ count: count() })
      .from(schoolReferrals)
      .leftJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(subscriptions, and(
        eq(clients.id, subscriptions.clientId),
        eq(subscriptions.status, 'active')
      ))
      .where(and(...whereConditions))

    // Get earnings data for each client from commission transactions
    const clientIds = clientsData.map(c => c.client?.id).filter(Boolean) as string[]
    const earningsData = clientIds.length > 0 ? await db
      .select({
        clientId: sql<string>`${partnerCommissionTransactions.subscriptionId}`, // Using subscription as proxy for client
        totalEarned: sum(partnerCommissionTransactions.commissionAmount),
        monthlyEarning: sum(sql`CASE WHEN ${partnerCommissionTransactions.createdAt} >= DATE_TRUNC('month', CURRENT_DATE) THEN ${partnerCommissionTransactions.commissionAmount} ELSE 0 END`),
        lastPaymentDate: sql`MAX(${partnerCommissionTransactions.createdAt})`
      })
      .from(partnerCommissionTransactions)
      .where(eq(partnerCommissionTransactions.partnerId, partner.id))
      .groupBy(partnerCommissionTransactions.subscriptionId) : []

    // Get support tickets count for each client
    const supportTicketsData = clientIds.length > 0 ? await db
      .select({
        clientId: supportTickets.clientId,
        total: count(),
        open: sum(sql`CASE WHEN ${supportTickets.status} IN ('open', 'in_progress') THEN 1 ELSE 0 END`),
        resolved: sum(sql`CASE WHEN ${supportTickets.status} = 'resolved' THEN 1 ELSE 0 END`)
      })
      .from(supportTickets)
      .where(sql`${supportTickets.clientId} IN (${sql.join(clientIds.map(id => sql`${id}`), sql`, `)})`)
      .groupBy(supportTickets.clientId) : []

    // Transform data to match frontend interface
    const transformedClients = clientsData
      .filter(item => item.client) // Filter out null clients
      .map(item => {
        const client = item.client!
        const earnings = earningsData.find(e => e.clientId === client.id)
        const tickets = supportTicketsData.find(t => t.clientId === client.id)

        return {
          id: client.id,
          school: {
            name: client.schoolName,
            email: client.email,
            contactPerson: client.contactPerson || '',
            phone: client.phone || '',
            address: client.address || '',
            studentCount: client.actualStudentCount || 0,
            status: client.status
          },
          referralDate: item.referralDate?.toISOString() || '',
          subscription: item.subscription ? {
            id: item.subscription.id,
            status: item.subscription.status,
            plan: item.subscription.planName,
            pricePerStudent: parseFloat(item.subscription.pricePerStudent),
            monthlyAmount: parseFloat(item.subscription.monthlyAmount),
            startDate: item.subscription.startDate || '',
            endDate: null, // Not tracked in current schema
            studentsCount: item.subscription.studentCount
          } : null,
          earnings: {
            totalEarned: parseFloat(earnings?.totalEarned || '0'),
            monthlyEarning: parseFloat(earnings?.monthlyEarning || '0'),
            lastPaymentDate: earnings?.lastPaymentDate ? String(earnings.lastPaymentDate) : null
          },
          supportTickets: {
            total: Number(tickets?.total || 0),
            open: Number(tickets?.open || 0),
            resolved: Number(tickets?.resolved || 0)
          }
        }
      })

    // Calculate summary statistics using billing subscriptions
    const [activeSubscriptionsCount] = await db
      .select({ count: count() })
      .from(billingSubscriptions)
      .innerJoin(schoolReferrals, eq(billingSubscriptions.clientId, schoolReferrals.clientId))
      .where(and(
        eq(schoolReferrals.partnerId, partner.id),
        eq(billingSubscriptions.status, 'active')
      ))

    // Calculate total monthly commission (not full school payments)
    const [monthlyCommissionTotal] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerCommissionTransactions.commissionAmount} AS DECIMAL)), 0)`
      })
      .from(partnerCommissionTransactions)
      .innerJoin(billingSubscriptions, eq(partnerCommissionTransactions.subscriptionId, billingSubscriptions.id))
      .where(and(
        eq(partnerCommissionTransactions.partnerId, partner.id),
        eq(billingSubscriptions.status, 'active'),
        eq(partnerCommissionTransactions.status, 'eligible')
      ))

    const summary = {
      totalClients: totalCount.count,
      activeSubscriptions: activeSubscriptionsCount.count,
      totalMonthlyRevenue: monthlyCommissionTotal.total || 0,
      totalEarnings: transformedClients.reduce((sum, c) => sum + c.earnings.totalEarned, 0)
    }

    return c.json({
      success: true,
      data: {
        clients: transformedClients,
        pagination: {
          page,
          limit,
          total: totalCount.count,
          totalPages: Math.ceil(totalCount.count / limit)
        },
        summary
      }
    })

  } catch (error) {
    console.error('Partner clients error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch clients data'
    }, 500)
  }
})

// Get detailed partner performance analytics
app.get('/analytics', partnerAuthMiddleware, async (c) => {
  try {
    const partner = getCurrentPartner(c)
    if (!partner) {
      return c.json({ error: 'Partner not found' }, 401)
    }

    const timeRange = c.req.query('range') || '6months' // 1month, 3months, 6months, 1year
    const now = new Date()
    let startDate: Date

    switch (timeRange) {
      case '1month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
        break
      case '3months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
        break
      case '1year':
        startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        break
      default: // 6months
        startDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate())
    }

    // Get monthly breakdown of referrals and earnings
    const monthlyData = await db
      .select({
        month: sql`DATE_TRUNC('month', ${schoolReferrals.referredAt})`,
        referralCount: count(schoolReferrals.id),
        earnings: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`
      })
      .from(schoolReferrals)
      .leftJoin(partnerEarnings, and(
        eq(schoolReferrals.partnerId, partnerEarnings.partnerId),
        sql`DATE_TRUNC('month', ${schoolReferrals.referredAt}) = DATE_TRUNC('month', ${partnerEarnings.calculatedAt})`
      ))
      .where(and(
        eq(schoolReferrals.partnerId, partner.id),
        gte(schoolReferrals.referredAt, startDate)
      ))
      .groupBy(sql`DATE_TRUNC('month', ${schoolReferrals.referredAt})`)
      .orderBy(sql`DATE_TRUNC('month', ${schoolReferrals.referredAt})`)

    // Get conversion funnel data using billing subscriptions
    const funnelData = await db
      .select({
        totalReferrals: count(schoolReferrals.id),
        registeredClients: sql<number>`COUNT(DISTINCT ${clients.id})`,
        activeClients: sql<number>`COUNT(DISTINCT CASE WHEN ${clients.status} = 'active' THEN ${clients.id} END)`,
        subscribedClients: sql<number>`COUNT(DISTINCT CASE WHEN ${billingSubscriptions.status} = 'active' THEN ${clients.id} END)`
      })
      .from(schoolReferrals)
      .leftJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(billingSubscriptions, and(
        eq(clients.id, billingSubscriptions.clientId),
        eq(billingSubscriptions.status, 'active')
      ))
      .where(and(
        eq(schoolReferrals.partnerId, partner.id),
        gte(schoolReferrals.referredAt, startDate)
      ))

    // Get top performing schools by commission earnings
    const topSchools = await db
      .select({
        school: {
          id: clients.id,
          name: clients.schoolName,
          studentCount: clients.actualStudentCount
        },
        subscription: {
          monthlyAmount: billingSubscriptions.monthlyAmount,
          status: billingSubscriptions.status
        },
        totalEarnings: sql<number>`COALESCE(SUM(CAST(${partnerCommissionTransactions.commissionAmount} AS DECIMAL)), 0)`,
        referralDate: schoolReferrals.referredAt
      })
      .from(schoolReferrals)
      .leftJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(billingSubscriptions, eq(clients.id, billingSubscriptions.clientId))
      .leftJoin(partnerCommissionTransactions,
        eq(schoolReferrals.partnerId, partnerCommissionTransactions.partnerId)
      )
      .where(and(
        eq(schoolReferrals.partnerId, partner.id),
        gte(schoolReferrals.referredAt, startDate)
      ))
      .groupBy(clients.id, clients.schoolName, clients.actualStudentCount, billingSubscriptions.monthlyAmount, billingSubscriptions.status, schoolReferrals.referredAt)
      .orderBy(desc(sql<number>`COALESCE(SUM(CAST(${partnerCommissionTransactions.commissionAmount} AS DECIMAL)), 0)`))
      .limit(10)

    return c.json({
      success: true,
      data: {
        timeRange,
        monthlyTrends: monthlyData,
        conversionFunnel: funnelData[0] || {
          totalReferrals: 0,
          registeredClients: 0,
          activeClients: 0,
          subscribedClients: 0
        },
        topPerformingSchools: topSchools
      }
    })

  } catch (error) {
    console.error('Partner analytics error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch analytics data'
    }, 500)
  }
})

// Get support tickets for partner's referred schools
app.get('/support/tickets', partnerAuthMiddleware, async (c) => {
  try {
    const partner = getCurrentPartner(c)
    if (!partner) {
      return c.json({ error: 'Partner not found' }, 401)
    }

    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const status = c.req.query('status') // Filter by status
    const priority = c.req.query('priority') // Filter by priority

    const offset = (page - 1) * limit

    // Build query conditions
    let whereConditions = [eq(schoolReferrals.partnerId, partner.id)]
    
    if (status) {
      whereConditions.push(eq(supportTickets.status, status))
    }
    
    if (priority) {
      whereConditions.push(eq(supportTickets.priority, priority))
    }

    // Get tickets with school information
    const tickets = await db
      .select({
        ticket: {
          id: supportTickets.id,
          title: supportTickets.title,
          description: supportTickets.description,
          priority: supportTickets.priority,
          status: supportTickets.status,
          category: supportTickets.category,
          createdAt: supportTickets.createdAt,
          updatedAt: supportTickets.updatedAt
        },
        school: {
          id: clients.id,
          name: clients.schoolName,
          email: clients.email
        },
        creator: {
          name: clientUsers.name,
          email: clientUsers.email
        }
      })
      .from(supportTickets)
      .leftJoin(schoolReferrals, eq(supportTickets.clientId, schoolReferrals.clientId))
      .leftJoin(clients, eq(supportTickets.clientId, clients.id))
      .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
      .where(and(...whereConditions))
      .orderBy(desc(supportTickets.updatedAt))
      .limit(limit)
      .offset(offset)

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(supportTickets)
      .leftJoin(schoolReferrals, eq(supportTickets.clientId, schoolReferrals.clientId))
      .where(and(...whereConditions))

    return c.json({
      success: true,
      data: {
        tickets,
        pagination: {
          page,
          limit,
          total: totalCount.count,
          pages: Math.ceil(totalCount.count / limit)
        }
      }
    })

  } catch (error) {
    console.error('Partner support tickets error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch support tickets'
    }, 500)
  }
})

// Get specific support ticket details
app.get('/support/tickets/:ticketId', partnerAuthMiddleware, async (c) => {
  try {
    const partner = getCurrentPartner(c)
    if (!partner) {
      return c.json({ error: 'Partner not found' }, 401)
    }

    const ticketId = c.req.param('ticketId')

    // Get ticket with school information and verify partner access
    const [ticketData] = await db
      .select({
        ticket: supportTickets,
        school: {
          id: clients.id,
          name: clients.schoolName,
          email: clients.email
        },
        creator: {
          name: clientUsers.name,
          email: clientUsers.email
        }
      })
      .from(supportTickets)
      .leftJoin(schoolReferrals, eq(supportTickets.clientId, schoolReferrals.clientId))
      .leftJoin(clients, eq(supportTickets.clientId, clients.id))
      .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
      .where(and(
        eq(supportTickets.id, ticketId),
        eq(schoolReferrals.partnerId, partner.id)
      ))
      .limit(1)

    if (!ticketData) {
      return c.json({
        success: false,
        error: 'Support ticket not found or access denied'
      }, 404)
    }

    // Get ticket messages
    const messages = await db
      .select({
        id: ticketMessages.id,
        message: ticketMessages.message,
        senderType: ticketMessages.senderType,
        attachments: ticketMessages.attachments,
        createdAt: ticketMessages.createdAt
      })
      .from(ticketMessages)
      .where(eq(ticketMessages.ticketId, ticketId))
      .orderBy(ticketMessages.createdAt)

    return c.json({
      success: true,
      data: {
        ticket: ticketData.ticket,
        school: ticketData.school,
        creator: ticketData.creator,
        messages,
        messageCount: messages.length
      }
    })

  } catch (error) {
    console.error('Partner ticket details error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch ticket details'
    }, 500)
  }
})

// Partner ticket escalation - Add priority comment and escalate to admin
app.post('/support/tickets/:id/escalate',
  partnerAuthMiddleware,
  zValidator('json', z.object({
    escalationReason: z.string().min(10, 'Escalation reason must be at least 10 characters'),
    newPriority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
    internalNotes: z.string().optional()
  })),
  async (c) => {
    try {
      const partner = getCurrentPartner(c)
      if (!partner) {
        return c.json({ error: 'Partner not found' }, 401)
      }

      const ticketId = c.req.param('id')
      const { escalationReason, newPriority, internalNotes } = c.req.valid('json')

      // Verify partner has access to this ticket
      const [ticketData] = await db
        .select({
          ticket: {
            id: supportTickets.id,
            title: supportTickets.title,
            status: supportTickets.status,
            priority: supportTickets.priority,
            clientId: supportTickets.clientId
          },
          school: {
            name: clients.schoolName
          }
        })
        .from(supportTickets)
        .leftJoin(schoolReferrals, eq(supportTickets.clientId, schoolReferrals.clientId))
        .leftJoin(clients, eq(supportTickets.clientId, clients.id))
        .where(and(
          eq(supportTickets.id, ticketId),
          eq(schoolReferrals.partnerId, partner.id)
        ))
        .limit(1)

      if (!ticketData) {
        return c.json({
          success: false,
          error: 'Support ticket not found or access denied'
        }, 404)
      }

      // Update ticket priority if specified
      if (newPriority && newPriority !== ticketData.ticket.priority) {
        await db
          .update(supportTickets)
          .set({
            priority: newPriority,
            updatedAt: new Date()
          })
          .where(eq(supportTickets.id, ticketId))
      }

      // Add escalation message
      const escalationMessage = `**PARTNER ESCALATION**\n\n**Partner:** ${partner.name} (${partner.companyName})\n**Reason:** ${escalationReason}\n\n${internalNotes ? `**Internal Notes:** ${internalNotes}` : ''}`

      await db.insert(ticketMessages).values({
        ticketId: ticketId,
        message: escalationMessage,
        senderType: 'partner',
        senderId: partner.id
      })

      // Send notifications about escalation (using status update notification)
      const currentStatus = ticketData.ticket.status || 'open'
      await supportNotificationService.notifyTicketStatusUpdate(
        ticketData.ticket.id,
        currentStatus as string,
        currentStatus as string // Status didn't change, but priority did
      )

      return c.json({
        success: true,
        message: 'Ticket escalated successfully',
        data: {
          ticketId,
          newPriority: newPriority || ticketData.ticket.priority,
          escalatedAt: new Date().toISOString()
        }
      })

    } catch (error) {
      console.error('Partner ticket escalation error:', error)
      return c.json({
        success: false,
        error: 'Failed to escalate ticket'
      }, 500)
    }
  }
)

// Update support ticket status (partner actions)
app.put('/support/tickets/:id/status',
  partnerAuthMiddleware,
  zValidator('json', z.object({
    status: z.enum(['in_progress', 'resolved']),
    note: z.string().optional()
  })),
  async (c) => {
    try {
      const partner = getCurrentPartner(c)
      if (!partner) {
        return c.json({ error: 'Partner not found' }, 401)
      }

      const ticketId = c.req.param('id')
      const { status, note } = c.req.valid('json')

      // Verify partner has access to this ticket
      const [ticketData] = await db
        .select({
          ticket: {
            id: supportTickets.id,
            status: supportTickets.status,
            clientId: supportTickets.clientId
          },
          school: {
            name: clients.schoolName
          }
        })
        .from(supportTickets)
        .leftJoin(schoolReferrals, eq(supportTickets.clientId, schoolReferrals.clientId))
        .leftJoin(clients, eq(supportTickets.clientId, clients.id))
        .where(and(
          eq(supportTickets.id, ticketId),
          eq(schoolReferrals.partnerId, partner.id)
        ))
        .limit(1)

      if (!ticketData) {
        return c.json({
          success: false,
          error: 'Support ticket not found or access denied'
        }, 404)
      }

      // Update ticket status
      await db
        .update(supportTickets)
        .set({
          status,
          updatedAt: new Date(),
          ...(status === 'resolved' && { resolvedAt: new Date() })
        })
        .where(eq(supportTickets.id, ticketId))

      // Add system message about status change
      const statusMessage = note
        ? `Status updated to "${status}" by partner ${partner.name}. Note: ${note}`
        : `Status updated to "${status}" by partner ${partner.name}`

      await db.insert(ticketMessages).values({
        ticketId: ticketId,
        message: statusMessage,
        senderType: 'partner',
        senderId: partner.id
      })

      // Send notifications about status change
      await supportNotificationService.notifyTicketStatusUpdate(
        ticketData.ticket.id,
        ticketData.ticket.status || 'open',
        status
      )

      return c.json({
        success: true,
        message: 'Ticket status updated successfully',
        data: {
          ticketId,
          newStatus: status,
          updatedAt: new Date().toISOString()
        }
      })

    } catch (error) {
      console.error('Partner ticket status update error:', error)
      return c.json({
        success: false,
        error: 'Failed to update ticket status'
      }, 500)
    }
  }
)

// Add partner comment to support ticket
app.post('/support/tickets/:id/messages',
  partnerAuthMiddleware,
  zValidator('json', z.object({
    message: z.string().min(1, 'Message is required'),
    isInternal: z.boolean().default(false)
  })),
  async (c) => {
    try {
      const partner = getCurrentPartner(c)
      if (!partner) {
        return c.json({ error: 'Partner not found' }, 401)
      }

      const ticketId = c.req.param('id')
      const { message, isInternal } = c.req.valid('json')

      // Verify partner has access to this ticket
      const [ticketData] = await db
        .select({
          ticket: {
            id: supportTickets.id,
            title: supportTickets.title,
            status: supportTickets.status,
            clientId: supportTickets.clientId
          }
        })
        .from(supportTickets)
        .leftJoin(schoolReferrals, eq(supportTickets.clientId, schoolReferrals.clientId))
        .where(and(
          eq(supportTickets.id, ticketId),
          eq(schoolReferrals.partnerId, partner.id)
        ))
        .limit(1)

      if (!ticketData) {
        return c.json({
          success: false,
          error: 'Support ticket not found or access denied'
        }, 404)
      }

      // Add partner message
      const [newMessage] = await db.insert(ticketMessages).values({
        ticketId: ticketId,
        message,
        senderType: 'partner',
        senderId: partner.id
      }).returning()

      // Update ticket timestamp
      await db
        .update(supportTickets)
        .set({ updatedAt: new Date() })
        .where(eq(supportTickets.id, ticketId))

      // Send notifications if not internal
      if (!isInternal) {
        await supportNotificationService.notifyNewMessage(
          ticketData.ticket.id,
          newMessage.id,
          'admin' // Use 'admin' as closest match since 'partner' is not supported
        )
      }

      return c.json({
        success: true,
        message: 'Comment added successfully',
        data: {
          messageId: newMessage.id,
          createdAt: newMessage.createdAt
        }
      })

    } catch (error) {
      console.error('Partner ticket message error:', error)
      return c.json({
        success: false,
        error: 'Failed to add comment'
      }, 500)
    }
  }
)

// Get partner earnings and withdrawal history
app.get('/earnings', partnerAuthMiddleware, async (c) => {
  try {
    const partner = getCurrentPartner(c)
    if (!partner) {
      return c.json({ error: 'Partner not found' }, 401)
    }

    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')

    const offset = (page - 1) * limit

    // Get earnings history with client details
    const earnings = await db
      .select({
        id: partnerEarnings.id,
        clientName: clients.schoolName,
        amount: partnerEarnings.partnerEarning,
        month: sql<string>`EXTRACT(MONTH FROM ${partnerEarnings.calculatedAt})::text`,
        year: sql<number>`EXTRACT(YEAR FROM ${partnerEarnings.calculatedAt})::integer`,
        createdAt: partnerEarnings.calculatedAt
      })
      .from(partnerEarnings)
      .leftJoin(clients, eq(partnerEarnings.clientId, clients.id))
      .where(eq(partnerEarnings.partnerId, partner.id))
      .orderBy(desc(partnerEarnings.calculatedAt))
      .limit(limit)
      .offset(offset)

    // Get withdrawal history
    const withdrawals = await db
      .select({
        id: withdrawalRequests.id,
        amount: withdrawalRequests.requestedAmount,
        status: withdrawalRequests.status,
        requestDate: withdrawalRequests.requestedAt,
        processedDate: withdrawalRequests.processedAt,
        bankDetails: withdrawalRequests.bankDetailsSnapshot,
        transactionId: withdrawalRequests.transactionReference,
        notes: withdrawalRequests.rejectionReason
      })
      .from(withdrawalRequests)
      .where(eq(withdrawalRequests.partnerId, partner.id))
      .orderBy(desc(withdrawalRequests.requestedAt))
      .limit(10)

    // Get earnings summary
    const currentDate = new Date()
    const currentMonth = currentDate.getMonth() + 1
    const currentYear = currentDate.getFullYear()
    const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1
    const lastMonthYear = currentMonth === 1 ? currentYear - 1 : currentYear

    const [summary] = await db
      .select({
        totalEarnings: sql<string>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)::text`,
        thisMonthEarnings: sql<string>`COALESCE(SUM(CASE WHEN EXTRACT(MONTH FROM ${partnerEarnings.calculatedAt}) = ${currentMonth} AND EXTRACT(YEAR FROM ${partnerEarnings.calculatedAt}) = ${currentYear} THEN CAST(${partnerEarnings.partnerEarning} AS DECIMAL) ELSE 0 END), 0)::text`,
        lastMonthEarnings: sql<string>`COALESCE(SUM(CASE WHEN EXTRACT(MONTH FROM ${partnerEarnings.calculatedAt}) = ${lastMonth} AND EXTRACT(YEAR FROM ${partnerEarnings.calculatedAt}) = ${lastMonthYear} THEN CAST(${partnerEarnings.partnerEarning} AS DECIMAL) ELSE 0 END), 0)::text`,
        yearToDateEarnings: sql<string>`COALESCE(SUM(CASE WHEN EXTRACT(YEAR FROM ${partnerEarnings.calculatedAt}) = ${currentYear} THEN CAST(${partnerEarnings.partnerEarning} AS DECIMAL) ELSE 0 END), 0)::text`,
        availableForWithdrawal: sql<string>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN CAST(${partnerEarnings.partnerEarning} AS DECIMAL) ELSE 0 END), 0)::text`
      })
      .from(partnerEarnings)
      .where(eq(partnerEarnings.partnerId, partner.id))

    // Get pending withdrawals amount
    const [pendingWithdrawals] = await db
      .select({
        total: sql<string>`COALESCE(SUM(CAST(${withdrawalRequests.requestedAmount} AS DECIMAL)), 0)::text`
      })
      .from(withdrawalRequests)
      .where(and(
        eq(withdrawalRequests.partnerId, partner.id),
        eq(withdrawalRequests.status, 'pending')
      ))

    // Calculate growth
    const thisMonth = parseFloat(summary?.thisMonthEarnings || '0')
    const lastMonthAmount = parseFloat(summary?.lastMonthEarnings || '0')
    const growth = lastMonthAmount > 0 ? ((thisMonth - lastMonthAmount) / lastMonthAmount) * 100 : 0

    return c.json({
      success: true,
      data: {
        summary: {
          totalEarnings: summary?.totalEarnings || '0',
          thisMonthEarnings: summary?.thisMonthEarnings || '0',
          lastMonthEarnings: summary?.lastMonthEarnings || '0',
          yearToDateEarnings: summary?.yearToDateEarnings || '0',
          availableForWithdrawal: summary?.availableForWithdrawal || '0',
          pendingWithdrawals: pendingWithdrawals?.total || '0',
          growth: Math.round(growth * 100) / 100
        },
        recentEarnings: earnings,
        withdrawalHistory: withdrawals
      }
    })

  } catch (error) {
    console.error('Partner earnings error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch earnings data'
    }, 500)
  }
})

// Request withdrawal
app.post('/earnings/withdraw', 
  partnerAuthMiddleware,
  zValidator('json', z.object({
    amount: z.number().positive('Amount must be positive'),
    bankDetails: z.object({
      accountNumber: z.string().min(1, 'Account number required'),
      ifscCode: z.string().min(1, 'IFSC code required'),
      accountHolderName: z.string().min(1, 'Account holder name required')
    })
  })),
  async (c) => {
    try {
      const partner = getCurrentPartner(c)
      const { amount, bankDetails } = c.req.valid('json')

      // TODO: Validate available balance
      // TODO: Create withdrawal request

      return c.json({
        success: true,
        message: 'Withdrawal request submitted successfully',
        data: {
          requestId: 'wr_' + Date.now(),
          amount,
          status: 'pending',
          estimatedProcessingTime: '3-5 business days'
        }
      })

    } catch (error) {
      console.error('Partner withdrawal request error:', error)
      return c.json({
        success: false,
        error: 'Failed to submit withdrawal request'
      }, 500)
    }
  }
)

export default app
