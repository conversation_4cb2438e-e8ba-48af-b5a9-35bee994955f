# Razorpay Webhook Setup Recommendations

## Current Webhook Configuration Analysis

### ✅ What's Correct in Your Setup
- **Comprehensive Event Coverage**: All 40 events selected is perfect for testing and production
- **Test Mode**: Smart approach to start with test mode before going live
- **Alert Email**: `<EMAIL>` configured for failure notifications
- **Auto-generated Secret**: `YUgksHLU5_R52RM` provides good security

### ⚠️ Recommended Changes Before Creating

#### 1. Update Webhook URL
**Current**: `https://schopio.vercel.app/`
**Recommended**: `https://schopio.vercel.app/api/webhooks/razorpay`

**Reason**: Specific endpoint for webhook handling with proper routing

#### 2. Environment Variable Setup
Store the webhook secret securely:
```env
RAZORPAY_WEBHOOK_SECRET=YUgksHLU5_R52RM
```

#### 3. Webhook Handler Implementation Required
Create the endpoint before activating webhook:
```typescript
// app/api/webhooks/razorpay/route.ts
import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-razorpay-signature');
    
    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET!)
      .update(body)
      .digest('hex');
    
    if (signature !== expectedSignature) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }
    
    const event = JSON.parse(body);
    
    // Process webhook event
    await processWebhookEvent(event);
    
    return NextResponse.json({ status: 'success' });
  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json({ error: 'Processing failed' }, { status: 500 });
  }
}
```

## Webhook Events Relevance for Schopio

### 🎯 Critical Events for Partner Commission System

#### Payment Events (School Payments)
- **payment.authorized** - School payment authorized, hold partner commission
- **payment.captured** - School payment confirmed, evaluate commission release
- **payment.failed** - School payment failed, hold partner commission indefinitely
- **payment.dispute.created** - Dispute raised, hold/reverse partner commission

#### Transfer Events (Partner Payouts)
- **transfer.processed** - Partner commission successfully transferred
- **transfer.failed** - Partner payout failed, retry or manual intervention
- **transfer.reversed** - Commission reversed due to dispute/chargeback

#### Fund Account Events (Partner Verification)
- **fund_account.validated** - Partner bank account verified for payouts
- **fund_account.validation.failed** - Partner account invalid, hold payouts

#### Subscription Events (Billing Lifecycle)
- **subscription.activated** - New school subscription, setup commission tracking
- **subscription.cancelled** - School cancelled, handle prorated commissions
- **subscription.charged** - Monthly billing successful, process commissions

### 📊 Event Processing Priority Matrix

| Event Category | Priority | Impact on Commission | Action Required |
|---------------|----------|---------------------|-----------------|
| Payment Captured | HIGH | Release commission | Automated release logic |
| Payment Failed | HIGH | Hold commission | Indefinite hold |
| Transfer Processed | MEDIUM | Update status | Partner notification |
| Subscription Cancelled | MEDIUM | Prorate commission | Calculation adjustment |
| Fund Account Validated | LOW | Enable payouts | Update partner status |

## Implementation Phases

### Phase 1: Basic Webhook Handler (Immediate)
```typescript
async function processWebhookEvent(event: any) {
  switch (event.event) {
    case 'payment.captured':
      await handleSchoolPaymentCaptured(event);
      break;
    case 'payment.failed':
      await handleSchoolPaymentFailed(event);
      break;
    case 'transfer.processed':
      await handlePartnerTransferProcessed(event);
      break;
    default:
      console.log(`Unhandled event: ${event.event}`);
  }
}
```

### Phase 2: Commission Logic Integration (Week 2-3)
```typescript
async function handleSchoolPaymentCaptured(event: any) {
  const payment = event.payload.payment.entity;
  
  // Find associated subscription and partner
  const subscription = await findSubscriptionByPayment(payment.id);
  if (!subscription?.partnerId) return;
  
  // Evaluate commission release conditions
  const releaseConditions = await evaluateReleaseConditions(subscription);
  
  if (releaseConditions.canRelease) {
    await releasePartnerCommission(subscription.partnerId, payment.amount);
  } else {
    await holdPartnerCommission(subscription.partnerId, releaseConditions.holdReason);
  }
}
```

### Phase 3: Advanced Risk Management (Week 4-5)
```typescript
interface ReleaseConditions {
  canRelease: boolean;
  holdReason?: string;
  holdUntilDate?: Date;
  riskScore: number;
  requiresManualApproval: boolean;
}

async function evaluateReleaseConditions(subscription: any): Promise<ReleaseConditions> {
  const conditions = {
    schoolPaymentCleared: await checkPaymentCleared(subscription.lastPaymentId),
    noActiveDisputes: await checkActiveDisputes(subscription.schoolId),
    partnerKycValid: await checkPartnerKyc(subscription.partnerId),
    withinGracePeriod: await checkGracePeriod(subscription.lastPaymentDate),
    riskScoreAcceptable: await calculateRiskScore(subscription) < 70
  };
  
  return {
    canRelease: Object.values(conditions).every(Boolean),
    holdReason: getHoldReason(conditions),
    riskScore: await calculateRiskScore(subscription),
    requiresManualApproval: await requiresManualApproval(subscription)
  };
}
```

## Security Considerations

### Webhook Signature Verification
```typescript
function verifyWebhookSignature(body: string, signature: string): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET!)
    .update(body)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}
```

### Idempotency Protection
```typescript
const processedWebhooks = new Set<string>();

async function processWebhookEvent(event: any) {
  const eventId = event.payload.payment?.entity?.id || event.id;
  
  if (processedWebhooks.has(eventId)) {
    console.log(`Duplicate webhook ignored: ${eventId}`);
    return;
  }
  
  processedWebhooks.add(eventId);
  // Process event...
}
```

## Monitoring & Alerting

### Webhook Health Monitoring
```typescript
interface WebhookMetrics {
  totalReceived: number;
  successfullyProcessed: number;
  failed: number;
  averageProcessingTime: number;
  lastProcessedAt: Date;
}

async function trackWebhookMetrics(event: any, processingTime: number, success: boolean) {
  await updateMetrics({
    event: event.event,
    processingTime,
    success,
    timestamp: new Date()
  });
}
```

### Alert Conditions
- Webhook processing failure rate > 5%
- No webhooks received for > 1 hour during business hours
- Commission release failures > 2%
- Partner payout failures > 1%

## Testing Strategy

### Test Mode Validation
1. **Create test webhook** with current configuration
2. **Generate test events** using Razorpay dashboard
3. **Verify webhook delivery** and processing
4. **Test signature verification** and error handling
5. **Validate commission logic** with test scenarios

### Production Readiness Checklist
- [ ] Webhook endpoint implemented and tested
- [ ] Signature verification working
- [ ] Error handling and retry logic
- [ ] Monitoring and alerting configured
- [ ] Commission calculation logic validated
- [ ] Partner notification system ready
- [ ] Admin dashboard for monitoring

## Recommendation: Proceed with Webhook Creation

### ✅ Yes, Create the Webhook Now

**Reasons:**
1. **Test Mode Safety**: No financial risk in test mode
2. **Early Integration**: Start receiving and processing test events
3. **Development Workflow**: Build and test webhook handler incrementally
4. **Event Familiarity**: Understand Razorpay event structure and timing

### Next Immediate Steps After Creation

1. **Implement Basic Handler**: Create `/api/webhooks/razorpay` endpoint
2. **Test Event Processing**: Generate test events and verify handling
3. **Add Signature Verification**: Implement security measures
4. **Build Commission Logic**: Integrate with existing partner system
5. **Monitor and Iterate**: Refine based on test event patterns

## Conclusion

Your webhook configuration is excellent for comprehensive event monitoring. The selection of all 40 events provides complete visibility into the payment and transfer lifecycle, which is essential for the sophisticated commission management system we've designed.

**Proceed with creating the webhook** and start building the handler incrementally. The test mode provides a safe environment to develop and validate the complex business logic required for automated partner commission distribution with proper risk management.
