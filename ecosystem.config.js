/**
 * PM2 Ecosystem Configuration for Schopio Production Deployment
 * Provides process management, monitoring, and auto-restart capabilities
 */

module.exports = {
  apps: [
    {
      // Main application
      name: 'schopio-main',
      script: 'npm',
      args: 'start',
      cwd: './',
      instances: process.env.PM2_INSTANCES || 'max',
      exec_mode: 'cluster',
      
      // Environment
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        AUTO_START_SERVICES: 'true',
        BILLING_SCHEDULER_ENABLED: 'true',
        HEALTH_MONITORING_ENABLED: 'true',
        MISSED_BILL_DETECTION_ENABLED: 'true'
      },
      
      // Resource limits
      max_memory_restart: process.env.PM2_MAX_MEMORY_RESTART || '1G',
      max_restarts: 10,
      min_uptime: '10s',
      
      // Logging
      log_file: './logs/schopio.log',
      out_file: './logs/schopio-out.log',
      error_file: './logs/schopio-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Auto-restart settings
      watch: false, // Disable in production
      ignore_watch: ['node_modules', 'logs', '.git'],
      
      // Health monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // Advanced settings
      kill_timeout: 5000,
      listen_timeout: 3000,
      reload_delay: 0,
      
      // Source map support
      source_map_support: true,
      
      // Instance variables
      instance_var: 'INSTANCE_ID',
      
      // Graceful shutdown
      shutdown_with_message: true,
      
      // Monitoring
      monitoring: true,
      pmx: true
    },
    
    // Health check service (separate process for reliability)
    {
      name: 'schopio-health-monitor',
      script: './scripts/health-monitor.js',
      instances: 1,
      exec_mode: 'fork',
      
      env: {
        NODE_ENV: 'production',
        HEALTH_CHECK_INTERVAL: '30000', // 30 seconds
        HEALTH_CHECK_TIMEOUT: '5000',   // 5 seconds
        MAIN_APP_URL: 'http://localhost:3000'
      },
      
      max_memory_restart: '256M',
      max_restarts: 5,
      min_uptime: '10s',
      
      log_file: './logs/health-monitor.log',
      out_file: './logs/health-monitor-out.log',
      error_file: './logs/health-monitor-error.log',
      
      cron_restart: '0 4 * * *', // Restart daily at 4 AM
      
      watch: false,
      autorestart: true
    }
  ],

  // Deployment configuration
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: '**************:your-org/schopio.git',
      path: '/var/www/schopio',
      
      // Pre-deployment
      'pre-deploy-local': 'echo "Starting deployment..."',
      
      // Post-receive (on server)
      'post-deploy': [
        'npm ci --production',
        'npm run build',
        'npm run db:migrate',
        'mkdir -p logs',
        'mkdir -p storage/pdfs',
        'pm2 reload ecosystem.config.js --env production',
        'pm2 save'
      ].join(' && '),
      
      // Pre-setup (first deployment)
      'pre-setup': [
        'apt update',
        'apt install -y nodejs npm postgresql-client',
        'npm install -g pm2',
        'pm2 install pm2-logrotate'
      ].join(' && '),
      
      // Post-setup
      'post-setup': [
        'pm2 startup',
        'pm2 save',
        'pm2 install pm2-server-monit'
      ].join(' && '),
      
      env: {
        NODE_ENV: 'production',
        AUTO_START_SERVICES: 'true'
      }
    },
    
    staging: {
      user: 'deploy',
      host: ['staging-server.com'],
      ref: 'origin/staging',
      repo: '**************:your-org/schopio.git',
      path: '/var/www/schopio-staging',
      
      'post-deploy': [
        'npm ci',
        'npm run build',
        'npm run db:migrate',
        'mkdir -p logs',
        'pm2 reload ecosystem.config.js --env staging'
      ].join(' && '),
      
      env: {
        NODE_ENV: 'staging',
        AUTO_START_SERVICES: 'true',
        BILLING_DRY_RUN: 'true'
      }
    }
  }
}
