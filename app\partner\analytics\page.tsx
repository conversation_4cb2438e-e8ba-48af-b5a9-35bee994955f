'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/Button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Pie<PERSON>hart,
  Calendar,
  Users,
  Banknote,
  Target,
  ArrowUpRight,
  ArrowDownRight,
  Download,
  RefreshCw
} from 'lucide-react'

interface AnalyticsData {
  overview: {
    totalReferrals: number
    activeClients: number
    conversionRate: number
    totalEarnings: string
    monthlyGrowth: number
    quarterlyGrowth: number
  }
  referralTrends: Array<{
    month: string
    referrals: number
    conversions: number
    earnings: string
  }>
  clientPerformance: Array<{
    clientName: string
    subscriptionValue: string
    monthlyEarning: string
    status: 'active' | 'inactive' | 'pending'
    joinDate: string
  }>
  conversionFunnel?: {
    totalReferrals: number
    registeredClients: number
    activeClients: number
    subscribedClients: number
  }
  monthlyTrends?: Array<{
    month: string
    referrals: number
    conversions: number
    earnings: string
  }>
  topPerformingSchools?: Array<{
    clientName: string
    subscriptionValue: string
    monthlyEarning: string
    status: 'active' | 'inactive' | 'pending'
    joinDate: string
  }>
  earningsBreakdown: Array<{
    month: string
    commission: string
    bonus: string
    total: string
  }>
}

export default function PartnerAnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [timeRange, setTimeRange] = useState('6months')
  const [refreshing, setRefreshing] = useState(false)
  const router = useRouter()

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem('partnerToken')
    if (!token) {
      router.push('/partner/login')
      return
    }

    fetchAnalyticsData()
  }, [router, timeRange])

  const fetchAnalyticsData = async () => {
    try {
      const token = localStorage.getItem('partnerToken')
      const response = await fetch(`/api/partner/analytics?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('partnerToken')
          localStorage.removeItem('partner')
          router.push('/partner/login')
          return
        }
        throw new Error('Failed to fetch analytics data')
      }

      const data = await response.json()
      setAnalyticsData(data.data)
    } catch (error) {
      console.error('Analytics error:', error)
      setError('Failed to load analytics data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = () => {
    setRefreshing(true)
    fetchAnalyticsData()
  }

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? (
      <ArrowUpRight className="w-4 h-4 text-green-600" />
    ) : (
      <ArrowDownRight className="w-4 h-4 text-red-600" />
    )
  }

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? 'text-green-600' : 'text-red-600'
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      active: 'default',
      inactive: 'secondary',
      pending: 'outline'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    )
  }

  if (error && !analyticsData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchAnalyticsData}>Try Again</Button>
        </div>
      </div>
    )
  }

  if (!analyticsData) return null

  const {
    monthlyTrends = [],
    conversionFunnel = { totalReferrals: 0, registeredClients: 0, activeClients: 0, subscribedClients: 0 },
    topPerformingSchools = []
  } = analyticsData || {}

  // Safe data processing with null checks
  const safeMonthlyTrends = Array.isArray(monthlyTrends) ? monthlyTrends : []
  const safeConversionFunnel = {
    totalReferrals: Number(conversionFunnel?.totalReferrals) || 0,
    registeredClients: Number(conversionFunnel?.registeredClients) || 0,
    activeClients: Number(conversionFunnel?.activeClients) || 0,
    subscribedClients: Number(conversionFunnel?.subscribedClients) || 0
  }
  const safeTopSchools = Array.isArray(topPerformingSchools) ? topPerformingSchools : []

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics & Performance</h1>
          <p className="text-gray-600 mt-1">Detailed insights into your referral performance</p>
        </div>
        <div className="flex gap-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1month">Last Month</SelectItem>
              <SelectItem value="3months">Last 3 Months</SelectItem>
              <SelectItem value="6months">Last 6 Months</SelectItem>
              <SelectItem value="1year">Last Year</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Referrals</p>
                <p className="text-3xl font-bold text-gray-900">{safeConversionFunnel.totalReferrals}</p>
                <p className="text-sm text-gray-500 mt-2">
                  All time referrals
                </p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Clients</p>
                <p className="text-3xl font-bold text-gray-900">{safeConversionFunnel.activeClients}</p>
                <p className="text-sm text-gray-500 mt-2">
                  Paying subscribers
                </p>
              </div>
              <Target className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                <p className="text-3xl font-bold text-gray-900">
                  {safeConversionFunnel.totalReferrals > 0
                    ? Math.round((safeConversionFunnel.activeClients / safeConversionFunnel.totalReferrals) * 100)
                    : 0}%
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Referral to client
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Subscribed Clients</p>
                <p className="text-3xl font-bold text-gray-900">{conversionFunnel.subscribedClients}</p>
                <p className="text-sm text-gray-500 mt-2">
                  With active subscriptions
                </p>
              </div>
              <Banknote className="w-8 h-8 text-emerald-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Conversion Funnel */}
      <Card>
        <CardHeader>
          <CardTitle>Conversion Funnel</CardTitle>
          <CardDescription>Track your referral conversion process</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{safeConversionFunnel.totalReferrals}</p>
              <p className="text-sm text-gray-600">Total Referrals</p>
            </div>
            <div className="text-center">
              <div className="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                <Calendar className="w-8 h-8 text-yellow-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{safeConversionFunnel.registeredClients}</p>
              <p className="text-sm text-gray-600">Registered Clients</p>
              <p className="text-xs text-gray-500">
                {safeConversionFunnel.totalReferrals > 0 ?
                  ((safeConversionFunnel.registeredClients / safeConversionFunnel.totalReferrals) * 100).toFixed(1) : 0}% conversion
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                <BarChart3 className="w-8 h-8 text-purple-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{safeConversionFunnel.activeClients}</p>
              <p className="text-sm text-gray-600">Active Clients</p>
              <p className="text-xs text-gray-500">
                {safeConversionFunnel.registeredClients > 0 ?
                  ((safeConversionFunnel.activeClients / safeConversionFunnel.registeredClients) * 100).toFixed(1) : 0}% conversion
              </p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                <Target className="w-8 h-8 text-green-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{safeConversionFunnel.subscribedClients}</p>
              <p className="text-sm text-gray-600">Subscribed Clients</p>
              <p className="text-xs text-gray-500">
                {safeConversionFunnel.activeClients > 0 ?
                  ((safeConversionFunnel.subscribedClients / safeConversionFunnel.activeClients) * 100).toFixed(1) : 0}% conversion
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Referral Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Referral Trends</CardTitle>
            <CardDescription>Monthly referral and conversion performance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {safeMonthlyTrends.length > 0 ? safeMonthlyTrends.map((trend, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{trend.month}</p>
                    <p className="text-sm text-gray-600">
                      {trend.referrals || 0} referrals • {trend.conversions || 0} conversions
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">₹{trend.earnings || 0}</p>
                    <p className="text-xs text-gray-500">
                      {(trend.conversions || 0) > 0 ? (((trend.conversions || 0) / (trend.referrals || 1)) * 100).toFixed(1) : 0}% rate
                    </p>
                  </div>
                </div>
              )) : (
                <div className="text-center py-8 text-gray-500">
                  <p>No monthly trend data available yet</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Top Performing Clients */}
        <Card>
          <CardHeader>
            <CardTitle>Client Performance</CardTitle>
            <CardDescription>Your highest value referrals</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {safeTopSchools.length > 0 ? safeTopSchools.map((client, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{client.clientName}</p>
                    <p className="text-sm text-gray-600">
                      Joined {new Date(client.joinDate).toLocaleDateString()}
                    </p>
                    <div className="mt-1">
                      {getStatusBadge(client.status)}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">₹{client.subscriptionValue}/mo</p>
                    <p className="text-sm text-green-600">₹{client.monthlyEarning} earning</p>
                  </div>
                </div>
              )) : (
                <div className="text-center py-8 text-gray-500">
                  <p>No client performance data available yet</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Earnings Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Earnings Breakdown</CardTitle>
          <CardDescription>Monthly commission and bonus breakdown</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Month</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-600">Commission</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-600">Bonus</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-600">Total</th>
                </tr>
              </thead>
              <tbody>
                {monthlyTrends.length > 0 ? monthlyTrends.map((earning, index) => (
                  <tr key={index} className="border-b">
                    <td className="py-3 px-4 font-medium text-gray-900">{earning.month || 'N/A'}</td>
                    <td className="py-3 px-4 text-right text-gray-900">₹{earning.earnings || 0}</td>
                    <td className="py-3 px-4 text-right text-gray-900">₹0</td>
                    <td className="py-3 px-4 text-right font-semibold text-green-600">₹{earning.earnings || 0}</td>
                  </tr>
                )) : (
                  <tr>
                    <td colSpan={4} className="py-8 text-center text-gray-500">
                      No earnings data available yet
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
