// Quick fix for referral codes - Create test codes immediately
// Run with: node tests/quick-fix-referral-codes.js

const BASE_URL = 'http://localhost:3000'

async function quickFixReferralCodes() {
  console.log('🚀 Quick Fix: Creating referral codes for testing...\n')

  try {
    // Step 1: Login as admin
    console.log('1. Logging in as admin...')
    const loginResponse = await fetch(`${BASE_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    })

    if (!loginResponse.ok) {
      console.log('   ❌ Admin login failed')
      return
    }

    const loginData = await loginResponse.json()
    const adminToken = loginData.token
    console.log('   ✅ Admin login successful')

    // Step 2: Create new partners with referral codes (since existing ones don't have codes)
    console.log('\n2. Creating new test partners with auto-generated referral codes...')
    
    const testPartners = [
      {
        name: 'Test Partner Alpha',
        email: '<EMAIL>',
        password: 'TestPartner@123',
        companyName: 'Alpha Education Solutions',
        phone: '+91-9999999991',
        address: 'Test Address Alpha, Mumbai, Maharashtra 400001',
        profitSharePercentage: 40.00
      },
      {
        name: 'Test Partner Beta',
        email: '<EMAIL>',
        password: 'TestPartner@456',
        companyName: 'Beta School Connect',
        phone: '+91-9999999992',
        address: 'Test Address Beta, Bangalore, Karnataka 560001',
        profitSharePercentage: 45.00
      }
    ]

    const createdCodes = []

    for (const partner of testPartners) {
      console.log(`   📝 Creating partner: ${partner.name}`)
      
      const partnerResponse = await fetch(`${BASE_URL}/api/admin/partners`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(partner)
      })

      if (partnerResponse.ok) {
        const partnerData = await partnerResponse.json()
        console.log(`      ✅ Partner created: ${partnerData.partner?.partnerCode || 'Unknown'}`)
        console.log(`      🎫 Referral code: ${partnerData.partner?.referralCode || 'Auto-generated'}`)
        
        if (partnerData.partner?.referralCode) {
          createdCodes.push({
            code: partnerData.partner.referralCode,
            partnerName: partnerData.partner.name
          })
        }
      } else {
        const error = await partnerResponse.text()
        console.log(`      ❌ Failed to create partner: ${error}`)
      }
    }

    // Step 3: Test the created codes
    console.log('\n3. Testing created referral codes...')
    
    for (const codeInfo of createdCodes) {
      console.log(`\n   🧪 Testing code: ${codeInfo.code}`)
      
      const validateResponse = await fetch(`${BASE_URL}/api/auth/referral/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: codeInfo.code })
      })

      if (validateResponse.ok) {
        const validateData = await validateResponse.json()
        if (validateData.valid) {
          console.log(`      ✅ Valid - Partner: ${validateData.referralCode.partnerName}`)
        } else {
          console.log(`      ❌ Invalid: ${validateData.error}`)
        }
      } else {
        console.log(`      ❌ Validation failed: ${validateResponse.status}`)
      }
    }

    // Step 4: Provide testing instructions
    console.log('\n🎉 Quick Fix Completed!')
    
    if (createdCodes.length > 0) {
      console.log('\n📋 Available referral codes for immediate testing:')
      createdCodes.forEach(code => {
        console.log(`   🎫 ${code.code} - ${code.partnerName}`)
      })
      
      console.log('\n💡 How to test:')
      console.log('   1. Go to school portal: http://localhost:3000/profile/settings')
      console.log('   2. Login as a school user')
      console.log('   3. Enter any of the above referral codes')
      console.log('   4. The 400 Bad Request error should be resolved!')
      
      console.log('\n🔧 If you still get errors, check:')
      console.log('   - Make sure you are logged in as a school user')
      console.log('   - Check browser console for any JavaScript errors')
      console.log('   - Verify the school user has proper authentication token')
    } else {
      console.log('\n❌ No referral codes were created successfully')
      console.log('\n🔧 Alternative solutions:')
      console.log('   1. Run the SQL script: scripts/fix-referral-codes.sql')
      console.log('   2. Use the admin dashboard to create partners')
      console.log('   3. Check database connection and permissions')
    }

    // Step 5: Provide manual SQL solution as backup
    console.log('\n📝 Manual SQL Solution (if needed):')
    console.log('   If the above didn\'t work, run this SQL in your database:')
    console.log('')
    console.log('   -- Quick test referral codes')
    console.log('   INSERT INTO referral_codes (partner_id, code, is_active, usage_count, created_at)')
    console.log('   SELECT id, \'MANUAL01\', true, 0, NOW() FROM partners LIMIT 1;')
    console.log('')
    console.log('   INSERT INTO referral_codes (partner_id, code, is_active, usage_count, created_at)')
    console.log('   SELECT id, \'MANUAL02\', true, 0, NOW() FROM partners LIMIT 1;')
    console.log('')
    console.log('   Then test with codes: MANUAL01, MANUAL02')

  } catch (error) {
    console.error('❌ Error in quick fix:', error.message)
    console.log('\n🔧 Fallback solution:')
    console.log('   1. Check if the development server is running')
    console.log('   2. Verify database connection')
    console.log('   3. Run the SQL script manually: scripts/fix-referral-codes.sql')
  }
}

// Run the quick fix
quickFixReferralCodes()
