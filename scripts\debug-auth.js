/**
 * Debug Authentication System
 * Tests token generation, validation, and middleware behavior
 */

const jwt = require('jsonwebtoken')

// Test JWT secret
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'

console.log('🔍 Debugging Authentication System...')
console.log('=' .repeat(50))

// Test 1: Token Generation
console.log('\n1. Testing Token Generation...')

function generateTestToken(userType, userId, email) {
  const payload = {
    userId,
    email,
    type: userType,
    jti: 'test-' + Date.now(),
    iat: Math.floor(Date.now() / 1000),
    iss: `schopio-${userType}`,
    aud: `schopio-${userType}-portal`
  }

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: userType === 'admin' ? '8h' : '12h',
    algorithm: 'HS256'
  })
}

const testTokens = {
  school: generateTestToken('school', 'test-school-id', '<EMAIL>'),
  admin: generateTestToken('admin', 'test-admin-id', '<EMAIL>'),
  partner: generateTestToken('partner', 'test-partner-id', '<EMAIL>')
}

console.log('✅ Generated test tokens:')
Object.entries(testTokens).forEach(([type, token]) => {
  console.log(`   ${type}: ${token.slice(0, 50)}...`)
})

// Test 2: Token Validation
console.log('\n2. Testing Token Validation...')

function validateTestToken(token, expectedType) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET)
    
    if (decoded.type !== expectedType) {
      return { valid: false, error: `Type mismatch: expected ${expectedType}, got ${decoded.type}` }
    }
    
    const now = Math.floor(Date.now() / 1000)
    if (decoded.exp && decoded.exp < now) {
      return { valid: false, error: 'Token expired' }
    }
    
    return { valid: true, decoded }
  } catch (error) {
    return { valid: false, error: error.message }
  }
}

Object.entries(testTokens).forEach(([type, token]) => {
  const result = validateTestToken(token, type)
  console.log(`   ${type}: ${result.valid ? '✅ Valid' : '❌ Invalid - ' + result.error}`)
  if (result.valid) {
    console.log(`     - User ID: ${result.decoded.userId}`)
    console.log(`     - Email: ${result.decoded.email}`)
    console.log(`     - Expires: ${new Date(result.decoded.exp * 1000).toLocaleString()}`)
  }
})

// Test 3: Cross-type validation (should fail)
console.log('\n3. Testing Cross-type Validation (should fail)...')

const crossTests = [
  { token: testTokens.school, expectedType: 'admin' },
  { token: testTokens.admin, expectedType: 'partner' },
  { token: testTokens.partner, expectedType: 'school' }
]

crossTests.forEach(({ token, expectedType }) => {
  const result = validateTestToken(token, expectedType)
  console.log(`   Cross-validation: ${result.valid ? '❌ Should have failed' : '✅ Correctly rejected - ' + result.error}`)
})

// Test 4: Expired token
console.log('\n4. Testing Expired Token...')

const expiredToken = jwt.sign(
  {
    userId: 'test-id',
    email: '<EMAIL>',
    type: 'admin',
    exp: Math.floor(Date.now() / 1000) - 3600 // Expired 1 hour ago
  },
  JWT_SECRET
)

const expiredResult = validateTestToken(expiredToken, 'admin')
console.log(`   Expired token: ${expiredResult.valid ? '❌ Should have failed' : '✅ Correctly rejected - ' + expiredResult.error}`)

// Test 5: Malformed token
console.log('\n5. Testing Malformed Token...')

const malformedResult = validateTestToken('invalid.token.here', 'admin')
console.log(`   Malformed token: ${malformedResult.valid ? '❌ Should have failed' : '✅ Correctly rejected - ' + malformedResult.error}`)

// Test 6: Environment check
console.log('\n6. Environment Check...')
console.log(`   JWT_SECRET: ${JWT_SECRET === 'fallback-secret-key' ? '⚠️ Using fallback secret' : '✅ Custom secret set'}`)
console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'not set'}`)

// Test 7: Cookie format test
console.log('\n7. Testing Cookie Format...')

const cookieValue = testTokens.admin
const cookieString = `adminToken=${cookieValue}; path=/; max-age=604800; samesite=strict`
console.log(`   Cookie string: ${cookieString.slice(0, 100)}...`)

// Test parsing cookie
const parsedCookie = cookieString.split(';')[0].split('=')[1]
const cookieValidation = validateTestToken(parsedCookie, 'admin')
console.log(`   Cookie token validation: ${cookieValidation.valid ? '✅ Valid' : '❌ Invalid - ' + cookieValidation.error}`)

console.log('\n🎉 Authentication Debug Complete!')
console.log('=' .repeat(50))
