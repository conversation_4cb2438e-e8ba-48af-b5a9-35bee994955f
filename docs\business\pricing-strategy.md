# 💰 Schopio Pricing Strategy & Business Logic

## 📊 **PRICING MODEL OVERVIEW**

### **Core Pricing Structure**
- **Base Rate**: ₹80 per student per month
- **Billing Cycle**: Monthly recurring billing
- **Payment Model**: Pay-and-use subscription
- **Minimum Commitment**: No minimum contract period

### **Pricing Calculation Formula**
```
Monthly Subscription = Student Count × ₹80
Annual Discount = 10 months payment for 12 months service (2 months free)
```

---

## 🎯 **DETAILED PRICING RULES**

### **1. Student Count Based Pricing**

#### **Pricing Tiers**
```
Small Schools (1-100 students):     ₹80/student/month
Medium Schools (101-500 students):  ₹80/student/month  
Large Schools (501+ students):      ₹80/student/month
```

**Note**: Flat rate pricing for simplicity - no tier discounts currently

#### **Student Count Updates**
- **Mid-cycle changes**: Prorated billing for remaining days
- **Increase in students**: Additional charges from next billing cycle
- **Decrease in students**: Credit applied to next billing cycle
- **Maximum changes per month**: 2 updates allowed

### **2. Annual Billing Discount**

#### **Discount Structure**
- **Monthly billing**: Full ₹80/student/month
- **Annual billing**: Pay for 10 months, get 12 months service
- **Effective discount**: 16.67% (2 months free)
- **Payment**: Full annual amount due upfront

#### **Annual Billing Calculation**
```typescript
const monthlyAmount = studentCount * 80;
const annualAmount = monthlyAmount * 10; // 2 months free
const totalSavings = monthlyAmount * 2;
```

### **3. Demo vs Production Pricing**

#### **Demo Requests**
- **Duration**: 30 days free trial
- **Features**: Full access to all modules
- **Student limit**: Up to 50 students for demo
- **Conversion**: Can upgrade to paid anytime during demo

#### **Production Subscriptions**
- **Setup fee**: ₹0 (no setup charges)
- **Activation**: Immediate upon payment confirmation
- **Features**: All 8+ modules included
- **Support**: Email and ticket support included

---

## 💳 **BILLING & PAYMENT RULES**

### **1. Billing Cycle Management**

#### **Monthly Billing**
- **Billing date**: Same date each month as subscription start
- **Grace period**: 3 days after due date
- **Late payment penalty**: 2% per day after grace period
- **Service suspension**: After 15 days of non-payment

#### **Billing Date Examples**
```
Subscription Start: January 15th
Next Billing Dates: Feb 15, Mar 15, Apr 15, etc.

Grace Period: 3 days (Feb 18, Mar 18, Apr 18)
Penalty Start: Day 4 (Feb 19, Mar 19, Apr 19)
Suspension: Day 16 (Mar 2, Apr 2, May 2)
```

### **2. Payment Processing**

#### **Accepted Payment Methods**
- **Credit/Debit Cards**: Visa, Mastercard, RuPay
- **Net Banking**: All major Indian banks
- **UPI**: All UPI-enabled apps
- **Wallets**: Paytm, PhonePe, Google Pay

#### **Payment Gateway**
- **Primary**: Razorpay
- **Backup**: Manual bank transfer (for failed payments)
- **Currency**: INR only
- **Processing time**: Instant for digital payments

### **3. Failed Payment Handling**

#### **Retry Logic**
- **Automatic retries**: 3 attempts over 7 days
- **Retry schedule**: Day 1, Day 3, Day 7
- **Manual retry**: School can retry payment anytime
- **Payment recovery**: Full service restoration upon successful payment

#### **Communication Flow**
```
Day 0: Payment due notification
Day 1: Payment failed - first retry
Day 3: Payment failed - second retry + email
Day 7: Payment failed - final retry + SMS
Day 10: Service suspension warning
Day 15: Service suspended
```

---

## 🤝 **PARTNER REVENUE SHARING**

### **1. Partner Commission Structure**

#### **Commission Rates**
- **Standard partners**: 35% of monthly subscription
- **Premium partners**: 40% of monthly subscription  
- **Exclusive partners**: 50% of monthly subscription
- **Commission period**: Lifetime of client relationship

#### **Commission Calculation**
```typescript
const monthlySubscription = studentCount * 80;
const partnerCommission = monthlySubscription * (commissionRate / 100);
const companyRevenue = monthlySubscription - partnerCommission;
```

### **2. Expense Deduction System**

#### **Deductible Expenses**
- **Setup costs**: Server setup, customization
- **Training costs**: Partner training, certification
- **Marketing costs**: Approved marketing materials
- **Support costs**: Technical support provided to client

#### **Expense Calculation**
```typescript
const grossCommission = monthlySubscription * (commissionRate / 100);
const netCommission = grossCommission - monthlyExpenses;
const partnerPayout = Math.max(netCommission, 0); // No negative payouts
```

### **3. Partner Payout Process**

#### **Payout Schedule**
- **Frequency**: Monthly
- **Payout date**: 5th of every month
- **Minimum payout**: ₹1,000
- **Payment method**: NEFT bank transfer

#### **Payout Calculation Period**
```
January earnings: Paid on February 5th
February earnings: Paid on March 5th
March earnings: Paid on April 5th
```

---

## 📋 **SUBSCRIPTION LIFECYCLE RULES**

### **1. Subscription States**

#### **State Definitions**
- **ACTIVE**: Service running, payments current
- **GRACE**: Payment overdue but within grace period
- **SUSPENDED**: Service suspended due to non-payment
- **CANCELLED**: Subscription terminated by school
- **EXPIRED**: Subscription ended naturally

### **2. State Transition Rules**

#### **ACTIVE → GRACE**
- **Trigger**: Payment failure on billing date
- **Duration**: 3 days
- **Service**: Continues normally
- **Actions**: Send payment reminders

#### **GRACE → SUSPENDED**
- **Trigger**: No payment after 3-day grace period
- **Service**: Limited access (view-only)
- **Actions**: Daily penalty charges (2%)
- **Recovery**: Full payment + penalties

#### **SUSPENDED → CANCELLED**
- **Trigger**: 15 days in suspended state
- **Service**: Complete access termination
- **Data**: Retained for 30 days
- **Recovery**: New subscription required

### **3. Cancellation & Refund Policy**

#### **Voluntary Cancellation**
- **Notice period**: 30 days advance notice
- **Refund**: Prorated refund for unused period
- **Data export**: 30 days to download data
- **Re-activation**: Possible within 90 days

#### **Involuntary Cancellation**
- **Reason**: Non-payment after suspension period
- **Refund**: No refund for unpaid amounts
- **Data retention**: 30 days backup retention
- **Collection**: Outstanding amounts may be pursued

---

## 🎯 **BUSINESS METRICS & KPIs**

### **1. Revenue Metrics**
- **MRR (Monthly Recurring Revenue)**: Sum of all active subscriptions
- **ARR (Annual Recurring Revenue)**: MRR × 12
- **ARPU (Average Revenue Per User)**: Total revenue / number of clients
- **Churn Rate**: Cancelled subscriptions / total subscriptions

### **2. Partner Metrics**
- **Partner Revenue Share**: Total commissions paid to partners
- **Partner Acquisition Cost**: Cost to onboard new partners
- **Partner Lifetime Value**: Total revenue generated by partner
- **Partner Churn**: Partners who stop referring clients

### **3. Client Metrics**
- **Customer Acquisition Cost (CAC)**: Marketing spend / new clients
- **Customer Lifetime Value (CLV)**: Average revenue per client over lifetime
- **Payment Success Rate**: Successful payments / total payment attempts
- **Support Ticket Volume**: Average tickets per client per month
