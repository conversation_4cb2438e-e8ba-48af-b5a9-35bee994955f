'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Brain, TrendingUp, AlertTriangle, Target, BarChart3, Users, ArrowRight, Zap, Eye, Shield } from 'lucide-react'

const AICapabilitiesSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const aiFeatures = [
    {
      icon: AlertTriangle,
      title: 'Dropout Risk Prediction',
      description: 'AI identifies at-risk students 6 months in advance',
      metric: '35% improvement in retention',
      color: 'text-red-600 bg-red-100',
      gradient: 'from-red-500 to-orange-500'
    },
    {
      icon: TrendingUp,
      title: 'Performance Analytics',
      description: 'Predictive insights on academic performance trends',
      metric: '40% better learning outcomes',
      color: 'text-blue-600 bg-blue-100',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Target,
      title: 'Personalized Learning',
      description: 'AI-driven recommendations for individual students',
      metric: '50% faster skill development',
      color: 'text-emerald-600 bg-emerald-100',
      gradient: 'from-emerald-500 to-green-500'
    },
    {
      icon: BarChart3,
      title: 'Resource Optimization',
      description: 'Smart allocation of teachers and classroom resources',
      metric: '30% cost reduction',
      color: 'text-purple-600 bg-purple-100',
      gradient: 'from-purple-500 to-indigo-500'
    }
  ]

  const valuePropositions = [
    {
      title: 'Early Intervention System',
      challenge: 'Students falling behind unnoticed',
      solution: 'AI identifies struggling students 6 weeks early',
      result: 'Prevent academic failures before they happen',
      metric: '6 weeks',
      metricLabel: 'Early Warning',
      icon: AlertTriangle,
      color: 'from-red-500 to-orange-500'
    },
    {
      title: 'Performance Optimization',
      challenge: 'Inconsistent learning outcomes',
      solution: 'Real-time analytics for personalized learning',
      result: 'Optimize teaching methods for each student',
      metric: '40%',
      metricLabel: 'Better Outcomes',
      icon: Target,
      color: 'from-emerald-500 to-green-500'
    },
    {
      title: 'Resource Intelligence',
      challenge: 'Inefficient resource allocation',
      solution: 'AI-powered scheduling and optimization',
      result: 'Maximize efficiency of staff and facilities',
      metric: '30%',
      metricLabel: 'Efficiency Gain',
      icon: BarChart3,
      color: 'from-blue-500 to-cyan-500'
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#ffffff08_1px,transparent_1px),linear-gradient(to_bottom,#ffffff08_1px,transparent_1px)] bg-[size:24px_24px] opacity-50" />
      <div className="absolute top-20 right-10 w-32 h-32 bg-blue-500 rounded-full opacity-10 blur-3xl" />
      <div className="absolute bottom-20 left-10 w-40 h-40 bg-emerald-500 rounded-full opacity-10 blur-3xl" />

      <div className="container mx-auto px-4 relative">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-500/20 text-blue-300 border border-blue-500/30 px-6 py-3 rounded-full text-lg font-bold mb-6 backdrop-blur-sm">
            <Brain className="w-5 h-5" />
            AI-Powered Intelligence
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            The Future of Education is 
            <span className="bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent"> Predictive</span>
          </h2>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Our AI doesn&apos;t just manage data—it predicts outcomes, prevents problems, and optimizes every aspect of your school&apos;s operations.
          </p>
        </motion.div>

        {/* AI Features Grid */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20"
        >
          {aiFeatures.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/15 transition-all duration-300 group">
                <CardContent padding="lg">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 ${feature.color} group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">{feature.title}</h3>
                  <p className="text-blue-200 text-sm mb-4">{feature.description}</p>
                  <div className={`inline-flex items-center gap-2 bg-gradient-to-r ${feature.gradient} text-white px-3 py-1 rounded-full text-xs font-bold`}>
                    <TrendingUp className="w-3 h-3" />
                    {feature.metric}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Value Propositions */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="mb-16"
        >
          <h3 className="text-3xl font-bold text-white text-center mb-4">What You&apos;ll Get with AI-Powered Management</h3>
          <p className="text-xl text-blue-200 text-center mb-12 max-w-3xl mx-auto">
            Transform your school operations with intelligent systems that predict, prevent, and optimize every aspect of education delivery.
          </p>
          <div className="grid md:grid-cols-3 gap-8">
            {valuePropositions.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="h-full bg-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group">
                  <CardContent padding="xl">
                    <div className="text-center">
                      <div className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                        <value.icon className="w-8 h-8 text-white" />
                      </div>

                      <div className="mb-6">
                        <div className="text-3xl font-bold text-slate-900 mb-1">{value.metric}</div>
                        <div className="text-sm font-semibold text-slate-600">{value.metricLabel}</div>
                      </div>

                      <h4 className="text-xl font-bold text-slate-900 mb-4">{value.title}</h4>

                      <div className="space-y-4 text-left">
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                          <div className="text-sm font-semibold text-red-700 mb-1">Current Challenge:</div>
                          <div className="text-sm text-red-600">{value.challenge}</div>
                        </div>

                        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="text-sm font-semibold text-blue-700 mb-1">AI Solution:</div>
                          <div className="text-sm text-blue-600">{value.solution}</div>
                        </div>

                        <div className="p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
                          <div className="text-sm font-semibold text-emerald-700 mb-1">Your Benefit:</div>
                          <div className="text-sm text-emerald-600 font-medium">{value.result}</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Interactive Demo CTA */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
          className="text-center"
        >
          <Card className="bg-gradient-to-r from-blue-600/20 to-emerald-600/20 backdrop-blur-sm border-blue-500/30 max-w-2xl mx-auto">
            <CardContent padding="xl">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-lg flex items-center justify-center">
                  <Eye className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">See AI in Action</h3>
              </div>
              <p className="text-blue-100 mb-6">
                Experience how our AI identifies at-risk students, predicts performance trends, and optimizes your school&apos;s operations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  icon={ArrowRight}
                  iconPosition="right"
                  className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white font-bold px-8 py-4"
                >
                  Book AI Demo
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4"
                >
                  Download AI Guide
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default AICapabilitiesSection
