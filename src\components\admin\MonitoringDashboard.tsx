/**
 * Production Monitoring Dashboard Component
 * 
 * Provides real-time monitoring interface for Schopio system health,
 * business metrics, security events, and operational status.
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/Button'
// Note: Tabs, Progress, and Alert components need to be implemented
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
// import { Progress } from '@/components/ui/progress'
// import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Banknote,
  Shield,
  TrendingUp,
  Users,
  Zap,
  RefreshCw,
  AlertCircle,
  Database,
  CreditCard,
  Lock
} from 'lucide-react'

interface MonitoringData {
  timestamp: Date
  overallHealth: {
    score: number
    status: 'healthy' | 'warning' | 'critical'
    issues: string[]
  }
  businessMetrics: {
    activeSubscriptions: number
    monthlyRecurringRevenue: number
    churnRate: number
    paymentSuccessRate: number
    averageRevenuePerUser: number
    newSubscriptionsToday: number
    subscriptionsByStatus: Record<string, number>
  }
  billingHealth: {
    invoicesGenerated: number
    paymentsProcessed: number
    failedPayments: number
    webhookSuccess: number
    subscriptionsInGrace: number
    overdueRevenue: number
  }
  securityMetrics: {
    authenticationFailures: number
    rateLimitViolations: number
    suspiciousActivity: number
    blockedIPs: number
    securityEvents: Array<{
      type: string
      count: number
      lastOccurrence: Date
    }>
  }
  systemPerformance: {
    databaseHealth: {
      connectionCount: number
      slowQueries: number
      indexUsage: number
    }
    apiPerformance: {
      averageResponseTime: number
      errorRate: number
      requestVolume: number
    }
    resourceUtilization: {
      memoryUsage: number
      cpuUsage: number
      diskUsage: number
    }
  }
}

export default function MonitoringDashboard() {
  const [data, setData] = useState<MonitoringData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())
  const [autoRefresh, setAutoRefresh] = useState(true)

  const fetchMonitoringData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/monitoring/dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch monitoring data: ${response.statusText}`)
      }

      const result = await response.json()
      setData(result)
      setError(null)
      setLastRefresh(new Date())
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch monitoring data')
      console.error('Monitoring data fetch error:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMonitoringData()
  }, [])

  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      fetchMonitoringData()
    }, 30000) // Refresh every 30 seconds

    return () => clearInterval(interval)
  }, [autoRefresh])

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (loading && !data) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Loading monitoring data...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="border border-red-200 bg-red-50 p-4 rounded-lg">
        <div className="flex items-center space-x-2">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <h3 className="font-medium text-red-800">Monitoring Error</h3>
        </div>
        <p className="text-red-700 mt-2">{error}</p>
        <Button
          className="mt-2 bg-red-600 text-white hover:bg-red-700"
          onClick={fetchMonitoringData}
        >
          Retry
        </Button>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="border border-gray-200 bg-gray-50 p-4 rounded-lg">
        <div className="flex items-center space-x-2">
          <AlertCircle className="h-4 w-4 text-gray-600" />
          <h3 className="font-medium text-gray-800">No Data Available</h3>
        </div>
        <p className="text-gray-700 mt-2">Monitoring data is not available at the moment.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Monitoring</h1>
          <p className="text-gray-600">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            className={`px-4 py-2 rounded ${autoRefresh ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? 'Disable' : 'Enable'} Auto-refresh
          </Button>
          <Button
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            onClick={fetchMonitoringData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overall Health */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>System Health</span>
            <span className={`px-2 py-1 rounded text-sm ${
              data.overallHealth.status === 'healthy' ? 'bg-green-100 text-green-800' :
              data.overallHealth.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {data.overallHealth.status.charAt(0).toUpperCase() + data.overallHealth.status.slice(1)}
            </span>
          </CardTitle>
          <CardDescription>
            Overall system health score and status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Health Score</span>
                <span className={`text-2xl font-bold ${getHealthStatusColor(data.overallHealth.status)}`}>
                  {data.overallHealth.score}/100
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${data.overallHealth.score}%` }}
                ></div>
              </div>
            </div>

            {data.overallHealth.issues.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-red-600">Active Issues:</h4>
                {data.overallHealth.issues.map((issue, index) => (
                  <div key={index} className="border border-red-200 bg-red-50 p-3 rounded">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <span className="text-red-800">{issue}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Business Metrics */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Business Metrics</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
              <Users className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.businessMetrics.activeSubscriptions}</div>
              <p className="text-xs text-gray-500">
                +{data.businessMetrics.newSubscriptionsToday} today
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Recurring Revenue</CardTitle>
              <Banknote className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(data.businessMetrics.monthlyRecurringRevenue)}
              </div>
              <p className="text-xs text-gray-500">
                ARPU: {formatCurrency(data.businessMetrics.averageRevenuePerUser)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Payment Success Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatPercentage(data.businessMetrics.paymentSuccessRate)}
              </div>
              <p className="text-xs text-gray-500">Last 30 days</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${data.businessMetrics.churnRate > 10 ? 'text-red-600' : 'text-green-600'}`}>
                {formatPercentage(data.businessMetrics.churnRate)}
              </div>
              <p className="text-xs text-gray-500">This month</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Billing Health */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Billing Health</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Webhook Success Rate</CardTitle>
              <Zap className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${data.billingHealth.webhookSuccess < 98 ? 'text-red-600' : 'text-green-600'}`}>
                {formatPercentage(data.billingHealth.webhookSuccess)}
              </div>
              <p className="text-xs text-gray-500">Last 24 hours</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed Payments</CardTitle>
              <AlertTriangle className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${data.billingHealth.failedPayments > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {data.billingHealth.failedPayments}
              </div>
              <p className="text-xs text-gray-500">Today</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Subscriptions in Grace</CardTitle>
              <AlertCircle className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${data.billingHealth.subscriptionsInGrace > 0 ? 'text-yellow-600' : 'text-green-600'}`}>
                {data.billingHealth.subscriptionsInGrace}
              </div>
              <p className="text-xs text-gray-500">
                {formatCurrency(data.billingHealth.overdueRevenue)} at risk
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Security Metrics */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Security Metrics</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Auth Failures</CardTitle>
              <Lock className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${data.securityMetrics.authenticationFailures > 100 ? 'text-red-600' : 'text-green-600'}`}>
                {data.securityMetrics.authenticationFailures}
              </div>
              <p className="text-xs text-gray-500">Last 24 hours</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Rate Limit Violations</CardTitle>
              <Shield className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.securityMetrics.rateLimitViolations}</div>
              <p className="text-xs text-gray-500">Last 24 hours</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Blocked IPs</CardTitle>
              <AlertTriangle className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.securityMetrics.blockedIPs}</div>
              <p className="text-xs text-gray-500">Currently blocked</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Suspicious Activity</CardTitle>
              <AlertCircle className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${data.securityMetrics.suspiciousActivity > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {data.securityMetrics.suspiciousActivity}
              </div>
              <p className="text-xs text-gray-500">Events detected</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">System Performance</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Database Health</CardTitle>
              <Database className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Connections:</span>
                  <span className="font-medium">{data.systemPerformance.databaseHealth.connectionCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Slow Queries:</span>
                  <span className={`font-medium ${data.systemPerformance.databaseHealth.slowQueries > 10 ? 'text-red-600' : 'text-green-600'}`}>
                    {data.systemPerformance.databaseHealth.slowQueries}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Index Usage:</span>
                  <span className={`font-medium ${data.systemPerformance.databaseHealth.indexUsage < 80 ? 'text-yellow-600' : 'text-green-600'}`}>
                    {formatPercentage(data.systemPerformance.databaseHealth.indexUsage)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">API Performance</CardTitle>
              <Activity className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Avg Response:</span>
                  <span className="font-medium">{data.systemPerformance.apiPerformance.averageResponseTime}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Error Rate:</span>
                  <span className="font-medium">{formatPercentage(data.systemPerformance.apiPerformance.errorRate)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Requests:</span>
                  <span className="font-medium">{data.systemPerformance.apiPerformance.requestVolume}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Resource Usage</CardTitle>
              <TrendingUp className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Memory:</span>
                  <span className="font-medium">{formatPercentage(data.systemPerformance.resourceUtilization.memoryUsage)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">CPU:</span>
                  <span className="font-medium">{formatPercentage(data.systemPerformance.resourceUtilization.cpuUsage)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Disk:</span>
                  <span className="font-medium">{formatPercentage(data.systemPerformance.resourceUtilization.diskUsage)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
