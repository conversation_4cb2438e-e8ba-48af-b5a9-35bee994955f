'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Rocket, 
  Headphones, 
  GraduationCap, 
  Shield, 
  Clock, 
  Users, 
  CheckCircle, 
  ArrowRight,
  Calendar,
  Database,
  Award,
  Zap
} from 'lucide-react'

const ImplementationSupportSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const implementationSteps = [
    {
      step: "Day 1-3",
      title: "Setup & Configuration",
      description: "Initial system setup, data structure configuration, and basic customization",
      icon: Rocket,
      color: "text-blue-600 bg-blue-100"
    },
    {
      step: "Day 4-7", 
      title: "Data Migration",
      description: "Secure transfer of existing data with zero downtime and full backup",
      icon: Database,
      color: "text-emerald-600 bg-emerald-100"
    },
    {
      step: "Day 8-10",
      title: "Staff Training",
      description: "Comprehensive training for administrators, teachers, and support staff",
      icon: GraduationCap,
      color: "text-purple-600 bg-purple-100"
    },
    {
      step: "Day 11-14",
      title: "Go Live & Support",
      description: "System launch with dedicated support team monitoring everything",
      icon: Zap,
      color: "text-yellow-600 bg-yellow-100"
    }
  ]

  const supportFeatures = [
    {
      icon: Rocket,
      title: "Quick Setup",
      subtitle: "Live in 14 days",
      description: "Fastest implementation in the industry with proven methodology",
      benefits: ["Zero downtime migration", "Parallel system testing", "Gradual rollout option"]
    },
    {
      icon: Headphones,
      title: "Expert Support", 
      subtitle: "24/7 dedicated team",
      description: "Round-the-clock support from education technology specialists",
      benefits: ["Dedicated account manager", "Priority response times", "Multi-language support"]
    },
    {
      icon: GraduationCap,
      title: "Training Included",
      subtitle: "All staff levels",
      description: "Comprehensive training program for every user type in your school",
      benefits: ["Role-based training modules", "Video tutorials library", "Ongoing skill development"]
    },
    {
      icon: Shield,
      title: "Data Security",
      subtitle: "Bank-level encryption",
      description: "Enterprise-grade security with compliance certifications",
      benefits: ["ISO 27001 certified", "GDPR compliant", "Regular security audits"]
    }
  ]

  const guarantees = [
    {
      icon: Clock,
      title: "14-Day Implementation",
      description: "Guaranteed go-live within 2 weeks or we work for free until completion"
    },
    {
      icon: Database,
      title: "100% Data Safety",
      description: "Complete data migration guarantee with full backup and rollback options"
    },
    {
      icon: Users,
      title: "Staff Adoption Success",
      description: "95% staff adoption rate within 30 days or additional training at no cost"
    },
    {
      icon: Award,
      title: "ROI Achievement",
      description: "Measurable ROI within 6 months or we'll optimize your setup for free"
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Rocket className="w-4 h-4" />
            Implementation & Support
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            From Setup to Success in 
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Just 14 Days</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Our proven implementation methodology ensures your school is up and running quickly with full staff adoption and ongoing support.
          </p>
        </motion.div>

        {/* Implementation Timeline */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="mb-20"
        >
          <h3 className="text-2xl font-bold text-slate-900 text-center mb-12">14-Day Implementation Timeline</h3>
          <div className="grid md:grid-cols-4 gap-6">
            {implementationSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative"
              >
                <Card className="h-full bg-gradient-to-br from-slate-50 to-blue-50 border-0 shadow-md hover:shadow-lg transition-all duration-300">
                  <CardContent padding="lg">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 ${step.color}`}>
                      <step.icon className="w-6 h-6" />
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-bold text-blue-600 mb-2">{step.step}</div>
                      <h4 className="text-lg font-bold text-slate-900 mb-2">{step.title}</h4>
                      <p className="text-sm text-slate-600">{step.description}</p>
                    </div>
                  </CardContent>
                </Card>
                {index < implementationSteps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-3 w-6 h-0.5 bg-blue-200 transform -translate-y-1/2" />
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Support Features */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="mb-16"
        >
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {supportFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="h-full bg-white border border-slate-200 shadow-md hover:shadow-lg transition-all duration-300 group">
                  <CardContent padding="lg">
                    <div className="text-center mb-4">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <feature.icon className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-slate-900 mb-1">{feature.title}</h3>
                      <p className="text-sm font-semibold text-blue-600 mb-3">{feature.subtitle}</p>
                      <p className="text-slate-600 text-sm mb-4">{feature.description}</p>
                    </div>
                    <div className="space-y-2">
                      {feature.benefits.map((benefit, benefitIndex) => (
                        <div key={benefitIndex} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="w-4 h-4 text-emerald-500 flex-shrink-0" />
                          <span className="text-slate-600">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Guarantees */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-slate-900 text-center mb-12">Our Implementation Guarantees</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {guarantees.map((guarantee, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="h-full bg-gradient-to-br from-emerald-50 to-blue-50 border border-emerald-200 shadow-md hover:shadow-lg transition-all duration-300">
                  <CardContent padding="lg">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <guarantee.icon className="w-6 h-6 text-emerald-600" />
                      </div>
                      <h4 className="text-lg font-bold text-slate-900 mb-2">{guarantee.title}</h4>
                      <p className="text-sm text-slate-600">{guarantee.description}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="text-center"
        >
          <Card className="bg-gradient-to-r from-blue-600 to-emerald-600 border-0 max-w-2xl mx-auto">
            <CardContent padding="xl">
              <div className="text-white">
                <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
                <p className="text-blue-100 mb-6">
                  Join 500+ schools that have successfully implemented Schopio. Book your implementation consultation today.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    size="lg"
                    icon={Calendar}
                    iconPosition="right"
                    className="bg-white text-blue-600 hover:bg-blue-50 font-bold px-8 py-4"
                  >
                    Schedule Implementation Call
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4"
                  >
                    Download Implementation Guide
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default ImplementationSupportSection
