import { NextRequest, NextResponse } from 'next/server'
import { SubscriptionAuthService } from '@/src/services/subscriptionAuthService'
import { db } from '@/src/db'
import { clientUsers, subscriptionAuth } from '@/src/db/schema'
import { eq, and } from 'drizzle-orm'
import jwt from 'jsonwebtoken'
import { auditLogger } from '@/src/services/auditLogger'

interface VerifyAuthRequest {
  razorpay_order_id: string
  razorpay_payment_id: string
  razorpay_signature: string
}

interface AuthenticatedUser {
  userId: string
  email: string
  clientId: string
  role: string
  type: string
}

export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const authHeader = request.headers.get('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    let authenticatedUser: AuthenticatedUser

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

      if (!decoded.userId || !decoded.email || !decoded.clientId) {
        return NextResponse.json(
          { error: 'Invalid token format' },
          { status: 401 }
        )
      }

      // Verify user exists and is active
      const [user] = await db.select()
        .from(clientUsers)
        .where(and(
          eq(clientUsers.id, decoded.userId),
          eq(clientUsers.isActive, true),
          eq(clientUsers.emailVerified, true)
        ))
        .limit(1)

      if (!user) {
        return NextResponse.json(
          { error: 'User not found or inactive' },
          { status: 401 }
        )
      }

      authenticatedUser = {
        userId: decoded.userId,
        email: decoded.email,
        clientId: decoded.clientId,
        role: decoded.role || user.role,
        type: decoded.type || 'school'
      }

    } catch (jwtError) {
      await auditLogger.logAuth('failed_login', {
        email: 'unknown',
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        success: false,
        errorMessage: 'Invalid JWT token'
      })

      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const body: VerifyAuthRequest = await request.json()
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = body

    // Validate required fields
    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      return NextResponse.json(
        { error: 'Missing required payment verification fields' },
        { status: 400 }
      )
    }

    // Verify the authentication record belongs to the authenticated user's client
    const [authRecord] = await db.select()
      .from(subscriptionAuth)
      .where(eq(subscriptionAuth.razorpayOrderId, razorpay_order_id))
      .limit(1)

    if (!authRecord) {
      return NextResponse.json(
        { error: 'Authentication record not found' },
        { status: 404 }
      )
    }

    if (authRecord.clientId !== authenticatedUser.clientId) {
      await auditLogger.logSecurity({
        type: 'unauthorized_access',
        userId: authenticatedUser.userId,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        details: {
          email: authenticatedUser.email,
          razorpayOrderId: razorpay_order_id,
          clientId: authenticatedUser.clientId,
          errorMessage: `Attempted to verify payment for order ${razorpay_order_id} not owned by client ${authenticatedUser.clientId}`
        },
        severity: 'high'
      })

      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Process authentication success
    const result = await SubscriptionAuthService.processAuthSuccess(
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature
    )

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      subscriptionStatus: result.subscriptionStatus,
      message: result.message || 'Subscription activated successfully'
    })

  } catch (error) {
    console.error('❌ Failed to verify subscription auth:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
