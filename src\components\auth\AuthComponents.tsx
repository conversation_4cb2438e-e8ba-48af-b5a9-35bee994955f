'use client'

import React, { useState, useEffect } from 'react'
import { AuthUtils, UserType, TokenPayload } from '@/src/utils/authUtils'

// React hook for authentication state
export function useAuth(userType: UserType) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<TokenPayload | null>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    const checkAuth = () => {
      const authenticated = AuthUtils.isAuthenticated(userType)
      const userData = AuthUtils.getCurrentUser(userType)
      
      setIsAuthenticated(authenticated)
      setUser(userData)
      setLoading(false)
    }
    
    checkAuth()
    
    // Check auth state on storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === `${userType}Token`) {
        checkAuth()
      }
    }
    
    window.addEventListener('storage', handleStorageChange)
    
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [userType])
  
  const login = (token: string) => {
    AuthUtils.setToken(userType, token)
    setIsAuthenticated(true)
    setUser(AuthUtils.getCurrentUser(userType))
  }
  
  const logout = () => {
    AuthUtils.logout(userType)
    setIsAuthenticated(false)
    setUser(null)
  }
  
  return {
    isAuthenticated,
    user,
    loading,
    login,
    logout
  }
}

// Route protection component
interface ProtectedRouteProps {
  userType: UserType
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ProtectedRoute({ userType, children, fallback }: ProtectedRouteProps) {
  const { isAuthenticated, loading } = useAuth(userType)
  
  if (loading) {
    return fallback || <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
  }
  
  if (!isAuthenticated) {
    // This will be handled by middleware, but good to have as backup
    AuthUtils.logout(userType)
    return null
  }
  
  return <>{children}</>
}

// Public route component (redirects if authenticated)
interface PublicRouteProps {
  userType: UserType
  children: React.ReactNode
}

export function PublicRoute({ userType, children }: PublicRouteProps) {
  const { isAuthenticated, loading } = useAuth(userType)
  
  useEffect(() => {
    if (!loading && isAuthenticated) {
      AuthUtils.redirectToDashboardIfAuthenticated(userType)
    }
  }, [isAuthenticated, loading, userType])
  
  if (loading) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
  }
  
  if (isAuthenticated) {
    return null // Will redirect
  }
  
  return <>{children}</>
}
