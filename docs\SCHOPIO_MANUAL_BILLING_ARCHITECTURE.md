# 🏗️ **SCHOPIO MANUAL BILLING ARCHITECTURE**
## Comprehensive Guide to Monthly Payment System for 5,000+ Schools

**Date**: July 10, 2025  
**Status**: ✅ **PRODUCTION ARCHITECTURE DOCUMENTATION**

---

## 📋 **EXECUTIVE SUMMARY**

⚠️ **IMPORTANT**: This document describes the **PLANNED ARCHITECTURE** for Schopio's billing system. The current production system operates as a **manual billing system** where admin intervention is required for most operations. See `PRODUCTION_REALITY_ASSESSMENT.md` for current capabilities.

**Planned System**: Schopio is designed to operate a **manual monthly billing system** that will handle 5,000+ schools with reliable payment processing, automated billing cycle management, and comprehensive error handling. Unlike automatic subscription models, Schopio's approach provides schools with control over their payment timing while maintaining systematic billing operations.

### **Key Architecture Principles (PLANNED)**
- **Manual Payment Control**: Schools initiate payments when ready ✅ **IMPLEMENTED**
- **Automated Billing Cycles**: System calculates due dates automatically ❌ **REQUIRES MANUAL TRIGGER**
- **Scalable Processing**: Handles thousands of concurrent payments ❌ **NOT YET IMPLEMENTED**
- **Comprehensive Tracking**: Full audit trail and payment history ✅ **IMPLEMENTED**
- **Error Resilience**: Robust error handling and recovery mechanisms ⚠️ **BASIC IMPLEMENTATION**

---

## 🔄 **1. MONTHLY PAYMENT CYCLE LOGIC**

### **Billing Cycle Calculation System**

#### **Database Schema Foundation**
```sql
-- Core subscription tracking
billingSubscriptions {
  id: UUID PRIMARY KEY,
  clientId: UUID,
  currentPeriodStart: DATE,     -- Current billing period start
  currentPeriodEnd: DATE,       -- Current billing period end  
  nextBillingDate: DATE,        -- When next payment is due
  monthlyAmount: DECIMAL,       -- Amount due each month
  status: ENUM('active', 'suspended', 'cancelled'),
  gracePeriodDays: INTEGER,     -- Days after due date before suspension
  createdAt: TIMESTAMP,
  updatedAt: TIMESTAMP
}

-- Payment history tracking
billingInvoices {
  id: UUID PRIMARY KEY,
  subscriptionId: UUID,
  invoiceNumber: STRING,
  amount: DECIMAL,
  dueDate: DATE,
  status: ENUM('pending', 'paid', 'overdue', 'cancelled'),
  paidAt: TIMESTAMP,
  razorpayOrderId: STRING,
  razorpayPaymentId: STRING
}
```

#### **Automated Due Date Calculation**
```typescript
// Monthly billing cycle logic
function calculateNextBillingDate(currentPeriodEnd: Date): Date {
  const nextBilling = new Date(currentPeriodEnd)
  nextBilling.setDate(nextBilling.getDate() + 1) // Day after period ends
  
  // Handle month-end edge cases
  if (nextBilling.getDate() !== currentPeriodEnd.getDate() + 1) {
    // Month overflow occurred (e.g., Jan 31 + 1 month = Mar 3)
    nextBilling.setDate(0) // Set to last day of intended month
  }
  
  return nextBilling
}

// Billing period advancement
function advanceBillingPeriod(subscription: Subscription): BillingPeriod {
  const currentEnd = new Date(subscription.currentPeriodEnd)
  const newStart = new Date(currentEnd)
  newStart.setDate(newStart.getDate() + 1)
  
  const newEnd = new Date(newStart)
  newEnd.setMonth(newEnd.getMonth() + 1)
  newEnd.setDate(newEnd.getDate() - 1) // Last day of billing month
  
  const nextBilling = calculateNextBillingDate(newEnd)
  
  return {
    currentPeriodStart: newStart,
    currentPeriodEnd: newEnd,
    nextBillingDate: nextBilling
  }
}
```

#### **System-Driven Billing Automation**
1. **Daily Billing Check** (Cron Job at 6:00 AM IST):
   ```typescript
   // Check for subscriptions due for billing
   const dueSubscriptions = await db.select()
     .from(billingSubscriptions)
     .where(
       and(
         eq(billingSubscriptions.status, 'active'),
         lte(billingSubscriptions.nextBillingDate, today())
       )
     )
   
   for (const subscription of dueSubscriptions) {
     await generateMonthlyInvoice(subscription)
     await advanceSubscriptionPeriod(subscription)
   }
   ```

2. **Invoice Generation Process**:
   ```typescript
   async function generateMonthlyInvoice(subscription: Subscription) {
     const invoice = await db.insert(billingInvoices).values({
       subscriptionId: subscription.id,
       invoiceNumber: generateInvoiceNumber(),
       amount: subscription.monthlyAmount,
       dueDate: subscription.nextBillingDate,
       status: 'pending'
     })
     
     // Create Razorpay order for payment
     const razorpayOrder = await razorpayService.createOrder({
       amount: subscription.monthlyAmount * 100, // Paise
       currency: 'INR',
       receipt: invoice.invoiceNumber,
       notes: {
         subscriptionId: subscription.id,
         clientId: subscription.clientId,
         billingPeriod: `${subscription.currentPeriodStart} to ${subscription.currentPeriodEnd}`
       }
     })
     
     await db.update(billingInvoices)
       .set({ razorpayOrderId: razorpayOrder.id })
       .where(eq(billingInvoices.id, invoice.id))
   }
   ```

---

## 💳 **2. RAZORPAY ORDER CREATION PROCESS**

### **Enterprise-Scale Order Management**

#### **Batch Processing Architecture**
```typescript
// Daily batch processing for 5,000+ schools
class BillingProcessor {
  private readonly BATCH_SIZE = 100
  private readonly MAX_CONCURRENT = 10
  
  async processDailyBilling() {
    const dueSubscriptions = await this.getDueSubscriptions()
    const batches = this.createBatches(dueSubscriptions, this.BATCH_SIZE)
    
    // Process batches with controlled concurrency
    for (const batch of batches) {
      await Promise.allSettled(
        batch.map(subscription => this.processSubscription(subscription))
      )
      
      // Rate limiting - respect Razorpay API limits
      await this.delay(1000) // 1 second between batches
    }
  }
  
  async processSubscription(subscription: Subscription) {
    try {
      // Generate invoice
      const invoice = await this.createInvoice(subscription)
      
      // Create Razorpay order
      const order = await this.createRazorpayOrder(invoice)
      
      // Update subscription billing period
      await this.advanceSubscriptionPeriod(subscription)
      
      // Send notification to school
      await this.sendBillingNotification(subscription, invoice, order)
      
    } catch (error) {
      await this.handleProcessingError(subscription, error)
    }
  }
}
```

#### **Razorpay Order Creation with Error Handling**
```typescript
async function createRazorpayOrder(invoice: Invoice): Promise<RazorpayOrder> {
  const maxRetries = 3
  let attempt = 0
  
  while (attempt < maxRetries) {
    try {
      const order = await razorpayService.createOrder({
        amount: Math.round(invoice.amount * 100), // Convert to paise
        currency: 'INR',
        receipt: invoice.invoiceNumber,
        notes: {
          subscriptionId: invoice.subscriptionId,
          clientId: invoice.clientId,
          invoiceId: invoice.id,
          billingMonth: format(invoice.dueDate, 'yyyy-MM')
        }
      })
      
      // Update invoice with order details
      await db.update(billingInvoices)
        .set({ 
          razorpayOrderId: order.id,
          status: 'pending'
        })
        .where(eq(billingInvoices.id, invoice.id))
      
      return order
      
    } catch (error) {
      attempt++
      if (attempt >= maxRetries) {
        // Mark invoice as failed and alert admin
        await this.markInvoiceFailed(invoice, error)
        throw error
      }
      
      // Exponential backoff
      await this.delay(Math.pow(2, attempt) * 1000)
    }
  }
}
```

---

## 🏢 **3. ENTERPRISE-SCALE PAYMENT MANAGEMENT**

### **High-Availability Architecture**

#### **Load Balancing and Scaling**
```typescript
// Microservice architecture for payment processing
class PaymentProcessingService {
  private readonly redis: Redis
  private readonly queue: Queue
  
  constructor() {
    this.redis = new Redis(process.env.REDIS_URL)
    this.queue = new Queue('payment-processing', {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      }
    })
  }
  
  // Queue payment processing jobs
  async queuePaymentProcessing(paymentData: PaymentData) {
    await this.queue.add('process-payment', paymentData, {
      priority: this.calculatePriority(paymentData),
      delay: this.calculateDelay(paymentData)
    })
  }
  
  // Process payments with worker scaling
  async startWorkers(concurrency: number = 10) {
    const worker = new Worker('payment-processing', async (job) => {
      return await this.processPayment(job.data)
    }, {
      connection: this.redis,
      concurrency
    })
    
    worker.on('completed', this.handlePaymentSuccess)
    worker.on('failed', this.handlePaymentFailure)
  }
}
```

#### **Error Handling and Recovery**
```typescript
// Comprehensive error handling system
class PaymentErrorHandler {
  async handlePaymentError(payment: Payment, error: Error) {
    const errorType = this.classifyError(error)
    
    switch (errorType) {
      case 'NETWORK_ERROR':
        await this.retryPayment(payment, { delay: 5000 })
        break
        
      case 'RAZORPAY_API_ERROR':
        await this.escalateToAdmin(payment, error)
        break
        
      case 'DATABASE_ERROR':
        await this.queueForRetry(payment, { attempts: 3 })
        break
        
      case 'VALIDATION_ERROR':
        await this.markPaymentInvalid(payment, error)
        break
        
      default:
        await this.logUnknownError(payment, error)
    }
  }
  
  async retryPayment(payment: Payment, options: RetryOptions) {
    const retryCount = await this.getRetryCount(payment.id)
    
    if (retryCount < options.maxRetries) {
      await this.scheduleRetry(payment, {
        delay: options.delay * Math.pow(2, retryCount),
        attempt: retryCount + 1
      })
    } else {
      await this.markPaymentFailed(payment)
      await this.notifyAdminOfFailure(payment)
    }
  }
}
```

#### **Monitoring and Alerting**
```typescript
// Real-time monitoring system
class PaymentMonitor {
  async monitorPaymentHealth() {
    const metrics = await this.collectMetrics()
    
    // Check critical thresholds
    if (metrics.failureRate > 0.05) { // 5% failure rate
      await this.alertAdmins('HIGH_FAILURE_RATE', metrics)
    }
    
    if (metrics.processingDelay > 300000) { // 5 minutes
      await this.alertAdmins('HIGH_PROCESSING_DELAY', metrics)
    }
    
    if (metrics.queueSize > 1000) {
      await this.scaleUpWorkers()
    }
  }
  
  async collectMetrics(): Promise<PaymentMetrics> {
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000)
    
    return {
      totalPayments: await this.countPayments(last24Hours),
      successfulPayments: await this.countSuccessfulPayments(last24Hours),
      failedPayments: await this.countFailedPayments(last24Hours),
      averageProcessingTime: await this.getAverageProcessingTime(last24Hours),
      queueSize: await this.getQueueSize(),
      failureRate: await this.calculateFailureRate(last24Hours)
    }
  }
}
```

---

## 🔘 **4. PAYMENT BUTTON STATE MANAGEMENT**

### **Dynamic Button State Logic**

#### **Payment Eligibility Determination**
```typescript
// School portal payment button logic
async function getPaymentButtonState(clientId: string): Promise<PaymentButtonState> {
  // Get active subscription
  const subscription = await db.select()
    .from(billingSubscriptions)
    .where(
      and(
        eq(billingSubscriptions.clientId, clientId),
        eq(billingSubscriptions.status, 'active')
      )
    )
    .limit(1)
  
  if (!subscription) {
    return { enabled: false, reason: 'NO_ACTIVE_SUBSCRIPTION' }
  }
  
  // Get pending invoices
  const pendingInvoices = await db.select()
    .from(billingInvoices)
    .where(
      and(
        eq(billingInvoices.subscriptionId, subscription.id),
        inArray(billingInvoices.status, ['pending', 'overdue'])
      )
    )
    .orderBy(asc(billingInvoices.dueDate))
  
  if (pendingInvoices.length === 0) {
    return { 
      enabled: false, 
      reason: 'NO_PENDING_PAYMENTS',
      nextDueDate: subscription.nextBillingDate
    }
  }
  
  const currentInvoice = pendingInvoices[0]
  const today = new Date()
  const dueDate = new Date(currentInvoice.dueDate)
  
  // Check if payment is due or overdue
  if (today >= dueDate) {
    return {
      enabled: true,
      invoice: currentInvoice,
      status: today > dueDate ? 'OVERDUE' : 'DUE',
      daysOverdue: today > dueDate ? Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0
    }
  }
  
  // Payment not yet due
  return {
    enabled: false,
    reason: 'PAYMENT_NOT_DUE',
    nextDueDate: currentInvoice.dueDate,
    daysUntilDue: Math.floor((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
  }
}
```

#### **Duplicate Payment Prevention**
```typescript
// Prevent duplicate payments for same billing period
async function validatePaymentRequest(invoiceId: string, clientId: string): Promise<ValidationResult> {
  // Check if invoice is already paid
  const invoice = await db.select()
    .from(billingInvoices)
    .where(eq(billingInvoices.id, invoiceId))
    .limit(1)
  
  if (!invoice) {
    return { valid: false, reason: 'INVOICE_NOT_FOUND' }
  }
  
  if (invoice.status === 'paid') {
    return { valid: false, reason: 'ALREADY_PAID' }
  }
  
  // Check for concurrent payment attempts
  const lockKey = `payment_lock:${invoiceId}`
  const lockAcquired = await redis.set(lockKey, clientId, 'EX', 300, 'NX') // 5 minute lock
  
  if (!lockAcquired) {
    return { valid: false, reason: 'PAYMENT_IN_PROGRESS' }
  }
  
  return { valid: true, lockKey }
}
```

#### **Real-Time Button Updates**
```typescript
// WebSocket updates for payment button state
class PaymentButtonManager {
  private wsConnections: Map<string, WebSocket> = new Map()
  
  async updateButtonState(clientId: string) {
    const buttonState = await getPaymentButtonState(clientId)
    const ws = this.wsConnections.get(clientId)
    
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'PAYMENT_BUTTON_UPDATE',
        data: buttonState
      }))
    }
  }
  
  // Update button after successful payment
  async handlePaymentSuccess(paymentData: PaymentData) {
    await this.updateButtonState(paymentData.clientId)
    
    // Schedule next button activation
    const subscription = await this.getSubscription(paymentData.clientId)
    const nextDueDate = subscription.nextBillingDate
    
    await this.scheduleButtonActivation(paymentData.clientId, nextDueDate)
  }
}
```

---

## 🔄 **5. POST-PAYMENT SUBSCRIPTION UPDATES**

### **Correct Billing Cycle Management**

#### **❌ INCORRECT APPROACH: Updating Start Dates**
```typescript
// DON'T DO THIS - Poor logic that breaks billing history
async function incorrectPaymentProcessing(paymentId: string) {
  const payment = await getPayment(paymentId)
  const subscription = await getSubscription(payment.subscriptionId)
  
  // ❌ WRONG: This breaks billing period tracking
  await db.update(billingSubscriptions)
    .set({
      currentPeriodStart: new Date(), // ❌ Loses billing history
      currentPeriodEnd: addMonths(new Date(), 1) // ❌ Inconsistent periods
    })
    .where(eq(billingSubscriptions.id, subscription.id))
}
```

#### **✅ CORRECT APPROACH: Maintaining Billing Integrity**
```typescript
// Proper payment processing that maintains billing cycle integrity
async function processSuccessfulPayment(razorpayPaymentId: string, razorpayOrderId: string) {
  // Find the invoice and subscription
  const invoice = await db.select()
    .from(billingInvoices)
    .where(eq(billingInvoices.razorpayOrderId, razorpayOrderId))
    .limit(1)
  
  const subscription = await db.select()
    .from(billingSubscriptions)
    .where(eq(billingSubscriptions.id, invoice.subscriptionId))
    .limit(1)
  
  // 1. Mark invoice as paid (preserves billing period)
  await db.update(billingInvoices)
    .set({
      status: 'paid',
      paidAt: new Date(),
      razorpayPaymentId
    })
    .where(eq(billingInvoices.id, invoice.id))
  
  // 2. Update subscription status if needed (but NOT billing dates)
  if (subscription.status === 'suspended') {
    await db.update(billingSubscriptions)
      .set({ status: 'active' })
      .where(eq(billingSubscriptions.id, subscription.id))
  }
  
  // 3. Create payment record for audit trail
  await db.insert(paymentRecords).values({
    invoiceId: invoice.id,
    subscriptionId: subscription.id,
    clientId: subscription.clientId,
    amount: invoice.amount,
    paymentMethod: 'razorpay',
    razorpayPaymentId,
    razorpayOrderId,
    paidAt: new Date(),
    billingPeriod: `${subscription.currentPeriodStart} to ${subscription.currentPeriodEnd}`
  })
  
  // 4. Calculate partner commission if applicable
  await calculatePartnerCommission(subscription.id, invoice.amount)
  
  // 5. Send confirmation notifications
  await sendPaymentConfirmation(subscription.clientId, invoice)
  
  // Note: Billing dates are ONLY updated by the daily billing cron job
  // This maintains consistent billing cycles regardless of payment timing
}
```

#### **Billing Cycle Advancement (Cron Job Only)**
```typescript
// Only the daily billing process should advance billing periods
async function dailyBillingAdvancement() {
  const subscriptionsToAdvance = await db.select()
    .from(billingSubscriptions)
    .where(
      and(
        eq(billingSubscriptions.status, 'active'),
        lte(billingSubscriptions.nextBillingDate, today())
      )
    )
  
  for (const subscription of subscriptionsToAdvance) {
    // Calculate new billing period
    const newPeriod = calculateNextBillingPeriod(subscription)
    
    // Update subscription with new billing period
    await db.update(billingSubscriptions)
      .set({
        currentPeriodStart: newPeriod.start,
        currentPeriodEnd: newPeriod.end,
        nextBillingDate: newPeriod.nextDue
      })
      .where(eq(billingSubscriptions.id, subscription.id))
    
    // Generate next month's invoice
    await generateMonthlyInvoice(subscription, newPeriod)
  }
}
```

### **Payment History and Audit Trail**
```typescript
// Comprehensive payment tracking
interface PaymentAuditTrail {
  paymentId: string
  invoiceId: string
  subscriptionId: string
  clientId: string
  amount: number
  billingPeriod: string
  paidAt: Date
  paymentMethod: string
  razorpayDetails: {
    paymentId: string
    orderId: string
    signature: string
  }
  partnerCommission?: {
    partnerId: string
    amount: number
    percentage: number
  }
}

// Query payment history
async function getPaymentHistory(clientId: string): Promise<PaymentHistory[]> {
  return await db.select({
    paymentDate: paymentRecords.paidAt,
    amount: paymentRecords.amount,
    billingPeriod: paymentRecords.billingPeriod,
    invoiceNumber: billingInvoices.invoiceNumber,
    status: billingInvoices.status
  })
  .from(paymentRecords)
  .innerJoin(billingInvoices, eq(paymentRecords.invoiceId, billingInvoices.id))
  .where(eq(paymentRecords.clientId, clientId))
  .orderBy(desc(paymentRecords.paidAt))
}
```

---

## 📊 **SYSTEM ARCHITECTURE SUMMARY**

### **Key Components**
1. **Billing Engine**: Automated billing cycle management
2. **Payment Processor**: Razorpay integration with error handling
3. **State Manager**: Payment button and UI state management
4. **Audit System**: Comprehensive payment and billing tracking
5. **Monitoring**: Real-time system health and alerting

### **Data Flow**
1. **Subscription Creation** → Initial billing period set
2. **Daily Cron Job** → Generates invoices and advances billing periods
3. **School Payment** → Processes payment without changing billing dates
4. **System Updates** → Updates payment status and audit records
5. **Next Cycle** → Automated billing continues on schedule

### **Scalability Features**
- **Batch Processing**: Handles 5,000+ schools efficiently
- **Queue Management**: Prevents system overload
- **Error Recovery**: Automatic retry and escalation
- **Load Balancing**: Distributed processing capability
- **Monitoring**: Real-time performance tracking

**This architecture ensures reliable, scalable, and maintainable billing operations for Schopio's growing user base.**

---

## ❓ **ANSWERS TO SPECIFIC QUESTIONS**

### **Q1: Monthly Payment Cycle Logic**
**Answer**: The system uses a **daily cron job** that runs at 6:00 AM IST to check `nextBillingDate` field in each subscription. When a subscription's `nextBillingDate` <= today, the system:
1. Generates a new invoice for the upcoming month
2. Creates a Razorpay order for payment
3. Advances the billing period (`currentPeriodStart`, `currentPeriodEnd`, `nextBillingDate`)
4. Sends notification to the school

**Key Fields**: `currentPeriodStart`, `currentPeriodEnd`, `nextBillingDate` - these track billing cycles automatically without manual intervention.

### **Q2: Razorpay Order Creation Process**
**Answer**: **Automated batch processing** handles 5,000+ schools:
- **Daily Cron Job** triggers at 6:00 AM IST
- **Batch Processing** (100 schools per batch, max 10 concurrent)
- **Rate Limiting** (1 second between batches to respect Razorpay limits)
- **Error Handling** (3 retry attempts with exponential backoff)
- **Queue System** for failed orders with admin alerts

### **Q3: Enterprise-Scale Payment Management**
**Answer**: **Microservice architecture** with:
- **Redis Queue System** for payment processing
- **Worker Scaling** (configurable concurrency)
- **Error Classification** (network, API, database, validation errors)
- **Automatic Retry Logic** with exponential backoff
- **Real-time Monitoring** with failure rate alerts (>5% triggers admin notification)
- **Load Balancing** with automatic worker scaling

### **Q4: Payment Button State Management**
**Answer**: **Dynamic state calculation**:
- Button **enabled** only when invoice status is 'pending' or 'overdue' AND due date <= today
- **Disabled** when no pending payments or payment not yet due
- **Duplicate Prevention** using Redis locks (5-minute lock per invoice)
- **Real-time Updates** via WebSocket connections
- Button **reactivates** automatically when next billing cycle generates new invoice

### **Q5: Post-Payment Subscription Updates**
**Answer**: **❌ NEVER update start dates after payment** - this breaks billing history!

**✅ CORRECT APPROACH**:
1. **Mark invoice as paid** (preserves billing period)
2. **Update subscription status** if suspended → active
3. **Create payment audit record**
4. **Calculate partner commission**
5. **Send confirmation notifications**

**Billing dates are ONLY updated by daily cron job** - this maintains consistent monthly cycles regardless of when schools actually pay.

**Key Principle**: Payment timing doesn't affect billing cycle timing - schools can pay late without disrupting the systematic monthly billing schedule.
