#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Schopio System
 * 
 * This script runs all available tests and generates comprehensive reports.
 * It can run tests in different modes: development, staging, production
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

// Test configuration
const testConfig = require('../tests/test-config');

// Test files to run
const testFiles = [
  'tests/comprehensive-system-test.js',
  'tests/school-billing-workflow-test.js',
  'tests/test-payment-gateway-integration.js',
  'tests/integration/subscription-system.test.js'
];

// Test results
let overallResults = {
  startTime: Date.now(),
  endTime: null,
  totalTests: 0,
  passedTests: 0,
  failedTests: 0,
  skippedTests: 0,
  testFiles: [],
  errors: [],
  performance: {
    averageResponseTime: 0,
    slowestTest: null,
    fastestTest: null
  }
};

/**
 * Print colored console output
 */
function colorLog(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Run a single test file
 */
async function runTestFile(testFile) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    colorLog(`\n🧪 Running: ${testFile}`, 'cyan');
    
    const child = spawn('node', [testFile], {
      stdio: 'pipe',
      cwd: path.dirname(__dirname)
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
      process.stdout.write(data);
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
      process.stderr.write(data);
    });
    
    child.on('close', (code) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const result = {
        file: testFile,
        exitCode: code,
        duration,
        stdout,
        stderr,
        success: code === 0
      };
      
      if (code === 0) {
        colorLog(`✅ PASSED: ${testFile} (${duration}ms)`, 'green');
      } else {
        colorLog(`❌ FAILED: ${testFile} (${duration}ms)`, 'red');
      }
      
      resolve(result);
    });
    
    child.on('error', (error) => {
      colorLog(`💥 ERROR: ${testFile} - ${error.message}`, 'red');
      resolve({
        file: testFile,
        exitCode: 1,
        duration: Date.now() - startTime,
        stdout: '',
        stderr: error.message,
        success: false,
        error: error.message
      });
    });
  });
}

/**
 * Run TypeScript compilation check
 */
async function runTypeScriptCheck() {
  colorLog('\n🔍 Running TypeScript compilation check...', 'blue');
  
  try {
    execSync('bunx tsc --noEmit --skipLibCheck', {
      cwd: path.dirname(__dirname),
      stdio: 'pipe'
    });
    colorLog('✅ TypeScript compilation check passed', 'green');
    return true;
  } catch (error) {
    colorLog('❌ TypeScript compilation check failed', 'red');
    colorLog(error.stdout?.toString() || error.message, 'red');
    return false;
  }
}

/**
 * Run ESLint check
 */
async function runLintCheck() {
  colorLog('\n🔍 Running ESLint check...', 'blue');
  
  try {
    execSync('npx eslint . --ext .js,.jsx,.ts,.tsx --max-warnings 0', {
      cwd: path.dirname(__dirname),
      stdio: 'pipe'
    });
    colorLog('✅ ESLint check passed', 'green');
    return true;
  } catch (error) {
    colorLog('⚠️ ESLint check found issues', 'yellow');
    // Don't fail the entire test suite for linting issues
    return true;
  }
}

/**
 * Check system prerequisites
 */
async function checkPrerequisites() {
  colorLog('\n🔧 Checking system prerequisites...', 'blue');
  
  const checks = [
    {
      name: 'Node.js version',
      check: () => {
        const version = process.version;
        const major = parseInt(version.slice(1).split('.')[0]);
        return major >= 18;
      }
    },
    {
      name: 'Environment variables',
      check: () => {
        const required = ['DATABASE_URL'];
        return required.every(env => process.env[env]);
      }
    },
    {
      name: 'Test database connection',
      check: async () => {
        try {
          // Simple database connection test
          const { db } = require('../src/db');
          await db.execute('SELECT 1');
          return true;
        } catch (error) {
          colorLog(`Database connection failed: ${error.message}`, 'red');
          return false;
        }
      }
    }
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    try {
      const result = await check.check();
      if (result) {
        colorLog(`✅ ${check.name}`, 'green');
      } else {
        colorLog(`❌ ${check.name}`, 'red');
        allPassed = false;
      }
    } catch (error) {
      colorLog(`❌ ${check.name}: ${error.message}`, 'red');
      allPassed = false;
    }
  }
  
  return allPassed;
}

/**
 * Generate test report
 */
function generateTestReport() {
  const duration = overallResults.endTime - overallResults.startTime;
  const successRate = overallResults.totalTests > 0 
    ? ((overallResults.passedTests / overallResults.totalTests) * 100).toFixed(1)
    : 0;
  
  const report = {
    summary: {
      startTime: new Date(overallResults.startTime).toISOString(),
      endTime: new Date(overallResults.endTime).toISOString(),
      duration: `${(duration / 1000).toFixed(2)}s`,
      totalTests: overallResults.totalTests,
      passedTests: overallResults.passedTests,
      failedTests: overallResults.failedTests,
      skippedTests: overallResults.skippedTests,
      successRate: `${successRate}%`
    },
    testFiles: overallResults.testFiles,
    errors: overallResults.errors,
    performance: overallResults.performance,
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      environment: testConfig.environment
    }
  };
  
  // Save JSON report
  const reportDir = './test-results';
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(reportDir, `test-report-${timestamp}.json`);
  
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  colorLog(`\n📄 Test report saved: ${reportFile}`, 'blue');
  
  return report;
}

/**
 * Print final test summary
 */
function printTestSummary() {
  const duration = overallResults.endTime - overallResults.startTime;
  const successRate = overallResults.totalTests > 0 
    ? ((overallResults.passedTests / overallResults.totalTests) * 100).toFixed(1)
    : 0;
  
  colorLog('\n' + '='.repeat(80), 'cyan');
  colorLog('📊 FINAL TEST RESULTS', 'cyan');
  colorLog('='.repeat(80), 'cyan');
  colorLog(`⏱️  Total Duration: ${(duration / 1000).toFixed(2)} seconds`, 'white');
  colorLog(`📁 Test Files Run: ${overallResults.testFiles.length}`, 'white');
  colorLog(`✅ Passed: ${overallResults.passedTests}`, 'green');
  colorLog(`❌ Failed: ${overallResults.failedTests}`, 'red');
  colorLog(`⚠️  Skipped: ${overallResults.skippedTests}`, 'yellow');
  colorLog(`📈 Success Rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
  
  if (overallResults.errors.length > 0) {
    colorLog('\n❌ Failed Tests:', 'red');
    overallResults.errors.forEach((error, index) => {
      colorLog(`   ${index + 1}. ${error.file}: ${error.message}`, 'red');
    });
  }
  
  colorLog('='.repeat(80), 'cyan');
  
  if (overallResults.failedTests === 0) {
    colorLog('🎉 All tests passed successfully!', 'green');
    return 0;
  } else {
    colorLog('💥 Some tests failed. Please review the errors above.', 'red');
    return 1;
  }
}

/**
 * Main test execution function
 */
async function runAllTests() {
  colorLog('🚀 Starting Comprehensive Test Suite', 'cyan');
  colorLog(`🌐 Environment: ${testConfig.environment}`, 'blue');
  colorLog(`📍 Base URL: ${testConfig.config.baseUrl}`, 'blue');
  colorLog('='.repeat(80), 'cyan');
  
  // Check prerequisites
  const prerequisitesPassed = await checkPrerequisites();
  if (!prerequisitesPassed) {
    colorLog('❌ Prerequisites check failed. Aborting tests.', 'red');
    return 1;
  }
  
  // Run static analysis
  await runTypeScriptCheck();
  await runLintCheck();
  
  // Run test files
  colorLog('\n🧪 Running Test Files...', 'cyan');
  
  for (const testFile of testFiles) {
    if (!fs.existsSync(testFile)) {
      colorLog(`⚠️  Test file not found: ${testFile}`, 'yellow');
      continue;
    }
    
    const result = await runTestFile(testFile);
    overallResults.testFiles.push(result);
    
    if (result.success) {
      overallResults.passedTests++;
    } else {
      overallResults.failedTests++;
      overallResults.errors.push({
        file: testFile,
        message: result.error || 'Test execution failed'
      });
    }
    
    overallResults.totalTests++;
  }
  
  overallResults.endTime = Date.now();
  
  // Generate reports
  generateTestReport();
  
  // Print summary and exit
  return printTestSummary();
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Usage: node scripts/run-all-tests.js [options]

Options:
  --help, -h          Show this help message
  --env <environment> Set test environment (development, staging, production)
  --file <testfile>   Run specific test file only
  --skip-static       Skip static analysis (TypeScript, ESLint)
  --verbose           Enable verbose output

Examples:
  node scripts/run-all-tests.js
  node scripts/run-all-tests.js --env staging
  node scripts/run-all-tests.js --file tests/comprehensive-system-test.js
  `);
  process.exit(0);
}

// Set environment if specified
if (args.includes('--env')) {
  const envIndex = args.indexOf('--env');
  if (envIndex + 1 < args.length) {
    process.env.NODE_ENV = args[envIndex + 1];
  }
}

// Run specific file if specified
if (args.includes('--file')) {
  const fileIndex = args.indexOf('--file');
  if (fileIndex + 1 < args.length) {
    testFiles.length = 0; // Clear array
    testFiles.push(args[fileIndex + 1]);
  }
}

// Run tests
if (require.main === module) {
  runAllTests()
    .then(exitCode => {
      process.exit(exitCode);
    })
    .catch(error => {
      colorLog(`💥 Test runner failed: ${error.message}`, 'red');
      console.error(error);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  runTestFile,
  generateTestReport
};
