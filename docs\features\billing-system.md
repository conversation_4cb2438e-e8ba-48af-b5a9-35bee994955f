# Billing System Design - Manual Monthly Billing

**Last Updated:** July 8, 2025
**Current Implementation:** Manual Monthly Billing System
**Status:** Implemented and Active

## 💰 Billing Architecture Overview

Our billing system handles manual monthly billing with flexible payment options, due date management, and penalty calculations. The system has transitioned from automatic subscriptions to manual payments to provide schools with maximum payment flexibility.

## 📊 Pricing Structure

### Base Pricing Model
```typescript
interface PricingTier {
  name: string;
  billingCycle: 'monthly' | 'yearly';
  pricePerStudent: number;
  discountPercentage: number;
  features: string[];
}

const pricingTiers: PricingTier[] = [
  {
    name: 'Basic Monthly',
    billingCycle: 'monthly',
    pricePerStudent: 80.00, // ₹80 per student per month
    discountPercentage: 0,
    features: ['All 8+ modules', 'AI insights', 'Real-time tracking']
  },
  {
    name: 'Basic Yearly',
    billingCycle: 'yearly',
    pricePerStudent: 65.00, // ₹65 per student per month (₹780/year)
    discountPercentage: 18.75, // 18.75% discount for yearly
    features: ['All 8+ modules', 'AI insights', 'Real-time tracking', 'Priority support']
  }
];
```

## 🔄 Pro-rated Billing Logic

### Mid-Month Registration Calculation
```typescript
interface ProRatedCalculation {
  baseAmount: number;
  proratedAmount: number;
  remainingDays: number;
  daysInMonth: number;
  nextBillingDate: Date;
  savings: number;
}

const calculateProRatedBilling = (
  registrationDate: Date,
  studentCount: number,
  monthlyRate: number
): ProRatedCalculation => {
  const year = registrationDate.getFullYear();
  const month = registrationDate.getMonth();
  
  // Get last day of current month
  const lastDayOfMonth = new Date(year, month + 1, 0).getDate();
  const registrationDay = registrationDate.getDate();
  
  // Calculate remaining days (including registration day)
  const remainingDays = lastDayOfMonth - registrationDay + 1;
  
  // Calculate daily rate
  const dailyRate = monthlyRate / lastDayOfMonth;
  
  // Calculate pro-rated amount
  const baseAmount = monthlyRate * studentCount;
  const proratedAmount = Math.round(dailyRate * remainingDays * studentCount * 100) / 100;
  
  // Next billing date is 1st of next month
  const nextBillingDate = new Date(year, month + 1, 1);
  
  return {
    baseAmount,
    proratedAmount,
    remainingDays,
    daysInMonth: lastDayOfMonth,
    nextBillingDate,
    savings: baseAmount - proratedAmount
  };
};

// Example: Registration on July 18th
// July has 31 days, so remaining days = 31 - 18 + 1 = 14 days
// Daily rate = ₹80 / 31 = ₹2.58
// Pro-rated amount = ₹2.58 × 14 × studentCount
```

### Student Count Changes Mid-Cycle
```typescript
const calculateMidCycleAdjustment = (
  currentStudentCount: number,
  newStudentCount: number,
  changeDate: Date,
  cycleEndDate: Date,
  monthlyRate: number
) => {
  const remainingDays = Math.ceil(
    (cycleEndDate.getTime() - changeDate.getTime()) / (1000 * 60 * 60 * 24)
  );
  
  const totalDaysInCycle = Math.ceil(
    (cycleEndDate.getTime() - new Date(cycleEndDate.getFullYear(), cycleEndDate.getMonth(), 1).getTime()) / (1000 * 60 * 60 * 24)
  );
  
  const dailyRate = monthlyRate / totalDaysInCycle;
  const studentDifference = newStudentCount - currentStudentCount;
  
  return {
    adjustmentAmount: dailyRate * remainingDays * studentDifference,
    effectiveDate: changeDate,
    remainingDays
  };
};
```

## 🔄 Billing Cycle Management

### Automatic Billing Cycle Creation
```typescript
interface BillingCycle {
  id: string;
  subscriptionId: string;
  cycleStart: Date;
  cycleEnd: Date;
  studentCount: number;
  baseAmount: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  isProrated: boolean;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  dueDate: Date;
}

const createBillingCycle = async (subscription: Subscription): Promise<BillingCycle> => {
  const now = new Date();
  const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  
  const plan = await getSubscriptionPlan(subscription.planId);
  const baseAmount = plan.pricePerStudent * subscription.studentCount;
  const discountAmount = baseAmount * (plan.discountPercentage / 100);
  const taxAmount = (baseAmount - discountAmount) * 0.18; // 18% GST
  const totalAmount = baseAmount - discountAmount + taxAmount;
  
  return {
    id: generateUUID(),
    subscriptionId: subscription.id,
    cycleStart,
    cycleEnd,
    studentCount: subscription.studentCount,
    baseAmount,
    discountAmount,
    taxAmount,
    totalAmount,
    isProrated: false,
    status: 'pending',
    dueDate: new Date(cycleEnd.getTime() + 7 * 24 * 60 * 60 * 1000) // 7 days grace
  };
};
```

## 💳 Razorpay Integration

### Payment Order Creation
```typescript
import Razorpay from 'razorpay';

const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_KEY_SECRET,
});

const createPaymentOrder = async (invoice: Invoice) => {
  const options = {
    amount: Math.round(invoice.totalAmount * 100), // Amount in paise
    currency: 'INR',
    receipt: `invoice_${invoice.invoiceNumber}`,
    notes: {
      invoiceId: invoice.id,
      clientId: invoice.clientId,
      billingCycle: invoice.billingCycleId
    }
  };
  
  const order = await razorpay.orders.create(options);
  
  // Store order details
  await updateInvoice(invoice.id, {
    razorpayOrderId: order.id,
    status: 'payment_pending'
  });
  
  return order;
};
```

### Payment Verification
```typescript
import crypto from 'crypto';

const verifyPaymentSignature = (
  paymentId: string,
  orderId: string,
  signature: string
): boolean => {
  const body = orderId + '|' + paymentId;
  const expectedSignature = crypto
    .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
    .update(body.toString())
    .digest('hex');
  
  return expectedSignature === signature;
};

const processPaymentSuccess = async (
  paymentId: string,
  orderId: string,
  signature: string
) => {
  if (!verifyPaymentSignature(paymentId, orderId, signature)) {
    throw new Error('Invalid payment signature');
  }
  
  // Update payment record
  const payment = await updatePayment({
    razorpayPaymentId: paymentId,
    status: 'success',
    processedAt: new Date()
  });
  
  // Update invoice status
  await updateInvoice(payment.invoiceId, {
    status: 'paid',
    paidDate: new Date()
  });
  
  // Update billing cycle
  await updateBillingCycle(payment.billingCycleId, {
    status: 'paid'
  });
  
  // Send payment confirmation email
  await sendPaymentConfirmation(payment.clientId, payment.invoiceId);
};
```

## 📧 Dunning Management

### Payment Reminder System
```typescript
interface ReminderSchedule {
  type: 'due_soon' | 'overdue' | 'final_notice';
  daysBefore: number;
  daysAfter: number;
  emailTemplate: string;
  smsTemplate?: string;
}

const reminderSchedule: ReminderSchedule[] = [
  {
    type: 'due_soon',
    daysBefore: 3,
    daysAfter: 0,
    emailTemplate: 'payment_due_soon',
    smsTemplate: 'payment_due_soon_sms'
  },
  {
    type: 'overdue',
    daysBefore: 0,
    daysAfter: 1,
    emailTemplate: 'payment_overdue',
    smsTemplate: 'payment_overdue_sms'
  },
  {
    type: 'final_notice',
    daysBefore: 0,
    daysAfter: 7,
    emailTemplate: 'final_notice',
    smsTemplate: 'final_notice_sms'
  }
];

const processPaymentReminders = async () => {
  const pendingInvoices = await getPendingInvoices();
  
  for (const invoice of pendingInvoices) {
    for (const reminder of reminderSchedule) {
      const shouldSend = shouldSendReminder(invoice, reminder);
      
      if (shouldSend) {
        await sendPaymentReminder(invoice, reminder);
        await recordReminderSent(invoice.id, reminder.type);
      }
    }
  }
};

const shouldSendReminder = (invoice: Invoice, reminder: ReminderSchedule): boolean => {
  const now = new Date();
  const dueDate = new Date(invoice.dueDate);
  const daysDifference = Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
  
  if (reminder.type === 'due_soon') {
    return daysDifference === -reminder.daysBefore;
  } else {
    return daysDifference === reminder.daysAfter;
  }
};
```

### Grace Period and Suspension Logic
```typescript
const handleOverduePayments = async () => {
  const overdueInvoices = await getOverdueInvoices();
  
  for (const invoice of overdueInvoices) {
    const daysPastDue = getDaysPastDue(invoice.dueDate);
    
    if (daysPastDue >= 15) {
      // Suspend subscription after 15 days
      await suspendSubscription(invoice.clientId, 'payment_overdue');
      await sendSuspensionNotice(invoice.clientId);
    } else if (daysPastDue >= 30) {
      // Cancel subscription after 30 days
      await cancelSubscription(invoice.clientId, 'payment_default');
      await sendCancellationNotice(invoice.clientId);
    }
  }
};
```

## 📊 Subscription Management

### Plan Upgrades/Downgrades
```typescript
const changeSubscriptionPlan = async (
  subscriptionId: string,
  newPlanId: string,
  effectiveDate: Date
) => {
  const subscription = await getSubscription(subscriptionId);
  const currentPlan = await getSubscriptionPlan(subscription.planId);
  const newPlan = await getSubscriptionPlan(newPlanId);
  
  // Calculate pro-rated adjustment
  const adjustment = calculatePlanChangeAdjustment(
    subscription,
    currentPlan,
    newPlan,
    effectiveDate
  );
  
  // Update subscription
  await updateSubscription(subscriptionId, {
    planId: newPlanId,
    updatedAt: new Date()
  });
  
  // Create adjustment billing cycle if needed
  if (adjustment.amount !== 0) {
    await createAdjustmentBillingCycle(subscription, adjustment);
  }
  
  return { success: true, adjustment };
};
```

### Automatic Renewal Logic
```typescript
const processSubscriptionRenewals = async () => {
  const expiringSubscriptions = await getExpiringSubscriptions();
  
  for (const subscription of expiringSubscriptions) {
    if (subscription.autoRenew && subscription.status === 'active') {
      // Create new billing cycle
      const newCycle = await createBillingCycle(subscription);
      
      // Generate invoice
      const invoice = await generateInvoice(newCycle);
      
      // Send renewal notice
      await sendRenewalNotice(subscription.clientId, invoice);
      
      // Update subscription end date
      await extendSubscription(subscription.id);
    }
  }
};
```

This comprehensive billing system ensures accurate, automated, and compliant subscription management with proper pro-rated calculations and payment processing.
