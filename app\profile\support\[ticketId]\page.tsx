'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { 
  ArrowLeft,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  ArrowUpDown,
  Calendar,
  User,
  FileText,
  Download,
  Send,
  Upload,
  X
} from 'lucide-react'
import Link from 'next/link'
import { useParams } from 'next/navigation'

interface TicketMessage {
  id: string
  message: string
  senderType: 'client' | 'admin'
  senderName: string
  attachments?: Array<{
    fileName: string
    fileUrl: string
    fileSize: number
    fileType: string
  }>
  createdAt: string
}

interface TicketDetails {
  id: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  category: string
  createdAt: string
  updatedAt: string
  assignedAdminName?: string
  canAddMessage: boolean
  canClose: boolean
}

interface TicketData {
  ticket: TicketDetails
  messages: TicketMessage[]
  messageCount: number
  statusHistory: Array<{
    status: string
    timestamp: string
    note: string
  }>
}

export default function TicketDetailPage() {
  const params = useParams()
  const ticketId = params.ticketId as string
  
  const [data, setData] = useState<TicketData | null>(null)
  const [loading, setLoading] = useState(true)
  const [replyMessage, setReplyMessage] = useState('')
  const [replyAttachments, setReplyAttachments] = useState<File[]>([])
  const [sendingReply, setSendingReply] = useState(false)
  const [error, setError] = useState<string>('')

  useEffect(() => {
    if (ticketId) {
      fetchTicketDetails()
    }
  }, [ticketId])

  const fetchTicketDetails = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('schoolToken')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/school/support/tickets/${ticketId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch ticket details')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch ticket details')
      }

      setData(result.data)
    } catch (error) {
      console.error('Error fetching ticket details:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch ticket details')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="w-3 h-3 mr-1" />Open</Badge>
      case 'in_progress':
        return <Badge className="bg-yellow-100 text-yellow-800"><ArrowUpDown className="w-3 h-3 mr-1" />In Progress</Badge>
      case 'resolved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Resolved</Badge>
      case 'closed':
        return <Badge className="bg-gray-100 text-gray-800"><XCircle className="w-3 h-3 mr-1" />Closed</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Badge className="bg-red-100 text-red-800">Urgent</Badge>
      case 'high':
        return <Badge className="bg-orange-100 text-orange-800">High</Badge>
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>
      case 'low':
        return <Badge className="bg-green-100 text-green-800">Low</Badge>
      default:
        return <Badge variant="secondary">{priority}</Badge>
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    const maxSize = 10 * 1024 * 1024 // 10MB
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ]

    Array.from(files).forEach(file => {
      if (file.size > maxSize) {
        setError(`File ${file.name} is too large. Maximum size is 10MB.`)
        return
      }

      if (!allowedTypes.includes(file.type)) {
        setError(`File type ${file.type} is not allowed.`)
        return
      }

      setReplyAttachments(prev => [...prev, file])
    })

    // Clear the input
    event.target.value = ''
  }

  const removeAttachment = (index: number) => {
    setReplyAttachments(prev => prev.filter((_, i) => i !== index))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleSendReply = async () => {
    if (!replyMessage.trim()) {
      setError('Please enter a message')
      return
    }

    setSendingReply(true)
    setError('')

    try {
      const token = localStorage.getItem('schoolToken')
      if (!token) {
        throw new Error('No authentication token found')
      }

      // Upload attachments first (simplified for demo)
      const uploadedAttachments = replyAttachments.map(file => ({
        fileName: file.name,
        fileUrl: `https://example.com/uploads/${file.name}`, // Placeholder
        fileSize: file.size,
        fileType: file.type
      }))

      const response = await fetch(`/api/school/support/tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: replyMessage,
          attachments: uploadedAttachments
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send reply')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to send reply')
      }

      // Clear form and refresh ticket details
      setReplyMessage('')
      setReplyAttachments([])
      await fetchTicketDetails()
    } catch (error) {
      console.error('Error sending reply:', error)
      setError(error instanceof Error ? error.message : 'Failed to send reply')
    } finally {
      setSendingReply(false)
    }
  }

  const handleCloseTicket = async () => {
    if (!confirm('Are you sure you want to close this ticket?')) {
      return
    }

    try {
      const token = localStorage.getItem('schoolToken')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/school/support/tickets/${ticketId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: 'closed',
          reason: 'Closed by school user'
        })
      })

      if (!response.ok) {
        throw new Error('Failed to close ticket')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to close ticket')
      }

      // Refresh ticket details
      await fetchTicketDetails()
    } catch (error) {
      console.error('Error closing ticket:', error)
      setError(error instanceof Error ? error.message : 'Failed to close ticket')
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading ticket details...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error && !data) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading ticket</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link href="/profile/support">
            <Button>Back to Support Tickets</Button>
          </Link>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Ticket not found</h3>
          <p className="text-gray-600 mb-4">The requested ticket could not be found.</p>
          <Link href="/profile/support">
            <Button>Back to Support Tickets</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Link href="/profile/support">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Tickets
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">{data.ticket.title}</h1>
          <p className="text-gray-600">Ticket #{data.ticket.id.slice(-8)}</p>
        </div>
        {data.ticket.canClose && data.ticket.status === 'resolved' && (
          <Button variant="outline" onClick={handleCloseTicket}>
            <XCircle className="w-4 h-4 mr-2" />
            Close Ticket
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Ticket Details */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Ticket Details
                </CardTitle>
                <div className="flex items-center gap-2">
                  {getStatusBadge(data.ticket.status)}
                  {getPriorityBadge(data.ticket.priority)}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-700 whitespace-pre-wrap">{data.ticket.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">Category:</span>
                    <span className="ml-2 text-gray-900">{data.ticket.category.replace('_', ' ')}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Created:</span>
                    <span className="ml-2 text-gray-900">{formatDate(data.ticket.createdAt)}</span>
                  </div>
                  {data.ticket.assignedAdminName && (
                    <div>
                      <span className="font-medium text-gray-600">Assigned to:</span>
                      <span className="ml-2 text-gray-900">{data.ticket.assignedAdminName}</span>
                    </div>
                  )}
                  <div>
                    <span className="font-medium text-gray-600">Last updated:</span>
                    <span className="ml-2 text-gray-900">{formatDate(data.ticket.updatedAt)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Messages */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Conversation ({data.messageCount} messages)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.messages.map((message) => (
                  <div key={message.id} className={`flex gap-3 ${message.senderType === 'client' ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      message.senderType === 'client' ? 'bg-blue-100' : 'bg-gray-100'
                    }`}>
                      <User className="w-4 h-4" />
                    </div>
                    <div className={`flex-1 max-w-lg ${message.senderType === 'client' ? 'text-right' : ''}`}>
                      <div className={`p-3 rounded-lg ${
                        message.senderType === 'client' 
                          ? 'bg-blue-600 text-white' 
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        <p className="whitespace-pre-wrap">{message.message}</p>
                        {message.attachments && message.attachments.length > 0 && (
                          <div className="mt-2 space-y-1">
                            {message.attachments.map((attachment, index) => (
                              <div key={index} className="flex items-center gap-2 text-sm">
                                <FileText className="w-4 h-4" />
                                <a 
                                  href={attachment.fileUrl} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="underline hover:no-underline"
                                >
                                  {attachment.fileName}
                                </a>
                                <span className="text-xs opacity-75">
                                  ({formatFileSize(attachment.fileSize)})
                                </span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                      <div className={`text-xs text-gray-500 mt-1 ${message.senderType === 'client' ? 'text-right' : ''}`}>
                        {message.senderName} • {formatDate(message.createdAt)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Reply Form */}
          {data.ticket.canAddMessage && (
            <Card>
              <CardHeader>
                <CardTitle>Add Reply</CardTitle>
                <CardDescription>
                  Send a message to continue the conversation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Textarea
                    value={replyMessage}
                    onChange={(e) => setReplyMessage(e.target.value)}
                    placeholder="Type your message here..."
                    rows={4}
                  />

                  {/* File Upload */}
                  <div>
                    <input
                      type="file"
                      id="reply-attachments"
                      multiple
                      onChange={handleFileUpload}
                      className="hidden"
                      accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt"
                    />
                    <label htmlFor="reply-attachments">
                      <Button type="button" variant="outline" size="sm" className="cursor-pointer">
                        <Upload className="w-4 h-4 mr-2" />
                        Attach Files
                      </Button>
                    </label>
                  </div>

                  {/* Attached Files */}
                  {replyAttachments.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="text-sm font-medium text-gray-700">Attached Files:</h5>
                      {replyAttachments.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-gray-500" />
                            <span className="text-sm">{file.name}</span>
                            <span className="text-xs text-gray-500">({formatFileSize(file.size)})</span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeAttachment(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  {error && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {error}
                      </p>
                    </div>
                  )}

                  <div className="flex justify-end">
                    <Button 
                      onClick={handleSendReply} 
                      disabled={sendingReply || !replyMessage.trim()}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {sendingReply ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2" />
                          Send Reply
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Status History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.statusHistory.map((status, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{status.note}</p>
                      <p className="text-xs text-gray-500">{formatDate(status.timestamp)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/profile/support/create">
                  <Button variant="outline" className="w-full justify-start">
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Create New Ticket
                  </Button>
                </Link>
                <Link href="/profile/support">
                  <Button variant="outline" className="w-full justify-start">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    All Tickets
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
