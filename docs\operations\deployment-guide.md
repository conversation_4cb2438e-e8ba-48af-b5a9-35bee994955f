# 🚀 Schopio Production Deployment Guide

## 📋 **DEPLOYMENT OVERVIEW**

### **Deployment Architecture**
- **Frontend**: Next.js application deployed on Vercel
- **Backend**: Hono.js API deployed on Vercel Edge Functions
- **Database**: Neon PostgreSQL (managed cloud database)
- **File Storage**: Vercel Blob Storage
- **CDN**: Vercel Edge Network

### **Environment Structure**
```
Production (schopio.com)
├── Frontend: Next.js on Vercel
├── Backend: Hono.js API on Vercel
├── Database: Neon PostgreSQL
└── Monitoring: Vercel Analytics + Custom monitoring
```

---

## 🔧 **PRE-DEPLOYMENT CHECKLIST**

### **1. Code Quality Verification**
```bash
# TypeScript compilation check
bunx tsc --noEmit

# Lint check
npm run lint

# Build verification
npm run build

# Test execution (if tests exist)
npm run test
```

### **2. Environment Variables Verification**
```bash
# Required production environment variables
DATABASE_URL=postgresql://[neon-connection-string]
JWT_SECRET=[secure-random-string-32-chars]
RAZORPAY_KEY_ID=[live-razorpay-key]
RAZORPAY_KEY_SECRET=[live-razorpay-secret]
RESEND_API_KEY=[resend-api-key]
FROM_EMAIL=<EMAIL>
FROM_NAME="Schopio"
NEXT_PUBLIC_API_URL=https://schopio.com/api
NODE_ENV=production
```

### **3. Database Migration Verification**
```bash
# Verify schema is up to date
bunx drizzle-kit push

# Verify database connectivity
bunx drizzle-kit studio
```

---

## 🌐 **VERCEL DEPLOYMENT PROCESS**

### **1. Initial Vercel Setup**

#### **Install Vercel CLI**
```bash
npm install -g vercel
vercel login
```

#### **Project Configuration**
```bash
# Initialize Vercel project
vercel

# Configure project settings
vercel --prod
```

#### **Vercel Configuration (vercel.json)**
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/[[...route]]/route.ts": {
      "runtime": "edge"
    }
  },
  "rewrites": [
    {
      "source": "/api/(.*)",
      "destination": "/app/api/[[...route]]/route.ts"
    }
  ]
}
```

### **2. Environment Variables Setup**

#### **Production Environment Variables**
```bash
# Set production environment variables
vercel env add DATABASE_URL production
vercel env add JWT_SECRET production
vercel env add RAZORPAY_KEY_ID production
vercel env add RAZORPAY_KEY_SECRET production
vercel env add RESEND_API_KEY production
vercel env add FROM_EMAIL production
vercel env add FROM_NAME production
vercel env add NEXT_PUBLIC_API_URL production
```

#### **Environment Variable Security**
- **Encryption**: All environment variables encrypted at rest
- **Access Control**: Limited access to production variables
- **Rotation**: Regular rotation of secrets (quarterly)
- **Audit**: Environment variable access logging

### **3. Domain Configuration**

#### **Custom Domain Setup**
```bash
# Add custom domain
vercel domains add schopio.com
vercel domains add www.schopio.com

# Configure DNS records
# A record: @ -> 76.76.19.61
# CNAME record: www -> cname.vercel-dns.com
```

#### **SSL Certificate**
- **Automatic**: Vercel automatically provisions SSL certificates
- **Renewal**: Automatic certificate renewal
- **Security**: TLS 1.3 support
- **HSTS**: HTTP Strict Transport Security enabled

---

## 🗄️ **DATABASE DEPLOYMENT**

### **1. Neon PostgreSQL Setup**

#### **Production Database Creation**
```bash
# Create production database on Neon
# 1. Login to Neon Console (neon.tech)
# 2. Create new project: "schopio-production"
# 3. Select region: Asia Pacific (Mumbai)
# 4. Copy connection string
```

#### **Database Configuration**
```
Database Name: schopio_production
Region: Asia Pacific (Mumbai)
Compute Size: 0.25 vCPU, 1 GB RAM (scalable)
Storage: Auto-scaling storage
Backup: Daily automated backups
```

### **2. Schema Deployment**

#### **Schema Migration**
```bash
# Set production database URL
export DATABASE_URL="postgresql://[production-connection-string]"

# Deploy schema to production
bunx drizzle-kit push

# Verify schema deployment
bunx drizzle-kit studio
```

#### **Initial Data Setup**
```bash
# Create admin user (run once)
# Execute admin user creation script
# Set up initial system configuration
```

### **3. Database Security**

#### **Security Configuration**
- **Connection**: SSL-only connections required
- **Authentication**: Strong password authentication
- **Network**: IP allowlist for admin access
- **Monitoring**: Connection and query monitoring

---

## 🔐 **SECURITY CONFIGURATION**

### **1. Application Security**

#### **Security Headers**
```typescript
// next.config.js security headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];
```

#### **CORS Configuration**
```typescript
// API CORS settings
const corsOptions = {
  origin: ['https://schopio.com', 'https://www.schopio.com'],
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};
```

### **2. API Security**

#### **Rate Limiting**
```typescript
// Rate limiting configuration
const rateLimits = {
  auth: '5 requests per minute',
  api: '100 requests per minute',
  uploads: '10 requests per minute'
};
```

#### **Authentication Security**
- **JWT Expiry**: 24 hours for regular tokens
- **Refresh Tokens**: 30 days expiry
- **Password Policy**: Minimum 8 characters, complexity requirements
- **Session Management**: Secure session handling

---

## 📊 **MONITORING & LOGGING**

### **1. Application Monitoring**

#### **Vercel Analytics**
```bash
# Enable Vercel Analytics
vercel analytics enable

# Configure custom events
vercel analytics add-event "user_login"
vercel analytics add-event "subscription_created"
vercel analytics add-event "payment_completed"
```

#### **Error Monitoring**
```typescript
// Error tracking setup
const errorMonitoring = {
  service: 'Vercel Error Tracking',
  alerts: 'Email + Slack notifications',
  retention: '30 days error logs',
  sampling: '100% error capture'
};
```

### **2. Performance Monitoring**

#### **Core Web Vitals**
- **LCP (Largest Contentful Paint)**: < 2.5 seconds
- **FID (First Input Delay)**: < 100 milliseconds
- **CLS (Cumulative Layout Shift)**: < 0.1
- **TTFB (Time to First Byte)**: < 600 milliseconds

#### **API Performance**
```typescript
// API monitoring metrics
const apiMetrics = {
  responseTime: 'Average response time per endpoint',
  errorRate: 'Error rate percentage',
  throughput: 'Requests per minute',
  availability: 'Uptime percentage'
};
```

### **3. Database Monitoring**

#### **Neon Monitoring**
- **Connection Pool**: Monitor connection usage
- **Query Performance**: Slow query identification
- **Storage Usage**: Database size monitoring
- **Backup Status**: Backup success/failure tracking

---

## 🔄 **DEPLOYMENT WORKFLOW**

### **1. Continuous Deployment**

#### **Git Workflow**
```bash
# Production deployment workflow
git checkout main
git pull origin main
git tag v1.0.0
git push origin v1.0.0

# Automatic deployment triggers
# - Push to main branch
# - Tagged releases
# - Manual deployment via Vercel dashboard
```

#### **Deployment Pipeline**
```
Code Push → Build → Test → Deploy → Monitor
     ↓         ↓      ↓       ↓        ↓
   GitHub → Vercel → Auto → Live → Analytics
```

### **2. Rollback Procedures**

#### **Automatic Rollback**
```bash
# Vercel automatic rollback triggers
- Build failure
- Health check failure
- Error rate spike (>5%)
```

#### **Manual Rollback**
```bash
# Manual rollback process
vercel rollback [deployment-url]

# Or via Vercel dashboard
# 1. Go to deployments
# 2. Select previous stable deployment
# 3. Click "Promote to Production"
```

### **3. Health Checks**

#### **Deployment Health Checks**
```typescript
// Health check endpoints
GET /api/health - System health status
GET /api/health/database - Database connectivity
GET /api/health/external - External service status
```

#### **Post-Deployment Verification**
```bash
# Automated verification checklist
1. Health check endpoints respond
2. Authentication flow works
3. Database connectivity verified
4. Payment gateway connectivity
5. Email service functionality
```

---

## 🚨 **INCIDENT RESPONSE**

### **1. Incident Classification**

#### **Severity Levels**
```
P0 (Critical): Complete system outage
P1 (High): Major feature broken
P2 (Medium): Minor feature issue
P3 (Low): Cosmetic or enhancement
```

#### **Response Times**
```
P0: 15 minutes response, 2 hours resolution
P1: 1 hour response, 8 hours resolution
P2: 4 hours response, 24 hours resolution
P3: 24 hours response, 72 hours resolution
```

### **2. Incident Response Process**

#### **Immediate Response**
```
1. Incident Detection (monitoring alerts)
2. Initial Assessment (severity classification)
3. Team Notification (on-call engineer)
4. Customer Communication (status page update)
5. Investigation & Resolution
6. Post-incident Review
```

#### **Communication Channels**
- **Status Page**: status.schopio.com
- **Email Notifications**: Critical incidents
- **Slack Alerts**: Internal team notifications
- **Customer Support**: Direct customer communication
