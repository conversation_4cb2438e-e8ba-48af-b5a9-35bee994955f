'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Loader2, Zap } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'dots' | 'pulse' | 'bounce'
  text?: string
  className?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  text,
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  }

  if (variant === 'dots') {
    return (
      <div className={`flex items-center justify-center gap-2 ${className}`}>
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className={`bg-blue-600 rounded-full ${size === 'sm' ? 'w-2 h-2' : size === 'lg' ? 'w-4 h-4' : 'w-3 h-3'}`}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              delay: index * 0.2
            }}
          />
        ))}
        {text && (
          <span className={`ml-2 text-slate-600 ${textSizeClasses[size]}`}>
            {text}
          </span>
        )}
      </div>
    )
  }

  if (variant === 'pulse') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <motion.div
          className={`bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full ${sizeClasses[size]}`}
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.8, 1, 0.8]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        {text && (
          <span className={`ml-3 text-slate-600 ${textSizeClasses[size]}`}>
            {text}
          </span>
        )}
      </div>
    )
  }

  if (variant === 'bounce') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <motion.div
          className={`bg-gradient-to-r from-blue-600 to-emerald-600 rounded-xl flex items-center justify-center ${sizeClasses[size]}`}
          animate={{
            y: [0, -10, 0],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Zap className={`text-white ${size === 'sm' ? 'w-2 h-2' : size === 'lg' ? 'w-5 h-5' : 'w-3 h-3'}`} />
        </motion.div>
        {text && (
          <span className={`ml-3 text-slate-600 ${textSizeClasses[size]}`}>
            {text}
          </span>
        )}
      </div>
    )
  }

  // Default spinner
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <Loader2 className={`animate-spin text-blue-600 ${sizeClasses[size]}`} />
      {text && (
        <span className={`ml-3 text-slate-600 ${textSizeClasses[size]}`}>
          {text}
        </span>
      )}
    </div>
  )
}

export default LoadingSpinner
