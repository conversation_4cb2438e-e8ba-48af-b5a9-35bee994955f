/**
 * Admin Referral Verification Workflow Test
 * Tests the complete referral verification process from admin perspective
 */

const BASE_URL = 'http://localhost:3000'

// Test data
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Admin@123456'
}

const TEST_REFERRAL_CODE = 'JCOHLSTJ' // <PERSON>'s referral code

async function testAdminReferralVerificationWorkflow() {
  console.log('🧪 Starting Admin Referral Verification Workflow Test...\n')

  try {
    // Step 1: Admin Login
    console.log('1️⃣ Testing Admin Login...')
    const adminLoginResponse = await fetch(`${BASE_URL}/api/admin/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    })

    if (!adminLoginResponse.ok) {
      throw new Error(`Admin login failed: ${adminLoginResponse.status}`)
    }

    const adminLoginData = await adminLoginResponse.json()
    const adminToken = adminLoginData.token
    console.log('✅ Admin login successful')

    // Step 2: Get Pending Referrals
    console.log('\n2️⃣ Testing Get Pending Referrals...')
    const pendingReferralsResponse = await fetch(`${BASE_URL}/api/admin/referrals/pending`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })

    if (!pendingReferralsResponse.ok) {
      throw new Error(`Get pending referrals failed: ${pendingReferralsResponse.status}`)
    }

    const pendingReferralsData = await pendingReferralsResponse.json()
    console.log('✅ Pending referrals retrieved successfully')
    console.log(`📊 Found ${pendingReferralsData.referrals?.length || 0} pending referrals`)

    if (pendingReferralsData.referrals && pendingReferralsData.referrals.length > 0) {
      const testReferral = pendingReferralsData.referrals[0]
      console.log(`🎯 Testing with referral ID: ${testReferral.id}`)
      console.log(`📝 School: ${testReferral.schoolName}`)
      console.log(`🤝 Partner: ${testReferral.partnerName}`)
      console.log(`🔗 Code: ${testReferral.referralCode}`)

      // Step 3: Test Referral Verification
      console.log('\n3️⃣ Testing Referral Verification...')
      const verifyResponse = await fetch(`${BASE_URL}/api/admin/referrals/${testReferral.id}/verify`, {
        method: 'PUT',
        headers: { 
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!verifyResponse.ok) {
        console.log('⚠️ Verification failed (expected if already verified)')
        const verifyError = await verifyResponse.json()
        console.log(`📄 Error: ${verifyError.error}`)
      } else {
        const verifyData = await verifyResponse.json()
        console.log('✅ Referral verification successful')
        console.log(`📝 Message: ${verifyData.message}`)
      }

      // Step 4: Test Referral Rejection (with a different referral if available)
      if (pendingReferralsData.referrals.length > 1) {
        const secondReferral = pendingReferralsData.referrals[1]
        console.log('\n4️⃣ Testing Referral Rejection...')
        console.log(`🎯 Testing rejection with referral ID: ${secondReferral.id}`)

        const rejectResponse = await fetch(`${BASE_URL}/api/admin/referrals/${secondReferral.id}/reject`, {
          method: 'PUT',
          headers: { 
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            reason: 'Test rejection - Invalid partner verification documents'
          })
        })

        if (!rejectResponse.ok) {
          console.log('⚠️ Rejection failed (expected if already processed)')
          const rejectError = await rejectResponse.json()
          console.log(`📄 Error: ${rejectError.error}`)
        } else {
          const rejectData = await rejectResponse.json()
          console.log('✅ Referral rejection successful')
          console.log(`📝 Message: ${rejectData.message}`)
        }
      } else {
        console.log('\n4️⃣ Skipping rejection test - only one referral available')
      }

      // Step 5: Verify Updated Pending List
      console.log('\n5️⃣ Testing Updated Pending Referrals List...')
      const updatedPendingResponse = await fetch(`${BASE_URL}/api/admin/referrals/pending`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (!updatedPendingResponse.ok) {
        throw new Error(`Updated pending referrals failed: ${updatedPendingResponse.status}`)
      }

      const updatedPendingData = await updatedPendingResponse.json()
      console.log('✅ Updated pending referrals retrieved successfully')
      console.log(`📊 Remaining pending referrals: ${updatedPendingData.referrals?.length || 0}`)

    } else {
      console.log('ℹ️ No pending referrals found for testing')
      console.log('💡 Create a test referral through school portal first')
    }

    // Step 6: Test School Referral Status (if we have a test school)
    console.log('\n6️⃣ Testing School Referral Status Display...')
    console.log('ℹ️ This requires manual verification through school portal')
    console.log('📝 Steps to verify:')
    console.log('   1. Login to school portal')
    console.log('   2. Go to Profile > Settings')
    console.log('   3. Check referral status display')
    console.log('   4. Verify status shows: Verified, Rejected, or Pending')

    console.log('\n🎉 Admin Referral Verification Workflow Test Completed!')
    console.log('📊 Test Results Summary:')
    console.log('   ✅ Admin authentication: PASSED')
    console.log('   ✅ Pending referrals API: PASSED')
    console.log('   ✅ Referral verification API: TESTED')
    console.log('   ✅ Referral rejection API: TESTED')
    console.log('   ✅ Updated pending list: PASSED')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('🔍 Error details:', error)
  }
}

// Run the test
testAdminReferralVerificationWorkflow()
