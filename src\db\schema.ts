// Drizzle ORM Schema Definition
import { pgTable, uuid, varchar, text, integer, timestamp, boolean, decimal, date, jsonb } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// ===== LEADS MANAGEMENT =====

export const leads = pgTable('leads', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  schoolName: varchar('school_name', { length: 255 }).notNull(),
  contactPerson: varchar('contact_person', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }),
  estimatedStudents: integer('estimated_students'),
  source: varchar('source', { length: 50 }), // 'landing_page', 'referral', 'demo_request', 'ai_chat', 'contact_form', 'other'
  status: varchar('status', { length: 20 }).default('new'), // 'new', 'contacted', 'demo_scheduled', 'proposal_sent', 'converted', 'lost'
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const demoBookings = pgTable('demo_bookings', {
  id: uuid('id').primaryKey().defaultRandom(),
  leadId: uuid('lead_id').references(() => leads.id, { onDelete: 'cascade' }),
  scheduledDate: timestamp('scheduled_date').notNull(),
  demoType: varchar('demo_type', { length: 20 }), // 'online', 'onsite'
  status: varchar('status', { length: 20 }).default('scheduled'), // 'scheduled', 'completed', 'cancelled', 'rescheduled'
  meetingLink: varchar('meeting_link', { length: 500 }),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== CLIENT MANAGEMENT =====

export const clients = pgTable('clients', {
  id: uuid('id').primaryKey().defaultRandom(),
  leadId: uuid('lead_id').references(() => leads.id, { onDelete: 'set null' }),
  schoolName: varchar('school_name', { length: 255 }).notNull(),
  schoolCode: varchar('school_code', { length: 50 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  phone: varchar('phone', { length: 20 }),
  address: text('address'),
  contactPerson: varchar('contact_person', { length: 255 }),
  actualStudentCount: integer('actual_student_count').notNull(),
  estimatedStudentCount: integer('estimated_student_count'),
  averageMonthlyFee: decimal('average_monthly_fee', { precision: 10, scale: 2 }), // Average fee per student per month (from software request)
  classFee: decimal('class_fee', { precision: 10, scale: 2 }), // Legacy field - keeping for backward compatibility
  onboardingStatus: varchar('onboarding_status', { length: 20 }).default('pending'), // 'pending', 'in_progress', 'completed'
  status: varchar('status', { length: 20 }).default('active'), // 'active', 'suspended', 'cancelled'
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const clientUsers = pgTable('client_users', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  role: varchar('role', { length: 20 }).default('admin'), // 'admin', 'billing', 'viewer'
  isActive: boolean('is_active').default(true),
  emailVerified: boolean('email_verified').default(false),
  otpCode: varchar('otp_code', { length: 6 }),
  otpExpiresAt: timestamp('otp_expires_at'),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// ===== SUBSCRIPTION MANAGEMENT =====

export const subscriptionPlans = pgTable('subscription_plans', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  billingCycle: varchar('billing_cycle', { length: 20 }).notNull(), // 'monthly', 'yearly'
  pricePerStudent: decimal('price_per_student', { precision: 10, scale: 2 }).notNull(),
  discountPercentage: decimal('discount_percentage', { precision: 5, scale: 2 }).default('0'),
  features: jsonb('features'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
});

export const subscriptions = pgTable('subscriptions', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),
  planId: uuid('plan_id').references(() => subscriptionPlans.id, { onDelete: 'set null' }),
  planName: varchar('plan_name', { length: 100 }).default('Basic Plan'), // Plan name for display
  studentCount: integer('student_count').notNull(),
  pricePerStudent: decimal('price_per_student', { precision: 10, scale: 2 }).notNull(), // Price per student per month
  monthlyAmount: decimal('monthly_amount', { precision: 10, scale: 2 }).notNull(), // Calculated total monthly amount (pricePerStudent * studentCount)
  yearlyDiscountPercentage: decimal('yearly_discount_percentage', { precision: 5, scale: 2 }).default('16.67'), // Default 2-month discount (16.67%)
  startDate: date('start_date').notNull(),
  endDate: date('end_date'),
  nextBillingDate: date('next_billing_date').notNull(), // Next billing cycle date
  status: varchar('status', { length: 20 }).default('active'), // 'active', 'cancelled', 'suspended', 'pending'
  autoRenew: boolean('auto_renew').default(true),
  razorpaySubscriptionId: varchar('razorpay_subscription_id', { length: 100 }), // Razorpay subscription ID
  razorpayPlanId: varchar('razorpay_plan_id', { length: 100 }), // Razorpay plan ID
  razorpayCustomerId: varchar('razorpay_customer_id', { length: 100 }), // Razorpay customer ID for subscription billing
  activatedAt: timestamp('activated_at'), // When subscription was activated for automatic billing
  billingCycle: varchar('billing_cycle', { length: 20 }).default('monthly'), // 'monthly', 'yearly'
  dueDate: integer('due_date').default(15), // Due date of the month (1-31)
  gracePeriodDays: integer('grace_period_days').default(3), // Grace period in days
  setupFee: decimal('setup_fee', { precision: 10, scale: 2 }).default('0'), // One-time setup fee
  discountPercentage: decimal('discount_percentage', { precision: 5, scale: 2 }).default('0'), // Additional discount percentage
  notes: text('notes'), // Admin notes about the subscription
  createdBy: uuid('created_by').references(() => adminUsers.id, { onDelete: 'set null' }), // Admin who created the subscription

  // Operational Expenses Breakdown
  operationalExpenses: jsonb('operational_expenses'), // JSON object containing expense breakdown
  databaseCosts: decimal('database_costs', { precision: 10, scale: 2 }).default('0'),
  websiteMaintenance: decimal('website_maintenance', { precision: 10, scale: 2 }).default('0'),
  supportCosts: decimal('support_costs', { precision: 10, scale: 2 }).default('0'),
  infrastructureCosts: decimal('infrastructure_costs', { precision: 10, scale: 2 }).default('0'),
  totalOperationalExpenses: decimal('total_operational_expenses', { precision: 10, scale: 2 }).default('0'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Subscription Change Log for audit trail
export const subscriptionChangeLog = pgTable('subscription_change_log', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),
  adminId: uuid('admin_id').references(() => adminUsers.id, { onDelete: 'set null' }),
  changeType: varchar('change_type', { length: 50 }).notNull(), // 'price_update', 'student_count_change', 'expense_update', etc.
  oldValues: jsonb('old_values'), // Previous values
  newValues: jsonb('new_values'), // New values
  effectiveDate: date('effective_date').notNull(),
  billingAdjustment: jsonb('billing_adjustment'), // Billing cycle adjustment details
  commissionAdjustment: jsonb('commission_adjustment'), // Commission recalculation details
  reason: text('reason'), // Admin-provided reason for change
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== ENHANCED BILLING SYSTEM (Industry Best Practices) =====

// Billing Plans - Reusable templates for subscription pricing
export const billingPlans = pgTable('billing_plans', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  billingCycle: varchar('billing_cycle', { length: 20 }).notNull(), // 'monthly', 'yearly'
  pricePerStudent: decimal('price_per_student', { precision: 10, scale: 2 }).notNull(),
  setupFee: decimal('setup_fee', { precision: 10, scale: 2 }).default('0'),
  trialPeriodDays: integer('trial_period_days').default(0),
  features: jsonb('features'), // JSON array of included features
  isActive: boolean('is_active').default(true),
  razorpayPlanId: varchar('razorpay_plan_id', { length: 100 }), // Razorpay plan ID
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Enhanced Subscriptions with proper state management
export const billingSubscriptions = pgTable('billing_subscriptions', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),
  planId: uuid('plan_id').references(() => billingPlans.id, { onDelete: 'set null' }),

  // Subscription Details
  studentCount: integer('student_count').notNull(),
  pricePerStudent: decimal('price_per_student', { precision: 10, scale: 2 }).notNull(),
  monthlyAmount: decimal('monthly_amount', { precision: 10, scale: 2 }).notNull(),

  // Subscription Lifecycle
  status: varchar('status', { length: 20 }).default('incomplete'), // 'trialing', 'active', 'incomplete', 'incomplete_expired', 'past_due', 'canceled', 'unpaid', 'paused'
  currentPeriodStart: date('current_period_start').notNull(),
  currentPeriodEnd: date('current_period_end').notNull(),
  nextBillingDate: date('next_billing_date').notNull(),
  trialStart: date('trial_start'),
  trialEnd: date('trial_end'),

  // Payment Configuration
  billingCycle: varchar('billing_cycle', { length: 20 }).default('monthly'),
  gracePeriodDays: integer('grace_period_days').default(3),
  penaltyRate: decimal('penalty_rate', { precision: 5, scale: 2 }).default('2.00'), // 2% daily penalty
  autoRenew: boolean('auto_renew').default(true),

  // Manual Billing Fields
  dueDate: date('due_date'),
  currentPenaltyAmount: decimal('current_penalty_amount', { precision: 10, scale: 2 }).default('0.00'),
  lastPaymentDate: date('last_payment_date'),
  paymentStatus: varchar('payment_status', { length: 20 }).default('pending'), // 'pending', 'paid', 'overdue', 'grace_period'
  autoPenaltyEnabled: boolean('auto_penalty_enabled').default(true),

  // Discount System Fields
  hasActiveDiscount: boolean('has_active_discount').default(false),
  currentDiscountPercentage: decimal('current_discount_percentage', { precision: 5, scale: 2 }).default('0.00'),
  discountEndDate: date('discount_end_date'),
  // Enhanced discount fields for comprehensive time-based discount management
  discountStartDate: date('discount_start_date'),
  originalMonthlyAmount: decimal('original_monthly_amount', { precision: 10, scale: 2 }),
  discountReason: text('discount_reason'),

  // Advance Payment Fields
  advancePaymentBalance: decimal('advance_payment_balance', { precision: 10, scale: 2 }).default('0.00'),
  advanceMonthsRemaining: integer('advance_months_remaining').default(0),

  // Operational Expenses Breakdown
  operationalExpenses: jsonb('operational_expenses'), // JSON object containing expense breakdown
  databaseCosts: decimal('database_costs', { precision: 10, scale: 2 }).default('0'),
  websiteMaintenance: decimal('website_maintenance', { precision: 10, scale: 2 }).default('0'),
  supportCosts: decimal('support_costs', { precision: 10, scale: 2 }).default('0'),
  infrastructureCosts: decimal('infrastructure_costs', { precision: 10, scale: 2 }).default('0'),
  totalOperationalExpenses: decimal('total_operational_expenses', { precision: 10, scale: 2 }).default('0'),

  // Admin Notes and Metadata
  notes: text('notes'), // Admin notes about the subscription
  setupFee: decimal('setup_fee', { precision: 10, scale: 2 }).default('0'), // One-time setup fee

  // Razorpay Integration (Legacy - for migration)
  razorpaySubscriptionId: varchar('razorpay_subscription_id', { length: 100 }),
  razorpayCustomerId: varchar('razorpay_customer_id', { length: 100 }),

  // Audit Fields
  activatedAt: timestamp('activated_at'),
  canceledAt: timestamp('canceled_at'),
  cancelReason: text('cancel_reason'),
  createdBy: uuid('created_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Manual Billing Transactions
export const billingTransactions = pgTable('billing_transactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),

  // Transaction Details
  originalAmount: decimal('original_amount', { precision: 10, scale: 2 }), // Amount before discount
  discountAmount: decimal('discount_amount', { precision: 10, scale: 2 }).default('0.00'),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(), // Amount after discount
  penaltyAmount: decimal('penalty_amount', { precision: 10, scale: 2 }).default('0.00'),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(), // Final amount including penalties
  dueDate: date('due_date').notNull(),
  paymentDate: timestamp('payment_date'),

  // Advance Payment Fields
  isAdvancePayment: boolean('is_advance_payment').default(false),
  advanceMonthsCovered: integer('advance_months_covered').default(1),

  // Invoice and Receipt Management
  invoiceNumber: varchar('invoice_number', { length: 50 }),
  receiptNumber: varchar('receipt_number', { length: 50 }),
  invoicePdfPath: varchar('invoice_pdf_path', { length: 500 }),
  receiptPdfPath: varchar('receipt_pdf_path', { length: 500 }),
  emailSentAt: timestamp('email_sent_at'),

  // Razorpay Integration
  razorpayPaymentId: varchar('razorpay_payment_id', { length: 255 }),
  razorpayOrderId: varchar('razorpay_order_id', { length: 255 }),
  paymentMethod: varchar('payment_method', { length: 50 }),

  // Status Management
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'paid', 'failed', 'overdue'
  failureReason: text('failure_reason'),
  notes: text('notes'),

  // Audit Fields
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Enhanced Invoices with proper lifecycle management
export const billingInvoices = pgTable('billing_invoices', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),

  // Invoice Identification
  invoiceNumber: varchar('invoice_number', { length: 50 }).notNull().unique(),

  // Invoice Amounts
  subtotal: decimal('subtotal', { precision: 10, scale: 2 }).notNull(),
  discountAmount: decimal('discount_amount', { precision: 10, scale: 2 }).default('0'),
  taxAmount: decimal('tax_amount', { precision: 10, scale: 2 }).default('0'),
  penaltyAmount: decimal('penalty_amount', { precision: 10, scale: 2 }).default('0'),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),

  // Invoice Lifecycle
  status: varchar('status', { length: 20 }).default('draft'), // 'draft', 'open', 'paid', 'void', 'uncollectible'

  // Important Dates
  issuedDate: date('issued_date').notNull(),
  dueDate: date('due_date').notNull(),
  paidDate: date('paid_date'),
  voidedDate: date('voided_date'),

  // Billing Period
  periodStart: date('period_start').notNull(),
  periodEnd: date('period_end').notNull(),

  // Razorpay Integration
  razorpayOrderId: varchar('razorpay_order_id', { length: 100 }),

  // Additional Fields
  pdfUrl: varchar('pdf_url', { length: 500 }),
  notes: text('notes'),

  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Invoice Line Items for detailed billing
export const billingInvoiceItems = pgTable('billing_invoice_items', {
  id: uuid('id').primaryKey().defaultRandom(),
  invoiceId: uuid('invoice_id').references(() => billingInvoices.id, { onDelete: 'cascade' }),

  // Item Details
  description: varchar('description', { length: 255 }).notNull(),
  quantity: integer('quantity').notNull(),
  unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),

  // Item Type
  type: varchar('type', { length: 20 }).notNull(), // 'subscription', 'setup_fee', 'penalty', 'discount', 'tax'

  // Period (for subscription items)
  periodStart: date('period_start'),
  periodEnd: date('period_end'),

  createdAt: timestamp('created_at').defaultNow(),
});

// ===== SUBSCRIPTION AUTHENTICATION =====

export const subscriptionAuth = pgTable('subscription_auth', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => subscriptions.id, { onDelete: 'cascade' }).notNull(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }).notNull(),
  razorpayOrderId: varchar('razorpay_order_id', { length: 100 }).notNull(),
  razorpayPaymentId: varchar('razorpay_payment_id', { length: 100 }),
  razorpayCustomerId: varchar('razorpay_customer_id', { length: 100 }).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(), // Authentication amount in rupees
  currency: varchar('currency', { length: 3 }).default('INR'),
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'completed', 'failed'
  authType: varchar('auth_type', { length: 30 }).default('initial_authentication'), // 'initial_authentication', 'reauth'
  failureReason: text('failure_reason'), // Reason for failure if status is 'failed'
  createdAt: timestamp('created_at').defaultNow(),
  completedAt: timestamp('completed_at'),
  failedAt: timestamp('failed_at')
});

export const razorpayCustomers = pgTable('razorpay_customers', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }).notNull(),
  razorpayCustomerId: varchar('razorpay_customer_id', { length: 100 }).notNull().unique(),
  customerEmail: varchar('customer_email', { length: 255 }).notNull(),
  customerName: varchar('customer_name', { length: 255 }).notNull(),
  customerContact: varchar('customer_contact', { length: 20 }),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
});

// ===== DISCOUNT-BASED BILLING SYSTEM =====

// Subscription Discounts - Time-limited discount management
export const subscriptionDiscounts = pgTable('subscription_discounts', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),

  // Discount Configuration
  discountPercentage: decimal('discount_percentage', { precision: 5, scale: 2 }).notNull(), // e.g., 20.00 for 20%
  discountDurationMonths: integer('discount_duration_months').notNull(), // e.g., 6 months
  startDate: date('start_date').notNull(),
  endDate: date('end_date').notNull(),
  remainingMonths: integer('remaining_months').notNull(),

  // Status and Metadata
  isActive: boolean('is_active').default(true),
  reason: text('reason'), // Admin reason for discount

  // Audit Fields
  createdBy: uuid('created_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Subscription Expenses - Operational cost tracking per subscription
export const subscriptionExpenses = pgTable('subscription_expenses', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),

  // Expense Details
  monthlyOperationalCost: decimal('monthly_operational_cost', { precision: 10, scale: 2 }).notNull(),
  expenseBreakdown: jsonb('expense_breakdown'), // Store individual expense categories
  description: text('description'),
  category: varchar('category', { length: 50 }).default('operational'), // 'operational', 'infrastructure', 'support'

  // Effective Period
  effectiveFrom: date('effective_from').notNull(),
  effectiveUntil: date('effective_until'),
  isActive: boolean('is_active').default(true),

  // Audit Fields
  createdBy: uuid('created_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Partner Commission Configuration - Commission settings per partner-subscription pair
export const partnerCommissionConfig = pgTable('partner_commission_config', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id, { onDelete: 'cascade' }),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),

  // Commission Settings
  commissionPercentage: decimal('commission_percentage', { precision: 5, scale: 2 }).notNull(), // Applied to profit after expenses
  holdingPeriodDays: integer('holding_period_days').default(30),

  // Status
  isActive: boolean('is_active').default(true),

  // Audit Fields
  createdBy: uuid('created_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Enhanced Partner Commission Transactions - Detailed commission tracking
export const partnerCommissionTransactions = pgTable('partner_commission_transactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id, { onDelete: 'cascade' }),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),
  paymentId: uuid('payment_id').references(() => billingTransactions.id, { onDelete: 'cascade' }),

  // Financial Breakdown
  schoolPaymentAmount: decimal('school_payment_amount', { precision: 10, scale: 2 }).notNull(),
  discountAmount: decimal('discount_amount', { precision: 10, scale: 2 }).default('0.00'),
  operationalExpenses: decimal('operational_expenses', { precision: 10, scale: 2 }).notNull(),
  profitAmount: decimal('profit_amount', { precision: 10, scale: 2 }).notNull(), // school_payment - discount - expenses
  commissionPercentage: decimal('commission_percentage', { precision: 5, scale: 2 }).notNull(),
  commissionAmount: decimal('commission_amount', { precision: 10, scale: 2 }).notNull(), // profit_amount * commission_percentage
  penaltyAmount: decimal('penalty_amount', { precision: 10, scale: 2 }).default('0.00'), // Not included in commission

  // Status Management
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'held', 'eligible', 'paid', 'cancelled'
  holdUntilDate: date('hold_until_date'),
  eligibleDate: date('eligible_date'),
  paidDate: date('paid_date'),

  // Payout Information
  payoutMethod: varchar('payout_method', { length: 20 }), // 'neft', 'bank_transfer', 'upi'
  transactionReference: varchar('transaction_reference', { length: 100 }), // TRN number
  payoutAmount: decimal('payout_amount', { precision: 10, scale: 2 }),
  payoutNotes: text('payout_notes'),

  // Audit Fields
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Advance Payments - Track multi-month advance payments
export const advancePayments = pgTable('advance_payments', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),
  paymentId: uuid('payment_id').references(() => billingTransactions.id, { onDelete: 'cascade' }),

  // Advance Payment Details
  monthsPaid: integer('months_paid').notNull(),
  amountPerMonth: decimal('amount_per_month', { precision: 10, scale: 2 }).notNull(),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),

  // Period Coverage
  startMonth: date('start_month').notNull(),
  endMonth: date('end_month').notNull(),
  remainingMonths: integer('remaining_months').notNull(),

  // Status
  status: varchar('status', { length: 20 }).default('active'), // 'active', 'consumed', 'refunded'

  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// ===== ENHANCED PAYMENT PROCESSING =====

// Payment Methods - Store customer payment instruments
export const billingPaymentMethods = pgTable('billing_payment_methods', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),

  // Payment Method Details
  type: varchar('type', { length: 20 }).notNull(), // 'card', 'bank_account', 'upi', 'wallet'
  isDefault: boolean('is_default').default(false),

  // Razorpay Integration
  razorpayPaymentMethodId: varchar('razorpay_payment_method_id', { length: 100 }),

  // Card Details (masked)
  cardLast4: varchar('card_last4', { length: 4 }),
  cardBrand: varchar('card_brand', { length: 20 }), // 'visa', 'mastercard', 'amex', etc.
  cardExpMonth: integer('card_exp_month'),
  cardExpYear: integer('card_exp_year'),

  // Bank Details (masked)
  bankName: varchar('bank_name', { length: 100 }),
  accountLast4: varchar('account_last4', { length: 4 }),

  // Status
  isActive: boolean('is_active').default(true),

  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Enhanced Payments with proper lifecycle tracking
export const billingPayments = pgTable('billing_payments', {
  id: uuid('id').primaryKey().defaultRandom(),
  invoiceId: uuid('invoice_id').references(() => billingInvoices.id, { onDelete: 'cascade' }),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),
  paymentMethodId: uuid('payment_method_id').references(() => billingPaymentMethods.id, { onDelete: 'set null' }),

  // Payment Details
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).default('INR'),

  // Payment Status (following Stripe/Razorpay patterns)
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'succeeded', 'failed', 'canceled', 'requires_action', 'requires_payment_method'

  // Razorpay Integration
  razorpayPaymentId: varchar('razorpay_payment_id', { length: 100 }),
  razorpayOrderId: varchar('razorpay_order_id', { length: 100 }),
  razorpaySignature: varchar('razorpay_signature', { length: 255 }),

  // Payment Method Used
  paymentMethod: varchar('payment_method', { length: 50 }),

  // Failure Handling
  failureReason: text('failure_reason'),
  failureCode: varchar('failure_code', { length: 50 }),

  // Important Timestamps
  authorizedAt: timestamp('authorized_at'),
  capturedAt: timestamp('captured_at'),
  failedAt: timestamp('failed_at'),

  // Retry Information
  attemptCount: integer('attempt_count').default(1),
  nextRetryAt: timestamp('next_retry_at'),

  // Commission Processing Integration
  partnerCommissionProcessed: boolean('partner_commission_processed').default(false),
  commissionProcessingDate: timestamp('commission_processing_date'),
  commissionEscrowId: uuid('commission_escrow_id'), // Will add reference after table definition

  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Payment Events Log for audit trail
export const billingPaymentEvents = pgTable('billing_payment_events', {
  id: uuid('id').primaryKey().defaultRandom(),
  paymentId: uuid('payment_id').references(() => billingPayments.id, { onDelete: 'cascade' }),

  // Event Details
  eventType: varchar('event_type', { length: 50 }).notNull(), // 'created', 'authorized', 'captured', 'failed', 'refunded'
  status: varchar('status', { length: 20 }).notNull(),

  // Event Data
  eventData: jsonb('event_data'), // Store webhook payload or additional event data

  // Source
  source: varchar('source', { length: 20 }).default('system'), // 'system', 'webhook', 'manual'

  createdAt: timestamp('created_at').defaultNow(),
});

// Enhanced Payment Reminders
export const billingPaymentReminders = pgTable('billing_payment_reminders', {
  id: uuid('id').primaryKey().defaultRandom(),
  invoiceId: uuid('invoice_id').references(() => billingInvoices.id, { onDelete: 'cascade' }),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),

  // Reminder Configuration
  reminderType: varchar('reminder_type', { length: 20 }).notNull(), // 'due_soon', 'overdue', 'final_notice', 'grace_period'
  daysBefore: integer('days_before'), // Days before due date (for due_soon)
  daysAfter: integer('days_after'), // Days after due date (for overdue)

  // Delivery Status
  sentDate: timestamp('sent_date').notNull(),
  emailSent: boolean('email_sent').default(false),
  smsSent: boolean('sms_sent').default(false),

  // Response Tracking
  emailOpened: boolean('email_opened').default(false),
  emailClicked: boolean('email_clicked').default(false),

  createdAt: timestamp('created_at').defaultNow(),
});

// ===== SUPPORT SYSTEM =====

export const supportTickets = pgTable('support_tickets', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  priority: varchar('priority', { length: 20 }).default('medium'), // 'low', 'medium', 'high', 'urgent'
  status: varchar('status', { length: 20 }).default('open'), // 'open', 'in_progress', 'resolved', 'closed'
  category: varchar('category', { length: 50 }), // 'billing', 'technical', 'feature_request', 'bug'
  assignedTo: uuid('assigned_to'),
  createdBy: uuid('created_by').references(() => clientUsers.id, { onDelete: 'set null' }),
  resolvedAt: timestamp('resolved_at'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const ticketMessages = pgTable('ticket_messages', {
  id: uuid('id').primaryKey().defaultRandom(),
  ticketId: uuid('ticket_id').references(() => supportTickets.id, { onDelete: 'cascade' }),
  senderType: varchar('sender_type', { length: 20 }).notNull(), // 'client', 'admin'
  senderId: uuid('sender_id').notNull(),
  message: text('message').notNull(),
  attachments: jsonb('attachments'),
  createdAt: timestamp('created_at').defaultNow(),
})

// Audit Logs Table
export const auditLogs = pgTable('audit_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => clientUsers.id, { onDelete: 'cascade' }),
  adminId: uuid('admin_id').references(() => adminUsers.id, { onDelete: 'set null' }),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }),
  action: varchar('action', { length: 100 }).notNull(),
  resource: varchar('resource', { length: 100 }).notNull(),
  resourceId: uuid('resource_id'),
  details: jsonb('details'),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  success: boolean('success').notNull(),
  errorMessage: text('error_message'),
  severity: varchar('severity', { length: 20 }).notNull(), // 'low', 'medium', 'high', 'critical'
  category: varchar('category', { length: 20 }).notNull(), // 'auth', 'admin', 'payment', 'data', 'security', 'system'
  timestamp: timestamp('timestamp').defaultNow().notNull(),
})

// ===== WEBHOOK IDEMPOTENCY TRACKING =====
export const webhookIdempotency = pgTable('webhook_idempotency', {
  id: uuid('id').primaryKey().defaultRandom(),
  idempotencyKey: varchar('idempotency_key', { length: 255 }).notNull().unique(),
  webhookSource: varchar('webhook_source', { length: 50 }).notNull(), // 'razorpay', 'stripe', etc.
  eventType: varchar('event_type', { length: 100 }).notNull(), // 'payment.captured', 'payment.failed', etc.
  webhookData: jsonb('webhook_data').notNull(), // Store the complete webhook payload
  processedAt: timestamp('processed_at').defaultNow(),
  processingResult: jsonb('processing_result'), // Store the result of processing
  createdAt: timestamp('created_at').defaultNow(),
});

// Rate Limiting Table
export const rateLimits = pgTable('rate_limits', {
  id: uuid('id').primaryKey().defaultRandom(),
  identifier: varchar('identifier', { length: 255 }).notNull(), // IP address or user ID
  endpoint: varchar('endpoint', { length: 255 }).notNull(),
  requestCount: integer('request_count').default(1),
  windowStart: timestamp('window_start').defaultNow().notNull(),
  lastRequest: timestamp('last_request').defaultNow().notNull(),
  blocked: boolean('blocked').default(false),
  blockedUntil: timestamp('blocked_until'),
})

// Security Events Table
export const securityEvents = pgTable('security_events', {
  id: uuid('id').primaryKey().defaultRandom(),
  eventType: varchar('event_type', { length: 50 }).notNull(),
  userId: uuid('user_id').references(() => clientUsers.id, { onDelete: 'cascade' }),
  adminId: uuid('admin_id').references(() => adminUsers.id, { onDelete: 'set null' }),
  ipAddress: varchar('ip_address', { length: 45 }).notNull(),
  userAgent: text('user_agent'),
  details: jsonb('details'),
  severity: varchar('severity', { length: 20 }).notNull(),
  resolved: boolean('resolved').default(false),
  resolvedBy: uuid('resolved_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  resolvedAt: timestamp('resolved_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// ===== ADMIN USERS =====

export const adminUsers = pgTable('admin_users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  role: varchar('role', { length: 20 }).notNull(), // 'super_admin', 'sales', 'support', 'billing'
  permissions: jsonb('permissions'),
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== SOFTWARE REQUEST WORKFLOW =====

export const termsConditions = pgTable('terms_conditions', {
  id: uuid('id').primaryKey().defaultRandom(),
  version: varchar('version', { length: 10 }).notNull().unique(),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  effectiveDate: date('effective_date').notNull(),
  isActive: boolean('is_active').default(true),
  createdBy: uuid('created_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow(),
});

export const softwareRequests = pgTable('software_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }).notNull(),
  requestType: varchar('request_type', { length: 20 }).notNull(), // 'demo', 'production'

  // School operational data
  studentCount: integer('student_count').notNull(),
  facultyCount: integer('faculty_count').notNull(),
  completeAddress: text('complete_address').notNull(),
  contactNumber: varchar('contact_number', { length: 20 }).notNull(),
  primaryEmail: varchar('primary_email', { length: 255 }).notNull(),

  // Fee structure (for production requests only) - Simplified to single average fee
  averageMonthlyFee: decimal('average_monthly_fee', { precision: 10, scale: 2 }), // Average fee per student per month

  // Legacy fields - keeping for backward compatibility and data migration
  class1Fee: decimal('class_1_fee', { precision: 10, scale: 2 }),
  class4Fee: decimal('class_4_fee', { precision: 10, scale: 2 }),
  class6Fee: decimal('class_6_fee', { precision: 10, scale: 2 }),
  class10Fee: decimal('class_10_fee', { precision: 10, scale: 2 }),
  class11Fee: decimal('class_11_fee', { precision: 10, scale: 2 }),
  class12Fee: decimal('class_12_fee', { precision: 10, scale: 2 }),
  class1112Fee: decimal('class_11_12_fee', { precision: 10, scale: 2 }), // Legacy field - keeping for backward compatibility
  calculatedAverageFee: decimal('calculated_average_fee', { precision: 10, scale: 2 }), // Legacy field - replaced by averageMonthlyFee

  // Request status workflow
  status: varchar('status', { length: 20 }).default('pending'),
  // 'pending', 'under_review', 'approved', 'rejected', 'setup_in_progress', 'activated'

  // Terms acceptance (production only)
  termsAccepted: boolean('terms_accepted').default(false),
  termsAcceptedAt: timestamp('terms_accepted_at'),
  termsVersion: varchar('terms_version', { length: 10 }),
  ipAddress: varchar('ip_address', { length: 45 }), // IPv4/IPv6 support
  userAgent: text('user_agent'),

  // Admin workflow
  reviewedBy: uuid('reviewed_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  reviewNotes: text('review_notes'),
  rejectionReason: text('rejection_reason'),

  // Timestamps
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  approvedAt: timestamp('approved_at'),
  activatedAt: timestamp('activated_at'),
});

export const requestStatusHistory = pgTable('request_status_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  requestId: uuid('request_id').references(() => softwareRequests.id, { onDelete: 'cascade' }).notNull(),
  fromStatus: varchar('from_status', { length: 20 }),
  toStatus: varchar('to_status', { length: 20 }).notNull(),
  changedBy: uuid('changed_by'), // admin_user_id or client_user_id
  changeReason: text('change_reason'),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== RELATIONS =====

export const leadsRelations = relations(leads, ({ one, many }) => ({
  client: one(clients, { fields: [leads.id], references: [clients.leadId] }),
  demoBookings: many(demoBookings),
}));

export const clientsRelations = relations(clients, ({ one, many }) => ({
  lead: one(leads, { fields: [clients.leadId], references: [leads.id] }),
  users: many(clientUsers),
  subscription: one(subscriptions, { fields: [clients.id], references: [subscriptions.clientId] }),
  billingSubscriptions: many(billingSubscriptions),
  billingInvoices: many(billingInvoices),
  billingPayments: many(billingPayments),
  billingPaymentMethods: many(billingPaymentMethods),
  billingTransactions: many(billingTransactions),
  supportTickets: many(supportTickets),
  softwareRequests: many(softwareRequests),
  schoolReferral: one(schoolReferrals, { fields: [clients.id], references: [schoolReferrals.clientId] }),
  partnerEarnings: many(partnerEarnings),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one }) => ({
  client: one(clients, { fields: [subscriptions.clientId], references: [clients.id] }),
  plan: one(subscriptionPlans, { fields: [subscriptions.planId], references: [subscriptionPlans.id] }),
}));

// ===== NEW BILLING RELATIONS =====

export const billingPlansRelations = relations(billingPlans, ({ many }) => ({
  subscriptions: many(billingSubscriptions),
}));

export const billingSubscriptionsRelations = relations(billingSubscriptions, ({ one, many }) => ({
  client: one(clients, { fields: [billingSubscriptions.clientId], references: [clients.id] }),
  plan: one(billingPlans, { fields: [billingSubscriptions.planId], references: [billingPlans.id] }),
  createdBy: one(adminUsers, { fields: [billingSubscriptions.createdBy], references: [adminUsers.id] }),
  invoices: many(billingInvoices),
  payments: many(billingPayments),
  transactions: many(billingTransactions),
  // Discount System Relations
  discounts: many(subscriptionDiscounts),
  expenses: many(subscriptionExpenses),
  commissionConfigs: many(partnerCommissionConfig),
  commissionTransactions: many(partnerCommissionTransactions),
  advancePayments: many(advancePayments),
}));

export const billingInvoicesRelations = relations(billingInvoices, ({ one, many }) => ({
  subscription: one(billingSubscriptions, { fields: [billingInvoices.subscriptionId], references: [billingSubscriptions.id] }),
  client: one(clients, { fields: [billingInvoices.clientId], references: [clients.id] }),
  items: many(billingInvoiceItems),
  payments: many(billingPayments),
  reminders: many(billingPaymentReminders),
}));

export const billingInvoiceItemsRelations = relations(billingInvoiceItems, ({ one }) => ({
  invoice: one(billingInvoices, { fields: [billingInvoiceItems.invoiceId], references: [billingInvoices.id] }),
}));

export const billingPaymentMethodsRelations = relations(billingPaymentMethods, ({ one, many }) => ({
  client: one(clients, { fields: [billingPaymentMethods.clientId], references: [clients.id] }),
  payments: many(billingPayments),
}));

export const billingPaymentsRelations = relations(billingPayments, ({ one, many }) => ({
  invoice: one(billingInvoices, { fields: [billingPayments.invoiceId], references: [billingInvoices.id] }),
  subscription: one(billingSubscriptions, { fields: [billingPayments.subscriptionId], references: [billingSubscriptions.id] }),
  client: one(clients, { fields: [billingPayments.clientId], references: [clients.id] }),
  paymentMethod: one(billingPaymentMethods, { fields: [billingPayments.paymentMethodId], references: [billingPaymentMethods.id] }),
  events: many(billingPaymentEvents),
  commissionEscrow: one(partnerCommissionEscrow, { fields: [billingPayments.commissionEscrowId], references: [partnerCommissionEscrow.id] }),
}));

export const billingTransactionsRelations = relations(billingTransactions, ({ one, many }) => ({
  subscription: one(billingSubscriptions, { fields: [billingTransactions.subscriptionId], references: [billingSubscriptions.id] }),
  client: one(clients, { fields: [billingTransactions.clientId], references: [clients.id] }),
  // Discount System Relations
  commissionTransactions: many(partnerCommissionTransactions),
  advancePayment: one(advancePayments, { fields: [billingTransactions.id], references: [advancePayments.paymentId] }),
}));

export const billingPaymentEventsRelations = relations(billingPaymentEvents, ({ one }) => ({
  payment: one(billingPayments, { fields: [billingPaymentEvents.paymentId], references: [billingPayments.id] }),
}));

export const billingPaymentRemindersRelations = relations(billingPaymentReminders, ({ one }) => ({
  invoice: one(billingInvoices, { fields: [billingPaymentReminders.invoiceId], references: [billingInvoices.id] }),
  client: one(clients, { fields: [billingPaymentReminders.clientId], references: [clients.id] }),
}));

export const supportTicketsRelations = relations(supportTickets, ({ one, many }) => ({
  client: one(clients, { fields: [supportTickets.clientId], references: [clients.id] }),
  createdBy: one(clientUsers, { fields: [supportTickets.createdBy], references: [clientUsers.id] }),
  messages: many(ticketMessages),
}));

// Software Request Relations
export const softwareRequestsRelations = relations(softwareRequests, ({ one, many }) => ({
  client: one(clients, { fields: [softwareRequests.clientId], references: [clients.id] }),
  reviewedBy: one(adminUsers, { fields: [softwareRequests.reviewedBy], references: [adminUsers.id] }),
  statusHistory: many(requestStatusHistory),
}));

export const requestStatusHistoryRelations = relations(requestStatusHistory, ({ one }) => ({
  request: one(softwareRequests, { fields: [requestStatusHistory.requestId], references: [softwareRequests.id] }),
}));

export const termsConditionsRelations = relations(termsConditions, ({ one }) => ({
  createdBy: one(adminUsers, { fields: [termsConditions.createdBy], references: [adminUsers.id] }),
}));

export const adminUsersRelations = relations(adminUsers, ({ many }) => ({
  reviewedRequests: many(softwareRequests),
  createdTerms: many(termsConditions),
  createdPartners: many(partners),
  createdExpenses: many(operationalExpenses),
  reviewedWithdrawals: many(withdrawalRequests),
  processedWithdrawals: many(withdrawalRequests),
  partnerTransactions: many(partnerTransactions),
}));

// ===== PARTNER REFERRAL SYSTEM =====

export const partners = pgTable('partners', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerCode: varchar('partner_code', { length: 8 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  companyName: varchar('company_name', { length: 255 }),
  phone: varchar('phone', { length: 20 }).notNull(),
  address: text('address').notNull(),

  // Bank details (encrypted)
  bankAccountNumber: varchar('bank_account_number', { length: 100 }), // Encrypted
  bankIfscCode: varchar('bank_ifsc_code', { length: 11 }),
  bankAccountHolderName: varchar('bank_account_holder_name', { length: 255 }),

  // Profit sharing
  profitSharePercentage: decimal('profit_share_percentage', { precision: 5, scale: 2 }), // Individual override or NULL for global

  // Fund Account Integration
  primaryFundAccountId: uuid('primary_fund_account_id'), // Will add reference after table definition
  fundAccountVerified: boolean('fund_account_verified').default(false),
  razorpayContactId: varchar('razorpay_contact_id', { length: 100 }), // Razorpay contact for Route integration
  autoPayoutEnabled: boolean('auto_payout_enabled').default(false),
  minimumPayoutAmount: decimal('minimum_payout_amount', { precision: 10, scale: 2 }).default('1000'), // ₹1000 minimum

  // Status and metadata
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  emailVerified: boolean('email_verified').default(false),

  // Audit fields
  createdBy: uuid('created_by').references(() => adminUsers.id, { onDelete: 'set null' }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const referralCodes = pgTable('referral_codes', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id, { onDelete: 'cascade' }).notNull(),
  code: varchar('code', { length: 8 }).notNull().unique(),
  isActive: boolean('is_active').default(true),
  usageCount: integer('usage_count').default(0),
  maxUsage: integer('max_usage'), // NULL for unlimited
  createdAt: timestamp('created_at').defaultNow(),
  deactivatedAt: timestamp('deactivated_at'),
  deactivatedBy: uuid('deactivated_by').references(() => adminUsers.id, { onDelete: 'set null' }),
});

export const schoolReferrals = pgTable('school_referrals', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }).notNull(),
  partnerId: uuid('partner_id').references(() => partners.id, { onDelete: 'cascade' }).notNull(),
  referralCodeId: uuid('referral_code_id').references(() => referralCodes.id, { onDelete: 'cascade' }).notNull(),
  referredAt: timestamp('referred_at').defaultNow(),
  referralSource: varchar('referral_source', { length: 20 }).notNull(), // 'registration', 'profile_update'
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  isActive: boolean('is_active').default(true),

  // Audit trail
  appliedBy: uuid('applied_by').references(() => clientUsers.id, { onDelete: 'set null' }),
  verifiedAt: timestamp('verified_at'),
  verifiedBy: uuid('verified_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  rejectionReason: text('rejection_reason'), // Reason for rejection if rejected
});

export const operationalExpenses = pgTable('operational_expenses', {
  id: uuid('id').primaryKey().defaultRandom(),
  categoryName: varchar('category_name', { length: 100 }).notNull(),
  description: text('description'),

  // Amount configuration
  amountPerSchool: decimal('amount_per_school', { precision: 10, scale: 2 }),
  isPercentage: boolean('is_percentage').default(false),
  percentageValue: decimal('percentage_value', { precision: 5, scale: 2 }),

  // Applicability
  isActive: boolean('is_active').default(true),
  appliesTo: varchar('applies_to', { length: 20 }).default('all'), // 'all', 'specific_partners', 'specific_schools'

  // Audit fields
  createdBy: uuid('created_by').references(() => adminUsers.id, { onDelete: 'set null' }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const partnerEarnings = pgTable('partner_earnings', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id, { onDelete: 'cascade' }).notNull(),
  clientId: uuid('client_id').references(() => clients.id, { onDelete: 'cascade' }).notNull(),
  invoiceId: uuid('invoice_id').references(() => billingInvoices.id, { onDelete: 'cascade' }).notNull(),
  paymentId: uuid('payment_id').references(() => billingPayments.id, { onDelete: 'cascade' }),

  // Financial calculations
  grossAmount: decimal('gross_amount', { precision: 10, scale: 2 }).notNull(), // School payment amount
  totalExpenses: decimal('total_expenses', { precision: 10, scale: 2 }).notNull(), // Sum of all expenses
  netProfit: decimal('net_profit', { precision: 10, scale: 2 }).notNull(), // Gross - Expenses
  partnerSharePercentage: decimal('partner_share_percentage', { precision: 5, scale: 2 }).notNull(),
  partnerEarning: decimal('partner_earning', { precision: 10, scale: 2 }).notNull(), // Net profit × percentage

  // Status tracking
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'available', 'withdrawn'
  calculatedAt: timestamp('calculated_at').defaultNow(),
  availableAt: timestamp('available_at'), // When school payment confirmed
  withdrawnAt: timestamp('withdrawn_at'),

  // Expense breakdown (JSON for transparency)
  expenseBreakdown: jsonb('expense_breakdown'), // Detailed expense categories and amounts

  // Escrow Integration
  escrowId: uuid('escrow_id'), // Will add reference after table definition
  escrowStatus: varchar('escrow_status', { length: 20 }).default('manual'), // 'manual', 'automated', 'escrow_managed'

  // Audit trail
  calculatedBy: uuid('calculated_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  notes: text('notes'),
});

export const withdrawalRequests = pgTable('withdrawal_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id, { onDelete: 'cascade' }).notNull(),
  requestedAmount: decimal('requested_amount', { precision: 10, scale: 2 }).notNull(),
  availableBalance: decimal('available_balance', { precision: 10, scale: 2 }).notNull(), // Balance at time of request

  // Status workflow
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'approved', 'processed', 'rejected'
  requestMonth: date('request_month').notNull(), // YYYY-MM-01 format for monthly limit

  // Timestamps
  requestedAt: timestamp('requested_at').defaultNow(),
  reviewedAt: timestamp('reviewed_at'),
  processedAt: timestamp('processed_at'),

  // Admin workflow
  reviewedBy: uuid('reviewed_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  processedBy: uuid('processed_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  transactionReference: varchar('transaction_reference', { length: 100 }),

  // Bank details snapshot (for audit)
  bankDetailsSnapshot: jsonb('bank_details_snapshot'),

  // Rejection handling
  rejectionReason: text('rejection_reason'),

  // Processing details
  processingFee: decimal('processing_fee', { precision: 10, scale: 2 }).default('0'),
  netAmount: decimal('net_amount', { precision: 10, scale: 2 }), // Requested - Processing fee

  // Metadata
  metadata: jsonb('metadata'), // Additional processing information
});

export const partnerTransactions = pgTable('partner_transactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id, { onDelete: 'cascade' }).notNull(),

  // Transaction details
  transactionType: varchar('transaction_type', { length: 20 }).notNull(), // 'EARNING', 'WITHDRAWAL', 'ADJUSTMENT', 'BONUS', 'PENALTY'
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  description: text('description').notNull(),

  // Reference linking
  referenceId: uuid('reference_id'), // Links to earnings, withdrawals, etc.
  referenceType: varchar('reference_type', { length: 50 }), // 'partner_earning', 'withdrawal_request', 'manual_adjustment'

  // Balance tracking
  balanceBefore: decimal('balance_before', { precision: 10, scale: 2 }).notNull(),
  balanceAfter: decimal('balance_after', { precision: 10, scale: 2 }).notNull(),

  // Audit trail
  createdBy: uuid('created_by'), // admin_user_id for manual transactions
  createdAt: timestamp('created_at').defaultNow(),

  // Additional data
  metadata: jsonb('metadata'), // Additional transaction details
  isReversible: boolean('is_reversible').default(false),
  reversedAt: timestamp('reversed_at'),
  reversedBy: uuid('reversed_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  reversalReason: text('reversal_reason'),
});

// Global configuration for partner system
export const partnerSystemConfig = pgTable('partner_system_config', {
  id: uuid('id').primaryKey().defaultRandom(),
  configKey: varchar('config_key', { length: 100 }).notNull().unique(),
  configValue: text('config_value').notNull(),
  dataType: varchar('data_type', { length: 20 }).notNull(), // 'string', 'number', 'boolean', 'json'
  description: text('description'),
  isActive: boolean('is_active').default(true),
  updatedBy: uuid('updated_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// ===== PARTNER REFERRAL RELATIONS =====

export const partnersRelations = relations(partners, ({ one, many }) => ({
  createdBy: one(adminUsers, { fields: [partners.createdBy], references: [adminUsers.id] }),
  referralCodes: many(referralCodes),
  schoolReferrals: many(schoolReferrals),
  earnings: many(partnerEarnings),
  withdrawalRequests: many(withdrawalRequests),
  transactions: many(partnerTransactions),
  primaryFundAccount: one(partnerFundAccounts, { fields: [partners.primaryFundAccountId], references: [partnerFundAccounts.id] }),
  fundAccounts: many(partnerFundAccounts),
  commissionEscrows: many(partnerCommissionEscrow),
}));

export const referralCodesRelations = relations(referralCodes, ({ one, many }) => ({
  partner: one(partners, { fields: [referralCodes.partnerId], references: [partners.id] }),
  deactivatedBy: one(adminUsers, { fields: [referralCodes.deactivatedBy], references: [adminUsers.id] }),
  schoolReferrals: many(schoolReferrals),
}));

export const schoolReferralsRelations = relations(schoolReferrals, ({ one }) => ({
  client: one(clients, { fields: [schoolReferrals.clientId], references: [clients.id] }),
  partner: one(partners, { fields: [schoolReferrals.partnerId], references: [partners.id] }),
  referralCode: one(referralCodes, { fields: [schoolReferrals.referralCodeId], references: [referralCodes.id] }),
  appliedBy: one(clientUsers, { fields: [schoolReferrals.appliedBy], references: [clientUsers.id] }),
  verifiedBy: one(adminUsers, { fields: [schoolReferrals.verifiedBy], references: [adminUsers.id] }),
}));

export const operationalExpensesRelations = relations(operationalExpenses, ({ one }) => ({
  createdBy: one(adminUsers, { fields: [operationalExpenses.createdBy], references: [adminUsers.id] }),
}));

export const partnerEarningsRelations = relations(partnerEarnings, ({ one }) => ({
  partner: one(partners, { fields: [partnerEarnings.partnerId], references: [partners.id] }),
  client: one(clients, { fields: [partnerEarnings.clientId], references: [clients.id] }),
  invoice: one(billingInvoices, { fields: [partnerEarnings.invoiceId], references: [billingInvoices.id] }),
  payment: one(billingPayments, { fields: [partnerEarnings.paymentId], references: [billingPayments.id] }),
  calculatedBy: one(adminUsers, { fields: [partnerEarnings.calculatedBy], references: [adminUsers.id] }),
  escrow: one(partnerCommissionEscrow, { fields: [partnerEarnings.escrowId], references: [partnerCommissionEscrow.id] }),
}));

export const withdrawalRequestsRelations = relations(withdrawalRequests, ({ one }) => ({
  partner: one(partners, { fields: [withdrawalRequests.partnerId], references: [partners.id] }),
  reviewedBy: one(adminUsers, { fields: [withdrawalRequests.reviewedBy], references: [adminUsers.id] }),
  processedBy: one(adminUsers, { fields: [withdrawalRequests.processedBy], references: [adminUsers.id] }),
}));

export const partnerTransactionsRelations = relations(partnerTransactions, ({ one }) => ({
  partner: one(partners, { fields: [partnerTransactions.partnerId], references: [partners.id] }),
  createdBy: one(adminUsers, { fields: [partnerTransactions.createdBy], references: [adminUsers.id] }),
  reversedBy: one(adminUsers, { fields: [partnerTransactions.reversedBy], references: [adminUsers.id] }),
}));

export const partnerSystemConfigRelations = relations(partnerSystemConfig, ({ one }) => ({
  updatedBy: one(adminUsers, { fields: [partnerSystemConfig.updatedBy], references: [adminUsers.id] }),
}));

// ===== ENHANCED COMMISSION ESCROW SYSTEM =====

// Partner Commission Escrow Management
export const partnerCommissionEscrow = pgTable('partner_commission_escrow', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id, { onDelete: 'cascade' }).notNull(),
  schoolId: uuid('school_id').references(() => clients.id, { onDelete: 'cascade' }).notNull(),
  subscriptionId: uuid('subscription_id').references(() => billingSubscriptions.id, { onDelete: 'cascade' }),

  // Commission Details
  monthYear: varchar('month_year', { length: 7 }).notNull(), // "2025-01"
  baseAmount: decimal('base_amount', { precision: 10, scale: 2 }).notNull(), // School payment amount
  commissionPercentage: decimal('commission_percentage', { precision: 5, scale: 2 }).notNull(),
  commissionAmount: decimal('commission_amount', { precision: 10, scale: 2 }).notNull(),
  operationalExpenses: decimal('operational_expenses', { precision: 10, scale: 2 }).default('0'),
  netCommission: decimal('net_commission', { precision: 10, scale: 2 }).notNull(),

  // Escrow States
  escrowStatus: varchar('escrow_status', { length: 20 }).default('pending'),
  // 'pending', 'school_paid', 'held', 'released', 'reversed', 'disputed'

  // Dependency Tracking
  schoolPaymentStatus: varchar('school_payment_status', { length: 20 }),
  schoolPaymentId: uuid('school_payment_id'), // Will add reference after table definition
  schoolPaymentDate: timestamp('school_payment_date'),

  // Release Conditions
  holdUntilDate: timestamp('hold_until_date'),
  releaseConditions: jsonb('release_conditions'),
  autoReleaseEnabled: boolean('auto_release_enabled').default(true),
  riskScore: integer('risk_score').default(0), // 0-100 risk assessment score

  // Razorpay Integration
  razorpayTransferId: varchar('razorpay_transfer_id', { length: 100 }),
  razorpayAccountId: varchar('razorpay_account_id', { length: 100 }),
  transferStatus: varchar('transfer_status', { length: 20 }), // 'pending', 'processing', 'processed', 'failed', 'reversed'

  // Audit Trail
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  createdBy: uuid('created_by').references(() => adminUsers.id, { onDelete: 'set null' }),
  releasedAt: timestamp('released_at'),
  releasedBy: uuid('released_by').references(() => adminUsers.id, { onDelete: 'set null' }),

  // Constraints - will be handled by unique index
});

// Partner Fund Account Validation
export const partnerFundAccounts = pgTable('partner_fund_accounts', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').notNull(), // Will add reference after table definition

  // Bank Account Details
  accountNumber: varchar('account_number', { length: 20 }).notNull(),
  ifscCode: varchar('ifsc_code', { length: 11 }).notNull(),
  accountHolderName: varchar('account_holder_name', { length: 100 }).notNull(),
  bankName: varchar('bank_name', { length: 100 }),
  branchName: varchar('branch_name', { length: 100 }),

  // Validation Status
  validationStatus: varchar('validation_status', { length: 20 }).default('pending'),
  // 'pending', 'verified', 'failed', 'expired', 'blocked'
  validationDate: timestamp('validation_date'),
  validationReference: varchar('validation_reference', { length: 100 }),
  validationNotes: text('validation_notes'),

  // Razorpay Integration
  razorpayFundAccountId: varchar('razorpay_fund_account_id', { length: 100 }),
  razorpayContactId: varchar('razorpay_contact_id', { length: 100 }),

  // Account Management
  isPrimary: boolean('is_primary').default(false),
  isActive: boolean('is_active').default(true),

  // Audit Trail
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  verifiedBy: uuid('verified_by').references(() => adminUsers.id, { onDelete: 'set null' }),
});

// Commission Release Audit Log
export const commissionReleaseAudit = pgTable('commission_release_audit', {
  id: uuid('id').primaryKey().defaultRandom(),
  escrowId: uuid('escrow_id').references(() => partnerCommissionEscrow.id, { onDelete: 'cascade' }).notNull(),

  // Action Details
  actionType: varchar('action_type', { length: 50 }).notNull(),
  // 'created', 'held', 'released', 'reversed', 'disputed', 'manual_override', 'risk_assessment', 'condition_check'
  actionReason: text('action_reason'),
  previousStatus: varchar('previous_status', { length: 20 }),
  newStatus: varchar('new_status', { length: 20 }),

  // Financial Impact
  amountAffected: decimal('amount_affected', { precision: 10, scale: 2 }),

  // System Context
  triggeredBy: varchar('triggered_by', { length: 50 }), // 'webhook', 'cron', 'admin', 'api', 'system'
  triggeredByUser: uuid('triggered_by_user').references(() => adminUsers.id, { onDelete: 'set null' }),
  systemReference: varchar('system_reference', { length: 100 }), // webhook_id, cron_job_id, etc.

  // Risk Assessment Data
  riskFactors: jsonb('risk_factors'), // Risk factors considered
  conditionsEvaluated: jsonb('conditions_evaluated'), // Release conditions checked

  // Audit Trail
  createdAt: timestamp('created_at').defaultNow(),
  metadata: jsonb('metadata'), // Additional context data
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
});



// ===== ESCROW SYSTEM RELATIONS =====

export const partnerCommissionEscrowRelations = relations(partnerCommissionEscrow, ({ one, many }) => ({
  partner: one(partners, { fields: [partnerCommissionEscrow.partnerId], references: [partners.id] }),
  school: one(clients, { fields: [partnerCommissionEscrow.schoolId], references: [clients.id] }),
  subscription: one(billingSubscriptions, { fields: [partnerCommissionEscrow.subscriptionId], references: [billingSubscriptions.id] }),
  schoolPayment: one(billingPayments, { fields: [partnerCommissionEscrow.schoolPaymentId], references: [billingPayments.id] }),
  createdBy: one(adminUsers, { fields: [partnerCommissionEscrow.createdBy], references: [adminUsers.id] }),
  releasedBy: one(adminUsers, { fields: [partnerCommissionEscrow.releasedBy], references: [adminUsers.id] }),
  auditLogs: many(commissionReleaseAudit),
}));

export const partnerFundAccountsRelations = relations(partnerFundAccounts, ({ one }) => ({
  partner: one(partners, { fields: [partnerFundAccounts.partnerId], references: [partners.id] }),
  verifiedBy: one(adminUsers, { fields: [partnerFundAccounts.verifiedBy], references: [adminUsers.id] }),
}));

export const commissionReleaseAuditRelations = relations(commissionReleaseAudit, ({ one }) => ({
  escrow: one(partnerCommissionEscrow, { fields: [commissionReleaseAudit.escrowId], references: [partnerCommissionEscrow.id] }),
  triggeredByUser: one(adminUsers, { fields: [commissionReleaseAudit.triggeredByUser], references: [adminUsers.id] }),
}));

// ===== DISCOUNT SYSTEM RELATIONS =====

export const subscriptionDiscountsRelations = relations(subscriptionDiscounts, ({ one }) => ({
  subscription: one(billingSubscriptions, { fields: [subscriptionDiscounts.subscriptionId], references: [billingSubscriptions.id] }),
  createdBy: one(adminUsers, { fields: [subscriptionDiscounts.createdBy], references: [adminUsers.id] }),
}));

export const subscriptionExpensesRelations = relations(subscriptionExpenses, ({ one }) => ({
  subscription: one(billingSubscriptions, { fields: [subscriptionExpenses.subscriptionId], references: [billingSubscriptions.id] }),
  createdBy: one(adminUsers, { fields: [subscriptionExpenses.createdBy], references: [adminUsers.id] }),
}));

export const partnerCommissionConfigRelations = relations(partnerCommissionConfig, ({ one }) => ({
  partner: one(partners, { fields: [partnerCommissionConfig.partnerId], references: [partners.id] }),
  subscription: one(billingSubscriptions, { fields: [partnerCommissionConfig.subscriptionId], references: [billingSubscriptions.id] }),
  createdBy: one(adminUsers, { fields: [partnerCommissionConfig.createdBy], references: [adminUsers.id] }),
}));

export const partnerCommissionTransactionsRelations = relations(partnerCommissionTransactions, ({ one }) => ({
  partner: one(partners, { fields: [partnerCommissionTransactions.partnerId], references: [partners.id] }),
  subscription: one(billingSubscriptions, { fields: [partnerCommissionTransactions.subscriptionId], references: [billingSubscriptions.id] }),
  payment: one(billingTransactions, { fields: [partnerCommissionTransactions.paymentId], references: [billingTransactions.id] }),
}));

export const advancePaymentsRelations = relations(advancePayments, ({ one }) => ({
  subscription: one(billingSubscriptions, { fields: [advancePayments.subscriptionId], references: [billingSubscriptions.id] }),
  payment: one(billingTransactions, { fields: [advancePayments.paymentId], references: [billingTransactions.id] }),
}));


