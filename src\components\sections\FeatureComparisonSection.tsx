'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Check, X, Star, ArrowRight, Zap } from 'lucide-react'

const FeatureComparisonSection = () => {

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const comparisonData = {
    features: [
      {
        category: 'Core Modules',
        items: [
          { feature: 'Admin Dashboard', competitors: 'basic', ours: 'advanced' },
          { feature: 'Fee Management', competitors: 'basic', ours: 'advanced' },
          { feature: 'Student Portal', competitors: 'limited', ours: 'complete' },
          { feature: 'Teacher Portal', competitors: 'extra', ours: 'included' },
          { feature: 'Parent Gateway', competitors: 'extra', ours: 'included' },
          { feature: 'Library Management', competitors: 'extra', ours: 'included' },
          { feature: 'Transport Management', competitors: 'extra', ours: 'included' },
          { feature: 'Hostel Management', competitors: 'extra', ours: 'included' },
        ]
      },
      {
        category: 'Advanced Features',
        items: [
          { feature: 'AI-Powered Analytics', competitors: 'none', ours: 'advanced' },
          { feature: 'Predictive Insights', competitors: 'none', ours: 'advanced' },
          { feature: 'Real-time Web Notifications', competitors: 'basic', ours: 'advanced' },
          { feature: 'Responsive Web Design', competitors: 'basic', ours: 'advanced' },
          { feature: 'Multi-language Support', competitors: 'limited', ours: 'complete' },
          { feature: 'Custom Reports', competitors: 'limited', ours: 'unlimited' },
        ]
      },
      {
        category: 'Implementation & Support',
        items: [
          { feature: 'Setup Time', competitors: '4-8 weeks', ours: '14 days' },
          { feature: 'Data Migration', competitors: 'extra cost', ours: 'free included' },
          { feature: 'Staff Training', competitors: 'basic only', ours: 'comprehensive' },
          { feature: 'Support Hours', competitors: 'business hours', ours: '24/7 dedicated' },
          { feature: 'Implementation Cost', competitors: '₹2-5L extra', ours: 'included' },
          { feature: 'ROI Timeline', competitors: '12+ months', ours: '6 months' },
        ]
      }
    ]
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'advanced':
      case 'complete':
      case 'included':
      case 'unlimited':
        return <div className="w-6 h-6 rounded-full bg-emerald-100 flex items-center justify-center">
          <Check className="w-4 h-4 text-emerald-600 font-bold" />
        </div>
      case 'basic':
      case 'limited':
        return <div className="w-6 h-6 rounded-full bg-yellow-100 flex items-center justify-center">
          <div className="w-3 h-3 bg-yellow-600 rounded-full" />
        </div>
      case 'extra':
      case 'none':
        return <div className="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center">
          <X className="w-4 h-4 text-red-600 font-bold" />
        </div>
      default:
        return null
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'advanced': return 'Advanced'
      case 'complete': return 'Complete'
      case 'included': return 'Included'
      case 'unlimited': return 'Unlimited'
      case 'basic': return 'Basic'
      case 'limited': return 'Limited'
      case 'extra': return 'Extra Cost'
      case 'none': return 'Not Available'
      default: return status
    }
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 pt-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Star className="w-4 h-4 fill-current" />
            See Why Schools Choose Us
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
            Complete System vs. <span className="text-red-600">Partial Solutions</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Get everything you need in one comprehensive system, not scattered across multiple incomplete solutions.
          </p>
        </motion.div>

        {/* Section Header */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-6 py-3 rounded-full text-lg font-bold">
            <Zap className="w-5 h-5 fill-current" />
            Feature Comparison
          </div>
        </motion.div>

        {/* Feature Comparison Table */}
        {(
          <motion.div
            key="features"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-8"
          >
            {comparisonData.features.map((category, categoryIndex) => (
              <Card key={categoryIndex} className="overflow-visible shadow-xl border-0 bg-gradient-to-br from-white to-blue-50 mt-8">
                <CardHeader padding="md">
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
                    {category.category}
                  </h3>
                </CardHeader>
                <CardContent padding="none">
                  {/* Desktop Table */}
                  <div className="hidden md:block overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gradient-to-r from-slate-800 to-blue-900">
                        <tr>
                          <th className="text-left py-6 px-8 font-bold text-white text-lg">Feature</th>
                          <th className="text-center py-6 px-8 font-bold text-red-200 text-lg">
                            <div className="flex items-center justify-center gap-2">
                              <X className="w-5 h-5" />
                              Competitors
                            </div>
                          </th>
                          <th className="text-center py-6 px-8 font-bold text-emerald-200 text-lg relative">
                            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 z-10">
                              <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-slate-900 px-3 py-1 rounded-full text-xs font-bold shadow-lg whitespace-nowrap">
                                ⭐ MOST POPULAR
                              </div>
                            </div>
                            <div className="flex items-center justify-center gap-2 mt-4">
                              <Zap className="w-5 h-5 text-yellow-300" />
                              Schopio
                            </div>
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white">
                        {category.items.map((item, itemIndex) => (
                          <motion.tr
                            key={itemIndex}
                            initial={{ opacity: 0, x: -20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.5, delay: itemIndex * 0.1 }}
                            className="border-t border-slate-200 hover:bg-blue-50/50 hover:shadow-sm transition-all duration-300"
                          >
                            <td className="py-5 px-8 font-semibold text-slate-900 text-base">{item.feature}</td>
                            <td className="py-5 px-8 text-center">
                              <div className="flex items-center justify-center gap-3">
                                {getStatusIcon(item.competitors)}
                                <span className="text-base font-medium text-slate-700">
                                  {getStatusText(item.competitors)}
                                </span>
                              </div>
                            </td>
                            <td className="py-5 px-8 text-center bg-emerald-50/50">
                              <div className="flex items-center justify-center gap-3">
                                {getStatusIcon(item.ours)}
                                <span className="text-base font-bold text-emerald-700">
                                  {getStatusText(item.ours)}
                                </span>
                              </div>
                            </td>
                          </motion.tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Mobile Cards */}
                  <div className="md:hidden p-4 space-y-4">
                    {category.items.map((item, itemIndex) => (
                      <motion.div
                        key={itemIndex}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5, delay: itemIndex * 0.1 }}
                        className="bg-white border border-slate-200 rounded-lg p-4 shadow-sm mt-6"
                      >
                        <h5 className="font-bold text-slate-900 mb-3 text-center">{item.feature}</h5>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center p-3 bg-red-50 rounded-lg">
                            <div className="text-xs text-red-600 font-medium mb-2">Competitors</div>
                            <div className="flex flex-col items-center gap-2">
                              {getStatusIcon(item.competitors)}
                              <span className="text-sm font-medium text-slate-700">
                                {getStatusText(item.competitors)}
                              </span>
                            </div>
                          </div>
                          <div className="text-center p-3 bg-emerald-50 rounded-lg relative">
                            {itemIndex === 0 && categoryIndex === 0 && (
                              <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 z-10">
                                <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-slate-900 px-2 py-1 rounded-full text-xs font-bold shadow-lg whitespace-nowrap">
                                  ⭐ POPULAR
                                </div>
                              </div>
                            )}
                            <div className="text-xs text-emerald-600 font-medium mb-2">Schopio</div>
                            <div className="flex flex-col items-center gap-2">
                              {getStatusIcon(item.ours)}
                              <span className="text-sm font-bold text-emerald-700">
                                {getStatusText(item.ours)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </motion.div>
        )}

        {/* ROI Calculator CTA */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
          className="mt-16 text-center"
        >
          <Card className="bg-gradient-to-r from-emerald-600 to-blue-600 border-0 max-w-2xl mx-auto">
            <CardContent padding="xl">
              <div className="text-white">
                <h3 className="text-2xl font-bold mb-4">Ready to Transform Your School?</h3>
                <p className="text-emerald-100 mb-6">
                  Discover the perfect package for your school and explore all the features that make Schopio the complete solution.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    size="lg"
                    icon={ArrowRight}
                    iconPosition="right"
                    className="bg-white text-emerald-600 hover:bg-emerald-50 font-bold px-8 py-4"
                    onClick={() => window.location.href = '/packages'}
                  >
                    Find Your Package
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4"
                    onClick={() => window.location.href = '/solutions'}
                  >
                    View All Features
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default FeatureComparisonSection
