#!/usr/bin/env node

/**
 * Quick Database Migration Runner
 * Run this script to add operational expenses columns to billingSubscriptions table
 */

const { Pool } = require('pg');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

async function runMigration() {
  const pool = new Pool(dbConfig);
  
  try {
    console.log('🔄 Starting database migration...');
    
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      console.log('🚀 Executing migration...');
      
      // Add operational expenses fields
      await client.query(`
        ALTER TABLE billing_subscriptions 
        ADD COLUMN IF NOT EXISTS operational_expenses JSONB,
        ADD COLUMN IF NOT EXISTS database_costs DECIMAL(10,2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS website_maintenance DECIMAL(10,2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS support_costs DECIMAL(10,2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS infrastructure_costs DECIMAL(10,2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS total_operational_expenses DECIMAL(10,2) DEFAULT 0;
      `);
      
      // Add admin notes and metadata fields
      await client.query(`
        ALTER TABLE billing_subscriptions 
        ADD COLUMN IF NOT EXISTS notes TEXT,
        ADD COLUMN IF NOT EXISTS setup_fee DECIMAL(10,2) DEFAULT 0;
      `);
      
      // Add discount tracking fields
      await client.query(`
        ALTER TABLE billing_subscriptions 
        ADD COLUMN IF NOT EXISTS discount_start_date DATE,
        ADD COLUMN IF NOT EXISTS discount_end_date DATE,
        ADD COLUMN IF NOT EXISTS discount_reason TEXT;
      `);
      
      // Add created_by field for audit trail
      await client.query(`
        ALTER TABLE billing_subscriptions 
        ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES admin_users(id) ON DELETE SET NULL;
      `);
      
      // Update existing records to have default values
      await client.query(`
        UPDATE billing_subscriptions 
        SET 
          database_costs = COALESCE(database_costs, 0),
          website_maintenance = COALESCE(website_maintenance, 0),
          support_costs = COALESCE(support_costs, 0),
          infrastructure_costs = COALESCE(infrastructure_costs, 0),
          total_operational_expenses = COALESCE(total_operational_expenses, 0),
          setup_fee = COALESCE(setup_fee, 0)
        WHERE 
          database_costs IS NULL 
          OR website_maintenance IS NULL 
          OR support_costs IS NULL 
          OR infrastructure_costs IS NULL 
          OR total_operational_expenses IS NULL 
          OR setup_fee IS NULL;
      `);
      
      await client.query('COMMIT');
      console.log('✅ Migration completed successfully!');
      
      // Verify migration
      console.log('🔍 Verifying migration...');
      const verifyResult = await client.query(`
        SELECT 
          column_name, 
          data_type, 
          is_nullable, 
          column_default
        FROM information_schema.columns 
        WHERE table_name = 'billing_subscriptions' 
          AND column_name IN (
            'operational_expenses', 
            'database_costs', 
            'website_maintenance', 
            'support_costs', 
            'infrastructure_costs', 
            'total_operational_expenses',
            'notes',
            'setup_fee',
            'discount_start_date',
            'discount_end_date',
            'discount_reason',
            'created_by'
          )
        ORDER BY column_name;
      `);
      
      console.log('📋 Added columns:');
      verifyResult.rows.forEach(row => {
        console.log(`   ✓ ${row.column_name} (${row.data_type})`);
      });
      
      console.log(`\n🎉 Migration successful! Added ${verifyResult.rows.length} columns to billing_subscriptions table.`);
      console.log('\n📝 Next steps:');
      console.log('   1. Restart your application server');
      console.log('   2. Test subscription creation and editing');
      console.log('   3. Verify operational expenses are being saved');
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('📝 Full error:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run migration
runMigration().catch(console.error);
