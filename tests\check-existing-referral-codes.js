// Check existing referral codes in the system
// Run with: node tests/check-existing-referral-codes.js

const BASE_URL = 'http://localhost:3000'

async function checkExistingReferralCodes() {
  console.log('🔍 Checking existing referral codes in the system...\n')

  try {
    // Step 1: Login as admin
    console.log('1. Logging in as admin...')
    const loginResponse = await fetch(`${BASE_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123456'
      })
    })

    if (!loginResponse.ok) {
      console.log('   ❌ Admin login failed')
      return
    }

    const loginData = await loginResponse.json()
    const adminToken = loginData.token
    console.log('   ✅ Admin login successful')

    // Step 2: Get all partners
    console.log('\n2. Getting all partners...')
    const partnersResponse = await fetch(`${BASE_URL}/api/admin/partners?limit=50`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    })

    if (!partnersResponse.ok) {
      console.log('   ❌ Failed to get partners')
      return
    }

    const partnersData = await partnersResponse.json()
    console.log(`   ✅ Found ${partnersData.partners?.length || 0} partners`)

    if (!partnersData.partners || partnersData.partners.length === 0) {
      console.log('\n❌ No partners found in the system!')
      console.log('   You need to create partners first.')
      return
    }

    // Step 3: Get referral codes for each partner
    console.log('\n3. Getting referral codes for each partner...')
    
    let totalCodes = 0
    const availableCodes = []

    for (const partner of partnersData.partners) {
      console.log(`\n   📝 Partner: ${partner.name} (${partner.email})`)
      console.log(`      Company: ${partner.companyName || 'N/A'}`)
      console.log(`      Status: ${partner.isActive ? 'Active' : 'Inactive'}`)
      
      // Get partner details including referral codes
      const partnerDetailResponse = await fetch(`${BASE_URL}/api/admin/partners/${partner.id}`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      })
      
      if (partnerDetailResponse.ok) {
        const partnerDetail = await partnerDetailResponse.json()
        
        if (partnerDetail.referralCodes && partnerDetail.referralCodes.length > 0) {
          console.log(`      📋 Referral Codes:`)
          partnerDetail.referralCodes.forEach(code => {
            console.log(`         🎫 ${code.code} (${code.isActive ? 'Active' : 'Inactive'}) - Used: ${code.usageCount || 0} times`)
            if (code.isActive) {
              availableCodes.push({
                code: code.code,
                partnerName: partner.name,
                companyName: partner.companyName
              })
              totalCodes++
            }
          })
        } else {
          console.log(`      ❌ No referral codes found`)
        }
      } else {
        console.log(`      ❌ Failed to get partner details`)
      }
    }

    // Step 4: Test some codes
    console.log(`\n4. Testing available referral codes (${totalCodes} total)...`)
    
    if (availableCodes.length === 0) {
      console.log('   ❌ No active referral codes found!')
      console.log('   You need to create referral codes for the partners.')
      return
    }

    for (const codeInfo of availableCodes.slice(0, 5)) { // Test first 5 codes
      console.log(`\n   🧪 Testing code: ${codeInfo.code}`)
      
      const validateResponse = await fetch(`${BASE_URL}/api/auth/referral/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: codeInfo.code })
      })

      if (validateResponse.ok) {
        const validateData = await validateResponse.json()
        if (validateData.valid) {
          console.log(`      ✅ Valid - Partner: ${validateData.referralCode.partnerName}`)
        } else {
          console.log(`      ❌ Invalid: ${validateData.error}`)
        }
      } else {
        console.log(`      ❌ Validation failed: ${validateResponse.status}`)
      }
    }

    // Step 5: Summary
    console.log('\n🎉 Summary:')
    console.log(`   👥 Total Partners: ${partnersData.partners.length}`)
    console.log(`   🎫 Active Referral Codes: ${totalCodes}`)
    
    if (totalCodes > 0) {
      console.log('\n📋 Available codes for testing:')
      availableCodes.forEach(code => {
        console.log(`   🎫 ${code.code} - ${code.partnerName} (${code.companyName || 'No Company'})`)
      })
      
      console.log('\n💡 You can now test referral code functionality with any of the above codes!')
      console.log('   Go to school portal → Profile → Settings → Enter referral code')
    } else {
      console.log('\n❌ No referral codes available for testing!')
      console.log('   Partners exist but have no referral codes.')
    }

  } catch (error) {
    console.error('❌ Error checking referral codes:', error.message)
  }
}

// Run the check
checkExistingReferralCodes()
