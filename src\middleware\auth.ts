import jwt from 'jsonwebtoken'
import { Context } from 'hono'
import { db } from '@/src/db'
import { clientUsers } from '@/src/db/schema'
import { eq } from 'drizzle-orm'

export interface AuthUser {
  id: string
  email: string
  name: string
  role: string
  clientId: string
}

// Middleware to verify JWT token
export const authMiddleware = async (c: Context, next: () => Promise<void>) => {
  try {
    const authHeader = c.req.header('Authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix
    
    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any
    
    if (!decoded.userId || !decoded.email) {
      return c.json({ error: 'Invalid token format' }, 401)
    }

    // Fetch user from database to ensure they still exist and are active
    const [user] = await db
      .select()
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user) {
      return c.json({ error: 'User not found' }, 401)
    }

    if (!user.isActive) {
      return c.json({ error: 'Account is deactivated' }, 403)
    }

    if (!user.emailVerified) {
      return c.json({ error: 'Email not verified' }, 403)
    }

    // Add user to context
    c.set('user', {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      clientId: user.clientId
    } as AuthUser)

    await next()
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    
    console.error('Auth middleware error:', error)
    return c.json({ error: 'Authentication failed' }, 500)
  }
}

// Optional middleware - doesn't fail if no token provided
export const optionalAuthMiddleware = async (c: Context, next: () => Promise<void>) => {
  try {
    const authHeader = c.req.header('Authorization')
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any
        
        if (decoded.userId && decoded.email) {
          const [user] = await db
            .select()
            .from(clientUsers)
            .where(eq(clientUsers.id, decoded.userId))
            .limit(1)

          if (user && user.isActive && user.emailVerified) {
            c.set('user', {
              id: user.id,
              email: user.email,
              name: user.name,
              role: user.role,
              clientId: user.clientId
            } as AuthUser)
          }
        }
      } catch (error) {
        // Ignore token errors in optional middleware
      }
    }

    await next()
  } catch (error) {
    console.error('Optional auth middleware error:', error)
    await next()
  }
}

// Helper function to get current user from context
export const getCurrentUser = (c: Context): AuthUser | null => {
  return c.get('user') || null
}

// Helper function to require authentication
export const requireAuth = (c: Context): AuthUser => {
  const user = getCurrentUser(c)
  if (!user) {
    throw new Error('Authentication required')
  }
  return user
}

// Helper function to check if user has specific role
export const hasRole = (user: AuthUser, role: string): boolean => {
  return user.role === role
}

// Helper function to check if user has any of the specified roles
export const hasAnyRole = (user: AuthUser, roles: string[]): boolean => {
  return roles.includes(user.role)
}

// Role-based middleware
export const requireRole = (allowedRoles: string[]) => {
  return async (c: Context, next: () => Promise<void>) => {
    const user = getCurrentUser(c)
    
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401)
    }

    if (!hasAnyRole(user, allowedRoles)) {
      return c.json({ 
        error: 'Insufficient permissions',
        required: allowedRoles,
        current: user.role
      }, 403)
    }

    await next()
  }
}

// Admin-only middleware
export const requireAdmin = requireRole(['admin'])

// Helper to generate new JWT token
export const generateAuthToken = (userId: string, email: string): string => {
  return jwt.sign(
    { userId, email },
    process.env.JWT_SECRET || 'fallback-secret-key',
    { expiresIn: '7d' }
  )
}
