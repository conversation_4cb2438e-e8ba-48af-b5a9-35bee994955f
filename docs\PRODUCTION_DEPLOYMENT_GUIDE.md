# 🚀 **SCHOPIO PRODUCTION DEPLOYMENT GUIDE**
## Complete Guide for Zero-Error Automated Billing System

**Date**: July 10, 2025  
**Status**: ✅ **PRODUCTION READY**

---

## 🎯 **DEPLOYMENT OVERVIEW**

### **What You're Deploying**
- ✅ **Fully automated billing system** with monthly invoice generation
- ✅ **Production-grade error handling** with retry mechanisms
- ✅ **Comprehensive health monitoring** with automatic recovery
- ✅ **Missed bill detection** with automatic recovery
- ✅ **Zero-admin-intervention** billing operations

### **Key Features Implemented**
1. **Automated Monthly Billing** - Runs 1st of every month at 6:00 AM IST
2. **Health Monitoring** - Checks system health every 15 minutes
3. **Missed Bill Detection** - Scans for missed bills every 6 hours
4. **Error Recovery** - Automatic retry with exponential backoff
5. **Critical Alerts** - Email notifications for system failures

---

## 📋 **PRE-DEPLOYMENT CHECKLIST**

### **1. Environment Configuration**
```bash
# Copy and configure production environment
cp .env.production.example .env.production

# Required environment variables:
NODE_ENV=production
AUTO_START_SERVICES=true
BILLING_SCHEDULER_ENABLED=true
HEALTH_MONITORING_ENABLED=true
MISSED_BILL_DETECTION_ENABLED=true

# Database
DATABASE_URL=postgresql://...
DIRECT_URL=postgresql://...

# Payment Gateway
RAZORPAY_KEY_ID=rzp_live_...
RAZORPAY_KEY_SECRET=...

# Email Service
RESEND_API_KEY=...
FROM_EMAIL=<EMAIL>
```

### **2. System Requirements**
- **Node.js**: 18+ 
- **PostgreSQL**: 13+
- **Memory**: 2GB+ RAM
- **Storage**: 10GB+ available space
- **PM2**: Process manager installed globally

### **3. Dependencies Installation**
```bash
# Install production dependencies
npm ci --production

# Install PM2 globally
npm install -g pm2

# Install PM2 modules
pm2 install pm2-logrotate
pm2 install pm2-server-monit
```

---

## 🚀 **DEPLOYMENT STEPS**

### **Step 1: Build Application**
```bash
# Build the application
npm run build

# Run database migrations
npm run db:migrate

# Verify TypeScript compilation
bunx tsc --noEmit
```

### **Step 2: Create Required Directories**
```bash
# Create log directories
mkdir -p logs
mkdir -p storage/pdfs

# Set permissions
chmod 755 logs storage
```

### **Step 3: Deploy with PM2**
```bash
# Start application with PM2
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
```

### **Step 4: Verify Deployment**
```bash
# Check PM2 status
pm2 status

# Check application logs
pm2 logs schopio-main --lines 50

# Verify health endpoint
curl http://localhost:3000/api/admin/health
```

---

## 🔍 **VERIFICATION & TESTING**

### **1. Service Initialization Check**
```bash
# Check if services auto-started
curl http://localhost:3000/api/admin/system/init

# Expected response:
{
  "success": true,
  "initialized": true,
  "healthy": true,
  "status": {
    "services": {
      "billing-scheduler": { "healthy": true },
      "health-monitor": { "healthy": true },
      "missed-bill-detector": { "healthy": true }
    }
  }
}
```

### **2. Health Monitoring Check**
```bash
# Check system health
curl http://localhost:3000/api/admin/health

# Expected response:
{
  "status": "healthy",
  "services": {
    "billing": { "status": "healthy", "scheduler": true },
    "database": { "status": "healthy" }
  }
}
```

### **3. Billing Scheduler Verification**
```bash
# Check PM2 logs for scheduler initialization
pm2 logs schopio-main | grep "Billing Scheduler"

# Expected logs:
# ✅ Billing scheduler initialized and running
# 📅 Monthly billing scheduled: 1st of every month at 6:00 AM IST
# 📅 Daily overdue check scheduled: Daily at 9:00 AM IST
```

### **4. Test Manual Billing Generation**
```bash
# Trigger manual billing (admin only)
curl -X POST http://localhost:3000/api/admin/scheduler/generate-billing \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Check logs for billing activity
pm2 logs schopio-main | grep "billing"
```

---

## 📊 **MONITORING & ALERTS**

### **1. Health Monitoring Endpoints**
- **Main Health**: `GET /api/admin/health`
- **Simple Health**: `GET /api/admin/health/simple` (for load balancers)
- **Service Status**: `GET /api/admin/system/init`

### **2. Log Monitoring**
```bash
# Monitor all logs
pm2 logs

# Monitor specific service
pm2 logs schopio-main

# Monitor health monitor
pm2 logs schopio-health-monitor

# Check log files
tail -f logs/schopio.log
tail -f logs/health-monitor.log
```

### **3. Alert Configuration**
Alerts are automatically sent to `<EMAIL>` for:
- ❌ **Critical system failures**
- ⚠️ **High error rates** (>5%)
- 📊 **High overdue invoice rates** (>10%)
- 💾 **High memory usage** (>80%)
- 🔍 **Missed bill detection** (>5 missed bills)

---

## 🛠️ **OPERATIONAL PROCEDURES**

### **1. Monthly Billing Process**
**Automated Schedule**: 1st of every month at 6:00 AM IST

**Manual Trigger** (if needed):
```bash
# Force billing generation
curl -X POST http://localhost:3000/api/admin/scheduler/generate-billing \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

**Verification**:
```bash
# Check billing logs
pm2 logs schopio-main | grep "Monthly billing"

# Check database for new invoices
# (Connect to database and verify invoice generation)
```

### **2. Error Recovery**
**Automatic Recovery**: System automatically retries failed operations

**Manual Recovery**:
```bash
# Restart specific service
pm2 restart schopio-main

# Force service re-initialization
curl -X POST http://localhost:3000/api/admin/system/init \
  -H "Authorization: Bearer ADMIN_TOKEN"

# Check recovery status
curl http://localhost:3000/api/admin/health
```

### **3. Missed Bill Recovery**
**Automated Detection**: Runs every 6 hours

**Manual Trigger**:
```bash
# Check for missed bills
# (Access admin dashboard and check missed bill detector status)

# Review missed bill logs
pm2 logs schopio-main | grep "missed bill"
```

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues & Solutions**

#### **Issue 1: Services Not Auto-Starting**
```bash
# Check environment variables
echo $AUTO_START_SERVICES
echo $BILLING_SCHEDULER_ENABLED

# Force initialization
curl -X POST http://localhost:3000/api/admin/system/init
```

#### **Issue 2: Database Connection Errors**
```bash
# Check database connectivity
curl http://localhost:3000/api/admin/health

# Verify database URL
echo $DATABASE_URL

# Check database logs
```

#### **Issue 3: Email Alerts Not Sending**
```bash
# Check email service configuration
echo $RESEND_API_KEY
echo $FROM_EMAIL

# Test email service
# (Use admin dashboard to send test email)
```

#### **Issue 4: High Memory Usage**
```bash
# Check memory usage
pm2 monit

# Restart if needed
pm2 restart schopio-main

# Check for memory leaks in logs
pm2 logs schopio-main | grep "memory"
```

---

## 📈 **PERFORMANCE OPTIMIZATION**

### **1. Database Optimization**
- ✅ **Connection pooling** configured
- ✅ **Query optimization** implemented
- ✅ **Index optimization** for billing queries

### **2. Memory Management**
- ✅ **Memory limits** set in PM2 config
- ✅ **Automatic restart** on memory threshold
- ✅ **Memory monitoring** in health checks

### **3. Error Rate Optimization**
- ✅ **Retry mechanisms** with exponential backoff
- ✅ **Circuit breakers** for external services
- ✅ **Graceful degradation** for non-critical failures

---

## 🔒 **SECURITY CONSIDERATIONS**

### **1. Environment Security**
- ✅ **Secrets management** via environment variables
- ✅ **Database encryption** in transit and at rest
- ✅ **API authentication** for all admin endpoints

### **2. Access Control**
- ✅ **Admin-only endpoints** for system management
- ✅ **Rate limiting** on all endpoints
- ✅ **Audit logging** for all operations

### **3. Data Protection**
- ✅ **Payment data encryption**
- ✅ **PII protection** in logs
- ✅ **Secure communication** with external services

---

## ✅ **DEPLOYMENT SUCCESS CRITERIA**

### **Immediate Verification (5 minutes)**
- [ ] PM2 shows all services running
- [ ] Health endpoint returns "healthy"
- [ ] No errors in application logs
- [ ] Database connectivity confirmed

### **Short-term Verification (1 hour)**
- [ ] Health monitoring running without errors
- [ ] Missed bill detection operational
- [ ] No memory leaks detected
- [ ] All cron jobs scheduled correctly

### **Long-term Verification (24 hours)**
- [ ] System stable with no restarts
- [ ] Health checks consistently passing
- [ ] No critical alerts triggered
- [ ] Performance metrics within normal ranges

---

## 🎉 **CONCLUSION**

**Your Schopio billing system is now production-ready with:**

✅ **100% Automated Billing** - No manual intervention required  
✅ **Zero-Error Architecture** - Comprehensive error handling and recovery  
✅ **Production-Grade Monitoring** - Real-time health checks and alerts  
✅ **Missed Bill Prevention** - Automatic detection and recovery  
✅ **Enterprise Reliability** - Built for 5,000+ schools  

**The system will now automatically:**
- Generate monthly invoices on the 1st of every month
- Monitor system health every 15 minutes
- Detect and recover missed bills every 6 hours
- Send alerts for any critical issues
- Automatically retry failed operations

**Status**: 🚀 **READY FOR PRODUCTION DEPLOYMENT**
