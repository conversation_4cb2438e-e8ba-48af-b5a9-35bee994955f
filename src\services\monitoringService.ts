/**
 * Production Monitoring Service for Schopio Subscription System
 * 
 * Provides comprehensive monitoring capabilities for business metrics,
 * system performance, security events, and operational health.
 */

import { drizzle } from 'drizzle-orm/neon-http'
import { neon } from '@neondatabase/serverless'
import {
  subscriptions,
  billingInvoices,
  billingPayments,
  billingSubscriptions,
  subscriptionAuth,
  razorpayCustomers,
  webhookIdempotency,
  securityEvents,
  rateLimits,
  auditLogs
} from '../db/schema'
import { eq, gte, lte, and, desc, sql, count, sum, avg } from 'drizzle-orm'

const dbSql = neon(process.env.DATABASE_URL!)
const db = drizzle(dbSql)

export interface BusinessMetrics {
  activeSubscriptions: number
  monthlyRecurringRevenue: number
  churnRate: number
  paymentSuccessRate: number
  averageRevenuePerUser: number
  newSubscriptionsToday: number
  subscriptionsByStatus: Record<string, number>
}

export interface SystemPerformance {
  databaseHealth: {
    connectionCount: number
    slowQueries: number
    indexUsage: number
  }
  apiPerformance: {
    averageResponseTime: number
    errorRate: number
    requestVolume: number
  }
  resourceUtilization: {
    memoryUsage: number
    cpuUsage: number
    diskUsage: number
  }
}

export interface SecurityMetrics {
  authenticationFailures: number
  rateLimitViolations: number
  suspiciousActivity: number
  blockedIPs: number
  securityEvents: Array<{
    type: string
    count: number
    lastOccurrence: Date
  }>
}

export interface BillingHealth {
  invoicesGenerated: number
  paymentsProcessed: number
  failedPayments: number
  webhookSuccess: number
  subscriptionsInGrace: number
  overdueRevenue: number
}

/**
 * Get comprehensive business metrics
 */
export async function getBusinessMetrics(): Promise<BusinessMetrics> {
  const today = new Date()
  const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
  
  // Active subscriptions and MRR
  const activeSubscriptionsResult = await db
    .select({
      count: count(),
      totalRevenue: sum(subscriptions.monthlyAmount)
    })
    .from(subscriptions)
    .where(eq(subscriptions.status, 'active'))

  // Subscriptions by status
  const subscriptionsByStatusResult = await db
    .select({
      status: subscriptions.status,
      count: count()
    })
    .from(subscriptions)
    .groupBy(subscriptions.status)

  // New subscriptions today
  const newSubscriptionsResult = await db
    .select({ count: count() })
    .from(subscriptions)
    .where(gte(subscriptions.createdAt, new Date(today.toDateString())))

  // Payment success rate (last 30 days)
  const paymentStatsResult = await db
    .select({
      total: count(),
      successful: sum(sql`CASE WHEN ${billingPayments.status} = 'completed' THEN 1 ELSE 0 END`)
    })
    .from(billingPayments)
    .where(gte(billingPayments.createdAt, thirtyDaysAgo))

  // Calculate churn rate (simplified)
  const cancelledThisMonth = await db
    .select({ count: count() })
    .from(subscriptions)
    .where(
      and(
        eq(subscriptions.status, 'cancelled'),
        gte(subscriptions.updatedAt, new Date(today.getFullYear(), today.getMonth(), 1))
      )
    )

  const activeCount = activeSubscriptionsResult[0]?.count || 0
  const totalRevenue = Number(activeSubscriptionsResult[0]?.totalRevenue || 0)
  const successfulPayments = Number(paymentStatsResult[0]?.successful || 0)
  const totalPayments = paymentStatsResult[0]?.total || 0
  const cancelledCount = cancelledThisMonth[0]?.count || 0

  return {
    activeSubscriptions: activeCount,
    monthlyRecurringRevenue: totalRevenue,
    churnRate: activeCount > 0 ? (cancelledCount / activeCount) * 100 : 0,
    paymentSuccessRate: totalPayments > 0 ? (successfulPayments / totalPayments) * 100 : 0,
    averageRevenuePerUser: activeCount > 0 ? totalRevenue / activeCount : 0,
    newSubscriptionsToday: newSubscriptionsResult[0]?.count || 0,
    subscriptionsByStatus: subscriptionsByStatusResult.reduce((acc, item) => {
      if (item.status) {
        acc[item.status] = item.count
      }
      return acc
    }, {} as Record<string, number>)
  }
}

/**
 * Get billing automation health metrics
 */
export async function getBillingHealth(): Promise<BillingHealth> {
  const today = new Date()
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

  // Invoices generated today
  const invoicesGenerated = await db
    .select({ count: count() })
    .from(billingInvoices)
    .where(gte(billingInvoices.createdAt, new Date(today.toDateString())))

  // Payments processed today
  const paymentsProcessed = await db
    .select({ count: count() })
    .from(billingPayments)
    .where(gte(billingPayments.createdAt, new Date(today.toDateString())))

  // Failed payments today
  const failedPayments = await db
    .select({ count: count() })
    .from(billingPayments)
    .where(
      and(
        eq(billingPayments.status, 'failed'),
        gte(billingPayments.createdAt, new Date(today.toDateString()))
      )
    )

  // Webhook success rate (last 24 hours)
  const webhookStats = await db
    .select({
      total: count(),
      successful: sum(sql`CASE WHEN processed_successfully = true THEN 1 ELSE 0 END`)
    })
    .from(webhookIdempotency)
    .where(gte(webhookIdempotency.createdAt, yesterday))

  // Subscriptions in grace period
  const subscriptionsInGrace = await db
    .select({
      count: count(),
      totalRevenue: sum(subscriptions.monthlyAmount)
    })
    .from(subscriptions)
    .where(
      and(
        eq(subscriptions.status, 'overdue'),
        sql`${subscriptions.nextBillingDate} <= ${today.toISOString()}`
      )
    )

  return {
    invoicesGenerated: invoicesGenerated[0]?.count || 0,
    paymentsProcessed: paymentsProcessed[0]?.count || 0,
    failedPayments: failedPayments[0]?.count || 0,
    webhookSuccess: webhookStats[0]?.total > 0 
      ? (Number(webhookStats[0]?.successful || 0) / webhookStats[0].total) * 100 
      : 100,
    subscriptionsInGrace: subscriptionsInGrace[0]?.count || 0,
    overdueRevenue: Number(subscriptionsInGrace[0]?.totalRevenue || 0)
  }
}

/**
 * Get security metrics and events
 */
export async function getSecurityMetrics(): Promise<SecurityMetrics> {
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)

  // Authentication failures
  const authFailures = await db
    .select({ count: count() })
    .from(securityEvents)
    .where(
      and(
        eq(securityEvents.eventType, 'authentication_failure'),
        gte(securityEvents.createdAt, twentyFourHoursAgo)
      )
    )

  // Rate limit violations
  const rateLimitViolations = await db
    .select({ count: count() })
    .from(rateLimits)
    .where(
      and(
        eq(rateLimits.blocked, true),
        gte(rateLimits.lastRequest, twentyFourHoursAgo)
      )
    )

  // Security events by type
  const securityEventsByType = await db
    .select({
      eventType: securityEvents.eventType,
      count: count(),
      lastOccurrence: sql<Date>`MAX(${securityEvents.createdAt})`
    })
    .from(securityEvents)
    .where(gte(securityEvents.createdAt, twentyFourHoursAgo))
    .groupBy(securityEvents.eventType)

  // Blocked IPs
  const blockedIPs = await db
    .select({ count: sql<number>`COUNT(DISTINCT ${rateLimits.identifier})` })
    .from(rateLimits)
    .where(
      and(
        eq(rateLimits.blocked, true),
        gte(rateLimits.lastRequest, twentyFourHoursAgo)
      )
    )

  return {
    authenticationFailures: authFailures[0]?.count || 0,
    rateLimitViolations: rateLimitViolations[0]?.count || 0,
    suspiciousActivity: securityEventsByType.filter(e => 
      ['suspicious_activity', 'multiple_failed_attempts', 'unusual_access_pattern'].includes(e.eventType)
    ).reduce((sum, e) => sum + e.count, 0),
    blockedIPs: Number(blockedIPs[0]?.count || 0),
    securityEvents: securityEventsByType.map(event => ({
      type: event.eventType,
      count: event.count,
      lastOccurrence: event.lastOccurrence
    }))
  }
}

/**
 * Get system performance metrics
 */
export async function getSystemPerformance(): Promise<SystemPerformance> {
  // Database health metrics (simplified for now)
  const dbHealthResult = await db.execute(sql`
    SELECT
      (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
      0 as slow_queries,
      85.0 as avg_index_usage
  `)

  const dbHealthRow = dbHealthResult.rows[0] as any

  return {
    databaseHealth: {
      connectionCount: Number(dbHealthRow?.active_connections || 0),
      slowQueries: Number(dbHealthRow?.slow_queries || 0),
      indexUsage: Number(dbHealthRow?.avg_index_usage || 85)
    },
    apiPerformance: {
      averageResponseTime: 0, // To be implemented with API monitoring
      errorRate: 0, // To be implemented with error tracking
      requestVolume: 0 // To be implemented with request logging
    },
    resourceUtilization: {
      memoryUsage: 0, // To be implemented with system monitoring
      cpuUsage: 0, // To be implemented with system monitoring
      diskUsage: 0 // To be implemented with system monitoring
    }
  }
}

/**
 * Get comprehensive monitoring dashboard data
 */
export async function getMonitoringDashboard() {
  const [businessMetrics, billingHealth, securityMetrics, systemPerformance] = await Promise.all([
    getBusinessMetrics(),
    getBillingHealth(),
    getSecurityMetrics(),
    getSystemPerformance()
  ])

  return {
    timestamp: new Date(),
    businessMetrics,
    billingHealth,
    securityMetrics,
    systemPerformance,
    overallHealth: calculateOverallHealth(businessMetrics, billingHealth, securityMetrics, systemPerformance)
  }
}

/**
 * Calculate overall system health score
 */
function calculateOverallHealth(
  business: BusinessMetrics,
  billing: BillingHealth,
  security: SecurityMetrics,
  performance: SystemPerformance
): { score: number; status: 'healthy' | 'warning' | 'critical'; issues: string[] } {
  let score = 100
  const issues: string[] = []

  // Business health checks
  if (business.paymentSuccessRate < 95) {
    score -= 10
    issues.push(`Low payment success rate: ${business.paymentSuccessRate.toFixed(1)}%`)
  }
  
  if (business.churnRate > 10) {
    score -= 15
    issues.push(`High churn rate: ${business.churnRate.toFixed(1)}%`)
  }

  // Billing health checks
  if (billing.webhookSuccess < 98) {
    score -= 20
    issues.push(`Webhook failures detected: ${(100 - billing.webhookSuccess).toFixed(1)}% failure rate`)
  }

  if (billing.subscriptionsInGrace > business.activeSubscriptions * 0.05) {
    score -= 10
    issues.push(`High number of subscriptions in grace period: ${billing.subscriptionsInGrace}`)
  }

  // Security checks
  if (security.authenticationFailures > 100) {
    score -= 15
    issues.push(`High authentication failures: ${security.authenticationFailures}`)
  }

  if (security.rateLimitViolations > 50) {
    score -= 10
    issues.push(`High rate limit violations: ${security.rateLimitViolations}`)
  }

  // Performance checks
  if (performance.databaseHealth.slowQueries > 10) {
    score -= 10
    issues.push(`Slow database queries detected: ${performance.databaseHealth.slowQueries}`)
  }

  if (performance.databaseHealth.indexUsage < 80) {
    score -= 5
    issues.push(`Low index usage: ${performance.databaseHealth.indexUsage}%`)
  }

  // Determine status
  let status: 'healthy' | 'warning' | 'critical'
  if (score >= 90) {
    status = 'healthy'
  } else if (score >= 70) {
    status = 'warning'
  } else {
    status = 'critical'
  }

  return { score: Math.max(0, score), status, issues }
}
