# Schopio Manual Billing System Documentation

## Overview

Schopio has transitioned from Razorpay subscription-based billing to a **Manual Monthly Billing System** due to practical limitations with subscription payments for large amounts (₹15,000+ UPI limit, limited debit card support). This system provides schools with flexible payment options while maintaining automated billing management.

## System Architecture

### Manual Billing Workflow

```
1. <PERSON><PERSON> creates subscription → 2. System generates monthly bills → 3. School receives due date notification
                                                ↓
6. <PERSON>min confirms payment ← 5. School makes payment ← 4. Grace period (3 days) + 2% daily penalty
```

## Key Features

### 1. **Due Date Management**
- Monthly billing cycle with clear due dates
- 3-day grace period after due date
- Automated penalty calculation (2% per day after grace period)
- Email/SMS notifications for upcoming and overdue payments

### 2. **Payment Methods**
- **UPI**: Full support for any amount (no ₹15,000 limit)
- **Debit Cards**: All banks supported
- **Credit Cards**: Full support
- **Net Banking**: All major banks
- **Wallets**: All popular wallets

### 3. **Penalty System**
- Grace period: 3 days after due date
- Penalty rate: 2% per day (compounding)
- Automatic penalty calculation and addition to next bill
- Clear penalty breakdown for transparency

### 4. **Admin Management**
- Real-time payment tracking
- Overdue account management
- Manual payment confirmation
- Penalty adjustment capabilities
- Comprehensive reporting

## Database Schema Changes

### Enhanced `billing_subscriptions` Table

```sql
-- New fields for manual billing
ALTER TABLE billing_subscriptions ADD COLUMN:
- due_date DATE NOT NULL
- grace_period_days INTEGER DEFAULT 3
- penalty_rate DECIMAL(5,2) DEFAULT 2.00
- current_penalty_amount DECIMAL(10,2) DEFAULT 0.00
- last_payment_date DATE
- payment_status ENUM('pending', 'paid', 'overdue', 'grace_period') DEFAULT 'pending'
- next_billing_date DATE
- auto_penalty_enabled BOOLEAN DEFAULT true
```

### New `billing_transactions` Table

```sql
CREATE TABLE billing_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES billing_subscriptions(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  penalty_amount DECIMAL(10,2) DEFAULT 0.00,
  total_amount DECIMAL(10,2) NOT NULL,
  due_date DATE NOT NULL,
  payment_date TIMESTAMP,
  razorpay_payment_id VARCHAR(255),
  razorpay_order_id VARCHAR(255),
  payment_method VARCHAR(50),
  status ENUM('pending', 'paid', 'failed', 'overdue') DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Endpoints

### School Portal APIs

#### 1. Get Billing Information
```
GET /api/school/billing
Response: {
  subscription: { id, schoolName, monthlyAmount, studentCount },
  currentBill: { amount, dueDate, status, penaltyAmount },
  paymentHistory: [...],
  nextBillingDate: "2025-02-01"
}
```

#### 2. Create Payment Order
```
POST /api/school/create-payment-order
Body: { amount, billId }
Response: { 
  razorpayOrderId, 
  amount, 
  currency: "INR",
  keyId: "rzp_test_xxx"
}
```

#### 3. Verify Payment
```
POST /api/school/verify-payment
Body: { 
  razorpayPaymentId, 
  razorpayOrderId, 
  razorpaySignature,
  billId 
}
Response: { success: true, paymentId }
```

### Admin Portal APIs

#### 1. Get All Billing Overview
```
GET /api/admin/billing/overview
Response: {
  totalSubscriptions: 150,
  pendingPayments: 25,
  overdueAccounts: 8,
  totalPendingAmount: 2400000,
  totalPenalties: 48000
}
```

#### 2. Get Overdue Accounts
```
GET /api/admin/billing/overdue
Response: [
  {
    schoolId, schoolName, amount, dueDate, 
    daysOverdue, penaltyAmount, totalAmount
  }
]
```

#### 3. Confirm Manual Payment
```
POST /api/admin/billing/confirm-payment
Body: { 
  subscriptionId, 
  amount, 
  paymentMethod: "manual",
  notes: "Cash payment received"
}
```

## Frontend Implementation

### School Portal Billing Page

#### Key Components:
1. **Current Bill Display**
   - Amount due
   - Due date with countdown
   - Grace period status
   - Penalty calculation (if applicable)

2. **Payment Button**
   - Razorpay integration for manual payments
   - All payment methods available
   - Real-time payment processing

3. **Payment History**
   - Past payments with dates
   - Payment methods used
   - Receipt downloads

#### Sample UI Structure:
```jsx
<BillingDashboard>
  <CurrentBillCard>
    <BillAmount>₹48,000</BillAmount>
    <DueDate>Due: Jan 31, 2025</DueDate>
    <GracePeriodStatus />
    <PenaltyCalculator />
    <PayNowButton />
  </CurrentBillCard>
  
  <PaymentHistory>
    <PaymentRecord />
  </PaymentHistory>
</BillingDashboard>
```

### Admin Portal Billing Management

#### Key Features:
1. **Dashboard Overview**
   - Total pending payments
   - Overdue accounts count
   - Revenue analytics
   - Penalty collections

2. **Account Management**
   - Individual school billing status
   - Manual payment confirmation
   - Penalty adjustments
   - Payment reminders

3. **Reporting**
   - Monthly collection reports
   - Overdue account analysis
   - Penalty trend analysis

## Razorpay Integration

### Manual Payment Implementation

```javascript
// Create Razorpay order for manual payment
const options = {
  key: "rzp_test_7sRTrImzZvG17j",
  amount: totalAmount * 100, // Amount in paise
  currency: "INR",
  name: "Schopio",
  description: `Monthly Subscription - ${schoolName}`,
  order_id: razorpayOrderId,
  handler: function(response) {
    // Verify payment on server
    verifyPayment(response);
  },
  prefill: {
    name: schoolName,
    email: schoolEmail,
    contact: schoolPhone
  },
  theme: {
    color: "#3399cc"
  }
};

const rzp = new Razorpay(options);
rzp.open();
```

### Payment Verification

```javascript
// Server-side payment verification
const crypto = require('crypto');

function verifyPayment(paymentData) {
  const { razorpay_payment_id, razorpay_order_id, razorpay_signature } = paymentData;
  
  const generated_signature = crypto
    .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
    .update(razorpay_order_id + "|" + razorpay_payment_id)
    .digest('hex');
    
  if (generated_signature === razorpay_signature) {
    // Payment is authentic - update database
    updatePaymentStatus(razorpay_payment_id, 'paid');
    return { success: true };
  }
  
  return { success: false, error: 'Invalid signature' };
}
```

## Automated Systems

### 1. Penalty Calculation Service

```javascript
// Daily cron job to calculate penalties
async function calculateDailyPenalties() {
  const overdueSubscriptions = await getOverdueSubscriptions();
  
  for (const subscription of overdueSubscriptions) {
    const daysOverdue = calculateDaysOverdue(subscription.due_date);
    const gracePeriodExpired = daysOverdue > subscription.grace_period_days;
    
    if (gracePeriodExpired) {
      const penaltyDays = daysOverdue - subscription.grace_period_days;
      const dailyPenalty = subscription.monthly_amount * (subscription.penalty_rate / 100);
      const totalPenalty = dailyPenalty * penaltyDays;
      
      await updateSubscriptionPenalty(subscription.id, totalPenalty);
    }
  }
}
```

### 2. Notification Service

```javascript
// Send due date reminders
async function sendPaymentReminders() {
  // 7 days before due date
  await sendReminderEmails('upcoming', 7);
  
  // 3 days before due date
  await sendReminderEmails('urgent', 3);
  
  // On due date
  await sendReminderEmails('due_today', 0);
  
  // After grace period
  await sendOverdueNotifications();
}
```

## Benefits of Manual Billing System

### For Schools:
1. **Payment Flexibility**: All payment methods available
2. **No Amount Restrictions**: Pay any amount via UPI/debit cards
3. **Clear Billing**: Transparent due dates and penalty calculations
4. **Grace Period**: 3-day buffer for payment processing
5. **Professional Experience**: No payment method limitations

### For Schopio:
1. **Higher Success Rate**: No payment method restrictions
2. **Better Cash Flow**: Predictable monthly collections
3. **Reduced Support**: Clear billing process reduces queries
4. **Scalable**: Works for any subscription amount
5. **Professional Image**: No "unprofessional" payment limitations

## Migration from Subscription System

### Steps Completed:
1. ✅ Database schema updated to support manual billing
2. ✅ All endpoints migrated from `subscriptions` to `billing_subscriptions`
3. ✅ Razorpay subscription references removed
4. ✅ Manual payment integration implemented

### Next Steps:
1. Implement penalty calculation system
2. Create admin billing management interface
3. Update school portal billing page
4. Set up automated notification system
5. Create comprehensive reporting dashboard

## Conclusion

The Manual Billing System provides a professional, flexible, and scalable solution for Schopio's school management platform. By removing the limitations of Razorpay subscriptions, schools can now pay using their preferred payment methods without restrictions, while Schopio maintains full control over billing cycles and penalty management.

This system is designed to handle the Indian market's preference for UPI and debit card payments while providing the reliability and professionalism expected from an enterprise SaaS platform.
