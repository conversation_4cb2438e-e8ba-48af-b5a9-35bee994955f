/**
 * Software Request Workflow Audit Script
 * Tests the complete software request workflow to identify issues
 */

const { neon } = require('@neondatabase/serverless')

const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const sql = neon(connectionString)

async function auditSoftwareRequestWorkflow() {
  try {
    console.log('🔍 Auditing Software Request Workflow...')

    // 1. Check existing software requests
    console.log('\n📋 Checking existing software requests...')
    const existingRequests = await sql`
      SELECT 
        sr.id, sr.request_type, sr.status, sr.student_count, sr.faculty_count,
        sr.average_monthly_fee, sr.calculated_average_fee, sr.terms_accepted,
        sr.created_at, sr.approved_at, sr.activated_at,
        c.school_name, c.email, c.status as client_status
      FROM software_requests sr
      LEFT JOIN clients c ON sr.client_id = c.id
      ORDER BY sr.created_at DESC
      LIMIT 10
    `

    console.log(`Found ${existingRequests.length} software requests:`)
    existingRequests.forEach((req, index) => {
      console.log(`  ${index + 1}. ${req.school_name || 'Unknown School'} - ${req.request_type} (${req.status})`)
      console.log(`     Students: ${req.student_count}, Fee: ₹${req.average_monthly_fee || req.calculated_average_fee || 'Not set'}`)
      console.log(`     Created: ${req.created_at}, Approved: ${req.approved_at || 'Not approved'}`)
    })

    // 2. Check for accepted requests that might not be showing properly
    console.log('\n🔍 Checking accepted/approved requests...')
    const acceptedRequests = await sql`
      SELECT 
        sr.id, sr.request_type, sr.status, sr.student_count,
        sr.average_monthly_fee, sr.calculated_average_fee,
        sr.approved_at, sr.activated_at,
        c.school_name, c.status as client_status,
        bs.id as subscription_id, bs.monthly_amount, bs.status as subscription_status
      FROM software_requests sr
      LEFT JOIN clients c ON sr.client_id = c.id
      LEFT JOIN billing_subscriptions bs ON c.id = bs.client_id
      WHERE sr.status IN ('approved', 'activated', 'completed')
      ORDER BY sr.approved_at DESC
    `

    console.log(`Found ${acceptedRequests.length} accepted requests:`)
    acceptedRequests.forEach((req, index) => {
      console.log(`  ${index + 1}. ${req.school_name} - ${req.request_type} (${req.status})`)
      console.log(`     Fee: ₹${req.average_monthly_fee || req.calculated_average_fee || 'Not set'}`)
      console.log(`     Subscription: ${req.subscription_id ? `₹${req.monthly_amount} (${req.subscription_status})` : 'No subscription'}`)
      console.log(`     Approved: ${req.approved_at || 'Not approved'}, Activated: ${req.activated_at || 'Not activated'}`)
    })

    // 3. Check fee structure issues
    console.log('\n💰 Checking fee structure issues...')
    const feeIssues = await sql`
      SELECT 
        sr.id, sr.request_type, sr.status,
        sr.average_monthly_fee, sr.calculated_average_fee,
        sr.student_count, sr.faculty_count,
        c.school_name,
        CASE 
          WHEN sr.average_monthly_fee IS NULL AND sr.calculated_average_fee IS NULL THEN 'No fee set'
          WHEN sr.average_monthly_fee IS NOT NULL AND sr.calculated_average_fee IS NOT NULL THEN 'Both fees set'
          WHEN sr.average_monthly_fee IS NULL THEN 'Only calculated fee'
          WHEN sr.calculated_average_fee IS NULL THEN 'Only average fee'
        END as fee_status
      FROM software_requests sr
      LEFT JOIN clients c ON sr.client_id = c.id
      WHERE sr.status != 'rejected'
      ORDER BY sr.created_at DESC
    `

    console.log('Fee structure analysis:')
    const feeStatusCounts = {}
    feeIssues.forEach(req => {
      feeStatusCounts[req.fee_status] = (feeStatusCounts[req.fee_status] || 0) + 1
      if (req.fee_status === 'No fee set' || req.fee_status === 'Both fees set') {
        console.log(`  ⚠️  ${req.school_name} (${req.request_type}): ${req.fee_status}`)
      }
    })

    console.log('\nFee status summary:')
    Object.entries(feeStatusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count} requests`)
    })

    // 4. Check payment status issues
    console.log('\n💳 Checking payment status issues...')
    const paymentStatusIssues = await sql`
      SELECT 
        sr.id, sr.request_type, sr.status as request_status,
        c.school_name,
        bs.id as subscription_id, bs.status as subscription_status,
        bi.id as invoice_id, bi.status as invoice_status, bi.total_amount,
        bp.id as payment_id, bp.status as payment_status, bp.amount as payment_amount
      FROM software_requests sr
      LEFT JOIN clients c ON sr.client_id = c.id
      LEFT JOIN billing_subscriptions bs ON c.id = bs.client_id
      LEFT JOIN billing_invoices bi ON bs.id = bi.subscription_id
      LEFT JOIN billing_payments bp ON bi.id = bp.invoice_id
      WHERE sr.status IN ('approved', 'activated', 'completed')
      ORDER BY sr.created_at DESC
    `

    console.log('Payment status analysis:')
    const paymentIssues = []
    paymentStatusIssues.forEach(req => {
      let issue = null
      if (req.subscription_id && !req.invoice_id) {
        issue = 'Has subscription but no invoice'
      } else if (req.invoice_id && !req.payment_id) {
        issue = 'Has invoice but no payment'
      } else if (req.payment_id && req.payment_status === 'pending') {
        issue = 'Payment status always pending'
      }

      if (issue) {
        paymentIssues.push({ ...req, issue })
        console.log(`  ⚠️  ${req.school_name}: ${issue}`)
        console.log(`     Request: ${req.request_status}, Subscription: ${req.subscription_status || 'None'}`)
        console.log(`     Invoice: ${req.invoice_status || 'None'}, Payment: ${req.payment_status || 'None'}`)
      }
    })

    // 5. Create test software request to verify workflow
    console.log('\n🧪 Creating test software request...')
    const timestamp = Date.now().toString().slice(-4)
    
    // Get admin user
    const [adminUser] = await sql`SELECT id FROM admin_users LIMIT 1`
    if (!adminUser) {
      console.error('❌ No admin user found')
      return
    }

    // Create test client
    const [testClient] = await sql`
      INSERT INTO clients (school_code, school_name, email, phone, status, actual_student_count)
      VALUES (${`TSR${timestamp}`}, 'Test Software Request School', ${`test${timestamp}@softreq.edu`}, '9876543210', 'pending', 150)
      RETURNING id, school_name
    `

    // Create test software request
    const [testRequest] = await sql`
      INSERT INTO software_requests (
        client_id, request_type, status, student_count, faculty_count,
        complete_address, contact_number, primary_email,
        average_monthly_fee, calculated_average_fee, terms_accepted
      )
      VALUES (
        ${testClient.id}, 'production', 'pending', 150, 25,
        'Test Address, Test City, Test State - 123456',
        '9876543210', ${`test${timestamp}@softreq.edu`},
        '11250.00', '11250.00', true
      )
      RETURNING id, request_type, status
    `

    console.log(`✅ Created test software request:`)
    console.log(`   Client: ${testClient.school_name} (${testClient.id})`)
    console.log(`   Request: ${testRequest.id} (${testRequest.request_type} - ${testRequest.status})`)
    console.log(`   Fee: ₹11,250/month (₹75 per student × 150 students)`)

    // 6. Summary of findings
    console.log('\n📊 AUDIT SUMMARY:')
    console.log(`   Total software requests: ${existingRequests.length}`)
    console.log(`   Accepted requests: ${acceptedRequests.length}`)
    console.log(`   Payment issues found: ${paymentIssues.length}`)
    
    console.log('\n🎯 ISSUES IDENTIFIED:')
    if (acceptedRequests.length === 0) {
      console.log('   ❌ No accepted software requests found - approval workflow may be broken')
    }
    
    const noSubscriptionCount = acceptedRequests.filter(req => !req.subscription_id).length
    if (noSubscriptionCount > 0) {
      console.log(`   ❌ ${noSubscriptionCount} accepted requests without subscriptions`)
    }

    const noFeeCount = feeIssues.filter(req => req.fee_status === 'No fee set').length
    if (noFeeCount > 0) {
      console.log(`   ❌ ${noFeeCount} requests without fee structure`)
    }

    if (paymentIssues.length > 0) {
      console.log(`   ❌ ${paymentIssues.length} requests with payment status issues`)
    }

    console.log('\n📝 RECOMMENDED ACTIONS:')
    console.log('   1. Test software request approval workflow with test request ID:', testRequest.id)
    console.log('   2. Verify fee structure calculation and display')
    console.log('   3. Check payment status updates in billing system')
    console.log('   4. Audit subscription creation during approval process')

    console.log('\n✅ Software request workflow audit completed!')

  } catch (error) {
    console.error('❌ Audit failed:', error)
    process.exit(1)
  }
}

auditSoftwareRequestWorkflow()
