#!/bin/bash

# 🚀 Schopio Production Deployment Script
# This script helps deploy the Schopio platform to production

set -e  # Exit on any error

echo "🚀 Starting Schopio Production Deployment"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        print_error "git is not installed"
        exit 1
    fi
    
    print_success "All dependencies are installed"
}

# Verify environment variables
check_environment() {
    print_status "Checking environment variables..."
    
    required_vars=(
        "DATABASE_URL"
        "DIRECT_URL"
        "JWT_SECRET"
        "ADMIN_JWT_SECRET"
        "RAZORPAY_KEY_ID"
        "RAZORPAY_KEY_SECRET"
        "RESEND_API_KEY"
        "FROM_EMAIL"
        "FROM_NAME"
        "FRONTEND_URL"
    )
    
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        print_warning "Please set these variables in your .env.local file"
        exit 1
    fi
    
    print_success "All required environment variables are set"
}

# Run tests
run_tests() {
    print_status "Running TypeScript compilation check..."
    
    if npm run type-check; then
        print_success "TypeScript compilation passed"
    else
        print_error "TypeScript compilation failed"
        exit 1
    fi
    
    print_status "Running linting..."
    
    if npm run lint; then
        print_success "Linting passed"
    else
        print_warning "Linting has warnings (non-critical)"
    fi
}

# Build the application
build_application() {
    print_status "Building application for production..."
    
    if npm run build; then
        print_success "Application built successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Database setup
setup_database() {
    print_status "Setting up database..."
    
    # Check if database is accessible
    print_status "Checking database connection..."
    
    # Run database migrations if needed
    print_status "Running database migrations..."
    
    # Note: Add actual migration commands here when available
    # npm run db:migrate
    
    print_success "Database setup completed"
}

# Security checks
security_checks() {
    print_status "Running security checks..."
    
    # Check for sensitive data in environment
    if grep -r "password\|secret\|key" .env* 2>/dev/null | grep -v ".env.example"; then
        print_warning "Found potential sensitive data in environment files"
    fi
    
    # Check for hardcoded secrets in code
    if grep -r "sk_live\|rzp_live" src/ app/ 2>/dev/null; then
        print_error "Found hardcoded production secrets in code"
        exit 1
    fi
    
    print_success "Security checks passed"
}

# Performance optimization
optimize_performance() {
    print_status "Optimizing performance..."
    
    # Check bundle sizes
    print_status "Analyzing bundle sizes..."
    
    # Check for large dependencies
    print_status "Checking for large dependencies..."
    
    print_success "Performance optimization completed"
}

# Backup current deployment (if exists)
backup_current() {
    print_status "Creating backup of current deployment..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    
    if [ -d ".next" ]; then
        mkdir -p "$BACKUP_DIR"
        cp -r .next "$BACKUP_DIR/"
        print_success "Backup created at $BACKUP_DIR"
    else
        print_status "No existing deployment to backup"
    fi
}

# Deploy to production
deploy() {
    print_status "Deploying to production..."
    
    # Start the application
    print_status "Starting production server..."
    
    # Note: Adjust this based on your deployment platform
    # For Vercel: vercel --prod
    # For PM2: pm2 start ecosystem.config.js --env production
    # For Docker: docker-compose up -d
    
    print_success "Deployment completed"
}

# Post-deployment checks
post_deployment_checks() {
    print_status "Running post-deployment checks..."
    
    # Check if application is responding
    print_status "Checking application health..."
    
    # Check database connectivity
    print_status "Verifying database connectivity..."
    
    # Check external services
    print_status "Verifying external service connectivity..."
    
    print_success "Post-deployment checks completed"
}

# Rollback function
rollback() {
    print_error "Deployment failed. Rolling back..."
    
    # Restore from backup if available
    LATEST_BACKUP=$(ls -t backups/ | head -n1)
    
    if [ -n "$LATEST_BACKUP" ]; then
        print_status "Restoring from backup: $LATEST_BACKUP"
        cp -r "backups/$LATEST_BACKUP/.next" ./
        print_success "Rollback completed"
    else
        print_error "No backup available for rollback"
    fi
}

# Main deployment process
main() {
    echo "🚀 Schopio Production Deployment"
    echo "================================"
    echo ""
    
    # Set trap for error handling
    trap rollback ERR
    
    # Run deployment steps
    check_dependencies
    check_environment
    security_checks
    backup_current
    run_tests
    build_application
    setup_database
    optimize_performance
    deploy
    post_deployment_checks
    
    echo ""
    print_success "🎉 Deployment completed successfully!"
    echo ""
    echo "📋 Next Steps:"
    echo "  1. Monitor application logs for any issues"
    echo "  2. Test critical user workflows"
    echo "  3. Verify payment processing functionality"
    echo "  4. Check email delivery system"
    echo "  5. Monitor system performance"
    echo ""
    echo "📞 Support:"
    echo "  - Check logs: npm run logs"
    echo "  - Monitor health: curl $FRONTEND_URL/api/health"
    echo "  - Admin dashboard: $FRONTEND_URL/admin/dashboard"
    echo ""
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "check")
        check_dependencies
        check_environment
        security_checks
        print_success "Pre-deployment checks passed"
        ;;
    "build")
        run_tests
        build_application
        print_success "Build completed"
        ;;
    "rollback")
        rollback
        ;;
    "help")
        echo "Usage: $0 [deploy|check|build|rollback|help]"
        echo ""
        echo "Commands:"
        echo "  deploy   - Full deployment process (default)"
        echo "  check    - Run pre-deployment checks only"
        echo "  build    - Build application only"
        echo "  rollback - Rollback to previous deployment"
        echo "  help     - Show this help message"
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
