import { Metadata } from 'next'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { 
  Building2, 
  Users, 
  BookOpen, 
  CreditCard, 
  BarChart3, 
  Settings, 
  Database, 
  CheckCircle,
  ArrowRight,
  Star,
  Award,
  Zap,
  Shield,
  Clock
} from 'lucide-react'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Best School ERP Software in India | Complete Educational ERP System | Schopio',
  description: 'Schopio is the leading school ERP software in India. Complete educational ERP system with integrated modules for administration, academics, finance, and operations. Trusted by 500+ schools.',
  keywords: 'school ERP software, school ERP system, educational ERP, ERP system for schools, best school ERP software India, educational ERP system, school enterprise resource planning, ERP software education',
  openGraph: {
    title: 'Best School ERP Software in India | Complete Educational ERP System | Schopio',
    description: 'Schopio is the leading school ERP software in India. Complete educational ERP system with integrated modules for administration, academics, finance, and operations. Trusted by 500+ schools.',
    type: 'website',
  },
}

export default function SchoolERPSoftwarePage() {
  const erpModules = [
    {
      icon: Users,
      title: "Human Resource Management",
      description: "Complete HR module for staff management, payroll, and performance tracking",
      features: ["Staff database", "Payroll management", "Performance tracking", "Leave management"]
    },
    {
      icon: BookOpen,
      title: "Academic Management",
      description: "Comprehensive academic module with curriculum, timetable, and examination management",
      features: ["Curriculum planning", "Timetable management", "Exam scheduling", "Grade management"]
    },
    {
      icon: CreditCard,
      title: "Financial Management",
      description: "Complete financial module with accounting, billing, and budget management",
      features: ["Fee collection", "Accounting system", "Budget planning", "Financial reports"]
    },
    {
      icon: Building2,
      title: "Infrastructure Management",
      description: "Facility and asset management with maintenance scheduling and inventory control",
      features: ["Asset tracking", "Maintenance scheduling", "Inventory management", "Facility booking"]
    },
    {
      icon: BarChart3,
      title: "Analytics & Reporting",
      description: "Advanced analytics module with comprehensive reporting and business intelligence",
      features: ["Performance analytics", "Custom dashboards", "Automated reports", "Data insights"]
    },
    {
      icon: Database,
      title: "Data Management",
      description: "Centralized data management with backup, security, and integration capabilities",
      features: ["Data backup", "Security protocols", "API integration", "Data migration"]
    }
  ]

  const erpBenefits = [
    {
      icon: Zap,
      title: "Streamlined Operations",
      description: "Integrate all school operations in one unified ERP system"
    },
    {
      icon: Clock,
      title: "Time Efficiency",
      description: "Reduce administrative time by 60% with automated processes"
    },
    {
      icon: Shield,
      title: "Data Security",
      description: "Enterprise-grade security with role-based access control"
    },
    {
      icon: Award,
      title: "Scalable Solution",
      description: "ERP system that grows with your educational institution"
    }
  ]

  const comparison = [
    {
      feature: "Integrated Modules",
      traditional: "Separate systems",
      erp: "Unified platform"
    },
    {
      feature: "Data Management",
      traditional: "Manual processes",
      erp: "Automated workflows"
    },
    {
      feature: "Reporting",
      traditional: "Limited reports",
      erp: "Comprehensive analytics"
    },
    {
      feature: "Cost Efficiency",
      traditional: "Multiple licenses",
      erp: "Single solution"
    }
  ]

  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-6">
              <Building2 className="w-4 h-4" />
              Complete Educational ERP System
            </div>
            <h1 className="text-5xl lg:text-7xl font-bold text-slate-900 mb-6">
              Best School ERP 
              <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent"> Software</span>
            </h1>
            <p className="text-xl text-slate-600 leading-relaxed mb-8">
              Schopio is India's leading school ERP software with complete educational ERP system. 
              Integrated modules for administration, academics, finance, HR, and operations management. 
              Transform your school with our comprehensive ERP solution.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo">
                <Button size="lg" className="bg-emerald-600 hover:bg-emerald-700">
                  Get ERP Demo <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/packages">
                <Button variant="outline" size="lg">
                  ERP Pricing
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* ERP Modules Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-4">
              Complete School ERP Modules
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Integrated ERP modules designed specifically for educational institutions
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {erpModules.map((module, index) => (
              <Card key={index} className="border-2 hover:border-emerald-200 transition-colors">
                <CardHeader>
                  <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
                    <module.icon className="w-6 h-6 text-emerald-600" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900">{module.title}</h3>
                  <p className="text-slate-600">{module.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {module.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center gap-2 text-sm text-slate-600">
                        <CheckCircle className="w-4 h-4 text-emerald-500" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* ERP Benefits Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-900 mb-4">
              Why Choose School ERP Software?
            </h2>
            <p className="text-xl text-slate-600">
              Transform your educational institution with integrated ERP system
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {erpBenefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <benefit.icon className="w-8 h-8 text-emerald-600" />
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">{benefit.title}</h3>
                <p className="text-slate-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Comparison Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-900 mb-4">
              Traditional Systems vs School ERP Software
            </h2>
            <p className="text-xl text-slate-600">
              See the difference an integrated ERP system makes
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b-2">
                    <th className="text-left py-4 px-6 text-slate-900 font-bold">Feature</th>
                    <th className="text-center py-4 px-6 text-slate-600">Traditional Systems</th>
                    <th className="text-center py-4 px-6 text-emerald-600 font-bold">School ERP Software</th>
                  </tr>
                </thead>
                <tbody>
                  {comparison.map((item, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-4 px-6 font-semibold text-slate-900">{item.feature}</td>
                      <td className="py-4 px-6 text-center text-slate-600">{item.traditional}</td>
                      <td className="py-4 px-6 text-center text-emerald-600 font-semibold">{item.erp}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-emerald-600 to-blue-600">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Implement School ERP Software?
            </h2>
            <p className="text-xl text-emerald-100 mb-8">
              Join 500+ schools using Schopio's comprehensive ERP system. 
              Transform your educational institution with our integrated solution!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo">
                <Button size="lg" className="bg-white text-emerald-600 hover:bg-gray-100">
                  Get ERP Demo <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/packages">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-emerald-600">
                  View ERP Pricing
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
