# Email Templates & Notification System

## 📧 Email Template Architecture

Comprehensive email system using Resend API with responsive templates, automated triggers, and multi-language support.

### Resend Integration
```typescript
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  from?: string;
  replyTo?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

class EmailService {
  private resend: Resend;
  private defaultFrom: string;

  constructor() {
    this.resend = new Resend(process.env.RESEND_API_KEY);
    this.defaultFrom = `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`;
  }

  async sendEmail(options: EmailOptions) {
    try {
      const result = await this.resend.emails.send({
        from: options.from || this.defaultFrom,
        to: options.to,
        subject: options.subject,
        html: options.html,
        replyTo: options.replyTo || process.env.FROM_EMAIL,
        attachments: options.attachments
      });

      console.log('Email sent successfully:', result.data?.id);
      return result;
    } catch (error) {
      console.error('Failed to send email:', error);
      throw error;
    }
  }

  async sendTemplateEmail(templateName: string, to: string, data: Record<string, any>) {
    const template = await this.getTemplate(templateName);
    const html = this.renderTemplate(template, data);

    return this.sendEmail({
      to,
      subject: data.subject || template.subject,
      html
    });
  }

  private renderTemplate(template: string, data: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] || match;
    });
  }

  private async getTemplate(templateName: string): Promise<{ subject: string; html: string }> {
    // Load template from file system or database
    const templates = {
      'welcome_lead': {
        subject: 'Welcome to Schopio!',
        html: welcomeLeadTemplate
      },
      'demo_confirmation': {
        subject: 'Demo Scheduled Successfully!',
        html: demoConfirmationTemplate
      },
      // ... other templates
    };

    return templates[templateName] || templates['welcome_lead'];
  }
}

const emailService = new EmailService();
```

## 🎨 Template Design System

### Brand Guidelines
```css
/* Email Brand Colors */
:root {
  --primary-blue: #2563eb;
  --success-green: #10b981;
  --warning-orange: #f59e0b;
  --error-red: #ef4444;
  --neutral-gray: #6b7280;
  --background: #f9fafb;
}

/* Typography */
.email-heading { font-family: 'Inter', sans-serif; font-weight: 600; }
.email-body { font-family: 'Inter', sans-serif; font-weight: 400; }
.email-button { font-weight: 500; border-radius: 8px; padding: 12px 24px; }
```

### Responsive Email Template Base
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        /* Reset styles */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; }
        
        /* Container */
        .email-container { max-width: 600px; margin: 0 auto; background: #ffffff; }
        .email-header { background: #2563eb; padding: 20px; text-align: center; }
        .email-body { padding: 30px 20px; }
        .email-footer { background: #f9fafb; padding: 20px; text-align: center; }
        
        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-body { padding: 20px 15px; }
            .email-button { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <img src="{{logoUrl}}" alt="Schopio" style="height: 40px;">
            <h1 style="color: white; margin-top: 10px;">{{companyName}}</h1>
        </div>
        <div class="email-body">
            {{content}}
        </div>
        <div class="email-footer">
            <p style="color: #6b7280; font-size: 14px;">
                © 2025 Schopio. All rights reserved.
            </p>
            <p style="color: #6b7280; font-size: 12px; margin-top: 10px;">
                <a href="{{unsubscribeUrl}}">Unsubscribe</a> | 
                <a href="{{privacyUrl}}">Privacy Policy</a> | 
                <a href="{{supportUrl}}">Support</a>
            </p>
        </div>
    </div>
</body>
</html>
```

## 📋 Lead Management Templates

### 1. Welcome Email (New Lead)
```html
<!-- Template: welcome_lead.html -->
<h2 style="color: #2563eb; margin-bottom: 20px;">Welcome to Schopio!</h2>

<p>Dear {{contactPerson}},</p>

<p>Thank you for your interest in our comprehensive school management solution. We're excited to help {{schoolName}} streamline operations and enhance educational outcomes.</p>

<div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3 style="color: #2563eb;">What makes us different:</h3>
    <ul style="margin-left: 20px; color: #374151;">
        <li>Complete functionality in basic plan (all 8+ modules)</li>
        <li>AI-powered insights with Gemma-3.27B</li>
        <li>Real-time IoT tracking</li>
        <li>Modern responsive UI</li>
    </ul>
</div>

<p>Our team will contact you within 24 hours to schedule a personalized demo.</p>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{demoBookingUrl}}" style="background: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
        Schedule Demo Now
    </a>
</div>

<p>Best regards,<br>
The Schopio Team</p>
```

### 2. Demo Confirmation
```html
<!-- Template: demo_confirmation.html -->
<h2 style="color: #10b981; margin-bottom: 20px;">Demo Scheduled Successfully!</h2>

<p>Dear {{contactPerson}},</p>

<p>Your demo for {{schoolName}} has been confirmed:</p>

<div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981;">
    <h3 style="color: #065f46;">Demo Details</h3>
    <p><strong>Date:</strong> {{demoDate}}</p>
    <p><strong>Time:</strong> {{demoTime}}</p>
    <p><strong>Duration:</strong> 45 minutes</p>
    <p><strong>Type:</strong> {{demoType}}</p>
    {{#if meetingLink}}
    <p><strong>Meeting Link:</strong> <a href="{{meetingLink}}">{{meetingLink}}</a></p>
    {{/if}}
</div>

<h3>What to expect:</h3>
<ul style="margin-left: 20px; color: #374151;">
    <li>Complete system walkthrough</li>
    <li>Live demonstration of key features</li>
    <li>Q&A session tailored to your needs</li>
    <li>Custom pricing discussion</li>
</ul>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{calendarLink}}" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
        Add to Calendar
    </a>
</div>
```

## 💰 Billing & Payment Templates

### 3. Invoice Generated
```html
<!-- Template: invoice_generated.html -->
<h2 style="color: #2563eb; margin-bottom: 20px;">New Invoice Generated</h2>

<p>Dear {{schoolName}} Team,</p>

<p>Your monthly invoice has been generated and is ready for payment.</p>

<div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0;">
    <h3 style="color: #1e293b;">Invoice Details</h3>
    <table style="width: 100%; margin-top: 15px;">
        <tr><td><strong>Invoice Number:</strong></td><td>{{invoiceNumber}}</td></tr>
        <tr><td><strong>Billing Period:</strong></td><td>{{billingPeriod}}</td></tr>
        <tr><td><strong>Student Count:</strong></td><td>{{studentCount}}</td></tr>
        <tr><td><strong>Amount:</strong></td><td style="color: #2563eb; font-weight: bold;">₹{{totalAmount}}</td></tr>
        <tr><td><strong>Due Date:</strong></td><td>{{dueDate}}</td></tr>
    </table>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{paymentUrl}}" style="background: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block; margin-right: 10px;">
        Pay Now
    </a>
    <a href="{{invoicePdfUrl}}" style="background: #6b7280; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
        Download PDF
    </a>
</div>

<p style="color: #6b7280; font-size: 14px;">
    Payment can be made through our secure portal using UPI, Net Banking, or Credit/Debit Cards.
</p>
```

### 4. Payment Confirmation
```html
<!-- Template: payment_success.html -->
<h2 style="color: #10b981; margin-bottom: 20px;">Payment Received Successfully!</h2>

<p>Dear {{schoolName}} Team,</p>

<p>We have successfully received your payment. Thank you for your prompt payment!</p>

<div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981;">
    <h3 style="color: #065f46;">Payment Details</h3>
    <table style="width: 100%; margin-top: 15px;">
        <tr><td><strong>Payment ID:</strong></td><td>{{paymentId}}</td></tr>
        <tr><td><strong>Invoice Number:</strong></td><td>{{invoiceNumber}}</td></tr>
        <tr><td><strong>Amount Paid:</strong></td><td style="color: #10b981; font-weight: bold;">₹{{amountPaid}}</td></tr>
        <tr><td><strong>Payment Date:</strong></td><td>{{paymentDate}}</td></tr>
        <tr><td><strong>Payment Method:</strong></td><td>{{paymentMethod}}</td></tr>
    </table>
</div>

<p>Your subscription remains active and all services will continue uninterrupted.</p>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{receiptUrl}}" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
        Download Receipt
    </a>
</div>
```

### 5. Payment Reminder (Due Soon)
```html
<!-- Template: payment_due_soon.html -->
<h2 style="color: #f59e0b; margin-bottom: 20px;">Payment Due in 3 Days</h2>

<p>Dear {{schoolName}} Team,</p>

<p>This is a friendly reminder that your monthly subscription payment is due in 3 days.</p>

<div style="background: #fffbeb; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
    <h3 style="color: #92400e;">Payment Details</h3>
    <p><strong>Invoice Number:</strong> {{invoiceNumber}}</p>
    <p><strong>Amount Due:</strong> <span style="color: #f59e0b; font-weight: bold;">₹{{amountDue}}</span></p>
    <p><strong>Due Date:</strong> {{dueDate}}</p>
</div>

<p>To avoid any service interruption, please make your payment before the due date.</p>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{paymentUrl}}" style="background: #f59e0b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
        Pay Now
    </a>
</div>
```

## 🎫 Support System Templates

### 6. Support Ticket Created
```html
<!-- Template: ticket_created.html -->
<h2 style="color: #2563eb; margin-bottom: 20px;">Support Ticket Created</h2>

<p>Dear {{userName}},</p>

<p>Your support ticket has been created successfully. Our team will respond within 24 hours.</p>

<div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3 style="color: #1e40af;">Ticket Details</h3>
    <p><strong>Ticket ID:</strong> {{ticketId}}</p>
    <p><strong>Subject:</strong> {{ticketSubject}}</p>
    <p><strong>Priority:</strong> {{priority}}</p>
    <p><strong>Category:</strong> {{category}}</p>
    <p><strong>Created:</strong> {{createdDate}}</p>
</div>

<div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 15px 0;">
    <p><strong>Your Message:</strong></p>
    <p style="color: #374151; margin-top: 10px;">{{ticketDescription}}</p>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ticketUrl}}" style="background: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
        View Ticket
    </a>
</div>
```

### 7. Support Ticket Response
```html
<!-- Template: ticket_response.html -->
<h2 style="color: #10b981; margin-bottom: 20px;">Response to Your Support Ticket</h2>

<p>Dear {{userName}},</p>

<p>Our support team has responded to your ticket <strong>#{{ticketId}}</strong>.</p>

<div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3 style="color: #065f46;">{{supportAgentName}} replied:</h3>
    <p style="color: #374151; margin-top: 10px;">{{responseMessage}}</p>
    <p style="color: #6b7280; font-size: 14px; margin-top: 15px;">
        <strong>Responded on:</strong> {{responseDate}}
    </p>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ticketUrl}}" style="background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
        Reply to Ticket
    </a>
</div>
```

## 🔧 System Notification Templates

### 8. Account Created (School Portal)
```html
<!-- Template: account_created.html -->
<h2 style="color: #2563eb; margin-bottom: 20px;">Welcome to Your School Portal!</h2>

<p>Dear {{userName}},</p>

<p>Your school portal account has been created successfully. You can now access all features of our school management system.</p>

<div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3 style="color: #1e40af;">Login Details</h3>
    <p><strong>School Code:</strong> {{schoolCode}}</p>
    <p><strong>Email:</strong> {{userEmail}}</p>
    <p><strong>Portal URL:</strong> <a href="{{portalUrl}}">{{portalUrl}}</a></p>
</div>

<div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #f59e0b;">
    <p><strong>Important:</strong> Please change your password after first login for security.</p>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{loginUrl}}" style="background: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; display: inline-block;">
        Login to Portal
    </a>
</div>
```

This comprehensive email template system using Resend ensures professional, consistent communication across all user touchpoints with proper branding and responsive design.
