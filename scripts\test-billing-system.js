#!/usr/bin/env node

/**
 * Test Script for Automated Billing System
 * Verifies all components are working correctly
 */

const http = require('http')
const https = require('https')

class BillingSystemTester {
  constructor() {
    this.baseUrl = process.env.TEST_BASE_URL || 'http://localhost:3000'
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    }
  }

  async runAllTests() {
    console.log('🧪 Starting Automated Billing System Tests...')
    console.log(`🔗 Base URL: ${this.baseUrl}`)
    console.log('=' .repeat(60))

    try {
      // Test 1: System Initialization
      await this.testSystemInitialization()

      // Test 2: Health Monitoring
      await this.testHealthMonitoring()

      // Test 3: Service Status
      await this.testServiceStatus()

      // Test 4: Billing Scheduler Status
      await this.testBillingSchedulerStatus()

      // Test 5: Error Handling
      await this.testErrorHandling()

      // Summary
      this.printSummary()

    } catch (error) {
      console.error('💥 Test suite failed:', error)
      process.exit(1)
    }
  }

  async testSystemInitialization() {
    console.log('🔄 Test 1: System Initialization')
    
    try {
      const response = await this.makeRequest('/api/admin/system/init')
      
      this.assert(response.success === true, 'System initialization should succeed')
      this.assert(response.initialized === true, 'System should be initialized')
      this.assert(response.healthy === true, 'System should be healthy')
      this.assert(response.status && response.status.services, 'Should have service status')
      
      console.log('✅ System initialization test passed')
      this.recordTest('System Initialization', true)
      
    } catch (error) {
      console.error('❌ System initialization test failed:', error.message)
      this.recordTest('System Initialization', false, error.message)
    }
  }

  async testHealthMonitoring() {
    console.log('🔄 Test 2: Health Monitoring')
    
    try {
      const response = await this.makeRequest('/api/admin/health')
      
      this.assert(response.status, 'Should have health status')
      this.assert(response.services, 'Should have service health info')
      this.assert(response.metrics, 'Should have system metrics')
      this.assert(response.timestamp, 'Should have timestamp')
      
      console.log('✅ Health monitoring test passed')
      this.recordTest('Health Monitoring', true)
      
    } catch (error) {
      console.error('❌ Health monitoring test failed:', error.message)
      this.recordTest('Health Monitoring', false, error.message)
    }
  }

  async testServiceStatus() {
    console.log('🔄 Test 3: Service Status Check')
    
    try {
      const response = await this.makeRequest('/api/admin/system/init')
      
      if (response.status && response.status.services) {
        const services = response.status.services
        
        // Check billing scheduler
        if (services['billing-scheduler']) {
          this.assert(
            services['billing-scheduler'].healthy === true,
            'Billing scheduler should be healthy'
          )
        }
        
        // Check health monitor
        if (services['health-monitor']) {
          this.assert(
            services['health-monitor'].healthy === true,
            'Health monitor should be healthy'
          )
        }
        
        // Check missed bill detector
        if (services['missed-bill-detector']) {
          this.assert(
            services['missed-bill-detector'].healthy === true,
            'Missed bill detector should be healthy'
          )
        }
      }
      
      console.log('✅ Service status test passed')
      this.recordTest('Service Status', true)
      
    } catch (error) {
      console.error('❌ Service status test failed:', error.message)
      this.recordTest('Service Status', false, error.message)
    }
  }

  async testBillingSchedulerStatus() {
    console.log('🔄 Test 4: Billing Scheduler Status')
    
    try {
      const healthResponse = await this.makeRequest('/api/admin/health')
      
      this.assert(
        healthResponse.services && healthResponse.services.billing,
        'Should have billing service info'
      )
      
      this.assert(
        healthResponse.services.billing.scheduler === true,
        'Billing scheduler should be running'
      )
      
      console.log('✅ Billing scheduler status test passed')
      this.recordTest('Billing Scheduler Status', true)
      
    } catch (error) {
      console.error('❌ Billing scheduler status test failed:', error.message)
      this.recordTest('Billing Scheduler Status', false, error.message)
    }
  }

  async testErrorHandling() {
    console.log('🔄 Test 5: Error Handling')
    
    try {
      // Test invalid endpoint
      try {
        await this.makeRequest('/api/admin/invalid-endpoint')
        this.recordTest('Error Handling', false, 'Should have returned 404')
      } catch (error) {
        // This is expected - 404 error
        if (error.message.includes('404') || error.message.includes('Not Found')) {
          console.log('✅ Error handling test passed (404 handled correctly)')
          this.recordTest('Error Handling', true)
        } else {
          throw error
        }
      }
      
    } catch (error) {
      console.error('❌ Error handling test failed:', error.message)
      this.recordTest('Error Handling', false, error.message)
    }
  }

  async makeRequest(path) {
    return new Promise((resolve, reject) => {
      const url = new URL(path, this.baseUrl)
      const client = url.protocol === 'https:' ? https : http
      
      const req = client.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Billing-System-Tester/1.0'
        }
      }, (res) => {
        let data = ''
        
        res.on('data', (chunk) => {
          data += chunk
        })
        
        res.on('end', () => {
          try {
            if (res.statusCode >= 400) {
              reject(new Error(`HTTP ${res.statusCode}: ${data}`))
              return
            }
            
            const parsed = JSON.parse(data)
            resolve(parsed)
          } catch (parseError) {
            if (res.statusCode === 200) {
              // For simple text responses
              resolve({ text: data.trim() })
            } else {
              reject(new Error(`Failed to parse response: ${data}`))
            }
          }
        })
      })
      
      req.on('timeout', () => {
        req.destroy()
        reject(new Error('Request timeout'))
      })
      
      req.on('error', (error) => {
        reject(error)
      })
      
      req.end()
    })
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(`Assertion failed: ${message}`)
    }
  }

  recordTest(name, passed, error = null) {
    this.results.tests.push({ name, passed, error })
    if (passed) {
      this.results.passed++
    } else {
      this.results.failed++
    }
  }

  printSummary() {
    console.log('\n' + '=' .repeat(60))
    console.log('📊 TEST SUMMARY')
    console.log('=' .repeat(60))
    
    console.log(`✅ Passed: ${this.results.passed}`)
    console.log(`❌ Failed: ${this.results.failed}`)
    console.log(`📊 Total: ${this.results.tests.length}`)
    
    if (this.results.failed > 0) {
      console.log('\n❌ FAILED TESTS:')
      this.results.tests
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.error}`)
        })
    }
    
    console.log('\n' + '=' .repeat(60))
    
    if (this.results.failed === 0) {
      console.log('🎉 ALL TESTS PASSED - BILLING SYSTEM IS WORKING CORRECTLY!')
      process.exit(0)
    } else {
      console.log('💥 SOME TESTS FAILED - PLEASE CHECK THE SYSTEM')
      process.exit(1)
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new BillingSystemTester()
  tester.runAllTests().catch(error => {
    console.error('💥 Test execution failed:', error)
    process.exit(1)
  })
}

module.exports = BillingSystemTester
