# Database Schema Design - Drizzle ORM

## 🗄️ Schema Overview

Multi-tenant SaaS database design with proper relationships for leads, clients, subscriptions, billing, and user management.

## 📋 Core Tables Structure

### 1. Leads Management
```sql
-- Leads from landing page
CREATE TABLE leads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  school_name VARCHAR(255) NOT NULL,
  contact_person VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  estimated_students INTEGER,
  source VARCHAR(50), -- 'landing_page', 'referral', 'demo_request'
  status VARCHAR(20) DEFAULT 'new', -- 'new', 'contacted', 'demo_scheduled', 'proposal_sent', 'converted', 'lost'
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Demo bookings
CREATE TABLE demo_bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID REFERENCES leads(id),
  scheduled_date TIMESTAMP NOT NULL,
  demo_type VARCHAR(20), -- 'online', 'onsite'
  status VARCHAR(20) DEFAULT 'scheduled', -- 'scheduled', 'completed', 'cancelled', 'rescheduled'
  meeting_link VARCHAR(500),
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 2. Client Management
```sql
-- Converted clients (schools)
CREATE TABLE clients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID REFERENCES leads(id),
  school_name VARCHAR(255) NOT NULL,
  school_code VARCHAR(50) UNIQUE NOT NULL, -- for login
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  address TEXT,
  contact_person VARCHAR(255),
  actual_student_count INTEGER NOT NULL,
  estimated_student_count INTEGER,
  onboarding_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'in_progress', 'completed'
  status VARCHAR(20) DEFAULT 'active', -- 'active', 'suspended', 'cancelled'
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Client portal users
CREATE TABLE client_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES clients(id),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(20) DEFAULT 'admin', -- 'admin', 'billing', 'viewer'
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Subscription Management
```sql
-- Subscription plans
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL, -- 'Basic Monthly', 'Basic Yearly'
  billing_cycle VARCHAR(20) NOT NULL, -- 'monthly', 'yearly'
  price_per_student DECIMAL(10,2) NOT NULL, -- ₹80.00, ₹65.00
  discount_percentage DECIMAL(5,2) DEFAULT 0, -- 18.75% for yearly
  features JSONB, -- List of included features
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Client subscriptions
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES clients(id),
  plan_id UUID REFERENCES subscription_plans(id),
  student_count INTEGER NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  status VARCHAR(20) DEFAULT 'active', -- 'active', 'cancelled', 'suspended'
  auto_renew BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 4. Billing System
```sql
-- Billing cycles
CREATE TABLE billing_cycles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES subscriptions(id),
  cycle_start DATE NOT NULL,
  cycle_end DATE NOT NULL,
  student_count INTEGER NOT NULL,
  base_amount DECIMAL(10,2) NOT NULL,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  is_prorated BOOLEAN DEFAULT false,
  prorated_days INTEGER,
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'paid', 'overdue', 'cancelled'
  due_date DATE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Invoices
CREATE TABLE invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  billing_cycle_id UUID REFERENCES billing_cycles(id),
  client_id UUID REFERENCES clients(id),
  invoice_number VARCHAR(50) UNIQUE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'sent', 'paid', 'overdue', 'cancelled'
  issued_date DATE NOT NULL,
  due_date DATE NOT NULL,
  paid_date DATE,
  pdf_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 5. Payment Processing
```sql
-- Payment transactions
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  invoice_id UUID REFERENCES invoices(id),
  client_id UUID REFERENCES clients(id),
  razorpay_payment_id VARCHAR(100),
  razorpay_order_id VARCHAR(100),
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'INR',
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'success', 'failed', 'refunded'
  payment_method VARCHAR(50), -- 'card', 'netbanking', 'upi', 'wallet'
  failure_reason TEXT,
  processed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Payment reminders
CREATE TABLE payment_reminders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  invoice_id UUID REFERENCES invoices(id),
  client_id UUID REFERENCES clients(id),
  reminder_type VARCHAR(20), -- 'due_soon', 'overdue', 'final_notice'
  sent_date TIMESTAMP NOT NULL,
  email_sent BOOLEAN DEFAULT false,
  sms_sent BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 6. Support System
```sql
-- Support tickets
CREATE TABLE support_tickets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES clients(id),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  priority VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
  status VARCHAR(20) DEFAULT 'open', -- 'open', 'in_progress', 'resolved', 'closed'
  category VARCHAR(50), -- 'billing', 'technical', 'feature_request', 'bug'
  assigned_to UUID, -- Reference to admin users
  created_by UUID REFERENCES client_users(id),
  resolved_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Ticket messages
CREATE TABLE ticket_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ticket_id UUID REFERENCES support_tickets(id),
  sender_type VARCHAR(20) NOT NULL, -- 'client', 'admin'
  sender_id UUID NOT NULL,
  message TEXT NOT NULL,
  attachments JSONB, -- Array of file URLs
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 7. Admin Users
```sql
-- Internal admin users
CREATE TABLE admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(20) NOT NULL, -- 'super_admin', 'sales', 'support', 'billing'
  permissions JSONB, -- Array of specific permissions
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔗 Key Relationships

### Entity Relationship Diagram
```
Leads (1) → (0..1) Clients
Leads (1) → (0..*) Demo_Bookings
Clients (1) → (1) Subscriptions
Clients (1) → (0..*) Client_Users
Clients (1) → (0..*) Support_Tickets
Subscriptions (1) → (0..*) Billing_Cycles
Billing_Cycles (1) → (1) Invoices
Invoices (1) → (0..*) Payments
Invoices (1) → (0..*) Payment_Reminders
Support_Tickets (1) → (0..*) Ticket_Messages
```

## 📊 Indexes for Performance

```sql
-- Performance indexes
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_clients_school_code ON clients(school_code);
CREATE INDEX idx_subscriptions_client_id ON subscriptions(client_id);
CREATE INDEX idx_billing_cycles_subscription_id ON billing_cycles(subscription_id);
CREATE INDEX idx_invoices_client_id ON invoices(client_id);
CREATE INDEX idx_payments_invoice_id ON payments(invoice_id);
CREATE INDEX idx_support_tickets_client_id ON support_tickets(client_id);
```

## 🛡️ Security Considerations

### Row Level Security (RLS)
```sql
-- Enable RLS for multi-tenant isolation
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_cycles ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- Example policy for client data isolation
CREATE POLICY client_isolation ON clients
  FOR ALL TO authenticated
  USING (id = current_setting('app.current_client_id')::UUID);
```

This schema design ensures data integrity, performance, and security while supporting the complete SaaS business model from lead generation to ongoing subscription management.
