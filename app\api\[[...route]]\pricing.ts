import { Hono } from "hono"
import { zValidator } from "@hono/zod-validator"
import { z } from "zod"

// Validation schemas
const pricingQuerySchema = z.object({
  studentCount: z.string().transform(val => parseInt(val)).refine(val => val > 0 && val <= 10000, "Student count must be between 1 and 10000"),
  billingCycle: z.enum(["monthly", "yearly"]).default("monthly"),
  discountCode: z.string().optional(),
})

const calculatePricingSchema = z.object({
  studentCount: z.number().min(1, "Student count must be at least 1").max(10000, "Student count too large"),
  billingCycle: z.enum(["monthly", "yearly"]).default("monthly"),
  discountCode: z.string().optional(),
  customDiscount: z.number().min(0).max(100).optional(), // For admin use
})

// Pricing constants
const PRICING_CONFIG = {
  basePrice: 80, // ₹80 per student per month
  yearlyDiscountPercent: 20, // 20% discount for yearly billing
  minimumStudents: 1,
  maximumStudents: 10000,
  currency: "INR",
  taxPercent: 18, // GST 18%
}

// Discount codes configuration
type DiscountCode = {
  percent: number
  description: string
  validUntil: string
}

const DISCOUNT_CODES: Record<string, DiscountCode> = {
  "EARLY20": { percent: 20, description: "Early Bird 20% Off", validUntil: "2024-12-31" },
  "SCHOOL15": { percent: 15, description: "School Special 15% Off", validUntil: "2024-12-31" },
  "TRIAL10": { percent: 10, description: "Trial User 10% Off", validUntil: "2024-12-31" },
}

// Volume-based discounts
const VOLUME_DISCOUNTS = [
  { minStudents: 1000, discountPercent: 15, description: "Large School Discount" },
  { minStudents: 500, discountPercent: 10, description: "Medium School Discount" },
  { minStudents: 200, discountPercent: 5, description: "Small School Discount" },
]

// Helper function to calculate pricing
function calculatePricing(studentCount: number, billingCycle: "monthly" | "yearly", discountCode?: string, customDiscount?: number) {
  const baseMonthlyPrice = PRICING_CONFIG.basePrice * studentCount
  
  // Calculate base amount based on billing cycle
  let baseAmount = baseMonthlyPrice
  let billingMultiplier = 1
  
  if (billingCycle === "yearly") {
    billingMultiplier = 12
    baseAmount = baseMonthlyPrice * billingMultiplier
  }
  
  // Apply yearly discount
  let yearlyDiscountAmount = 0
  if (billingCycle === "yearly") {
    yearlyDiscountAmount = baseAmount * (PRICING_CONFIG.yearlyDiscountPercent / 100)
  }
  
  // Apply volume discount
  let volumeDiscount = 0
  let volumeDiscountDescription = ""
  for (const discount of VOLUME_DISCOUNTS) {
    if (studentCount >= discount.minStudents) {
      volumeDiscount = discount.discountPercent
      volumeDiscountDescription = discount.description
      break
    }
  }
  const volumeDiscountAmount = baseAmount * (volumeDiscount / 100)
  
  // Apply discount code
  let codeDiscountAmount = 0
  let codeDiscountDescription = ""
  let isValidDiscountCode = false
  if (discountCode && DISCOUNT_CODES[discountCode]) {
    const discount = DISCOUNT_CODES[discountCode]
    const validUntil = new Date(discount.validUntil)
    if (validUntil > new Date()) {
      codeDiscountAmount = baseAmount * (discount.percent / 100)
      codeDiscountDescription = discount.description
      isValidDiscountCode = true
    }
  }
  
  // Apply custom discount (admin only)
  let customDiscountAmount = 0
  if (customDiscount && customDiscount > 0) {
    customDiscountAmount = baseAmount * (customDiscount / 100)
  }
  
  // Calculate total discounts (don't stack volume and code discounts, take the higher one)
  const totalDiscountAmount = yearlyDiscountAmount + Math.max(volumeDiscountAmount, codeDiscountAmount) + customDiscountAmount
  
  // Calculate subtotal after discounts
  const subtotal = baseAmount - totalDiscountAmount
  
  // Calculate tax
  const taxAmount = subtotal * (PRICING_CONFIG.taxPercent / 100)
  
  // Calculate final total
  const total = subtotal + taxAmount
  
  // Calculate savings compared to monthly billing without discounts
  const monthlyTotal = baseMonthlyPrice * 12 + (baseMonthlyPrice * 12 * PRICING_CONFIG.taxPercent / 100)
  const savings = billingCycle === "yearly" ? monthlyTotal - total : 0
  
  return {
    studentCount,
    billingCycle,
    pricePerStudent: PRICING_CONFIG.basePrice,
    baseAmount,
    discounts: {
      yearly: {
        amount: yearlyDiscountAmount,
        percent: billingCycle === "yearly" ? PRICING_CONFIG.yearlyDiscountPercent : 0,
        description: "Yearly Billing Discount"
      },
      volume: {
        amount: volumeDiscountAmount,
        percent: volumeDiscount,
        description: volumeDiscountDescription
      },
      code: {
        amount: isValidDiscountCode ? codeDiscountAmount : 0,
        percent: isValidDiscountCode ? DISCOUNT_CODES[discountCode!].percent : 0,
        description: codeDiscountDescription,
        code: discountCode,
        isValid: isValidDiscountCode
      },
      custom: {
        amount: customDiscountAmount,
        percent: customDiscount || 0,
        description: "Custom Discount"
      }
    },
    totalDiscountAmount,
    subtotal,
    taxAmount,
    taxPercent: PRICING_CONFIG.taxPercent,
    total,
    savings,
    currency: PRICING_CONFIG.currency,
    breakdown: {
      monthly: {
        basePrice: baseMonthlyPrice,
        afterDiscounts: billingCycle === "monthly" ? subtotal : baseMonthlyPrice - (totalDiscountAmount / billingMultiplier),
        withTax: billingCycle === "monthly" ? total : (baseMonthlyPrice - (totalDiscountAmount / billingMultiplier)) * (1 + PRICING_CONFIG.taxPercent / 100)
      }
    }
  }
}

// Create Hono app for pricing routes
const app = new Hono()

// Get pricing for specific student count
app.get(
  "/:studentCount",
  zValidator("param", z.object({ studentCount: z.string() })),
  zValidator("query", z.object({ 
    billingCycle: z.enum(["monthly", "yearly"]).default("monthly"),
    discountCode: z.string().optional()
  })),
  async (c) => {
    try {
      const { studentCount } = c.req.valid("param")
      const { billingCycle, discountCode } = c.req.valid("query")
      
      const count = parseInt(studentCount)
      
      if (isNaN(count) || count < PRICING_CONFIG.minimumStudents || count > PRICING_CONFIG.maximumStudents) {
        return c.json({
          success: false,
          error: `Student count must be between ${PRICING_CONFIG.minimumStudents} and ${PRICING_CONFIG.maximumStudents}`
        }, 400)
      }
      
      const pricing = calculatePricing(count, billingCycle, discountCode)
      
      return c.json({
        success: true,
        data: pricing
      })
      
    } catch (error) {
      console.error("Error calculating pricing:", error)
      return c.json({
        success: false,
        error: "Failed to calculate pricing"
      }, 500)
    }
  }
)

// Calculate pricing with detailed breakdown (POST for complex calculations)
app.post(
  "/calculate",
  zValidator("json", calculatePricingSchema),
  async (c) => {
    try {
      const { studentCount, billingCycle, discountCode, customDiscount } = c.req.valid("json")
      
      const pricing = calculatePricing(studentCount, billingCycle, discountCode, customDiscount)
      
      return c.json({
        success: true,
        data: pricing,
        message: "Pricing calculated successfully"
      })
      
    } catch (error) {
      console.error("Error calculating pricing:", error)
      return c.json({
        success: false,
        error: "Failed to calculate pricing"
      }, 500)
    }
  }
)

// Get available discount codes (public)
app.get(
  "/discounts/available",
  async (c) => {
    try {
      const currentDate = new Date()
      const availableDiscounts = Object.entries(DISCOUNT_CODES)
        .filter(([code, discount]) => new Date(discount.validUntil) > currentDate)
        .map(([code, discount]) => ({
          code,
          description: discount.description,
          percent: discount.percent,
          validUntil: discount.validUntil
        }))
      
      return c.json({
        success: true,
        data: availableDiscounts
      })
      
    } catch (error) {
      console.error("Error fetching discount codes:", error)
      return c.json({
        success: false,
        error: "Failed to fetch discount codes"
      }, 500)
    }
  }
)

// Validate discount code
app.get(
  "/discounts/validate/:code",
  async (c) => {
    try {
      const code = c.req.param("code").toUpperCase()
      
      if (!DISCOUNT_CODES[code]) {
        return c.json({
          success: false,
          error: "Invalid discount code",
          isValid: false
        }, 404)
      }
      
      const discount = DISCOUNT_CODES[code]
      const validUntil = new Date(discount.validUntil)
      const isValid = validUntil > new Date()
      
      return c.json({
        success: true,
        data: {
          code,
          description: discount.description,
          percent: discount.percent,
          validUntil: discount.validUntil,
          isValid
        }
      })
      
    } catch (error) {
      console.error("Error validating discount code:", error)
      return c.json({
        success: false,
        error: "Failed to validate discount code"
      }, 500)
    }
  }
)

// Get pricing tiers and volume discounts
app.get(
  "/tiers",
  async (c) => {
    try {
      return c.json({
        success: true,
        data: {
          basePrice: PRICING_CONFIG.basePrice,
          currency: PRICING_CONFIG.currency,
          billingCycles: [
            {
              type: "monthly",
              description: "Monthly Billing",
              discountPercent: 0
            },
            {
              type: "yearly",
              description: "Yearly Billing",
              discountPercent: PRICING_CONFIG.yearlyDiscountPercent
            }
          ],
          volumeDiscounts: VOLUME_DISCOUNTS,
          taxPercent: PRICING_CONFIG.taxPercent,
          minimumStudents: PRICING_CONFIG.minimumStudents,
          maximumStudents: PRICING_CONFIG.maximumStudents
        }
      })
      
    } catch (error) {
      console.error("Error fetching pricing tiers:", error)
      return c.json({
        success: false,
        error: "Failed to fetch pricing tiers"
      }, 500)
    }
  }
)

export default app
