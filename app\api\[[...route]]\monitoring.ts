/**
 * Monitoring API Routes for Schopio Production System
 * 
 * Provides comprehensive monitoring endpoints for business metrics,
 * system performance, security events, and operational health.
 */

import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { 
  getBusinessMetrics,
  getBillingHealth,
  getSecurityMetrics,
  getSystemPerformance,
  getMonitoringDashboard
} from '../../../src/services/monitoringService'
// Note: Admin auth middleware to be implemented
// import { adminAuthMiddleware } from '../../../src/middleware/adminAuth'

const app = new Hono()

// Apply admin authentication to all monitoring endpoints
// TODO: Implement admin auth middleware
// app.use('*', adminAuthMiddleware)

/**
 * GET /api/monitoring/health
 * System health check endpoint
 */
app.get('/health', async (c) => {
  try {
    const dashboard = await getMonitoringDashboard()
    
    return c.json({
      status: dashboard.overallHealth.status,
      score: dashboard.overallHealth.score,
      timestamp: dashboard.timestamp,
      issues: dashboard.overallHealth.issues,
      summary: {
        activeSubscriptions: dashboard.businessMetrics.activeSubscriptions,
        mrr: dashboard.businessMetrics.monthlyRecurringRevenue,
        paymentSuccessRate: dashboard.businessMetrics.paymentSuccessRate,
        webhookHealth: dashboard.billingHealth.webhookSuccess,
        securityAlerts: dashboard.securityMetrics.authenticationFailures
      }
    })
  } catch (error) {
    console.error('Health check failed:', error)
    return c.json({
      status: 'critical',
      score: 0,
      timestamp: new Date(),
      error: 'Health check failed',
      issues: ['System monitoring unavailable']
    }, 500)
  }
})

/**
 * GET /api/monitoring/dashboard
 * Complete monitoring dashboard data
 */
app.get('/dashboard', async (c) => {
  try {
    const dashboard = await getMonitoringDashboard()
    return c.json(dashboard)
  } catch (error) {
    console.error('Dashboard data fetch failed:', error)
    return c.json({
      error: 'Failed to fetch dashboard data',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

/**
 * GET /api/monitoring/business
 * Business metrics and KPIs
 */
app.get('/business', async (c) => {
  try {
    const metrics = await getBusinessMetrics()
    return c.json({
      success: true,
      data: metrics,
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Business metrics fetch failed:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch business metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

/**
 * GET /api/monitoring/billing
 * Billing automation health metrics
 */
app.get('/billing', async (c) => {
  try {
    const health = await getBillingHealth()
    return c.json({
      success: true,
      data: health,
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Billing health fetch failed:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch billing health',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

/**
 * GET /api/monitoring/security
 * Security events and metrics
 */
app.get('/security', async (c) => {
  try {
    const metrics = await getSecurityMetrics()
    return c.json({
      success: true,
      data: metrics,
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Security metrics fetch failed:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch security metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

/**
 * GET /api/monitoring/performance
 * System performance metrics
 */
app.get('/performance', async (c) => {
  try {
    const performance = await getSystemPerformance()
    return c.json({
      success: true,
      data: performance,
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Performance metrics fetch failed:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch performance metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

/**
 * GET /api/monitoring/alerts
 * Current system alerts and warnings
 */
app.get('/alerts', async (c) => {
  try {
    const dashboard = await getMonitoringDashboard()
    const { overallHealth, businessMetrics, billingHealth, securityMetrics } = dashboard
    
    const alerts = []
    
    // Critical alerts
    if (overallHealth.score < 70) {
      alerts.push({
        level: 'critical',
        message: `System health critical: ${overallHealth.score}/100`,
        timestamp: new Date(),
        category: 'system'
      })
    }
    
    if (businessMetrics.paymentSuccessRate < 95) {
      alerts.push({
        level: 'critical',
        message: `Low payment success rate: ${businessMetrics.paymentSuccessRate.toFixed(1)}%`,
        timestamp: new Date(),
        category: 'payments'
      })
    }
    
    if (billingHealth.webhookSuccess < 98) {
      alerts.push({
        level: 'critical',
        message: `Webhook failures: ${(100 - billingHealth.webhookSuccess).toFixed(1)}% failure rate`,
        timestamp: new Date(),
        category: 'billing'
      })
    }
    
    // Warning alerts
    if (businessMetrics.churnRate > 10) {
      alerts.push({
        level: 'warning',
        message: `High churn rate: ${businessMetrics.churnRate.toFixed(1)}%`,
        timestamp: new Date(),
        category: 'business'
      })
    }
    
    if (securityMetrics.authenticationFailures > 100) {
      alerts.push({
        level: 'warning',
        message: `High authentication failures: ${securityMetrics.authenticationFailures}`,
        timestamp: new Date(),
        category: 'security'
      })
    }
    
    if (billingHealth.subscriptionsInGrace > businessMetrics.activeSubscriptions * 0.05) {
      alerts.push({
        level: 'warning',
        message: `High subscriptions in grace period: ${billingHealth.subscriptionsInGrace}`,
        timestamp: new Date(),
        category: 'billing'
      })
    }
    
    return c.json({
      success: true,
      data: {
        totalAlerts: alerts.length,
        criticalAlerts: alerts.filter(a => a.level === 'critical').length,
        warningAlerts: alerts.filter(a => a.level === 'warning').length,
        alerts: alerts.sort((a, b) => {
          const levelPriority: Record<string, number> = { critical: 3, warning: 2, info: 1 }
          return levelPriority[b.level] - levelPriority[a.level]
        })
      },
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Alerts fetch failed:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch alerts',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

/**
 * GET /api/monitoring/metrics/summary
 * Quick summary of key metrics for status displays
 */
app.get('/metrics/summary', async (c) => {
  try {
    const [business, billing, security] = await Promise.all([
      getBusinessMetrics(),
      getBillingHealth(),
      getSecurityMetrics()
    ])
    
    return c.json({
      success: true,
      data: {
        subscriptions: {
          active: business.activeSubscriptions,
          mrr: business.monthlyRecurringRevenue,
          churn: business.churnRate
        },
        payments: {
          successRate: business.paymentSuccessRate,
          failedToday: billing.failedPayments,
          webhookHealth: billing.webhookSuccess
        },
        security: {
          authFailures: security.authenticationFailures,
          blockedIPs: security.blockedIPs,
          rateLimitViolations: security.rateLimitViolations
        },
        system: {
          subscriptionsInGrace: billing.subscriptionsInGrace,
          overdueRevenue: billing.overdueRevenue
        }
      },
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Summary metrics fetch failed:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch summary metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

/**
 * POST /api/monitoring/test-alert
 * Test alert system (development/testing only)
 */
app.post('/test-alert', 
  zValidator('json', z.object({
    level: z.enum(['info', 'warning', 'critical']),
    message: z.string(),
    category: z.string()
  })),
  async (c) => {
    const { level, message, category } = c.req.valid('json')
    
    // In production, this would trigger actual alert mechanisms
    console.log(`TEST ALERT [${level.toUpperCase()}] ${category}: ${message}`)
    
    return c.json({
      success: true,
      message: 'Test alert triggered',
      alert: {
        level,
        message,
        category,
        timestamp: new Date()
      }
    })
  }
)

export default app
