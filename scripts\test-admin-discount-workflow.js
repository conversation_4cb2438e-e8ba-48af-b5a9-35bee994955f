#!/usr/bin/env node

/**
 * Comprehensive Admin Discount Workflow Test
 * Tests the complete discount management interface and functionality
 */

const fs = require('fs');

console.log('🔧 Testing Complete Admin Discount Workflow...\n');

const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

function testFeature(featureName, testFunction) {
  try {
    console.log(`🔍 Testing: ${featureName}`);
    testFunction();
    console.log(`✅ PASS: ${featureName}\n`);
    testResults.passed++;
  } catch (error) {
    console.log(`❌ FAIL: ${featureName}`);
    console.log(`   Error: ${error.message}\n`);
    testResults.failed++;
    testResults.errors.push({ feature: featureName, error: error.message });
  }
}

// Test 1: Admin Dashboard Contains Discount Management
testFeature('Admin Dashboard Discount Management Interface', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for comprehensive discount management section
  if (!adminDashboardContent.includes('Time-Based Discount Management')) {
    throw new Error('Time-Based Discount Management section not found');
  }
  
  // Check for all required discount fields
  const requiredFields = [
    'Discount Percentage (%)',
    'Duration (Months)',
    'Start Date',
    'Discount Reason (Required)'
  ];
  
  requiredFields.forEach(field => {
    if (!adminDashboardContent.includes(field)) {
      throw new Error(`Required field "${field}" not found in admin interface`);
    }
  });
  
  console.log('   ✓ All required discount fields present');
});

// Test 2: Edit Subscription Modal Contains Discount Management
testFeature('Edit Subscription Modal Discount Interface', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for edit modal discount management
  const editModalSections = adminDashboardContent.split('Enhanced Time-Based Discount Management');

  if (editModalSections.length < 3) {
    throw new Error('Enhanced discount management not found in edit modal');
  }

  // Check that there are at least 2 instances (one in create modal, one in edit modal)
  console.log(`   ✓ Found ${editModalSections.length - 1} discount management sections`);

  if (editModalSections.length < 2) {
    throw new Error('Edit modal discount management section missing');
  }
  
  // Check for discount preview functionality in the admin dashboard
  if (!adminDashboardContent.includes('Discount Preview')) {
    throw new Error('Discount preview functionality not found');
  }
  
  console.log('   ✓ Edit modal contains complete discount management');
});

// Test 3: Discount Duration Options
testFeature('Discount Duration Selection Options', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  const durationOptions = [
    '1 Month',
    '2 Months', 
    '3 Months',
    '6 Months',
    '12 Months',
    '24 Months'
  ];
  
  durationOptions.forEach(option => {
    if (!adminDashboardContent.includes(option)) {
      throw new Error(`Duration option "${option}" not found`);
    }
  });
  
  console.log('   ✓ All duration options (1-24 months) available');
});

// Test 4: Payment-Based Start Date Logic
testFeature('Payment-Based Start Date Logic', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for payment-based start date options
  if (!adminDashboardContent.includes('Current Billing Period (if payment pending)')) {
    throw new Error('Current billing period option not found');
  }
  
  if (!adminDashboardContent.includes('Next Billing Period')) {
    throw new Error('Next billing period option not found');
  }
  
  if (!adminDashboardContent.includes('Start date depends on current month payment status')) {
    throw new Error('Payment status explanation not found');
  }
  
  console.log('   ✓ Payment-based start date logic implemented');
});

// Test 5: Discount Validation Rules
testFeature('Discount Validation Rules', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for validation rules
  if (!adminDashboardContent.includes('Discount must be between 1% and 100%')) {
    throw new Error('Percentage validation not found');
  }
  
  if (!adminDashboardContent.includes('High discount - requires super admin approval')) {
    throw new Error('High discount validation not found');
  }
  
  console.log('   ✓ Validation rules properly implemented');
});

// Test 6: Discount Preview Calculations
testFeature('Discount Preview Calculations', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for preview calculation fields
  const previewFields = [
    'Original Monthly Amount',
    'Discount Amount',
    'Final Monthly Amount',
    'Total Savings'
  ];
  
  previewFields.forEach(field => {
    if (!adminDashboardContent.includes(field)) {
      throw new Error(`Preview field "${field}" not found`);
    }
  });
  
  console.log('   ✓ Discount preview calculations implemented');
});

// Test 7: Form Data Initialization
testFeature('Form Data Initialization', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for newDiscount field initialization
  if (!adminDashboardContent.includes('newDiscount: {')) {
    throw new Error('newDiscount field initialization not found');
  }
  
  // Check for required newDiscount properties
  const newDiscountFields = ['percentage', 'durationMonths', 'startDate', 'reason'];
  newDiscountFields.forEach(field => {
    if (!adminDashboardContent.includes(`${field}:`)) {
      throw new Error(`newDiscount.${field} initialization not found`);
    }
  });
  
  console.log('   ✓ Form data properly initialized');
});

// Test 8: Discount Application API Integration
testFeature('Discount Application API Integration', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for API call to discount management endpoint
  if (!adminDashboardContent.includes('/api/discount-management/admin/subscriptions/')) {
    throw new Error('Discount management API endpoint not found');
  }
  
  // Check for proper API payload
  const apiFields = ['discountPercentage', 'durationMonths', 'startDate', 'reason'];
  apiFields.forEach(field => {
    if (!adminDashboardContent.includes(field)) {
      throw new Error(`API payload field "${field}" not found`);
    }
  });
  
  console.log('   ✓ API integration properly implemented');
});

// Test 9: Payment-Based Discount Service
testFeature('Payment-Based Discount Service', () => {
  const serviceContent = fs.readFileSync('src/services/paymentBasedDiscountService.ts', 'utf8');
  
  // Check for key service methods
  const serviceMethods = [
    'getPaymentStatus',
    'validateDiscountStartDate',
    'getDiscountStartDateOptions',
    'applyDiscountWithPaymentLogic'
  ];
  
  serviceMethods.forEach(method => {
    if (!serviceContent.includes(method)) {
      throw new Error(`Service method "${method}" not found`);
    }
  });
  
  console.log('   ✓ Payment-based discount service complete');
});

// Test 10: Business Logic Implementation
testFeature('Business Logic Implementation', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for business rule validations
  if (!adminDashboardContent.includes('Cannot apply a new discount while an existing discount is active')) {
    throw new Error('Active discount validation not found');
  }
  
  if (!adminDashboardContent.includes('Discounts of 50% or more cannot be applied for more than 12 months')) {
    throw new Error('High discount duration validation not found');
  }
  
  console.log('   ✓ Business logic properly implemented');
});

// Test 11: Current Discount Display
testFeature('Current Discount Display', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for current discount display section
  if (!adminDashboardContent.includes('Active Discount')) {
    throw new Error('Active discount display not found');
  }
  
  // Check for discount information fields
  const discountInfoFields = [
    'Original Amount',
    'Discounted Amount', 
    'Monthly Savings',
    'End Date'
  ];
  
  discountInfoFields.forEach(field => {
    if (!adminDashboardContent.includes(field)) {
      throw new Error(`Discount info field "${field}" not found`);
    }
  });
  
  console.log('   ✓ Current discount display implemented');
});

// Test 12: Error Handling
testFeature('Error Handling', () => {
  const adminDashboardContent = fs.readFileSync('app/admin/dashboard/page.tsx', 'utf8');
  
  // Check for error handling in discount application
  if (!adminDashboardContent.includes('Failed to apply discount')) {
    throw new Error('Discount application error handling not found');
  }
  
  if (!adminDashboardContent.includes('Subscription updated but discount application failed')) {
    throw new Error('Partial failure error handling not found');
  }
  
  console.log('   ✓ Comprehensive error handling implemented');
});

// Print Results
console.log('🎯 ADMIN DISCOUNT WORKFLOW TEST COMPLETED\n');
console.log('📊 RESULTS:');
console.log(`✅ Features Tested: ${testResults.passed}`);
console.log(`❌ Features Failed: ${testResults.failed}`);
console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%\n`);

if (testResults.failed > 0) {
  console.log('❌ FAILED FEATURES:');
  testResults.errors.forEach(error => {
    console.log(`   - ${error.feature}: ${error.error}`);
  });
  console.log('');
  process.exit(1);
} else {
  console.log('🎉 ALL ADMIN DISCOUNT FEATURES VERIFIED!\n');
  
  console.log('✅ VERIFIED FEATURES:');
  console.log('   - Complete discount management interface in admin dashboard');
  console.log('   - Enhanced edit subscription modal with discount controls');
  console.log('   - Comprehensive discount duration selection (1-24 months)');
  console.log('   - Payment-based start date logic implementation');
  console.log('   - Business rule validation and enforcement');
  console.log('   - Real-time discount preview calculations');
  console.log('   - Proper form data initialization');
  console.log('   - API integration for discount application');
  console.log('   - Payment-based discount service');
  console.log('   - Current discount display functionality');
  console.log('   - Comprehensive error handling');
  console.log('');
  console.log('🚀 ADMIN DISCOUNT WORKFLOW: FULLY FUNCTIONAL');
  
  process.exit(0);
}
