import { db } from '@/src/db'
import { subscriptions, billingSubscriptions, billingInvoices } from '@/src/db/schema'
import { eq, and, lte, gte, sql } from 'drizzle-orm'

export interface DueDateCalculation {
  dueDate: Date
  nextBillingDate: Date
  gracePeriodEnd: Date
  isOverdue: boolean
  daysOverdue: number
}

export interface BillingCycleUpdate {
  id: string
  status: 'pending' | 'active' | 'overdue' | 'suspended'
  daysOverdue?: number
  penaltyAmount?: number
}

export class DueDateManager {
  private static readonly PENALTY_RATE = 0.02 // 2% daily penalty
  private static readonly SUSPENSION_DAYS = 15 // Suspend after 15 days overdue

  /**
   * Calculate due date for a subscription based on billing cycle and due date preference
   */
  static calculateDueDate(
    currentPeriodStart: Date,
    billingCycle: 'monthly' | 'yearly',
    dueDateOfMonth: number,
    gracePeriodDays: number = 3
  ): DueDateCalculation {
    const dueDate = new Date(currentPeriodStart)
    
    // Set the due date based on billing cycle
    if (billingCycle === 'monthly') {
      dueDate.setMonth(dueDate.getMonth() + 1)
    } else {
      dueDate.setFullYear(dueDate.getFullYear() + 1)
    }
    
    // Set the specific day of the month
    dueDate.setDate(dueDateOfMonth)
    
    // Calculate grace period end
    const gracePeriodEnd = new Date(dueDate)
    gracePeriodEnd.setDate(gracePeriodEnd.getDate() + gracePeriodDays)
    
    // Calculate current status
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const isOverdue = today > gracePeriodEnd
    const daysOverdue = isOverdue ? Math.floor((today.getTime() - gracePeriodEnd.getTime()) / (1000 * 60 * 60 * 24)) : 0
    
    let status: 'current' | 'grace_period' | 'overdue' | 'suspended' = 'current'
    if (today > dueDate && today <= gracePeriodEnd) {
      status = 'grace_period'
    } else if (isOverdue && daysOverdue < this.SUSPENSION_DAYS) {
      status = 'overdue'
    } else if (daysOverdue >= this.SUSPENSION_DAYS) {
      status = 'suspended'
    }
    
    return {
      dueDate,
      nextBillingDate: dueDate,
      gracePeriodEnd,
      isOverdue,
      daysOverdue
    }
  }

  /**
   * Calculate penalty amount based on overdue days and total amount
   */
  static calculatePenalty(monthlyAmount: number, daysOverdue: number): number {
    if (daysOverdue <= 0) return 0
    return monthlyAmount * this.PENALTY_RATE * daysOverdue
  }

  /**
   * Update billing cycle statuses based on due dates
   */
  static async updateOverdueBillingCycles(): Promise<BillingCycleUpdate[]> {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      // Use database transaction with row-level locking for atomic operations
      const updates = await db.transaction(async (tx) => {
        // Get all active billing cycles that might be overdue (with row-level locking)
        const cycles = await tx.select({
          id: billingSubscriptions.id,
          nextBillingDate: billingSubscriptions.nextBillingDate,
          gracePeriodDays: billingSubscriptions.gracePeriodDays,
          monthlyAmount: billingSubscriptions.monthlyAmount,
          status: billingSubscriptions.status,
          // Get subscription details for due date calculation
          subscriptionDueDate: subscriptions.dueDate,
          subscriptionGracePeriod: subscriptions.gracePeriodDays
        })
        .from(billingSubscriptions)
        .leftJoin(subscriptions, eq(billingSubscriptions.id, subscriptions.id))
        .where(and(
          eq(billingSubscriptions.status, 'active'),
          lte(billingSubscriptions.nextBillingDate, today.toISOString().split('T')[0])
        ))
        .for('update')

        const cycleUpdates: BillingCycleUpdate[] = []

        for (const cycle of cycles) {
          const dueDate = new Date(cycle.nextBillingDate)
          const gracePeriodDays = cycle.gracePeriodDays || cycle.subscriptionGracePeriod || 3
          const gracePeriodEnd = new Date(dueDate)
          gracePeriodEnd.setDate(gracePeriodEnd.getDate() + gracePeriodDays)

          const isInGracePeriod = today > dueDate && today <= gracePeriodEnd
          const isOverdue = today > gracePeriodEnd
          const daysOverdue = isOverdue ? Math.floor((today.getTime() - gracePeriodEnd.getTime()) / (1000 * 60 * 60 * 24)) : 0

          let newStatus: 'pending' | 'active' | 'overdue' | 'suspended' = 'active'
          let penaltyAmount = 0

          if (isInGracePeriod) {
            newStatus = 'active' // Still in grace period
          } else if (isOverdue && daysOverdue < this.SUSPENSION_DAYS) {
            newStatus = 'overdue'
            penaltyAmount = this.calculatePenalty(parseFloat(cycle.monthlyAmount), daysOverdue)
          } else if (daysOverdue >= this.SUSPENSION_DAYS) {
            newStatus = 'suspended'
            penaltyAmount = this.calculatePenalty(parseFloat(cycle.monthlyAmount), daysOverdue)
          }

          // Update the billing cycle if status changed
          if (newStatus !== cycle.status || penaltyAmount > 0) {
            await tx.update(billingSubscriptions)
              .set({
                status: newStatus,})
              .where(eq(billingSubscriptions.id, cycle.id))

            cycleUpdates.push({
              id: cycle.id,
              status: newStatus,
              penaltyAmount,
              daysOverdue
            })
          }
        }

        return cycleUpdates
      })

      return updates
    } catch (error) {
      console.error('Error updating overdue billing cycles:', error)
      throw error
    }
  }

  /**
   * Update subscription statuses based on billing cycle statuses
   */
  static async updateSubscriptionStatuses(): Promise<void> {
    try {
      // Get subscriptions with overdue or suspended billing cycles
      const overdueSubscriptions = await db.select({
        id: subscriptions.id,
        billingStatus: billingSubscriptions.status
      })
      .from(subscriptions)
      .leftJoin(billingSubscriptions, eq(subscriptions.id, billingSubscriptions.id))
      .where(and(
        eq(subscriptions.status, 'active'),
        sql`${billingSubscriptions.status} IN ('overdue', 'suspended')`
      ))

      // Group by subscription and determine worst status
      const subscriptionUpdates = new Map<string, string>()
      
      for (const sub of overdueSubscriptions) {
        const currentStatus = subscriptionUpdates.get(sub.id) || 'active'

        if (sub.billingStatus === 'suspended' || currentStatus === 'suspended') {
          subscriptionUpdates.set(sub.id, 'suspended')
        } else if (sub.billingStatus === 'overdue' && currentStatus !== 'suspended') {
          subscriptionUpdates.set(sub.id, 'overdue')
        }
      }

      // Update subscription statuses
      for (const [subscriptionId, status] of subscriptionUpdates) {
        await db.update(subscriptions)
          .set({ status })
          .where(eq(subscriptions.id, subscriptionId))
      }

    } catch (error) {
      console.error('Error updating subscription statuses:', error)
      throw error
    }
  }

  /**
   * Get due date information for a specific subscription
   */
  static async getSubscriptionDueDateInfo(id: string): Promise<DueDateCalculation | null> {
    try {
      const [subscription] = await db.select({
        id: subscriptions.id,
        nextBillingDate: subscriptions.nextBillingDate,
        gracePeriodDays: subscriptions.gracePeriodDays,
        billingCycle: subscriptions.billingCycle,
        status: subscriptions.status
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, id))
      .limit(1)

      if (!subscription) return null

      const nextBilling = new Date(subscription.nextBillingDate)
      const dueDateCalc = this.calculateDueDate(
        nextBilling,
        subscription.billingCycle as 'monthly' | 'yearly',
        15, // Default due date of month
        subscription.gracePeriodDays || 3
      )

      return dueDateCalc
    } catch (error) {
      console.error('Error getting subscription due date info:', error)
      return null
    }
  }

  /**
   * Process all due date updates (to be called by scheduler)
   */
  static async processAllDueDateUpdates(): Promise<{
    billingCycleUpdates: BillingCycleUpdate[]
    subscriptionsUpdated: number
  }> {
    try {
      console.log('Starting due date processing...')
      
      const billingCycleUpdates = await this.updateOverdueBillingCycles()
      await this.updateSubscriptionStatuses()
      
      console.log(`Due date processing completed. Updated ${billingCycleUpdates.length} billing cycles`)
      
      return {
        billingCycleUpdates,
        subscriptionsUpdated: billingCycleUpdates.length
      }
    } catch (error) {
      console.error('Error in due date processing:', error)
      throw error
    }
  }
}
