'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, <PERSON>Content, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Users, 
  CheckCircle, 
  ArrowRight,
  Target,
  Award,
  BookOpen,
  Calendar,
  CreditCard,
  BarChart3,
  MessageSquare,
  Shield,
  Smartphone,
  Clock,
  TrendingUp,
  Settings
} from 'lucide-react'

interface AssessmentStep {
  id: string
  title: string
  question: string
  options: {
    id: string
    label: string
    value: number
    description?: string
  }[]
}

interface Feature {
  id: string
  name: string
  description: string
  icon: any
  category: string
  priority: number
}

const FeatureFitAssessment = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [showResults, setShowResults] = useState(false)

  const assessmentSteps: AssessmentStep[] = [
    {
      id: 'studentCount',
      title: 'School Size',
      question: 'How many students are enrolled in your school?',
      options: [
        { id: 'small', label: '100-500 students', value: 1, description: 'Small school' },
        { id: 'medium', label: '500-1500 students', value: 2, description: 'Medium school' },
        { id: 'large', label: '1500-3000 students', value: 3, description: 'Large school' },
        { id: 'xlarge', label: '3000+ students', value: 4, description: 'Very large school' }
      ]
    },
    {
      id: 'currentSystems',
      title: 'Current Technology',
      question: 'What best describes your current management system?',
      options: [
        { id: 'manual', label: 'Mostly paper-based/manual', value: 1, description: 'Traditional methods' },
        { id: 'basic', label: 'Basic software (Excel, simple tools)', value: 2, description: 'Basic digital tools' },
        { id: 'multiple', label: 'Multiple disconnected systems', value: 3, description: 'Various separate tools' },
        { id: 'integrated', label: 'Some integrated systems', value: 4, description: 'Partially connected' }
      ]
    },
    {
      id: 'priorities',
      title: 'Top Priorities',
      question: 'What is your school\'s highest priority right now?',
      options: [
        { id: 'admin', label: 'Reduce administrative workload', value: 1, description: 'Efficiency focus' },
        { id: 'communication', label: 'Improve parent communication', value: 2, description: 'Engagement focus' },
        { id: 'academic', label: 'Enhance academic tracking', value: 3, description: 'Performance focus' },
        { id: 'financial', label: 'Streamline fee management', value: 4, description: 'Financial focus' }
      ]
    },
    {
      id: 'urgency',
      title: 'Implementation Timeline',
      question: 'When do you need the new system operational?',
      options: [
        { id: 'immediate', label: 'Within 1 month', value: 4, description: 'Urgent need' },
        { id: 'quarter', label: 'Within 3 months', value: 3, description: 'Soon' },
        { id: 'semester', label: 'Within 6 months', value: 2, description: 'Planning ahead' },
        { id: 'year', label: 'Within 1 year', value: 1, description: 'Long-term planning' }
      ]
    },
    {
      id: 'budget',
      title: 'Investment Readiness',
      question: 'What\'s your approach to technology investment?',
      options: [
        { id: 'conservative', label: 'Start small, expand gradually', value: 1, description: 'Cautious approach' },
        { id: 'balanced', label: 'Balanced investment in key areas', value: 2, description: 'Strategic approach' },
        { id: 'comprehensive', label: 'Comprehensive solution preferred', value: 3, description: 'Complete transformation' },
        { id: 'premium', label: 'Best-in-class solution needed', value: 4, description: 'Premium approach' }
      ]
    }
  ]

  const features: Feature[] = [
    {
      id: 'student-management',
      name: 'Student Information System',
      description: 'Complete student profiles, enrollment, and academic records',
      icon: Users,
      category: 'Core',
      priority: 1
    },
    {
      id: 'attendance',
      name: 'Smart Attendance Tracking',
      description: 'Automated attendance with real-time notifications',
      icon: CheckCircle,
      category: 'Core',
      priority: 1
    },
    {
      id: 'fee-management',
      name: 'Fee Management System',
      description: 'Online payments, automated reminders, financial tracking',
      icon: CreditCard,
      category: 'Core',
      priority: 2
    },
    {
      id: 'parent-portal',
      name: 'Parent Communication Portal',
      description: 'Real-time updates, messaging, and engagement tools',
      icon: MessageSquare,
      category: 'Communication',
      priority: 2
    },
    {
      id: 'academic-tracking',
      name: 'Academic Performance Analytics',
      description: 'Grade management, progress tracking, and insights',
      icon: BarChart3,
      category: 'Academic',
      priority: 2
    },
    {
      id: 'timetable',
      name: 'AI-Powered Timetable Management',
      description: 'Automated scheduling with conflict resolution',
      icon: Calendar,
      category: 'Advanced',
      priority: 3
    },
    {
      id: 'mobile-app',
      name: 'Mobile Application',
      description: 'iOS and Android apps for all stakeholders',
      icon: Smartphone,
      category: 'Communication',
      priority: 3
    },
    {
      id: 'ai-insights',
      name: 'AI-Powered Predictive Insights',
      description: 'Student success prediction and intervention alerts',
      icon: TrendingUp,
      category: 'Advanced',
      priority: 4
    },
    {
      id: 'library',
      name: 'Digital Library Management',
      description: 'Book tracking, digital resources, and reading analytics',
      icon: BookOpen,
      category: 'Academic',
      priority: 3
    },
    {
      id: 'security',
      name: 'Advanced Security & Compliance',
      description: 'Data protection, audit trails, and compliance reporting',
      icon: Shield,
      category: 'Advanced',
      priority: 4
    }
  ]

  const getRecommendedFeatures = () => {
    const scores = {
      studentCount: parseInt(answers.studentCount) || 1,
      currentSystems: parseInt(answers.currentSystems) || 1,
      priorities: parseInt(answers.priorities) || 1,
      urgency: parseInt(answers.urgency) || 1,
      budget: parseInt(answers.budget) || 1
    }

    const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0)
    const avgScore = totalScore / 5

    // Filter features based on assessment
    let recommendedFeatures = features.filter(feature => {
      if (avgScore >= 3.5) return true // All features for high scores
      if (avgScore >= 2.5) return feature.priority <= 3 // Most features for medium scores
      return feature.priority <= 2 // Core features for lower scores
    })

    // Prioritize based on specific answers
    if (answers.priorities === '2') { // Communication priority
      recommendedFeatures = recommendedFeatures.filter(f => 
        f.category === 'Communication' || f.category === 'Core'
      )
    } else if (answers.priorities === '3') { // Academic priority
      recommendedFeatures = recommendedFeatures.filter(f => 
        f.category === 'Academic' || f.category === 'Core'
      )
    }

    return recommendedFeatures.slice(0, 8) // Limit to 8 features
  }

  const getPackageName = () => {
    const scores = {
      studentCount: parseInt(answers.studentCount) || 1,
      currentSystems: parseInt(answers.currentSystems) || 1,
      priorities: parseInt(answers.priorities) || 1,
      urgency: parseInt(answers.urgency) || 1,
      budget: parseInt(answers.budget) || 1
    }

    const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0)
    const avgScore = totalScore / 5

    if (avgScore >= 3.5) return 'Enterprise Plus Package'
    if (avgScore >= 2.5) return 'Professional Package'
    return 'Essential Package'
  }

  const handleAnswer = (stepId: string, optionId: string) => {
    setAnswers(prev => ({ ...prev, [stepId]: optionId }))
  }

  const nextStep = () => {
    if (currentStep < assessmentSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      setShowResults(true)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const resetAssessment = () => {
    setCurrentStep(0)
    setAnswers({})
    setShowResults(false)
  }

  const currentStepData = assessmentSteps[currentStep]
  const recommendedFeatures = getRecommendedFeatures()
  const packageName = getPackageName()
  const progress = ((currentStep + 1) / assessmentSteps.length) * 100

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-slate-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <Target className="w-4 h-4" />
            Feature Fit Assessment
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Find Your Perfect 
            <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Schopio Package</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Answer 5 quick questions to discover which features would benefit your school most. Get personalized recommendations without any pricing pressure.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            {!showResults ? (
              <motion.div
                key="assessment"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="bg-white border-0 shadow-xl">
                  {/* Progress Bar */}
                  <div className="h-2 bg-slate-100 rounded-t-xl">
                    <motion.div
                      className="h-full bg-gradient-to-r from-blue-500 to-emerald-500 rounded-t-xl"
                      initial={{ width: 0 }}
                      animate={{ width: `${progress}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>

                  <CardHeader padding="xl">
                    <div className="text-center">
                      <div className="text-sm text-blue-600 font-medium mb-2">
                        Question {currentStep + 1} of {assessmentSteps.length}
                      </div>
                      <h3 className="text-2xl font-bold text-slate-900 mb-2">
                        {currentStepData.title}
                      </h3>
                      <p className="text-lg text-slate-600">
                        {currentStepData.question}
                      </p>
                    </div>
                  </CardHeader>

                  <CardContent padding="xl">
                    <div className="space-y-4 mb-8">
                      {currentStepData.options.map((option) => (
                        <motion.button
                          key={option.id}
                          onClick={() => handleAnswer(currentStepData.id, option.value.toString())}
                          className={`w-full p-4 border-2 rounded-xl text-left transition-all duration-200 ${
                            answers[currentStepData.id] === option.value.toString()
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-slate-200 hover:border-blue-300 hover:bg-blue-25'
                          }`}
                          whileHover={{ scale: 1.01 }}
                          whileTap={{ scale: 0.99 }}
                        >
                          <div className="flex items-center gap-4">
                            <div className={`w-6 h-6 border-2 rounded-full flex items-center justify-center ${
                              answers[currentStepData.id] === option.value.toString()
                                ? 'border-blue-500 bg-blue-500'
                                : 'border-slate-300'
                            }`}>
                              {answers[currentStepData.id] === option.value.toString() && (
                                <div className="w-3 h-3 bg-white rounded-full" />
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="font-semibold text-slate-900">{option.label}</div>
                              {option.description && (
                                <div className="text-sm text-slate-600">{option.description}</div>
                              )}
                            </div>
                          </div>
                        </motion.button>
                      ))}
                    </div>

                    <div className="flex justify-between">
                      <Button
                        onClick={prevStep}
                        disabled={currentStep === 0}
                        variant="outline"
                        className="px-6 py-3 disabled:opacity-50"
                      >
                        Previous
                      </Button>
                      <Button
                        onClick={nextStep}
                        disabled={!answers[currentStepData.id]}
                        icon={currentStep === assessmentSteps.length - 1 ? Target : ArrowRight}
                        iconPosition="right"
                        className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:opacity-90 text-white font-bold px-6 py-3 disabled:opacity-50"
                      >
                        {currentStep === assessmentSteps.length - 1 ? 'Get Recommendations' : 'Next'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ) : (
              <motion.div
                key="results"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-8"
              >
                {/* Results Header */}
                <Card className="bg-gradient-to-r from-blue-600 to-emerald-600 border-0 shadow-xl text-white">
                  <CardContent padding="xl">
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-3 mb-4">
                        <Award className="w-8 h-8" />
                        <h3 className="text-2xl font-bold">Your Recommended Package</h3>
                      </div>
                      <h4 className="text-3xl font-bold mb-2">{packageName}</h4>
                      <p className="text-blue-100 text-lg">
                        Based on your school&apos;s needs and priorities
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* Recommended Features */}
                <Card className="bg-white border-0 shadow-xl">
                  <CardHeader padding="lg">
                    <h3 className="text-2xl font-bold text-slate-900 mb-2">
                      Recommended Features for Your School
                    </h3>
                    <p className="text-slate-600">
                      These features are specifically selected based on your assessment responses.
                    </p>
                  </CardHeader>

                  <CardContent padding="lg">
                    <div className="grid md:grid-cols-2 gap-6">
                      {recommendedFeatures.map((feature, index) => (
                        <motion.div
                          key={feature.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          className="flex items-start gap-4 p-4 border border-slate-200 rounded-xl hover:border-blue-300 hover:bg-blue-25 transition-all duration-200"
                        >
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <feature.icon className="w-5 h-5 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-slate-900 mb-1">{feature.name}</h4>
                            <p className="text-sm text-slate-600">{feature.description}</p>
                            <div className="inline-flex items-center gap-1 mt-2">
                              <div className={`w-2 h-2 rounded-full ${
                                feature.category === 'Core' ? 'bg-green-500' :
                                feature.category === 'Communication' ? 'bg-blue-500' :
                                feature.category === 'Academic' ? 'bg-purple-500' : 'bg-orange-500'
                              }`} />
                              <span className="text-xs text-slate-500 font-medium">{feature.category}</span>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* CTA Section */}
                <Card className="bg-gradient-to-r from-slate-900 to-blue-900 border-0 shadow-xl">
                  <CardContent padding="xl">
                    <div className="text-center text-white">
                      <h3 className="text-2xl font-bold mb-4">
                        Ready to Discuss Your Custom Solution?
                      </h3>
                      <p className="text-blue-200 mb-6 max-w-2xl mx-auto">
                        Our education specialists will create a personalized implementation plan based on your assessment results.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button
                          size="lg"
                          icon={ArrowRight}
                          iconPosition="right"
                          className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:opacity-90 text-white font-bold px-8 py-4"
                        >
                          Discuss Your Custom Solution
                        </Button>
                        <Button
                          onClick={resetAssessment}
                          variant="outline"
                          size="lg"
                          className="border-white text-white hover:bg-white/10 px-6 py-4"
                        >
                          Retake Assessment
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </section>
  )
}

export default FeatureFitAssessment
