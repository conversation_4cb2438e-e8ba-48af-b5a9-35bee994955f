-- Migration: Add Operational Expenses and Enhanced Fields to billingSubscriptions
-- Date: 2025-07-09
-- Purpose: Fix subscription form data persistence and partner profit calculation

-- Add operational expenses fields to billingSubscriptions table
ALTER TABLE billing_subscriptions 
ADD COLUMN IF NOT EXISTS operational_expenses JSONB,
ADD COLUMN IF NOT EXISTS database_costs DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS website_maintenance DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS support_costs DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS infrastructure_costs DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_operational_expenses DECIMAL(10,2) DEFAULT 0;

-- Add admin notes and metadata fields
ALTER TABLE billing_subscriptions 
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS setup_fee DECIMAL(10,2) DEFAULT 0;

-- Add discount tracking fields
ALTER TABLE billing_subscriptions 
ADD COLUMN IF NOT EXISTS discount_start_date DATE,
ADD COLUMN IF NOT EXISTS discount_end_date DATE,
ADD COLUMN IF NOT EXISTS discount_reason TEXT;

-- Add created_by field for audit trail
ALTER TABLE billing_subscriptions 
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES admin_users(id) ON DELETE SET NULL;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_billing_subscriptions_operational_expenses 
ON billing_subscriptions USING GIN (operational_expenses);

CREATE INDEX IF NOT EXISTS idx_billing_subscriptions_created_by 
ON billing_subscriptions (created_by);

-- Update existing records to have default values
UPDATE billing_subscriptions 
SET 
  database_costs = 0,
  website_maintenance = 0,
  support_costs = 0,
  infrastructure_costs = 0,
  total_operational_expenses = 0,
  setup_fee = 0
WHERE 
  database_costs IS NULL 
  OR website_maintenance IS NULL 
  OR support_costs IS NULL 
  OR infrastructure_costs IS NULL 
  OR total_operational_expenses IS NULL 
  OR setup_fee IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN billing_subscriptions.operational_expenses IS 'JSON object containing detailed expense breakdown for partner transparency';
COMMENT ON COLUMN billing_subscriptions.total_operational_expenses IS 'Sum of all operational expenses for quick calculation';
COMMENT ON COLUMN billing_subscriptions.notes IS 'Admin notes about the subscription';
COMMENT ON COLUMN billing_subscriptions.setup_fee IS 'One-time setup fee charged to the client';
COMMENT ON COLUMN billing_subscriptions.created_by IS 'Admin user who created this subscription';

-- Verify the migration
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default
FROM information_schema.columns 
WHERE table_name = 'billing_subscriptions' 
  AND column_name IN (
    'operational_expenses', 
    'database_costs', 
    'website_maintenance', 
    'support_costs', 
    'infrastructure_costs', 
    'total_operational_expenses',
    'notes',
    'setup_fee',
    'discount_start_date',
    'discount_end_date',
    'discount_reason',
    'created_by'
  )
ORDER BY column_name;
