/**
 * Check Subscription Requests in Database
 * This script will check if there are software requests in the database
 */

const { neon } = require('@neondatabase/serverless')

const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const sql = neon(connectionString)

async function checkSubscriptionRequests() {
  try {
    console.log('🔍 Checking Software Requests in Database...')
    console.log('=' .repeat(60))

    // 1. Check total count of software requests
    const [totalCount] = await sql`
      SELECT COUNT(*) as count FROM software_requests
    `
    console.log(`📊 Total Software Requests: ${totalCount.count}`)

    if (totalCount.count === 0) {
      console.log('❌ No software requests found in database')
      
      // Create some test data
      console.log('\n🔧 Creating test software requests...')
      
      // First, get or create a test client
      let [testClient] = await sql`
        SELECT id, school_name FROM clients WHERE email = '<EMAIL>' LIMIT 1
      `
      
      if (!testClient) {
        [testClient] = await sql`
          INSERT INTO clients (school_code, school_name, email, phone, status, actual_student_count)
          VALUES ('TEST001', 'Test School for Subscriptions', '<EMAIL>', '9876543210', 'active', 150)
          RETURNING id, school_name
        `
        console.log(`✅ Created test client: ${testClient.school_name}`)
      }

      // Create test software requests
      const testRequests = [
        {
          clientId: testClient.id,
          requestType: 'demo',
          status: 'pending',
          studentCount: 150,
          facultyCount: 20,
          calculatedAverageFee: 25000
        },
        {
          clientId: testClient.id,
          requestType: 'production',
          status: 'approved',
          studentCount: 200,
          facultyCount: 25,
          calculatedAverageFee: 30000
        },
        {
          clientId: testClient.id,
          requestType: 'demo',
          status: 'under_review',
          studentCount: 100,
          facultyCount: 15,
          calculatedAverageFee: 20000
        }
      ]

      for (const request of testRequests) {
        await sql`
          INSERT INTO software_requests (
            client_id, request_type, status, student_count, faculty_count, 
            calculated_average_fee, terms_accepted, created_at
          ) VALUES (
            ${request.clientId}, ${request.requestType}, ${request.status}, 
            ${request.studentCount}, ${request.facultyCount}, ${request.calculatedAverageFee}, 
            true, NOW()
          )
        `
      }
      
      console.log(`✅ Created ${testRequests.length} test software requests`)
    }

    // 2. Check requests by status
    console.log('\n📋 Software Requests by Status:')
    const statusCounts = await sql`
      SELECT status, COUNT(*) as count 
      FROM software_requests 
      GROUP BY status 
      ORDER BY count DESC
    `
    
    statusCounts.forEach(row => {
      console.log(`   ${row.status}: ${row.count} requests`)
    })

    // 3. Check requests by type
    console.log('\n📋 Software Requests by Type:')
    const typeCounts = await sql`
      SELECT request_type, COUNT(*) as count 
      FROM software_requests 
      GROUP BY request_type 
      ORDER BY count DESC
    `
    
    typeCounts.forEach(row => {
      console.log(`   ${row.request_type}: ${row.count} requests`)
    })

    // 4. Get recent requests with client details
    console.log('\n📋 Recent Software Requests:')
    const recentRequests = await sql`
      SELECT 
        sr.id,
        sr.request_type,
        sr.status,
        sr.student_count,
        sr.created_at,
        c.school_name,
        c.email
      FROM software_requests sr
      LEFT JOIN clients c ON sr.client_id = c.id
      ORDER BY sr.created_at DESC
      LIMIT 10
    `

    if (recentRequests.length === 0) {
      console.log('   ❌ No requests found')
    } else {
      recentRequests.forEach((request, index) => {
        console.log(`   ${index + 1}. ${request.school_name || 'Unknown School'}`)
        console.log(`      Type: ${request.request_type}, Status: ${request.status}`)
        console.log(`      Students: ${request.student_count}, Created: ${new Date(request.created_at).toLocaleDateString()}`)
        console.log(`      Email: ${request.email}`)
        console.log('')
      })
    }

    // 5. Test the API query that the frontend uses
    console.log('\n🔍 Testing Frontend Query (Pending Requests):')
    const pendingRequests = await sql`
      SELECT 
        sr.id,
        sr.client_id,
        sr.request_type,
        sr.status,
        sr.student_count,
        sr.faculty_count,
        sr.calculated_average_fee,
        sr.terms_accepted,
        sr.created_at,
        c.school_name,
        c.school_code,
        c.email
      FROM software_requests sr
      LEFT JOIN clients c ON sr.client_id = c.id
      WHERE sr.status IN ('pending', 'under_review')
      ORDER BY sr.created_at DESC
      LIMIT 20
    `

    console.log(`   Found ${pendingRequests.length} pending/under_review requests`)
    
    if (pendingRequests.length > 0) {
      console.log('   Sample request:')
      const sample = pendingRequests[0]
      console.log(`   - ID: ${sample.id}`)
      console.log(`   - School: ${sample.school_name}`)
      console.log(`   - Status: ${sample.status}`)
      console.log(`   - Type: ${sample.request_type}`)
      console.log(`   - Students: ${sample.student_count}`)
    }

    // 6. Test accepted requests query
    console.log('\n🔍 Testing Frontend Query (Accepted Requests):')
    const acceptedRequests = await sql`
      SELECT 
        sr.id,
        sr.client_id,
        sr.request_type,
        sr.status,
        sr.student_count,
        sr.faculty_count,
        sr.calculated_average_fee,
        sr.terms_accepted,
        sr.created_at,
        c.school_name,
        c.school_code,
        c.email
      FROM software_requests sr
      LEFT JOIN clients c ON sr.client_id = c.id
      WHERE sr.status IN ('approved', 'setup_in_progress', 'activated')
      ORDER BY sr.created_at DESC
      LIMIT 20
    `

    console.log(`   Found ${acceptedRequests.length} approved/setup_in_progress/activated requests`)

    console.log('\n🎉 Database Check Complete!')

  } catch (error) {
    console.error('❌ Database check failed:', error)
    process.exit(1)
  }
}

// Run the check
checkSubscriptionRequests()
