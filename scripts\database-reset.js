/**
 * Database Reset Script
 * Clears all data except admin_users table for system testing
 * Date: July 9, 2025
 */

const { drizzle } = require('drizzle-orm/neon-http')
const { neon } = require('@neondatabase/serverless')

// Database connection
const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const sql = neon(connectionString)
const db = drizzle(sql)

// Tables to clear in dependency order (preserving admin_users)
const tablesToClear = [
  // Clear child tables first (those with foreign keys)
  'ticket_messages',
  'support_tickets',
  'billing_payment_reminders',
  'billing_payments',
  'billing_invoices',
  'subscription_expenses',
  'subscription_discounts',
  'billing_subscriptions',
  'partner_earnings',
  'withdrawal_requests',
  'partner_transactions',
  'partner_commission_escrow',
  'partner_commission_transactions',
  'commission_release_audit',
  'school_referrals',
  'client_users',
  'software_requests',
  'audit_logs',
  'rate_limits',
  'security_events',
  'operational_expenses',

  // Clear parent tables last
  'subscriptions',
  'subscription_plans',
  'referral_codes',
  'partners',
  'clients',
  'leads'
]

async function resetDatabase() {
  try {
    console.log('🔄 Starting database reset...')
    console.log('⚠️  This will clear ALL data except admin_users table')
    
    // Backup admin users count
    const adminCountResult = await sql`SELECT COUNT(*) as count FROM admin_users`
    const adminCount = adminCountResult[0].count
    console.log(`📋 Found ${adminCount} admin users (will be preserved)`)
    
    // Note: Neon doesn't allow disabling foreign key constraints
    // We'll clear tables in dependency order instead
    console.log('🔧 Clearing tables in dependency order...')
    
    let clearedTables = 0
    let totalRecordsCleared = 0
    
    // Clear each table
    for (const table of tablesToClear) {
      try {
        // Get record count before clearing
        const countQuery = `SELECT COUNT(*) as count FROM ${table}`
        const countResult = await sql(countQuery)
        const recordCount = parseInt(countResult[0].count)

        if (recordCount > 0) {
          // Clear the table
          const deleteQuery = `DELETE FROM ${table}`
          await sql(deleteQuery)
          console.log(`✅ Cleared ${table}: ${recordCount} records`)
          totalRecordsCleared += recordCount
        } else {
          console.log(`⚪ ${table}: already empty`)
        }
        clearedTables++
      } catch (error) {
        if (error.message.includes('does not exist')) {
          console.log(`⚠️  Table ${table} does not exist, skipping...`)
        } else {
          console.error(`❌ Error clearing ${table}:`, error.message)
        }
      }
    }
    
    // Foreign key constraints are always enabled in Neon
    console.log('✅ Tables cleared in dependency order')
    
    // Reset sequences for clean IDs
    console.log('🔄 Resetting sequences...')
    const sequences = await sql`
      SELECT sequence_name 
      FROM information_schema.sequences 
      WHERE sequence_schema = 'public'
    `
    
    for (const seq of sequences) {
      try {
        const resetQuery = `ALTER SEQUENCE ${seq.sequence_name} RESTART WITH 1`
        await sql(resetQuery)
      } catch (error) {
        console.log(`⚠️  Could not reset sequence ${seq.sequence_name}`)
      }
    }
    
    // Verify admin users are still there
    const finalAdminCount = await sql`SELECT COUNT(*) as count FROM admin_users`
    const finalCount = finalAdminCount[0].count
    
    console.log('\n🎉 Database reset completed successfully!')
    console.log(`📊 Summary:`)
    console.log(`   - Tables processed: ${clearedTables}`)
    console.log(`   - Total records cleared: ${totalRecordsCleared}`)
    console.log(`   - Admin users preserved: ${finalCount}`)
    
    if (finalCount !== adminCount) {
      console.error('❌ WARNING: Admin user count changed during reset!')
    }
    
    console.log('\n📝 Next steps:')
    console.log('   1. Restart your application server')
    console.log('   2. Test admin login functionality')
    console.log('   3. Begin fresh system testing')
    
  } catch (error) {
    console.error('❌ Database reset failed:', error.message)
    console.error('📝 Full error:', error)
    process.exit(1)
  } finally {
    // Neon HTTP doesn't need explicit connection closing
    console.log('🔌 Database connection closed')
  }
}

// Run the reset
resetDatabase()
