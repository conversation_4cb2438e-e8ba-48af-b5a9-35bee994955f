'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  TrendingUp, 
  Users, 
  Clock, 
  CheckCircle, 
  ArrowRight,
  BarChart3,
  Target,
  Award,
  BookOpen,
  GraduationCap,
  Building,
  School
} from 'lucide-react'

interface SchoolType {
  id: string
  name: string
  icon: any
  description: string
}

interface Challenge {
  id: string
  name: string
  description: string
}

interface SuccessMetric {
  metric: string
  improvement: string
  description: string
  icon: any
}

const ImpactVisualizerSection = () => {
  const [selectedSchoolType, setSelectedSchoolType] = useState<string>('')
  const [selectedChallenges, setSelectedChallenges] = useState<string[]>([])
  const [showResults, setShowResults] = useState(false)

  const schoolTypes: SchoolType[] = [
    {
      id: 'elementary',
      name: 'Elementary School',
      icon: BookOpen,
      description: 'K-5 primary education (200-800 students)'
    },
    {
      id: 'middle',
      name: 'Middle School',
      icon: School,
      description: 'Grades 6-8 (300-1000 students)'
    },
    {
      id: 'high',
      name: 'High School',
      icon: GraduationCap,
      description: 'Grades 9-12 (500-2000 students)'
    },
    {
      id: 'k12',
      name: 'K-12 Campus',
      icon: Building,
      description: 'Complete K-12 campus (800-3000 students)'
    }
  ]

  const challenges: Challenge[] = [
    {
      id: 'admin',
      name: 'Administrative Burden',
      description: 'Too much time on paperwork and manual processes'
    },
    {
      id: 'communication',
      name: 'Parent Communication',
      description: 'Difficulty keeping parents informed and engaged'
    },
    {
      id: 'attendance',
      name: 'Attendance Tracking',
      description: 'Manual attendance and tardiness management'
    },
    {
      id: 'fees',
      name: 'Fee Management',
      description: 'Complex fee collection and financial tracking'
    },
    {
      id: 'performance',
      name: 'Student Performance',
      description: 'Lack of real-time academic insights'
    },
    {
      id: 'reporting',
      name: 'Data & Reporting',
      description: 'Time-consuming report generation'
    }
  ]

  const getSuccessMetrics = (schoolType: string, challenges: string[]): SuccessMetric[] => {
    const baseMetrics: Record<string, SuccessMetric[]> = {
      elementary: [
        {
          metric: '65% reduction in administrative time',
          improvement: 'From 25 hours to 9 hours per week',
          description: 'Automated attendance, fee tracking, and parent communications',
          icon: Clock
        },
        {
          metric: '85% improvement in parent engagement',
          improvement: 'From 40% to 74% active participation',
          description: 'Real-time updates and web portal access',
          icon: Users
        },
        {
          metric: '40% faster report generation',
          improvement: 'From 8 hours to 5 hours per report',
          description: 'Automated academic and behavioral reports',
          icon: BarChart3
        }
      ],
      middle: [
        {
          metric: '70% reduction in disciplinary incidents',
          improvement: 'Early intervention and tracking',
          description: 'Streamlined grade management and discipline tracking',
          icon: Target
        },
        {
          metric: '60% improvement in student retention',
          improvement: 'Predictive analytics for at-risk students',
          description: 'Predictive analytics identify at-risk students',
          icon: TrendingUp
        },
        {
          metric: '50% faster fee collection',
          improvement: 'Automated payment processing',
          description: 'Automated reminders and online payment options',
          icon: CheckCircle
        }
      ],
      high: [
        {
          metric: '75% improvement in schedule efficiency',
          improvement: 'AI-optimized timetables',
          description: 'AI-powered timetable optimization',
          icon: Award
        },
        {
          metric: '80% improvement in college readiness',
          improvement: 'Comprehensive transcript management',
          description: 'Automated transcript and credit management',
          icon: GraduationCap
        },
        {
          metric: '55% faster administrative workflows',
          improvement: 'Digital transformation',
          description: 'Digital forms and approval processes',
          icon: Clock
        }
      ],
      k12: [
        {
          metric: '85% improvement in cross-campus coordination',
          improvement: 'Unified management system',
          description: 'Integrated K-12 management with role-based access',
          icon: Building
        },
        {
          metric: '90% improvement in data accuracy',
          improvement: 'Centralized student information',
          description: 'Single source of truth for all student information',
          icon: BarChart3
        },
        {
          metric: '45% reduction in operational overhead',
          improvement: 'Streamlined multi-campus operations',
          description: 'Centralized management with campus-specific insights',
          icon: TrendingUp
        }
      ]
    }

    let metrics = baseMetrics[schoolType] || baseMetrics.elementary

    // Add challenge-specific metrics
    if (challenges.includes('communication')) {
      metrics.push({
        metric: '92% parent satisfaction increase',
        improvement: 'Real-time communication portal',
        description: 'Instant notifications and two-way messaging',
        icon: Users
      })
    }

    if (challenges.includes('performance')) {
      metrics.push({
        metric: '35% improvement in academic outcomes',
        improvement: 'Data-driven intervention strategies',
        description: 'Early identification of learning gaps',
        icon: Award
      })
    }

    return metrics.slice(0, 6) // Limit to 6 metrics for display
  }

  const handleChallengeToggle = (challengeId: string) => {
    setSelectedChallenges(prev => 
      prev.includes(challengeId) 
        ? prev.filter(id => id !== challengeId)
        : [...prev, challengeId]
    )
  }

  const handleVisualize = () => {
    if (selectedSchoolType && selectedChallenges.length > 0) {
      setShowResults(true)
    }
  }

  const resetTool = () => {
    setSelectedSchoolType('')
    setSelectedChallenges([])
    setShowResults(false)
  }

  const successMetrics = getSuccessMetrics(selectedSchoolType, selectedChallenges)
  const selectedSchool = schoolTypes.find(s => s.id === selectedSchoolType)

  return (
    <section className="py-20 bg-gradient-to-br from-emerald-50 to-blue-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
            <BarChart3 className="w-4 h-4" />
            Impact Visualizer
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            See Success Metrics from 
            <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent"> Similar Schools</span>
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            Discover the specific improvements schools like yours have achieved with Schopio. Select your school type and challenges to see relevant success stories.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            {!showResults ? (
              <motion.div
                key="selection"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-8"
              >
                {/* Step 1: School Type Selection */}
                <Card className="bg-white border-0 shadow-xl">
                  <CardHeader padding="lg">
                    <h3 className="text-2xl font-bold text-slate-900 mb-2">
                      Step 1: Select Your School Type
                    </h3>
                    <p className="text-slate-600">
                      Choose the option that best describes your educational institution.
                    </p>
                  </CardHeader>

                  <CardContent padding="lg">
                    <div className="grid md:grid-cols-2 gap-4">
                      {schoolTypes.map((type) => (
                        <motion.button
                          key={type.id}
                          onClick={() => setSelectedSchoolType(type.id)}
                          className={`p-4 border-2 rounded-xl text-left transition-all duration-200 ${
                            selectedSchoolType === type.id
                              ? 'border-emerald-500 bg-emerald-50'
                              : 'border-slate-200 hover:border-emerald-300 hover:bg-emerald-25'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="flex items-center gap-4">
                            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                              selectedSchoolType === type.id
                                ? 'bg-emerald-500 text-white'
                                : 'bg-slate-100 text-slate-600'
                            }`}>
                              <type.icon className="w-5 h-5" />
                            </div>
                            <div className="flex-1">
                              <div className="font-semibold text-slate-900">{type.name}</div>
                              <div className="text-sm text-slate-600">{type.description}</div>
                            </div>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Step 2: Challenge Selection */}
                {selectedSchoolType && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Card className="bg-white border-0 shadow-xl">
                      <CardHeader padding="lg">
                        <h3 className="text-2xl font-bold text-slate-900 mb-2">
                          Step 2: Select Your Challenges
                        </h3>
                        <p className="text-slate-600">
                          Choose the operational challenges your school currently faces. (Select multiple)
                        </p>
                      </CardHeader>

                      <CardContent padding="lg">
                        <div className="grid md:grid-cols-2 gap-4 mb-6">
                          {challenges.map((challenge) => (
                            <motion.button
                              key={challenge.id}
                              onClick={() => handleChallengeToggle(challenge.id)}
                              className={`p-4 border-2 rounded-xl text-left transition-all duration-200 ${
                                selectedChallenges.includes(challenge.id)
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-slate-200 hover:border-blue-300 hover:bg-blue-25'
                              }`}
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              <div className="flex items-center gap-4">
                                <div className={`w-6 h-6 border-2 rounded-full flex items-center justify-center ${
                                  selectedChallenges.includes(challenge.id)
                                    ? 'border-blue-500 bg-blue-500'
                                    : 'border-slate-300'
                                }`}>
                                  {selectedChallenges.includes(challenge.id) && (
                                    <CheckCircle className="w-4 h-4 text-white" />
                                  )}
                                </div>
                                <div className="flex-1">
                                  <div className="font-semibold text-slate-900">{challenge.name}</div>
                                  <div className="text-sm text-slate-600">{challenge.description}</div>
                                </div>
                              </div>
                            </motion.button>
                          ))}
                        </div>

                        <div className="text-center">
                          <Button
                            onClick={handleVisualize}
                            disabled={!selectedSchoolType || selectedChallenges.length === 0}
                            icon={BarChart3}
                            iconPosition="right"
                            size="lg"
                            className="bg-gradient-to-r from-emerald-600 to-blue-600 hover:opacity-90 text-white font-bold px-8 py-4 disabled:opacity-50"
                          >
                            Visualize Success Metrics
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </motion.div>
            ) : (
              <motion.div
                key="results"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-8"
              >
                {/* Results Header */}
                <Card className="bg-gradient-to-r from-emerald-600 to-blue-600 border-0 shadow-xl text-white">
                  <CardContent padding="xl">
                    <div className="text-center">
                      <h3 className="text-2xl font-bold mb-2">
                        Success Metrics for {selectedSchool?.name}
                      </h3>
                      <p className="text-emerald-100 text-lg">
                        Based on real results from similar schools using Schopio
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* Success Metrics Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {successMetrics.map((metric, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <Card className="h-full bg-white border border-slate-200 shadow-lg hover:shadow-xl transition-all duration-300">
                        <CardContent padding="lg">
                          <div className="text-center">
                            <div className="w-12 h-12 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                              <metric.icon className="w-6 h-6 text-emerald-600" />
                            </div>
                            <div className="text-2xl font-bold text-emerald-600 mb-2">{metric.metric}</div>
                            <div className="text-sm font-semibold text-slate-700 mb-3">{metric.improvement}</div>
                            <div className="text-sm text-slate-600">{metric.description}</div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>

                {/* CTA Section */}
                <Card className="bg-gradient-to-r from-slate-900 to-emerald-900 border-0 shadow-xl">
                  <CardContent padding="xl">
                    <div className="text-center text-white">
                      <h3 className="text-2xl font-bold mb-4">
                        Ready to Achieve Similar Results?
                      </h3>
                      <p className="text-blue-200 mb-6 max-w-2xl mx-auto">
                        Join hundreds of schools that have transformed their operations with Schopio. Get personalized recommendations for your specific needs.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button
                          size="lg"
                          icon={ArrowRight}
                          iconPosition="right"
                          className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:opacity-90 text-white font-bold px-8 py-4"
                        >
                          Get Custom Solution Recommendations
                        </Button>
                        <Button
                          onClick={resetTool}
                          variant="outline"
                          size="lg"
                          className="border-white text-white hover:bg-white/10 px-6 py-4"
                        >
                          Try Different School Type
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </section>
  )
}

export default ImpactVisualizerSection
